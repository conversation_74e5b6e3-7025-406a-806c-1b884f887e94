package com.weinuo.quickcommands22.utils

import android.content.Context
import android.content.pm.PackageManager
import android.util.Log

/**
 * 后台进程管理权限工具类
 * 
 * 管理KILL_BACKGROUND_PROCESSES权限的检查和处理
 * 该权限是普通权限，在AndroidManifest.xml中声明即可自动获得
 * 用于调用ActivityManager.killBackgroundProcesses()方法
 */
object KillBackgroundProcessesPermissionUtil {
    private const val TAG = "KillBgProcessesPermUtil"
    
    /**
     * 检查是否拥有后台进程管理权限
     * 
     * KILL_BACKGROUND_PROCESSES是普通权限，在AndroidManifest.xml中声明即可获得
     * 
     * @param context 上下文
     * @return 是否拥有权限
     */
    fun hasKillBackgroundProcessesPermission(context: Context): Boolean {
        return try {
            val permission = "android.permission.KILL_BACKGROUND_PROCESSES"
            val result = context.checkSelfPermission(permission)
            val hasPermission = result == PackageManager.PERMISSION_GRANTED
            
            Log.d(TAG, "KILL_BACKGROUND_PROCESSES permission check result: $hasPermission")
            hasPermission
        } catch (e: Exception) {
            Log.e(TAG, "Error checking KILL_BACKGROUND_PROCESSES permission", e)
            false
        }
    }
    
    /**
     * 获取权限状态描述
     * 
     * @param context 上下文
     * @return 权限状态描述
     */
    fun getPermissionStatus(context: Context): String {
        return if (hasKillBackgroundProcessesPermission(context)) {
            "已授权"
        } else {
            "未授权"
        }
    }
    
    /**
     * 检查权限是否可用于优化功能
     * 
     * @param context 上下文
     * @return 是否可用于优化
     */
    fun isAvailableForOptimization(context: Context): Boolean {
        val hasPermission = hasKillBackgroundProcessesPermission(context)
        Log.d(TAG, "Permission available for optimization: $hasPermission")
        return hasPermission
    }
}
