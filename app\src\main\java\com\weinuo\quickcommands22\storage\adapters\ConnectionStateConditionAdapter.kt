package com.weinuo.quickcommands22.storage.adapters

import android.util.Log
import com.weinuo.quickcommands22.model.*
import com.weinuo.quickcommands22.storage.NativeTypeStorageManager
import com.weinuo.quickcommands22.storage.AppListStorageEngine
import com.weinuo.quickcommands22.storage.StorageDomain
import com.weinuo.quickcommands22.storage.StorageOperation
import com.weinuo.quickcommands22.storage.StorageType

/**
 * 连接状态条件存储适配器
 *
 * 负责ConnectionStateCondition的原生数据类型存储和重建。
 * 将复杂的连接状态条件对象拆分为独立的原生字段进行存储，
 * 完全避免JSON序列化问题。
 *
 * 支持的字段类型：
 * - 基础字段：id、connectionType、subType
 * - 特定值：specificValue（如SSID名称、设备名称等）
 * - 设备地址：deviceAddress（如蓝牙设备地址）
 *
 * 存储格式示例：
 * condition_{id}_type = "connection_state"
 * condition_{id}_connection_type = "NETWORK_WIFI"
 * condition_{id}_sub_type = "WIFI_CONNECTED"
 * condition_{id}_specific_value = "MyWiFi"
 * condition_{id}_device_address = "00:11:22:33:44:55"
 *
 * @param storageManager 原生类型存储管理器
 * @param appListEngine 应用列表存储引擎
 */
class ConnectionStateConditionAdapter(
    storageManager: NativeTypeStorageManager,
    appListEngine: AppListStorageEngine
) : BaseConditionAdapter<ConnectionStateCondition>(storageManager, appListEngine) {

    companion object {
        private const val TAG = "ConnectionStateConditionAdapter"

        // 字段名常量
        private const val FIELD_CONNECTION_TYPE = "connection_type"
        private const val FIELD_SUB_TYPE = "sub_type"
        private const val FIELD_SPECIFIC_VALUE = "specific_value"
        private const val FIELD_DEVICE_ADDRESS = "device_address"
    }

    override fun getConditionType() = "connection_state"

    override fun save(condition: ConnectionStateCondition): Boolean {
        Log.d(TAG, "开始保存连接状态条件: ${condition.id}")

        return try {
            val prefix = getPrefix(condition.id)
            val operations = mutableListOf<StorageOperation>()

            // 基础字段
            operations.addAll(saveBaseFields(condition.id, condition))

            // 连接状态条件特有字段
            operations.addAll(listOf(
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_CONNECTION_TYPE}", condition.connectionType.name, StorageType.STRING),
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_SUB_TYPE}", condition.subType.name, StorageType.STRING),
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_SPECIFIC_VALUE}", condition.specificValue, StorageType.STRING),
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_DEVICE_ADDRESS}", condition.deviceAddress, StorageType.STRING)
            ))

            val success = storageManager.executeBatch(operations)
            if (success) {
                Log.d(TAG, "连接状态条件保存成功: ${condition.id}")
            } else {
                Log.e(TAG, "连接状态条件保存失败: ${condition.id}")
            }
            success

        } catch (e: Exception) {
            Log.e(TAG, "保存连接状态条件时发生异常: ${condition.id}", e)
            false
        }
    }

    override fun load(conditionId: String): ConnectionStateCondition? {
        Log.d(TAG, "开始加载连接状态条件: $conditionId")

        return try {
            val prefix = getPrefix(conditionId)

            // 检查条件是否存在
            val conditionType = storageManager.loadString(StorageDomain.CONDITIONS, "${prefix}type")
            if (conditionType != getConditionType()) {
                Log.w(TAG, "条件类型不匹配或条件不存在: $conditionId, 期望: ${getConditionType()}, 实际: $conditionType")
                return null
            }

            // 加载枚举字段
            val connectionType = storageManager.loadString(StorageDomain.CONDITIONS, "${prefix}${FIELD_CONNECTION_TYPE}").let {
                try {
                    ConnectionType.valueOf(it)
                } catch (e: IllegalArgumentException) {
                    Log.w(TAG, "无效的连接类型: $it, 使用默认值")
                    ConnectionType.NETWORK_GENERAL
                }
            }

            val subType = storageManager.loadString(StorageDomain.CONDITIONS, "${prefix}${FIELD_SUB_TYPE}").let {
                try {
                    ConnectionSubType.valueOf(it)
                } catch (e: IllegalArgumentException) {
                    Log.w(TAG, "无效的连接子类型: $it, 使用默认值")
                    ConnectionSubType.NETWORK_CONNECTED
                }
            }

            // 加载字符串字段
            val specificValue = storageManager.loadString(StorageDomain.CONDITIONS, "${prefix}${FIELD_SPECIFIC_VALUE}")
            val deviceAddress = storageManager.loadString(StorageDomain.CONDITIONS, "${prefix}${FIELD_DEVICE_ADDRESS}")

            // 重建ConnectionStateCondition对象
            val condition = ConnectionStateCondition(
                id = conditionId,
                connectionType = connectionType,
                subType = subType,
                specificValue = specificValue,
                deviceAddress = deviceAddress
            )

            Log.d(TAG, "连接状态条件加载成功: $conditionId")
            condition

        } catch (e: Exception) {
            Log.e(TAG, "加载连接状态条件时发生异常: $conditionId", e)
            null
        }
    }
}
