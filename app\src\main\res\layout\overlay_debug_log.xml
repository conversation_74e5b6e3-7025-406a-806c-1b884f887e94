<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="#CC000000"
    android:padding="8dp">

    <TextView
        android:id="@+id/logTextView1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="等待日志..."
        android:textColor="#FFFFFF"
        android:textSize="12sp"
        android:fontFamily="monospace"
        android:maxLines="1"
        android:ellipsize="end"
        android:maxWidth="280dp" />

    <TextView
        android:id="@+id/logTextView2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text=""
        android:textColor="#CCFFFFFF"
        android:textSize="12sp"
        android:fontFamily="monospace"
        android:maxLines="1"
        android:ellipsize="end"
        android:maxWidth="280dp" />

    <TextView
        android:id="@+id/logTextView3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text=""
        android:textColor="#99FFFFFF"
        android:textSize="12sp"
        android:fontFamily="monospace"
        android:maxLines="1"
        android:ellipsize="end"
        android:maxWidth="280dp" />

</LinearLayout>
