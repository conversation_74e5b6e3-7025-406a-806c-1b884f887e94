package com.weinuo.quickcommands22.ui.screens.skyblue


import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.statusBars
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.outlined.Edit
import androidx.compose.material.icons.outlined.Delete
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.Alignment
import androidx.compose.material3.ExperimentalMaterial3Api
import com.weinuo.quickcommands22.ui.components.themed.ThemedFloatingActionButton
import com.weinuo.quickcommands22.ui.theme.config.FloatingActionButtonConfig
import androidx.compose.material3.Icon
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.MenuDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.dp
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.DpOffset
import androidx.compose.foundation.background
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.layout.widthIn

import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.res.stringResource
import androidx.navigation.NavController
import com.weinuo.quickcommands22.R
import com.weinuo.quickcommands22.data.QuickCommandRepository
import com.weinuo.quickcommands22.data.SettingsRepository
import com.weinuo.quickcommands22.model.QuickCommand
import com.weinuo.quickcommands22.model.CameraTask
import com.weinuo.quickcommands22.model.CameraOperation
import com.weinuo.quickcommands22.model.MediaTask
import com.weinuo.quickcommands22.model.MediaOperation
import com.weinuo.quickcommands22.model.ApplicationTask
import com.weinuo.quickcommands22.model.ApplicationOperation
import com.weinuo.quickcommands22.model.FileOperationTask
import com.weinuo.quickcommands22.model.FileOperation
import com.weinuo.quickcommands22.model.AppStateCondition
import com.weinuo.quickcommands22.model.AppStateType
import androidx.compose.ui.platform.LocalContext
import com.weinuo.quickcommands22.shortcut.ShortcutManager
import com.weinuo.quickcommands22.ui.activities.QuickCommandFormActivity
import com.weinuo.quickcommands22.ui.theme.config.TopAppBarStyle
import com.weinuo.quickcommands22.ui.theme.config.TopAppBarConfig
import com.weinuo.quickcommands22.ui.components.themed.ThemedQuickCommandCard
import com.weinuo.quickcommands22.ui.components.themed.ThemedSearchTextField
import com.weinuo.quickcommands22.ui.components.skyblue.SkyBlueDialog
import com.weinuo.quickcommands22.ui.components.skyblue.SkyBlueDialogButton
import com.weinuo.quickcommands22.ui.components.skyblue.IOSStyleTextInput
import com.weinuo.quickcommands22.ui.theme.manager.DialogSpacingConfigurationManager
import com.weinuo.quickcommands22.ui.theme.manager.UISpacingConfigurationManager
import com.weinuo.quickcommands22.ui.theme.manager.LocalThemeContext
import com.weinuo.quickcommands22.ui.theme.skyblue.SkyBlueStyleConfiguration
import com.weinuo.quickcommands22.ui.theme.manager.ThemeManager
import com.weinuo.quickcommands22.ui.theme.config.DividerConfig


import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
// import dev.chrisbanes.haze.hazeSource // 不再需要，由ThemedScaffold内部处理
// import dev.chrisbanes.haze.hazeEffect // 不再在此文件中直接使用
// import androidx.compose.foundation.clickable // 不再在此文件中直接使用
import com.weinuo.quickcommands22.ui.effects.HazeManager
import com.weinuo.quickcommands22.ui.components.integrated.SetIntegratedTopAppBar
import com.weinuo.quickcommands22.ui.components.integrated.rememberIntegratedTopAppBarScrollBehavior
import androidx.compose.ui.input.nestedscroll.nestedScroll

/**
 * 天空蓝主题专用 - 快捷指令屏幕
 *
 * 显示用户创建的快捷指令列表，并提供创建新指令的功能
 * 此版本专为天空蓝主题设计，支持整合设计风格和模糊效果
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SkyBlueQuickCommandsScreen(
    navController: NavController,
    quickCommandRepository: QuickCommandRepository = QuickCommandRepository.getInstance(navController.context),
    shortcutManager: ShortcutManager = ShortcutManager(navController.context)
) {
    val focusManager = LocalFocusManager.current
    val context = LocalContext.current

    // 搜索状态
    var searchQuery by remember { mutableStateOf("") }

    // 菜单状态管理 - 现在使用全局HazeManager管理
    // var showContextMenu by remember { mutableStateOf(false) } // 已移除，使用全局状态
    // var contextMenuCommand by remember { mutableStateOf<QuickCommand?>(null) } // 已移除，使用全局状态

    // 获取实验性功能状态
    val settingsRepository = remember { SettingsRepository(context) }
    val globalSettings by settingsRepository.globalSettings.collectAsState()

    // 获取页面布局配置（天空蓝主题专用）
    val pageLayoutConfig = SkyBlueStyleConfiguration.getDynamicPageLayoutConfig(settingsRepository)
    val cardStyleConfig = SkyBlueStyleConfiguration.getDynamicCardStyleConfig(settingsRepository)

    // 获取指令列表
    val allQuickCommands by quickCommandRepository.quickCommands.collectAsState()

    // 过滤包含实验性功能的快捷指令
    val quickCommands = remember(allQuickCommands, globalSettings.experimentalFeaturesEnabled) {
        if (globalSettings.experimentalFeaturesEnabled) {
            // 实验性功能启用时，显示所有指令
            allQuickCommands
        } else {
            // 实验性功能未启用时，过滤掉包含实验性功能的指令
            allQuickCommands.filter { command ->
                !containsExperimentalFeatures(command)
            }
        }
    }

    // 根据搜索查询过滤快捷指令
    val filteredQuickCommands = remember(quickCommands, searchQuery) {
        if (searchQuery.isEmpty()) {
            quickCommands
        } else {
            quickCommands.filter { command ->
                command.name.contains(searchQuery, ignoreCase = true)
            }
        }
    }

    // 新建指令对话框已移除，现在直接启动Activity

    // 控制删除确认对话框的显示状态
    var showDeleteConfirmDialog by remember { mutableStateOf(false) }
    var quickCommandToDelete by remember { mutableStateOf<QuickCommand?>(null) }

    // 控制重命名对话框的显示状态
    var showRenameDialog by remember { mutableStateOf(false) }
    var quickCommandToRename by remember { mutableStateOf<QuickCommand?>(null) }

    // 计算TopAppBar高度，用于内容的初始顶部padding
    val density = LocalDensity.current
    val statusBarHeight = with(density) { WindowInsets.statusBars.getTop(density).toDp() }
    val topAppBarHeight = globalSettings.topAppBarHeight.dp + statusBarHeight // StandardTopAppBar高度

    // 获取HazeManager用于上下文菜单
    val hazeManager = remember { HazeManager.getInstance(context) }

    // 创建LazyColumn滚动状态
    val lazyListState = rememberLazyListState()

    // 创建滚动行为 - 支持可折叠标题栏，只有在内容可滚动时才允许标题栏折叠
    val scrollBehavior = rememberIntegratedTopAppBarScrollBehavior(
        canScroll = {
            // 检查LazyColumn是否可以滚动
            lazyListState.canScrollBackward || lazyListState.canScrollForward
        }
    )

    // 配置TopAppBar - 支持可折叠类型
    SetIntegratedTopAppBar(
        config = TopAppBarConfig(
            title = stringResource(R.string.quick_commands_title),
            style = TopAppBarStyle.STANDARD,
            scrollBehavior = scrollBehavior, // 关键：添加滚动行为
            windowInsets = WindowInsets.statusBars
        )
    )

    // 动态计算顶部padding - 根据标题栏类型优化内容定位
    val topPadding = remember(globalSettings.topAppBarType, statusBarHeight, topAppBarHeight) {
        if (globalSettings.topAppBarType == "collapsible") {
            // 可折叠模式：使用展开状态高度，确保内容不被遮挡
            152.dp + statusBarHeight
        } else {
            // 标准模式：使用固定高度
            topAppBarHeight
        }
    }

    // 直接返回页面内容，TopAppBar由IntegratedMainLayout处理
    Box(modifier = Modifier.fillMaxSize()) {
        // 主要内容区域
        LazyColumn(
            state = lazyListState,
            modifier = Modifier
                .fillMaxSize()
                .nestedScroll(scrollBehavior.nestedScrollConnection),
            contentPadding = PaddingValues(
                top = topPadding, // 动态计算的顶部padding
                bottom = pageLayoutConfig.bottomPadding // 使用动态配置的底部边距
            ),
            verticalArrangement = Arrangement.spacedBy(cardStyleConfig.itemSpacing)
        ) {
            // 搜索框
            item {
                ThemedSearchTextField(
                    searchQuery = searchQuery,
                    onSearchQueryChange = { searchQuery = it },
                    onClearSearch = {
                        searchQuery = ""
                        focusManager.clearFocus()
                    },
                    placeholder = stringResource(R.string.search_quick_commands),
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(pageLayoutConfig.searchFieldMargin)
                )
            }

            if (quickCommands.isEmpty()) {
                // 显示空状态
                item {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(400.dp), // 给一个固定高度
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = stringResource(R.string.no_quick_commands),
                            style = MaterialTheme.typography.bodyLarge,
                            color = MaterialTheme.colorScheme.onSurfaceVariant,
                            modifier = Modifier.padding(16.dp)
                        )
                    }
                }
            } else if (filteredQuickCommands.isEmpty()) {
                // 显示搜索无结果状态
                item {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(400.dp), // 给一个固定高度
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = stringResource(R.string.no_search_results),
                            style = MaterialTheme.typography.bodyLarge,
                            color = MaterialTheme.colorScheme.onSurfaceVariant,
                            modifier = Modifier.padding(16.dp)
                        )
                    }
                }
            } else {
                // 显示快捷指令列表
                items(filteredQuickCommands) { command ->
                    Box(modifier = Modifier.padding(horizontal = pageLayoutConfig.contentHorizontalPadding)) {
                        ThemedQuickCommandCard(
                            command = command,
                            onClick = { clickedCommand ->
                                // 启动快捷指令表单Activity（编辑模式）
                                QuickCommandFormActivity.startForEdit(context, clickedCommand.id)
                            },
                            onLongClick = {
                                // 显示上下文菜单 - 使用全局HazeManager
                                hazeManager.showContextMenu(command)
                            },
                            onEnabledChanged = { isEnabled ->
                                // 更新快捷指令的启用状态（使用协程）
                                val updatedCommand = command.copy(isEnabled = isEnabled)
                                CoroutineScope(Dispatchers.IO).launch {
                                    quickCommandRepository.saveCommand(updatedCommand)
                                }
                            }
                        )

                        // 上下文菜单 - 使用全局HazeManager状态
                        if (hazeManager.showContextMenu && hazeManager.contextMenuCommand?.id == command.id) {
                            QuickCommandContextMenu(
                                command = command,
                                onDismiss = {
                                    hazeManager.hideContextMenu()
                                },
                                onRename = {
                                    // 重命名功能 - 弹出重命名对话框
                                    quickCommandToRename = command
                                    showRenameDialog = true
                                    hazeManager.hideContextMenu()
                                },
                                onDelete = {
                                    // 删除功能
                                    quickCommandToDelete = command
                                    showDeleteConfirmDialog = true
                                    hazeManager.hideContextMenu()
                                }
                            )
                        }
                    }
                }
            }
        }

        // FloatingActionButton - 居中底部位置
        Box(
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .padding(bottom = 96.dp) // 80dp(导航栏高度) + 16dp(额外间距)
        ) {
            ThemedFloatingActionButton(
                config = FloatingActionButtonConfig(
                    onClick = {
                        // 启动快捷指令表单Activity（创建模式）
                        QuickCommandFormActivity.startForCreate(context)
                    },
                    content = {
                        Icon(
                            imageVector = Icons.Filled.Add,
                            contentDescription = stringResource(R.string.quick_command_new)
                        )
                    }
                )
            )
        }
    }

    // 新建指令对话框已移除，现在直接启动Activity

    // 删除确认对话框
    if (showDeleteConfirmDialog && quickCommandToDelete != null) {
        SkyBlueDialog(
            onDismissRequest = {
                showDeleteConfirmDialog = false
                quickCommandToDelete = null
            },
            settingsRepository = settingsRepository,
            title = { Text("是否删除此指令？") },
            text = {
                // 添加额外间距，仅用于删除对话框
                Spacer(modifier = Modifier.height(12.dp))
            },
            confirmButton = {
                SkyBlueDialogButton(
                    text = "删除",
                    onClick = {
                        val commandId = quickCommandToDelete!!.id

                        // 从存储中删除指令数据（使用协程）
                        CoroutineScope(Dispatchers.IO).launch {
                            quickCommandRepository.deleteCommand(commandId)
                        }

                        // 关闭对话框
                        showDeleteConfirmDialog = false
                        quickCommandToDelete = null
                    },
                    isPrimary = true,
                    settingsRepository = settingsRepository,
                    isDestructive = true // 标记为危险操作，使用红色
                )
            },
            dismissButton = {
                SkyBlueDialogButton(
                    text = "取消",
                    onClick = {
                        showDeleteConfirmDialog = false
                        quickCommandToDelete = null
                    },
                    isPrimary = false,
                    settingsRepository = settingsRepository
                )
            }
        )
    }

    // 重命名对话框
    if (showRenameDialog && quickCommandToRename != null) {
        var inputText by remember(quickCommandToRename) { mutableStateOf(quickCommandToRename!!.name) }

        // 获取UI间距配置
        val context = LocalContext.current
        val uiSpacingConfigManager = remember { UISpacingConfigurationManager.getInstance(context, settingsRepository) }
        val uiSpacingConfig = uiSpacingConfigManager.getUISpacingConfiguration()

        // 获取对话框间距配置
        val dialogSpacingConfigManager = remember { DialogSpacingConfigurationManager.getInstance(context, settingsRepository) }
        val dialogSpacingConfig = dialogSpacingConfigManager.getDialogSpacingConfiguration()

        SkyBlueDialog(
            onDismissRequest = {
                showRenameDialog = false
                quickCommandToRename = null
            },
            settingsRepository = settingsRepository,
            title = { Text("重命名指令") },
            text = {
                Column(
                    modifier = Modifier.padding(vertical = uiSpacingConfig.settingsDescriptionSpacing.dp)
                ) {
                    // iOS风格的文本输入框 - 与分割线缩进保持一致
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = dialogSpacingConfig.dividerHorizontalPadding.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        IOSStyleTextInput(
                            value = inputText,
                            onValueChange = { newValue ->
                                inputText = newValue
                            },
                            placeholder = "请输入指令名称",
                            modifier = Modifier.padding(bottom = uiSpacingConfig.settingsItemSpacing.dp),
                            settingsRepository = settingsRepository
                        )
                    }
                }
            },
            confirmButton = {
                SkyBlueDialogButton(
                    text = "确定",
                    onClick = {
                        val trimmedName = inputText.trim()
                        if (trimmedName.isNotEmpty()) {
                            val commandId = quickCommandToRename!!.id

                            // 更新指令名称（使用协程）
                            CoroutineScope(Dispatchers.IO).launch {
                                val updatedCommand = quickCommandToRename!!.copy(name = trimmedName)
                                quickCommandRepository.saveCommand(updatedCommand)
                            }

                            // 关闭对话框
                            showRenameDialog = false
                            quickCommandToRename = null
                        }
                    },
                    isPrimary = true,
                    settingsRepository = settingsRepository
                )
            },
            dismissButton = {
                SkyBlueDialogButton(
                    text = "取消",
                    onClick = {
                        showRenameDialog = false
                        quickCommandToRename = null
                    },
                    isPrimary = false,
                    settingsRepository = settingsRepository
                )
            }
        )
    }

    // 模糊遮罩层已移动到IntegratedMainLayout的全局层级
    // 现在由HazeManager统一管理，确保能够模糊所有组件包括底部导航栏
}

/**
 * 检查快捷指令是否包含实验性功能
 *
 * @param command 要检查的快捷指令
 * @return 如果包含实验性功能返回true，否则返回false
 */
private fun containsExperimentalFeatures(command: QuickCommand): Boolean {
    // 检查任务列表中是否包含实验性功能
    val hasExperimentalTasks = command.tasks.any { task ->
        when (task) {
            is CameraTask -> {
                // 检查是否是实验性的相机操作（拍照、录像）
                task.operation == CameraOperation.TAKE_PHOTO ||
                task.operation == CameraOperation.RECORD_VIDEO
            }
            is MediaTask -> {
                // 检查是否是实验性的媒体操作（麦克风录音）
                task.operation == MediaOperation.MICROPHONE_RECORDING
            }
            is ApplicationTask -> {
                // 检查是否使用了实验性的应用任务功能（跳过VPN应用）
                task.operation == ApplicationOperation.FORCE_STOP_APP && task.skipVpnApp
            }
            is FileOperationTask -> {
                // 检查是否是实验性的文件操作（文件管理，但不包括写入文件和打开文件）
                task.operation == FileOperation.FILE_OPERATION
            }
            else -> false
        }
    }

    // 检查触发条件中是否包含实验性功能
    val hasExperimentalTriggerConditions = command.triggerConditions.any { condition ->
        when (condition) {
            is AppStateCondition -> {
                // 检查是否是后台时间条件且使用了VPN配置
                condition.stateType == AppStateType.BACKGROUND_TIME_EXCEEDED && condition.skipVpnApp
            }
            else -> false
        }
    }

    // 检查中止条件中是否包含实验性功能
    val hasExperimentalAbortConditions = command.abortConditions.any { condition ->
        when (condition) {
            is AppStateCondition -> {
                // 检查是否是后台时间条件且使用了VPN配置
                condition.stateType == AppStateType.BACKGROUND_TIME_EXCEEDED && condition.skipVpnApp
            }
            else -> false
        }
    }

    return hasExperimentalTasks || hasExperimentalTriggerConditions || hasExperimentalAbortConditions
}

/**
 * 快捷指令上下文菜单组件
 *
 * 参考全局设置中的下拉菜单样式，提供圆角菜单功能
 */
@Composable
private fun QuickCommandContextMenu(
    command: QuickCommand,
    onDismiss: () -> Unit,
    onRename: () -> Unit,
    onDelete: () -> Unit
) {
    val context = LocalContext.current
    val themeManager = remember { ThemeManager.getInstance(context) }

    // 获取动态卡片样式配置（仅在天空蓝主题下使用）
    val cardStyle = if (themeManager.getCurrentThemeId() == "sky_blue") {
        val settingsRepository = remember { SettingsRepository(context) }
        SkyBlueStyleConfiguration.getDynamicCardStyleConfig(settingsRepository)
    } else {
        val themeContext = LocalThemeContext.current
        themeContext.styleConfiguration.cardStyle
    }

    // 获取主题上下文和UI间距配置（用于分割线）
    val settingsRepository = remember { SettingsRepository(context) }
    val themeContext = LocalThemeContext.current
    val uiSpacingConfigManager = remember {
        com.weinuo.quickcommands22.ui.theme.manager.UISpacingConfigurationManager.getInstance(context, settingsRepository)
    }
    val uiSpacingConfig = uiSpacingConfigManager.getUISpacingConfiguration()

    DropdownMenu(
        expanded = true,
        onDismissRequest = onDismiss,
        offset = DpOffset(x = 0.dp, y = 0.dp),
        modifier = Modifier
            .widthIn(min = 160.dp)
            .background(
                color = MaterialTheme.colorScheme.surfaceContainerLow,
                shape = RoundedCornerShape(cardStyle.defaultCornerRadius)
            ),
        shape = RoundedCornerShape(cardStyle.defaultCornerRadius)
    ) {
        // 重命名选项
        DropdownMenuItem(
            text = {
                androidx.compose.foundation.layout.Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    Icon(
                        imageVector = Icons.Outlined.Edit,
                        contentDescription = "重命名",
                        tint = MaterialTheme.colorScheme.onSurface
                    )
                    Text(
                        text = "重命名",
                        color = MaterialTheme.colorScheme.onSurface,
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            },
            onClick = onRename,
            colors = MenuDefaults.itemColors(
                textColor = MaterialTheme.colorScheme.onSurface
            ),
            contentPadding = PaddingValues(horizontal = 16.dp, vertical = 12.dp)
        )

        // 添加分割线（在菜单项之间）
        themeContext.componentFactory.createDivider()(
            DividerConfig(
                modifier = Modifier.padding(horizontal = (16 + uiSpacingConfig.dividerHorizontalPadding).dp),
                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
            )
        )

        // 删除选项
        DropdownMenuItem(
            text = {
                androidx.compose.foundation.layout.Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    Icon(
                        imageVector = Icons.Outlined.Delete,
                        contentDescription = "删除指令",
                        tint = MaterialTheme.colorScheme.onSurface
                    )
                    Text(
                        text = "删除指令",
                        color = MaterialTheme.colorScheme.onSurface,
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            },
            onClick = onDelete,
            colors = MenuDefaults.itemColors(
                textColor = MaterialTheme.colorScheme.onSurface
            ),
            contentPadding = PaddingValues(horizontal = 16.dp, vertical = 12.dp)
        )
    }
}

// BlurredContextMenuOverlay已移动到IntegratedMainLayout作为GlobalBlurredContextMenuOverlay
// 现在由HazeManager统一管理，确保能够模糊所有组件包括底部导航栏

