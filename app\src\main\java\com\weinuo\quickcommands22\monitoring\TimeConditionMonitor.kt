package com.weinuo.quickcommands22.monitoring

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import com.weinuo.quickcommands22.model.*
import com.weinuo.quickcommands22.execution.SharedConditionEvaluator
import kotlinx.coroutines.*
import java.util.*

/**
 * 时间条件监听器
 * 监听各种时间条件变化，触发相应的条件
 *
 * 支持的监听功能：
 * - 秒表倒计时
 * - 日出日落时间
 * - 日程时间（一次性、每日、每周等重复模式）
 * - 周期时间
 * - 延迟触发
 * - 周期触发
 */
class TimeConditionMonitor(
    private val context: Context,
    private val onConditionTriggered: (TimeBasedCondition, Map<String, Any>) -> Unit
) {

    companion object {
        private const val TAG = "TimeConditionMonitor"
        private const val PREFS_NAME = "time_condition_states"
        private const val PREFIX_CONDITION_STATE = "condition_state_"
    }

    // 协程作用域
    private val monitorScope = CoroutineScope(Dispatchers.Default + SupervisorJob())

    // 监听器状态
    private var isMonitoring = false

    // 注册的条件列表
    private val registeredConditions = mutableListOf<TimeBasedCondition>()

    // 时间检查任务
    private var timeCheckJob: Job? = null

    // 条件评估器
    private val conditionEvaluator = SharedConditionEvaluator(context)

    // SharedPreferences用于持久化状态
    private val sharedPreferences: SharedPreferences = context.getSharedPreferences(
        PREFS_NAME, Context.MODE_PRIVATE
    )

    // 上次检查的时间状态缓存
    private val lastTimeStates = mutableMapOf<String, Any>()

    init {
        // 启动时加载持久化状态
        loadPersistedStates()
    }

    /**
     * 加载持久化的状态
     */
    private fun loadPersistedStates() {
        try {
            val allPrefs = sharedPreferences.all
            for ((key, value) in allPrefs) {
                if (key.startsWith(PREFIX_CONDITION_STATE)) {
                    val stateKey = key.removePrefix(PREFIX_CONDITION_STATE)
                    value?.let { lastTimeStates[stateKey] = it }
                }
            }
            Log.d(TAG, "Loaded ${lastTimeStates.size} persisted condition states")
        } catch (e: Exception) {
            Log.e(TAG, "Error loading persisted states", e)
        }
    }

    /**
     * 保存状态到持久化存储
     */
    private fun saveState(key: String, value: Any) {
        try {
            lastTimeStates[key] = value

            val editor = sharedPreferences.edit()
            when (value) {
                is Boolean -> editor.putBoolean("$PREFIX_CONDITION_STATE$key", value)
                is Long -> editor.putLong("$PREFIX_CONDITION_STATE$key", value)
                is Int -> editor.putInt("$PREFIX_CONDITION_STATE$key", value)
                is String -> editor.putString("$PREFIX_CONDITION_STATE$key", value)
                else -> {
                    Log.w(TAG, "Unsupported state value type: ${value::class.simpleName}")
                    return
                }
            }
            editor.apply()
        } catch (e: Exception) {
            Log.e(TAG, "Error saving state: $key = $value", e)
        }
    }

    /**
     * 注册时间条件
     */
    fun registerCondition(condition: TimeBasedCondition) {
        synchronized(registeredConditions) {
            if (!registeredConditions.any { it.id == condition.id }) {
                registeredConditions.add(condition)
                Log.d(TAG, "Registered time condition: ${condition.getDescription()}")
            }
        }
    }

    /**
     * 取消注册时间条件
     */
    fun unregisterCondition(conditionId: String) {
        synchronized(registeredConditions) {
            val removed = registeredConditions.removeAll { it.id == conditionId }
            if (removed) {
                Log.d(TAG, "Unregistered time condition: $conditionId")
            }
        }
    }

    /**
     * 获取注册条件数量（用于测试）
     */
    fun getRegisteredConditionCount(): Int {
        return synchronized(registeredConditions) {
            registeredConditions.size
        }
    }

    /**
     * 检查是否正在监听（用于测试）
     */
    fun isMonitoring(): Boolean {
        return isMonitoring
    }

    /**
     * 清除所有注册的条件
     */
    fun clearAllConditions() {
        synchronized(registeredConditions) {
            registeredConditions.clear()
            lastTimeStates.clear()

            // 清除持久化状态
            try {
                val editor = sharedPreferences.edit()
                editor.clear()
                editor.apply()
                Log.d(TAG, "Cleared all time conditions and persisted states")
            } catch (e: Exception) {
                Log.e(TAG, "Error clearing persisted states", e)
            }
        }
    }

    /**
     * 开始监听时间条件
     */
    fun startMonitoring() {
        if (isMonitoring) {
            Log.d(TAG, "Time monitoring already started")
            return
        }

        try {
            startTimeCheckJob()
            isMonitoring = true
            Log.d(TAG, "Time monitoring started successfully")

        } catch (e: Exception) {
            Log.e(TAG, "Error starting time monitoring", e)
            stopMonitoring()
        }
    }

    /**
     * 停止监听时间条件
     */
    fun stopMonitoring() {
        if (!isMonitoring) {
            Log.d(TAG, "Time monitoring already stopped")
            return
        }

        try {
            timeCheckJob?.cancel()
            timeCheckJob = null

            isMonitoring = false
            Log.d(TAG, "Time monitoring stopped successfully")

        } catch (e: Exception) {
            Log.e(TAG, "Error stopping time monitoring", e)
        }
    }

    /**
     * 启动时间检查任务
     */
    private fun startTimeCheckJob() {
        timeCheckJob = monitorScope.launch {
            Log.d(TAG, "Starting time check job...")

            while (isActive) {
                try {
                    // 获取当前时间
                    val calendar = Calendar.getInstance()
                    val hour = calendar.get(Calendar.HOUR_OF_DAY)
                    val minute = calendar.get(Calendar.MINUTE)
                    val second = calendar.get(Calendar.SECOND)

                    Log.d(TAG, "Time check: ${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}:${second.toString().padStart(2, '0')}")

                    // 检查所有注册的时间条件
                    checkTimeConditions(hour, minute, second)

                    // 计算到下一分钟的延迟时间
                    val currentSeconds = calendar.get(Calendar.SECOND)
                    val currentMillis = calendar.get(Calendar.MILLISECOND)
                    val delayToNextMinute = (60 - currentSeconds) * 1000L - currentMillis + 100 // 加100毫秒确保跨过整分钟

                    Log.d(TAG, "Next time check in ${delayToNextMinute}ms")

                    delay(delayToNextMinute)
                } catch (e: CancellationException) {
                    Log.d(TAG, "Time check job cancelled")
                    break
                } catch (e: Exception) {
                    Log.e(TAG, "Error in time check job", e)
                    if (isActive) {
                        delay(60000) // 出错时等待1分钟再重试
                    }
                }
            }
            Log.d(TAG, "Time check job ended")
        }
    }

    /**
     * 检查时间条件
     */
    private suspend fun checkTimeConditions(hour: Int, minute: Int, second: Int) {
        try {
            synchronized(registeredConditions) {
                for (condition in registeredConditions) {
                    when (condition.timeConditionType) {
                        TimeConditionType.SCHEDULED_TIME -> {
                            checkScheduledTimeCondition(condition, hour, minute)
                        }
                        TimeConditionType.STOPWATCH -> {
                            checkStopwatchCondition(condition)
                        }
                        TimeConditionType.SUN_EVENT -> {
                            checkSunEventCondition(condition, hour, minute)
                        }
                        TimeConditionType.PERIODIC_TIME -> {
                            checkPeriodicTimeCondition(condition, hour, minute)
                        }
                        TimeConditionType.DELAYED_TRIGGER -> {
                            checkDelayedTriggerCondition(condition)
                        }
                        TimeConditionType.PERIODIC_TRIGGER -> {
                            checkPeriodicTriggerCondition(condition)
                        }
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error checking time conditions", e)
        }
    }

    /**
     * 检查日程时间条件
     */
    private fun checkScheduledTimeCondition(condition: TimeBasedCondition, hour: Int, minute: Int) {
        try {
            val targetHour = condition.hour
            val targetMinute = condition.minute

            if (hour == targetHour && minute == targetMinute) {
                // 检查重复模式
                val calendar = Calendar.getInstance()
                val currentDayOfWeek = DayOfWeek.fromCalendarValue(calendar.get(Calendar.DAY_OF_WEEK))

                val shouldTrigger = when (condition.timeRepeatMode) {
                    TimeRepeatMode.ONCE -> {
                        // 仅一次：检查是否已经触发过
                        val conditionKey = "scheduled_once_${condition.id}"
                        val hasTriggered = lastTimeStates[conditionKey] as? Boolean ?: false

                        if (!hasTriggered) {
                            val currentYear = calendar.get(Calendar.YEAR)
                            val currentMonth = calendar.get(Calendar.MONTH) + 1
                            val currentDay = calendar.get(Calendar.DAY_OF_MONTH)

                            val matches = condition.year == currentYear &&
                                        condition.month == currentMonth &&
                                        condition.day == currentDay

                            if (matches) {
                                saveState(conditionKey, true)
                            }
                            matches
                        } else {
                            false
                        }
                    }
                    TimeRepeatMode.DAILY -> {
                        // 每天：避免同一分钟内重复触发
                        val conditionKey = "scheduled_daily_${condition.id}_${hour}_${minute}"
                        val lastTriggered = lastTimeStates[conditionKey] as? Long ?: 0L
                        val currentTime = System.currentTimeMillis()

                        if (currentTime - lastTriggered > 60000) { // 至少间隔1分钟
                            saveState(conditionKey, currentTime)
                            true
                        } else {
                            false
                        }
                    }
                    TimeRepeatMode.WEEKLY -> {
                        if (condition.selectedDays.contains(currentDayOfWeek)) {
                            // 避免同一分钟内重复触发
                            val conditionKey = "scheduled_weekly_${condition.id}_${hour}_${minute}"
                            val lastTriggered = lastTimeStates[conditionKey] as? Long ?: 0L
                            val currentTime = System.currentTimeMillis()

                            if (currentTime - lastTriggered > 60000) { // 至少间隔1分钟
                                saveState(conditionKey, currentTime)
                                true
                            } else {
                                false
                            }
                        } else {
                            false
                        }
                    }
                    TimeRepeatMode.BI_WEEKLY -> {
                        // 双周模式需要更复杂的计算
                        if (condition.selectedDays.contains(currentDayOfWeek)) {
                            // 避免同一分钟内重复触发
                            val conditionKey = "scheduled_biweekly_${condition.id}_${hour}_${minute}"
                            val lastTriggered = lastTimeStates[conditionKey] as? Long ?: 0L
                            val currentTime = System.currentTimeMillis()

                            if (currentTime - lastTriggered > 60000) { // 至少间隔1分钟
                                saveState(conditionKey, currentTime)
                                true
                            } else {
                                false
                            }
                        } else {
                            false
                        }
                    }
                    TimeRepeatMode.MONTHLY -> {
                        if (calendar.get(Calendar.DAY_OF_MONTH) == condition.day) {
                            // 避免同一分钟内重复触发
                            val conditionKey = "scheduled_monthly_${condition.id}_${hour}_${minute}"
                            val lastTriggered = lastTimeStates[conditionKey] as? Long ?: 0L
                            val currentTime = System.currentTimeMillis()

                            if (currentTime - lastTriggered > 60000) { // 至少间隔1分钟
                                saveState(conditionKey, currentTime)
                                true
                            } else {
                                false
                            }
                        } else {
                            false
                        }
                    }
                    TimeRepeatMode.YEARLY -> {
                        val currentMonth = calendar.get(Calendar.MONTH) + 1
                        val currentDay = calendar.get(Calendar.DAY_OF_MONTH)

                        if (condition.month == currentMonth && condition.day == currentDay) {
                            // 避免同一分钟内重复触发
                            val conditionKey = "scheduled_yearly_${condition.id}_${hour}_${minute}"
                            val lastTriggered = lastTimeStates[conditionKey] as? Long ?: 0L
                            val currentTime = System.currentTimeMillis()

                            if (currentTime - lastTriggered > 60000) { // 至少间隔1分钟
                                saveState(conditionKey, currentTime)
                                true
                            } else {
                                false
                            }
                        } else {
                            false
                        }
                    }
                }

                if (shouldTrigger) {
                    Log.d(TAG, "Scheduled time condition triggered: ${condition.getDescription()}")
                    triggerCondition(condition, mapOf(
                        "hour" to hour,
                        "minute" to minute,
                        "repeatMode" to condition.timeRepeatMode.name,
                        "timestamp" to System.currentTimeMillis()
                    ))
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error checking scheduled time condition", e)
        }
    }

    /**
     * 检查秒表条件
     */
    private fun checkStopwatchCondition(condition: TimeBasedCondition) {
        try {
            val currentTime = System.currentTimeMillis()
            val targetDuration = (condition.stopwatchHours * 3600 +
                                condition.stopwatchMinutes * 60 +
                                condition.stopwatchSeconds) * 1000L

            val elapsedTime = currentTime - condition.startTime

            // 检查是否刚好到达目标时间（允许1分钟的误差范围）
            if (elapsedTime >= targetDuration) {
                val conditionKey = "stopwatch_${condition.id}"
                val lastTriggered = lastTimeStates[conditionKey] as? Long ?: 0L

                // 避免重复触发，至少间隔1分钟
                if (currentTime - lastTriggered > 60000) {
                    Log.d(TAG, "Stopwatch condition triggered: ${condition.getDescription()}")
                    saveState(conditionKey, currentTime)

                    triggerCondition(condition, mapOf(
                        "elapsedTime" to elapsedTime,
                        "targetDuration" to targetDuration,
                        "timestamp" to currentTime
                    ))
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error checking stopwatch condition", e)
        }
    }

    /**
     * 检查日出日落条件
     */
    private fun checkSunEventCondition(condition: TimeBasedCondition, hour: Int, minute: Int) {
        try {
            // 实现日出日落时间计算
            val isSatisfied = checkSunEventTime(condition, hour, minute)

            if (isSatisfied) {
                val conditionKey = "sun_event_${condition.id}_${hour}_${minute}"
                val lastTriggered = lastTimeStates[conditionKey] as? Long ?: 0L
                val currentTime = System.currentTimeMillis()

                // 避免重复触发，至少间隔1分钟
                if (currentTime - lastTriggered > 60000) {
                    Log.d(TAG, "Sun event condition triggered: ${condition.getDescription()}")
                    saveState(conditionKey, currentTime)

                    triggerCondition(condition, mapOf(
                        "sunEventType" to condition.sunEventType.name,
                        "hour" to hour,
                        "minute" to minute,
                        "timestamp" to currentTime
                    ))
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error checking sun event condition", e)
        }
    }

    /**
     * 检查日出日落时间是否匹配
     */
    private fun checkSunEventTime(condition: TimeBasedCondition, hour: Int, minute: Int): Boolean {
        return try {
            // 获取位置信息
            val latitude = condition.latitude ?: 39.9042 // 默认北京纬度
            val longitude = condition.longitude ?: 116.4074 // 默认北京经度

            // 计算日出日落时间
            val calendar = Calendar.getInstance()

            val targetTime = when (condition.sunEventType) {
                SunEventType.SUNRISE -> com.weinuo.quickcommands22.utils.SunriseSunsetCalculator.calculateSunrise(
                    latitude, longitude, calendar
                )
                SunEventType.SUNSET -> com.weinuo.quickcommands22.utils.SunriseSunsetCalculator.calculateSunset(
                    latitude, longitude, calendar
                )
            }

            if (targetTime == null) {
                Log.w(TAG, "Unable to calculate sun event time for latitude=$latitude, longitude=$longitude")
                return false
            }

            // 检查当前时间是否匹配目标时间（精确到分钟）
            val targetHour = targetTime.get(Calendar.HOUR_OF_DAY)
            val targetMinute = targetTime.get(Calendar.MINUTE)

            hour == targetHour && minute == targetMinute
        } catch (e: Exception) {
            Log.e(TAG, "Error calculating sun event time", e)
            false
        }
    }

    /**
     * 检查周期时间条件
     */
    private fun checkPeriodicTimeCondition(condition: TimeBasedCondition, hour: Int, minute: Int) {
        try {
            val calendar = Calendar.getInstance()
            val currentDayOfWeek = DayOfWeek.fromCalendarValue(calendar.get(Calendar.DAY_OF_WEEK))

            // 检查是否在指定的时间和日期
            if (hour == condition.hour && minute == condition.minute) {
                val shouldTrigger = when (condition.scheduledRepeatMode) {
                    ScheduledRepeatMode.ONCE -> {
                        // 仅一次：检查是否已经触发过
                        val conditionKey = "periodic_once_${condition.id}"
                        val hasTriggered = lastTimeStates[conditionKey] as? Boolean ?: false

                        if (!hasTriggered) {
                            val currentYear = calendar.get(Calendar.YEAR)
                            val currentMonth = calendar.get(Calendar.MONTH) + 1
                            val currentDay = calendar.get(Calendar.DAY_OF_MONTH)

                            val matches = condition.year == currentYear &&
                                        condition.month == currentMonth &&
                                        condition.day == currentDay

                            if (matches) {
                                saveState(conditionKey, true)
                            }
                            matches
                        } else {
                            false
                        }
                    }
                    ScheduledRepeatMode.DAILY -> {
                        // 每天：避免同一分钟内重复触发
                        val conditionKey = "periodic_daily_${condition.id}_${hour}_${minute}"
                        val lastTriggered = lastTimeStates[conditionKey] as? Long ?: 0L
                        val currentTime = System.currentTimeMillis()

                        if (currentTime - lastTriggered > 60000) { // 至少间隔1分钟
                            saveState(conditionKey, currentTime)
                            true
                        } else {
                            false
                        }
                    }
                    ScheduledRepeatMode.CUSTOM -> {
                        // 自定义模式：检查选择的星期几，避免同一分钟内重复触发
                        if (condition.selectedDays.contains(currentDayOfWeek)) {
                            val conditionKey = "periodic_custom_${condition.id}_${hour}_${minute}"
                            val lastTriggered = lastTimeStates[conditionKey] as? Long ?: 0L
                            val currentTime = System.currentTimeMillis()

                            if (currentTime - lastTriggered > 60000) { // 至少间隔1分钟
                                saveState(conditionKey, currentTime)
                                true
                            } else {
                                false
                            }
                        } else {
                            false
                        }
                    }
                }

                if (shouldTrigger) {
                    Log.d(TAG, "Periodic time condition triggered: ${condition.getDescription()}")
                    triggerCondition(condition, mapOf(
                        "hour" to hour,
                        "minute" to minute,
                        "repeatMode" to condition.scheduledRepeatMode.name,
                        "timestamp" to System.currentTimeMillis()
                    ))
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error checking periodic time condition", e)
        }
    }

    /**
     * 检查延迟触发条件
     */
    private fun checkDelayedTriggerCondition(condition: TimeBasedCondition) {
        try {
            val currentTime = System.currentTimeMillis()
            val delayMillis = when (condition.unit) {
                TimeIntervalUnit.SECONDS -> condition.interval * 1000L
                TimeIntervalUnit.MINUTES -> condition.interval * 60 * 1000L
                TimeIntervalUnit.HOURS -> condition.interval * 60 * 60 * 1000L
                else -> condition.interval * 1000L // 默认为秒
            }

            val elapsedTime = currentTime - condition.startTime

            // 检查是否到达延迟时间
            if (elapsedTime >= delayMillis) {
                val conditionKey = "delayed_trigger_${condition.id}"
                val lastTriggered = lastTimeStates[conditionKey] as? Long ?: 0L

                // 避免重复触发，至少间隔1分钟
                if (currentTime - lastTriggered > 60000) {
                    Log.d(TAG, "Delayed trigger condition triggered: ${condition.getDescription()}")
                    saveState(conditionKey, currentTime)

                    triggerCondition(condition, mapOf(
                        "elapsedTime" to elapsedTime,
                        "delayMillis" to delayMillis,
                        "interval" to condition.interval,
                        "unit" to condition.unit.name,
                        "timestamp" to currentTime
                    ))
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error checking delayed trigger condition", e)
        }
    }

    /**
     * 检查周期触发条件
     */
    private fun checkPeriodicTriggerCondition(condition: TimeBasedCondition) {
        try {
            val currentTime = System.currentTimeMillis()
            val intervalMillis = when (condition.unit) {
                TimeIntervalUnit.SECONDS -> condition.interval * 1000L
                TimeIntervalUnit.MINUTES -> condition.interval * 60 * 1000L
                TimeIntervalUnit.HOURS -> condition.interval * 60 * 60 * 1000L
                else -> condition.interval * 1000L // 默认为秒
            }

            val elapsedTime = currentTime - condition.startTime
            val conditionKey = "periodic_trigger_${condition.id}"
            val lastTriggered = lastTimeStates[conditionKey] as? Long ?: condition.startTime

            // 检查是否到达下一个周期
            if (elapsedTime >= intervalMillis && (currentTime - lastTriggered) >= intervalMillis) {
                Log.d(TAG, "Periodic trigger condition triggered: ${condition.getDescription()}")
                saveState(conditionKey, currentTime)

                triggerCondition(condition, mapOf(
                    "elapsedTime" to elapsedTime,
                    "intervalMillis" to intervalMillis,
                    "interval" to condition.interval,
                    "unit" to condition.unit.name,
                    "cycleCount" to (elapsedTime / intervalMillis).toInt(),
                    "timestamp" to currentTime
                ))
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error checking periodic trigger condition", e)
        }
    }

    /**
     * 触发条件
     */
    private fun triggerCondition(condition: TimeBasedCondition, eventData: Map<String, Any>) {
        try {
            onConditionTriggered(condition, eventData)
        } catch (e: Exception) {
            Log.e(TAG, "Error triggering condition: ${condition.getDescription()}", e)
        }
    }


}
