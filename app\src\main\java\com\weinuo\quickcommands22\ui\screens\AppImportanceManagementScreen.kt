package com.weinuo.quickcommands22.ui.screens

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.expandVertically
import androidx.compose.animation.shrinkVertically
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.AutoAwesome
import androidx.compose.material.icons.filled.ExpandLess
import androidx.compose.material.icons.filled.ExpandMore
import androidx.compose.material.icons.filled.Star
import androidx.compose.material.icons.outlined.StarBorder
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.foundation.Image
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.layout.ContentScale
import com.weinuo.quickcommands22.R
import com.weinuo.quickcommands22.utils.ImageUtils
import com.weinuo.quickcommands22.data.SettingsRepository
import com.weinuo.quickcommands22.model.AppInfo
import com.weinuo.quickcommands22.model.AppImportance
import com.weinuo.quickcommands22.utils.AppsCacheManager
import com.weinuo.quickcommands22.ui.components.themed.ThemedSearchTextField
import kotlinx.coroutines.launch
import android.app.usage.UsageStatsManager
import android.content.Context
import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * 应用重要性管理界面
 *
 * 允许用户为应用设置重要性级别，支持搜索、分组显示和批量操作
 * 使用通用缓存管理器，按需加载应用列表，避免不必要的电量消耗
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AppImportanceManagementScreen(
    onNavigateBack: () -> Unit
) {
    val context = LocalContext.current
    val scope = rememberCoroutineScope()
    val focusManager = LocalFocusManager.current
    val settingsRepository = remember { SettingsRepository(context) }

    var searchQuery by rememberSaveable { mutableStateOf("") }
    var isLoading by remember { mutableStateOf(true) }
    var userApps by remember { mutableStateOf<List<AppInfo>>(emptyList()) }
    var systemApps by remember { mutableStateOf<List<AppInfo>>(emptyList()) }
    var appImportanceMap by remember { mutableStateOf<Map<String, AppImportance>>(emptyMap()) }
    var pendingChanges by remember { mutableStateOf<Map<String, AppImportance?>>(emptyMap()) }
    var hasUnsavedChanges by remember { mutableStateOf(false) }

    // 分组展开状态
    var userAppsExpanded by remember { mutableStateOf(true) }
    var systemAppsExpanded by remember { mutableStateOf(false) }



    // 加载应用列表和重要性设置 - 使用通用缓存管理器
    LaunchedEffect(Unit) {
        scope.launch {
            try {
                val (userAppsList, systemAppsList) = AppsCacheManager.getAllApps(context)
                userApps = userAppsList
                systemApps = systemAppsList
                appImportanceMap = settingsRepository.getAppImportanceMap()
                isLoading = false
            } catch (e: Exception) {
                isLoading = false
            }
        }
    }

    // 过滤应用列表
    val filteredUserApps = remember(userApps, searchQuery) {
        if (searchQuery.isBlank()) {
            userApps
        } else {
            userApps.filter { app ->
                app.appName.contains(searchQuery, ignoreCase = true) ||
                app.packageName.contains(searchQuery, ignoreCase = true)
            }
        }
    }

    val filteredSystemApps = remember(systemApps, searchQuery) {
        if (searchQuery.isBlank()) {
            systemApps
        } else {
            systemApps.filter { app ->
                app.appName.contains(searchQuery, ignoreCase = true) ||
                app.packageName.contains(searchQuery, ignoreCase = true)
            }
        }
    }

    Column(modifier = Modifier.fillMaxSize()) {
        // 顶部应用栏
        TopAppBar(
            title = { Text("应用重要性管理") },
            navigationIcon = {
                IconButton(onClick = onNavigateBack) {
                    Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                }
            },
            actions = {
                // 保存按钮
                TextButton(
                    onClick = {
                        scope.launch {
                            // 保存所有待处理的更改
                            for ((packageName, importance) in pendingChanges) {
                                settingsRepository.setAppImportance(packageName, importance)
                            }
                            // 更新本地状态
                            appImportanceMap = appImportanceMap.toMutableMap().apply {
                                for ((packageName, importance) in pendingChanges) {
                                    if (importance == null) {
                                        remove(packageName)
                                    } else {
                                        put(packageName, importance)
                                    }
                                }
                            }
                            // 清除待处理更改
                            pendingChanges = emptyMap()
                            hasUnsavedChanges = false
                        }
                    },
                    enabled = hasUnsavedChanges
                ) {
                    Text("保存")
                }
            }
        )

        if (isLoading) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    CircularProgressIndicator()
                    Text(
                        text = "正在加载应用列表...",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        } else {
            Column(modifier = Modifier.fillMaxSize()) {
                // 搜索框
                ThemedSearchTextField(
                    searchQuery = searchQuery,
                    onSearchQueryChange = {
                        searchQuery = it
                        // 搜索时自动展开所有分组
                        if (it.isNotEmpty()) {
                            userAppsExpanded = true
                            systemAppsExpanded = true
                        }
                    },
                    onClearSearch = {
                        searchQuery = ""
                        focusManager.clearFocus()
                    },
                    placeholder = stringResource(R.string.search_apps),
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp)
                )

                // 智能推荐卡片
                SmartRecommendationCard(
                    modifier = Modifier.padding(horizontal = 16.dp),
                    onSmartRecommendation = {
                        scope.launch {
                            // 智能推荐优先级
                            val allApps = userApps + systemApps
                            val recommendedChanges = generateSmartPriorityRecommendations(allApps, context)
                            pendingChanges = pendingChanges.toMutableMap().apply {
                                putAll(recommendedChanges)
                            }
                            hasUnsavedChanges = true
                        }
                    }
                )

                // 应用列表 - 使用分组显示
                LazyColumn(
                    modifier = Modifier.fillMaxSize(),
                    contentPadding = PaddingValues(horizontal = 16.dp, vertical = 8.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    // 显示分组还是扁平列表
                    val showGrouped = searchQuery.isBlank()

                    if (showGrouped) {
                        // 分组显示
                        if (filteredUserApps.isNotEmpty()) {
                            // 用户应用分组
                            item {
                                ExpandableGroupHeader(
                                    title = "用户应用",
                                    count = filteredUserApps.size,
                                    expanded = userAppsExpanded,
                                    onToggle = { userAppsExpanded = !userAppsExpanded }
                                )
                            }

                            if (userAppsExpanded) {
                                items(filteredUserApps) { app ->
                                    AppImportanceItem(
                                        app = app,
                                        currentImportance = pendingChanges[app.packageName] ?: appImportanceMap[app.packageName],
                                        onImportanceChanged = { importance ->
                                            pendingChanges = pendingChanges.toMutableMap().apply {
                                                put(app.packageName, importance)
                                            }
                                            hasUnsavedChanges = true
                                        },
                                        isAppRunning = app.isRunning
                                    )
                                }
                            }
                        }

                        if (filteredSystemApps.isNotEmpty()) {
                            // 系统应用分组
                            item {
                                ExpandableGroupHeader(
                                    title = "系统应用",
                                    count = filteredSystemApps.size,
                                    expanded = systemAppsExpanded,
                                    onToggle = { systemAppsExpanded = !systemAppsExpanded }
                                )
                            }

                            if (systemAppsExpanded) {
                                items(filteredSystemApps) { app ->
                                    AppImportanceItem(
                                        app = app,
                                        currentImportance = pendingChanges[app.packageName] ?: appImportanceMap[app.packageName],
                                        onImportanceChanged = { importance ->
                                            pendingChanges = pendingChanges.toMutableMap().apply {
                                                put(app.packageName, importance)
                                            }
                                            hasUnsavedChanges = true
                                        },
                                        isAppRunning = app.isRunning
                                    )
                                }
                            }
                        }
                    } else {
                        // 搜索时显示扁平列表
                        val allFilteredApps = filteredUserApps + filteredSystemApps
                        items(allFilteredApps) { app ->
                            AppImportanceItem(
                                app = app,
                                currentImportance = pendingChanges[app.packageName] ?: appImportanceMap[app.packageName],
                                onImportanceChanged = { importance ->
                                    pendingChanges = pendingChanges.toMutableMap().apply {
                                        put(app.packageName, importance)
                                    }
                                    hasUnsavedChanges = true
                                },
                                isAppRunning = app.isRunning
                            )
                        }
                    }
                }
            }
        }
    }
}

/**
 * 应用重要性设置项
 */
@Composable
private fun AppImportanceItem(
    app: AppInfo,
    currentImportance: AppImportance?,
    onImportanceChanged: (AppImportance?) -> Unit,
    isAppRunning: Boolean = false
) {
    var expanded by remember { mutableStateOf(false) }

    Surface(
        modifier = Modifier
            .fillMaxWidth()
            .clickable(
                interactionSource = remember { MutableInteractionSource() },
                indication = null // 移除涟漪效果
            ) { expanded = !expanded },
        shape = RoundedCornerShape(12.dp),
        color = if (expanded) {
            MaterialTheme.colorScheme.primaryContainer
        } else {
            MaterialTheme.colorScheme.surfaceContainerLow
        },
        tonalElevation = 1.dp
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 应用图标（与AppSelectionScreen保持一致的显示方式）
                Image(
                    bitmap = ImageUtils.safeDrawableToBitmap(app.icon, 96, 96).asImageBitmap(),
                    contentDescription = "${app.appName}图标",
                    modifier = Modifier.size(48.dp),
                    contentScale = ContentScale.Fit
                )

                Spacer(modifier = Modifier.width(12.dp))

                // 应用信息
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = app.appName,
                        style = MaterialTheme.typography.titleMedium,
                        color = if (expanded) {
                            MaterialTheme.colorScheme.onPrimaryContainer
                        } else {
                            MaterialTheme.colorScheme.onSurface
                        },
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = app.packageName,
                            style = MaterialTheme.typography.bodyMedium,
                            color = if (expanded) {
                                MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.7f)
                            } else {
                                MaterialTheme.colorScheme.onSurfaceVariant
                            },
                            maxLines = 1,
                            overflow = TextOverflow.Ellipsis,
                            modifier = Modifier.weight(1f)
                        )
                        // 显示正在运行状态（与AppSelectionScreen保持一致）
                        if (isAppRunning) {
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(
                                text = "正在运行",
                                style = MaterialTheme.typography.labelSmall,
                                color = if (expanded) {
                                    MaterialTheme.colorScheme.onPrimaryContainer
                                } else {
                                    MaterialTheme.colorScheme.primary
                                },
                                modifier = Modifier
                                    .background(
                                        if (expanded) {
                                            MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.1f)
                                        } else {
                                            MaterialTheme.colorScheme.primaryContainer
                                        },
                                        RoundedCornerShape(4.dp)
                                    )
                                    .padding(horizontal = 6.dp, vertical = 2.dp)
                            )
                        }


                    }
                }

                Spacer(modifier = Modifier.width(8.dp))

                // 当前重要性显示
                if (currentImportance != null) {
                    Icon(
                        imageVector = Icons.Default.Star,
                        contentDescription = null,
                        tint = if (expanded) {
                            // 展开时使用统一的onPrimaryContainer颜色
                            MaterialTheme.colorScheme.onPrimaryContainer
                        } else {
                            // 未展开时使用重要性对应的颜色
                            when (currentImportance) {
                                AppImportance.VERY_IMPORTANT -> MaterialTheme.colorScheme.error
                                AppImportance.IMPORTANT -> MaterialTheme.colorScheme.primary
                                AppImportance.NORMAL -> MaterialTheme.colorScheme.secondary
                                AppImportance.UNIMPORTANT -> MaterialTheme.colorScheme.outline
                                AppImportance.VERY_UNIMPORTANT -> MaterialTheme.colorScheme.outlineVariant
                            }
                        },
                        modifier = Modifier.size(20.dp)
                    )
                } else {
                    Icon(
                        imageVector = Icons.Outlined.StarBorder,
                        contentDescription = null,
                        tint = if (expanded) {
                            MaterialTheme.colorScheme.onPrimaryContainer
                        } else {
                            MaterialTheme.colorScheme.outline
                        },
                        modifier = Modifier.size(20.dp)
                    )
                }
            }

            // 可展开的重要性选择区域
            AnimatedVisibility(
                visible = expanded,
                enter = expandVertically(),
                exit = shrinkVertically()
            ) {
                Column(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Spacer(modifier = Modifier.height(12.dp))

                    Text(
                        text = "设置重要性：",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onPrimaryContainer,
                        modifier = Modifier.padding(bottom = 8.dp)
                    )

                    Column(verticalArrangement = Arrangement.spacedBy(4.dp)) {
                        // 无重要性选项
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            RadioButton(
                                selected = currentImportance == null,
                                onClick = { onImportanceChanged(null) }
                            )
                            Text(
                                text = "普通（无特殊设置）",
                                color = MaterialTheme.colorScheme.onPrimaryContainer,
                                modifier = Modifier.padding(start = 8.dp)
                            )
                        }

                        // 重要性选项
                        AppImportance.values().forEach { importance ->
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                RadioButton(
                                    selected = currentImportance == importance,
                                    onClick = { onImportanceChanged(importance) }
                                )
                                Text(
                                    text = importance.displayName,
                                    color = MaterialTheme.colorScheme.onPrimaryContainer,
                                    modifier = Modifier.padding(start = 8.dp)
                                )
                            }
                        }
                    }
                }
            }
        }
    }
}

/**
 * 生成智能优先级推荐
 * 综合分析应用使用习惯（使用时长、打开频率、最近使用等）智能推荐优先级
 */
private suspend fun generateSmartPriorityRecommendations(
    apps: List<AppInfo>,
    context: Context
): Map<String, AppImportance?> {
    return withContext(Dispatchers.IO) {
        try {
            val usageStatsManager = context.getSystemService(Context.USAGE_STATS_SERVICE) as UsageStatsManager
            val currentTime = System.currentTimeMillis()
            val endTime = currentTime
            val startTime = endTime - 30L * 24 * 60 * 60 * 1000 // 30天前

            // 获取使用统计
            val usageStats = usageStatsManager.queryUsageStats(
                UsageStatsManager.INTERVAL_DAILY,
                startTime,
                endTime
            )

            // 创建包名到使用统计的映射
            val usageStatsMap = usageStats.groupBy { it.packageName }
                .mapValues { (_, stats) ->
                    AppUsageStats(
                        totalTime = stats.sumOf { it.totalTimeInForeground },
                        launchCount = stats.sumOf { it.totalTimeInForeground / 60000L }, // 使用时长估算使用频率
                        lastUsed = stats.maxOfOrNull { it.lastTimeUsed } ?: 0L
                    )
                }

            // 计算每个应用的智能评分
            val appScores = apps.mapNotNull { app ->
                val usage = usageStatsMap[app.packageName]
                if (usage != null && usage.totalTime > 0) {
                    app.packageName to calculateSmartImportanceScore(usage, currentTime)
                } else {
                    null
                }
            }.toMap()

            // 按评分排序并分配优先级
            val sortedApps = appScores.toList().sortedByDescending { it.second }
            val recommendations = mutableMapOf<String, AppImportance?>()

            if (sortedApps.isNotEmpty()) {
                val totalApps = sortedApps.size

                sortedApps.forEachIndexed { index, (packageName, score) ->
                    val percentile = (index.toDouble() / totalApps) * 100

                    val recommendedImportance = when {
                        percentile <= 8 -> AppImportance.VERY_IMPORTANT   // 前8%最重要
                        percentile <= 20 -> AppImportance.IMPORTANT       // 前20%重要
                        percentile <= 70 -> AppImportance.NORMAL          // 中间50%普通
                        percentile <= 90 -> AppImportance.UNIMPORTANT     // 后20%不重要
                        else -> AppImportance.VERY_UNIMPORTANT            // 后10%非常不重要
                    }

                    recommendations[packageName] = recommendedImportance
                }
            }

            Log.d("AppImportanceManagement", "Generated ${recommendations.size} smart priority recommendations based on comprehensive usage analysis")
            recommendations
        } catch (e: Exception) {
            Log.e("AppImportanceManagement", "Error generating smart priority recommendations", e)
            emptyMap()
        }
    }
}

/**
 * 应用使用统计数据类
 */
private data class AppUsageStats(
    val totalTime: Long,      // 总使用时间
    val launchCount: Long,    // 启动次数
    val lastUsed: Long        // 最后使用时间
)

/**
 * 计算应用重要性的智能评分
 * 综合考虑使用时长、使用频率、最近使用情况
 */
private fun calculateSmartImportanceScore(
    usage: AppUsageStats,
    currentTime: Long
): Double {
    // 标准化各项指标（0-1之间）
    val maxTime = 24 * 60 * 60 * 1000L // 24小时作为最大使用时间基准
    val maxLaunches = 100L // 100次作为最大启动次数基准
    val maxDaysSinceLastUsed = 7L // 7天作为最近使用的基准

    // 使用时长评分 (40%权重)
    val timeScore = minOf(usage.totalTime.toDouble() / maxTime, 1.0) * 0.4

    // 使用频率评分 (30%权重)
    val frequencyScore = minOf(usage.launchCount.toDouble() / maxLaunches, 1.0) * 0.3

    // 最近使用评分 (30%权重) - 越近使用评分越高
    val daysSinceLastUsed = (currentTime - usage.lastUsed) / (24 * 60 * 60 * 1000L)
    val recentScore = maxOf(0.0, 1.0 - (daysSinceLastUsed.toDouble() / maxDaysSinceLastUsed)) * 0.3

    return timeScore + frequencyScore + recentScore
}

/**
 * 智能推荐卡片组件
 */
@Composable
private fun SmartRecommendationCard(
    modifier: Modifier = Modifier,
    onSmartRecommendation: () -> Unit
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceContainerLow
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.AutoAwesome,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.size(20.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "智能推荐",
                    style = MaterialTheme.typography.titleMedium,
                    color = MaterialTheme.colorScheme.onSurface
                )
            }

            Text(
                text = "根据应用使用频率自动推荐优先级设置",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )

            Button(
                onClick = onSmartRecommendation,
                modifier = Modifier.fillMaxWidth()
            ) {
                Text("开始推荐")
            }
        }
    }
}

/**
 * 可展开分组标题组件
 */
@Composable
private fun ExpandableGroupHeader(
    title: String,
    count: Int,
    expanded: Boolean,
    onToggle: () -> Unit
) {
    Surface(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onToggle() },
        shape = RoundedCornerShape(8.dp),
        color = MaterialTheme.colorScheme.secondaryContainer.copy(alpha = 0.3f)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "$title ($count)",
                style = MaterialTheme.typography.titleSmall,
                color = MaterialTheme.colorScheme.secondary,
                modifier = Modifier.weight(1f)
            )
            Icon(
                imageVector = if (expanded) Icons.Default.ExpandLess else Icons.Default.ExpandMore,
                contentDescription = if (expanded) "收起" else "展开",
                tint = MaterialTheme.colorScheme.secondary
            )
        }
    }
}