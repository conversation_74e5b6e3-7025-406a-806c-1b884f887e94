package com.weinuo.quickcommands22.ui.recording

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.weinuo.quickcommands22.model.GestureRecording
import com.weinuo.quickcommands22.model.TouchEvent
import com.weinuo.quickcommands22.model.TouchEventType
import com.weinuo.quickcommands22.model.getDetailedDescription

/**
 * 手势录制编辑主界面
 *
 * 提供全屏的手势录制编辑功能，包括：
 * - 事件列表显示和编辑
 * - 延迟时间调整
 * - 事件删除和插入
 * - 撤销/重做功能
 * - 预览和保存功能
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun GestureRecordingEditScreen(
    recordingId: String,
    onNavigateBack: () -> Unit,
    onSaveCompleted: () -> Unit,
    onNavigateToActionEdit: (Int) -> Unit = {},
    onNavigateToPositionPicker: (Int) -> Unit = {},
    onContinueRecording: (Int) -> Unit = {},
    viewModel: GestureRecordingEditViewModel
) {

    val currentRecording by viewModel.currentRecording.collectAsState()
    val isLoading by viewModel.isLoading.collectAsState()
    val isSaving by viewModel.isSaving.collectAsState()
    val errorMessage by viewModel.errorMessage.collectAsState()

    // 移除了无限循环的数据检查，现在使用事件驱动的方式通过SharedPreferences监听数据更新



    // 错误信息显示
    LaunchedEffect(errorMessage) {
        if (errorMessage != null) {
            // 可以在这里显示SnackBar或其他错误提示
        }
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = "编辑手势录制",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Medium
                    )
                },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                    }
                },
                actions = {
                    // 保存按钮
                    IconButton(
                        onClick = {
                            if (viewModel.saveRecording()) {
                                onSaveCompleted()
                            }
                        },
                        enabled = !isSaving
                    ) {
                        if (isSaving) {
                            CircularProgressIndicator(
                                modifier = Modifier.size(20.dp),
                                strokeWidth = 2.dp
                            )
                        } else {
                            Icon(
                                Icons.Default.Save,
                                contentDescription = "保存",
                                tint = MaterialTheme.colorScheme.primary
                            )
                        }
                    }
                }
            )
        }
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            when {
                isLoading -> {
                    // 加载状态
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally,
                            verticalArrangement = Arrangement.spacedBy(16.dp)
                        ) {
                            CircularProgressIndicator()
                            Text(
                                text = "正在加载录制数据...",
                                style = MaterialTheme.typography.bodyMedium
                            )
                        }
                    }
                }

                currentRecording == null -> {
                    // 错误状态
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally,
                            verticalArrangement = Arrangement.spacedBy(16.dp)
                        ) {
                            Icon(
                                Icons.Default.Error,
                                contentDescription = null,
                                modifier = Modifier.size(48.dp),
                                tint = MaterialTheme.colorScheme.error
                            )
                            Text(
                                text = errorMessage ?: "无法加载录制数据",
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.error
                            )
                            Button(onClick = onNavigateBack) {
                                Text("返回")
                            }
                        }
                    }
                }

                else -> {
                    // 正常显示内容
                    GestureEditContent(
                        recording = currentRecording!!,
                        onEventEdit = { index -> onNavigateToActionEdit(index) },
                        onEventDeleted = { index -> viewModel.deleteEvent(index) },
                        onEventDuplicated = { index -> viewModel.duplicateEvent(index) },
                        onContinueRecording = { onContinueRecording(currentRecording!!.events.size) },
                        modifier = Modifier.fillMaxSize()
                    )
                }
            }
        }
    }
}

/**
 * 手势编辑内容组件
 */
@Composable
private fun GestureEditContent(
    recording: GestureRecording,
    onEventEdit: (Int) -> Unit,
    onEventDeleted: (Int) -> Unit,
    onEventDuplicated: (Int) -> Unit,
    onContinueRecording: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier) {
        // 录制信息头部
        RecordingInfoHeader(
            recording = recording,
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        )

        Divider()

        // 事件列表
        LazyColumn(
            modifier = Modifier.weight(1f),
            contentPadding = PaddingValues(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            itemsIndexed(
                items = recording.events,
                key = { index, event -> "${event.id}_${event.delayAfter}_${event.duration}" }
            ) { index, event ->
                EventEditCard(
                    event = event,
                    eventIndex = index,
                    onEdit = { onEventEdit(index) },
                    onDeleted = { onEventDeleted(index) },
                    onDuplicated = { onEventDuplicated(index) }
                )
            }
        }

        // 继续录制按钮
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.primaryContainer
            )
        ) {
            Button(
                onClick = onContinueRecording,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                colors = ButtonDefaults.buttonColors(
                    containerColor = MaterialTheme.colorScheme.primary
                )
            ) {
                Icon(
                    Icons.Default.Add,
                    contentDescription = null,
                    modifier = Modifier.size(20.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "继续录制",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium
                )
            }
        }
    }
}

/**
 * 录制信息头部组件
 */
@Composable
private fun RecordingInfoHeader(
    recording: GestureRecording,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Text(
                text = recording.name,
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )

            if (recording.description.isNotEmpty()) {
                Text(
                    text = recording.description,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }

            Row(
                horizontalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                InfoChip(
                    label = "事件数量",
                    value = "${recording.events.size}"
                )
                InfoChip(
                    label = "总时长",
                    value = "${recording.duration}ms"
                )
            }
        }
    }
}

/**
 * 信息标签组件
 */
@Composable
private fun InfoChip(
    label: String,
    value: String
) {
    Surface(
        shape = RoundedCornerShape(12.dp),
        color = MaterialTheme.colorScheme.primary.copy(alpha = 0.1f)
    ) {
        Row(
            modifier = Modifier.padding(horizontal = 12.dp, vertical = 6.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            Text(
                text = label,
                style = MaterialTheme.typography.labelSmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            Text(
                text = value,
                style = MaterialTheme.typography.labelMedium,
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.colorScheme.primary
            )
        }
    }
}

/**
 * 事件编辑卡片组件
 */
@Composable
private fun EventEditCard(
    event: TouchEvent,
    eventIndex: Int,
    onEdit: () -> Unit,
    onDeleted: () -> Unit,
    onDuplicated: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // 事件头部信息
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    // 事件类型图标
                    Icon(
                        imageVector = getEventTypeIcon(event.type),
                        contentDescription = null,
                        modifier = Modifier.size(20.dp),
                        tint = getEventTypeColor(event.type)
                    )

                    Text(
                        text = "事件 ${eventIndex + 1}",
                        style = MaterialTheme.typography.titleSmall,
                        fontWeight = FontWeight.Medium
                    )

                    // 事件类型标签
                    Surface(
                        shape = RoundedCornerShape(8.dp),
                        color = getEventTypeColor(event.type).copy(alpha = 0.1f)
                    ) {
                        Text(
                            text = getEventTypeName(event.type),
                            modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp),
                            style = MaterialTheme.typography.labelSmall,
                            color = getEventTypeColor(event.type)
                        )
                    }
                }

                // 操作按钮
                Row(
                    horizontalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    // 编辑按钮
                    IconButton(
                        onClick = onEdit,
                        modifier = Modifier.size(32.dp)
                    ) {
                        Icon(
                            Icons.Default.Edit,
                            contentDescription = "编辑动作",
                            modifier = Modifier.size(16.dp),
                            tint = MaterialTheme.colorScheme.primary
                        )
                    }

                    // 复制按钮
                    IconButton(
                        onClick = onDuplicated,
                        modifier = Modifier.size(32.dp)
                    ) {
                        Icon(
                            Icons.Default.ContentCopy,
                            contentDescription = "复制动作",
                            modifier = Modifier.size(16.dp)
                        )
                    }

                    // 删除按钮
                    IconButton(
                        onClick = onDeleted,
                        modifier = Modifier.size(32.dp)
                    ) {
                        Icon(
                            Icons.Default.Delete,
                            contentDescription = "删除动作",
                            modifier = Modifier.size(16.dp),
                            tint = MaterialTheme.colorScheme.error
                        )
                    }
                }
            }

            // 事件详细信息
            EventDetailsInfo(event)
        }
    }
}

/**
 * 事件详细信息组件
 */
@Composable
private fun EventDetailsInfo(event: TouchEvent) {
    val configuration = LocalConfiguration.current
    val density = LocalDensity.current
    val screenWidth = (configuration.screenWidthDp * density.density).toInt()
    val screenHeight = (configuration.screenHeightDp * density.density).toInt()

    Column(
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        // 动作描述
        Text(
            text = event.getDetailedDescription(screenWidth, screenHeight),
            style = MaterialTheme.typography.bodyMedium,
            fontWeight = FontWeight.Medium
        )

        // 延迟信息
        if (event.delayAfter > 0) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                Icon(
                    Icons.Default.Schedule,
                    contentDescription = null,
                    modifier = Modifier.size(16.dp),
                    tint = MaterialTheme.colorScheme.primary
                )
                Text(
                    text = event.getDelayDescription(),
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.primary
                )
            }
        }

        // 持续时间信息
        if (event.duration > 0) {
            Row(
                horizontalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                InfoItem(
                    label = "持续时间",
                    value = "${event.duration}ms"
                )
            }
        }

        // 触摸点数量（多点触控）
        if (event.position.touchPoints > 1) {
            InfoItem(
                label = "触摸点数量",
                value = "${event.position.touchPoints}点"
            )
        }

        // 用户描述
        if (event.description.isNotEmpty()) {
            Text(
                text = "\"${event.description}\"",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                fontStyle = androidx.compose.ui.text.font.FontStyle.Italic
            )
        }
    }
}

/**
 * 信息项组件
 */
@Composable
private fun InfoItem(
    label: String,
    value: String
) {
    Column {
        Text(
            text = label,
            style = MaterialTheme.typography.labelSmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        Text(
            text = value,
            style = MaterialTheme.typography.bodySmall,
            fontWeight = FontWeight.Medium
        )
    }
}

/**
 * 获取事件类型图标
 */
private fun getEventTypeIcon(type: TouchEventType) = when (type) {
    TouchEventType.TAP -> Icons.Default.TouchApp
    TouchEventType.LONG_PRESS -> Icons.Default.Timer
    TouchEventType.SWIPE -> Icons.Default.SwipeRight
    TouchEventType.MULTI_TAP -> Icons.Default.OpenWith
}

/**
 * 获取事件类型颜色
 */
@Composable
private fun getEventTypeColor(type: TouchEventType) = when (type) {
    TouchEventType.TAP -> MaterialTheme.colorScheme.primary
    TouchEventType.LONG_PRESS -> MaterialTheme.colorScheme.secondary
    TouchEventType.SWIPE -> MaterialTheme.colorScheme.tertiary
    TouchEventType.MULTI_TAP -> Color(0xFF9C27B0)
}

/**
 * 获取事件类型名称
 */
private fun getEventTypeName(type: TouchEventType) = when (type) {
    TouchEventType.TAP -> "点击"
    TouchEventType.LONG_PRESS -> "长按"
    TouchEventType.SWIPE -> "滑动"
    TouchEventType.MULTI_TAP -> "多点触控"
}
