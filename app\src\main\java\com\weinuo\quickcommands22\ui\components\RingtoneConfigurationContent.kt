package com.weinuo.quickcommands22.ui.components

import android.app.Activity
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.selection.selectableGroup
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.unit.dp
import com.weinuo.quickcommands22.ui.activities.RingtoneSelectionActivity
import com.weinuo.quickcommands22.utils.RingtoneHelper

/**
 * 通用铃声配置组件
 * 
 * 可复用的铃声配置UI组件，支持不同的铃声类型和配置模式。
 * 遵循项目的导航状态管理最佳实践，使用rememberSaveable保持状态。
 * 
 * @param title 配置标题
 * @param description 配置描述（可选）
 * @param ringtoneType 铃声类型，如果showTypeSelection为false则固定使用此类型
 * @param showTypeSelection 是否显示铃声类型选择，默认为true
 * @param initialSelectedRingtoneUri 初始选中的铃声URI
 * @param initialSelectedRingtoneName 初始选中的铃声名称
 * @param onComplete 配置完成回调，返回选中的铃声URI和名称
 */
@Composable
fun RingtoneConfigurationContent(
    title: String,
    description: String? = null,
    ringtoneType: RingtoneHelper.RingtoneType,
    showTypeSelection: Boolean = true,
    initialSelectedRingtoneUri: String = "",
    initialSelectedRingtoneName: String = "",
    onComplete: (selectedRingtoneUri: String, selectedRingtoneName: String) -> Unit
) {
    val context = LocalContext.current

    // 使用rememberSaveable保持导航过程中的状态
    var selectedRingtoneUri by rememberSaveable { mutableStateOf(initialSelectedRingtoneUri) }
    var selectedRingtoneName by rememberSaveable { mutableStateOf(initialSelectedRingtoneName) }
    var currentRingtoneType by rememberSaveable { mutableStateOf(ringtoneType) }

    // 铃声选择启动器
    val ringtoneSelectionLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            val data = result.data
            val ringtoneUri = data?.getStringExtra(RingtoneSelectionActivity.RESULT_RINGTONE_URI)
            val ringtoneName = data?.getStringExtra(RingtoneSelectionActivity.RESULT_RINGTONE_NAME)

            if (ringtoneUri != null && ringtoneName != null) {
                selectedRingtoneUri = ringtoneUri
                selectedRingtoneName = ringtoneName
            }
        }
    }

    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 标题
        Text(
            text = title,
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        // 描述（可选）
        if (description != null) {
            Text(
                text = description,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }

        // 铃声类型选择（可选）
        if (showTypeSelection) {
            Text(
                text = "铃声类型",
                style = MaterialTheme.typography.bodyLarge
            )

            Column(modifier = Modifier.selectableGroup()) {
                RingtoneHelper.RingtoneType.values().forEach { type ->
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .selectable(
                                selected = currentRingtoneType == type,
                                onClick = {
                                    currentRingtoneType = type
                                    // 切换类型时清除已选择的铃声
                                    selectedRingtoneUri = ""
                                    selectedRingtoneName = ""
                                },
                                role = Role.RadioButton
                            )
                            .padding(vertical = 4.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        RadioButton(
                            selected = currentRingtoneType == type,
                            onClick = {
                                currentRingtoneType = type
                                // 切换类型时清除已选择的铃声
                                selectedRingtoneUri = ""
                                selectedRingtoneName = ""
                            }
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = type.displayName,
                            style = MaterialTheme.typography.bodyMedium
                        )
                    }
                }
            }
        }

        // 铃声选择按钮
        OutlinedButton(
            onClick = {
                // 启动铃声选择Activity
                RingtoneSelectionActivity.startForSelection(
                    context = context,
                    ringtoneType = currentRingtoneType,
                    initialSelectedUri = selectedRingtoneUri
                )
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text(
                if (selectedRingtoneName.isNotEmpty()) {
                    "已选择: $selectedRingtoneName"
                } else {
                    "点击选择${currentRingtoneType.displayName}"
                }
            )
        }

        // 确认按钮
        Button(
            onClick = {
                onComplete(selectedRingtoneUri, selectedRingtoneName)
            },
            modifier = Modifier.fillMaxWidth(),
            enabled = selectedRingtoneUri.isNotEmpty()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 简化版铃声选择器组件
 * 
 * 用于在其他配置中嵌入铃声选择功能，不包含确认按钮
 * 
 * @param ringtoneType 铃声类型
 * @param selectedRingtoneUri 当前选中的铃声URI
 * @param selectedRingtoneName 当前选中的铃声名称
 * @param onRingtoneChanged 铃声变更回调
 * @param label 按钮标签，默认为"选择铃声"
 */
@Composable
fun RingtoneSelector(
    ringtoneType: RingtoneHelper.RingtoneType,
    selectedRingtoneUri: String,
    selectedRingtoneName: String,
    onRingtoneChanged: (uri: String, name: String) -> Unit,
    label: String = "选择铃声"
) {
    val context = LocalContext.current

    // 铃声选择启动器
    val ringtoneSelectionLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            val data = result.data
            val ringtoneUri = data?.getStringExtra(RingtoneSelectionActivity.RESULT_RINGTONE_URI)
            val ringtoneName = data?.getStringExtra(RingtoneSelectionActivity.RESULT_RINGTONE_NAME)

            if (ringtoneUri != null && ringtoneName != null) {
                onRingtoneChanged(ringtoneUri, ringtoneName)
            }
        }
    }

    OutlinedButton(
        onClick = {
            // 启动铃声选择Activity
            RingtoneSelectionActivity.startForSelection(
                context = context,
                ringtoneType = ringtoneType,
                initialSelectedUri = selectedRingtoneUri
            )
        },
        modifier = Modifier.fillMaxWidth()
    ) {
        Text(
            if (selectedRingtoneName.isNotEmpty()) {
                "已选择: $selectedRingtoneName"
            } else {
                label
            }
        )
    }
}
