package com.weinuo.quickcommands22.ui.components.themed

import android.content.Context
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.weinuo.quickcommands22.R
import com.weinuo.quickcommands22.model.SmartReminderConfig
import com.weinuo.quickcommands22.model.SmartReminderConfigType
import com.weinuo.quickcommands22.model.SmartReminderType
import com.weinuo.quickcommands22.permission.PermissionAwareOperationSelector
import com.weinuo.quickcommands22.permission.SmartReminderOperation
import com.weinuo.quickcommands22.storage.SmartReminderConfigAdapter
import com.weinuo.quickcommands22.ui.theme.config.SwitchConfig
import com.weinuo.quickcommands22.ui.theme.manager.LocalThemeContext
import com.weinuo.quickcommands22.ui.theme.manager.ThemeManager
import com.weinuo.quickcommands22.ui.theme.skyblue.SkyBlueStyleConfiguration
import com.weinuo.quickcommands22.data.SettingsRepository

/**
 * 主题感知的智慧提醒功能卡片组件 - iOS风格渐进式设计
 *
 * 设计原则（基于iOS设计规范）：
 * - 状态驱动的渐进式界面：未配置 → 已配置 → 已启用
 * - 主要操作：点击卡片整体区域进入配置/编辑
 * - 次要操作：开关控制（仅在已配置时显示）
 * - 清晰的视觉层次：标题 > 状态摘要 > 描述
 * - 统一的交互模式：与快捷指令界面保持一致
 *
 * 状态演进：
 * 1. 未配置状态：显示"轻触设置"引导，点击进入配置流程
 * 2. 已配置状态：显示配置摘要 + 开关，点击进入编辑界面
 * 3. 已启用状态：显示状态指示 + 配置摘要 + 开关
 *
 * @param reminderType 智慧提醒类型
 * @param config 当前配置
 * @param onCardClick 卡片点击回调（进入配置/编辑）
 * @param onToggle 开关切换回调（仅在已配置时可用）
 * @param modifier 修饰符
 */
@Composable
fun ThemedSmartReminderCard(
    reminderType: SmartReminderType,
    config: SmartReminderConfig,
    onCardClick: () -> Unit,
    onToggle: (Boolean) -> Unit,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    val themeContext = LocalThemeContext.current
    val themeManager = remember { ThemeManager.getInstance(context) }

    // 获取动态卡片样式配置（仅在天空蓝主题下使用）
    val cardStyle = if (themeManager.getCurrentThemeId() == "sky_blue") {
        val settingsRepository = remember { SettingsRepository(context) }
        SkyBlueStyleConfiguration.getDynamicCardStyleConfig(settingsRepository)
    } else {
        themeContext.styleConfiguration.cardStyle
    }

    // 获取全局设置以使用动态字体大小（仅在天空蓝主题下使用）
    val settingsRepository = remember { SettingsRepository(context) }
    val globalSettings by settingsRepository.globalSettings.collectAsState()

    // 主题感知的字体样式配置
    val titleStyle = if (themeManager.getCurrentThemeId() == "sky_blue") {
        // 天空蓝主题：使用全局设置的字体大小和字重
        MaterialTheme.typography.titleMedium.copy(
            fontSize = globalSettings.cardTitleFontSize.sp,
            fontWeight = when (globalSettings.cardTitleFontWeight) {
                "normal" -> FontWeight.Normal
                "medium" -> FontWeight.Medium
                "bold" -> FontWeight.Bold
                else -> FontWeight.Medium
            }
        )
    } else {
        // 海洋蓝主题：使用原始固定样式
        MaterialTheme.typography.titleMedium
    }

    val contentStyle = if (themeManager.getCurrentThemeId() == "sky_blue") {
        // 天空蓝主题：使用全局设置的字体大小和字重
        MaterialTheme.typography.bodyMedium.copy(
            fontSize = globalSettings.cardContentFontSize.sp,
            fontWeight = when (globalSettings.cardContentFontWeight) {
                "normal" -> FontWeight.Normal
                "medium" -> FontWeight.Medium
                "bold" -> FontWeight.Bold
                else -> FontWeight.Medium
            }
        )
    } else {
        // 海洋蓝主题：使用原始固定样式
        MaterialTheme.typography.bodyMedium
    }

    val smallContentStyle = if (themeManager.getCurrentThemeId() == "sky_blue") {
        // 天空蓝主题：使用全局设置的字体大小减2
        MaterialTheme.typography.bodySmall.copy(
            fontSize = (globalSettings.cardContentFontSize - 2).sp
        )
    } else {
        // 海洋蓝主题：使用原始固定样式
        MaterialTheme.typography.bodySmall
    }

    // 根据主题获取样式配置
    val cornerRadius = cardStyle.defaultCornerRadius
    val paddingValues = cardStyle.getPaddingValues()
    val elevation = cardStyle.defaultElevation

    // 权限检查状态
    var selectedOperation by remember { mutableStateOf<SmartReminderOperation?>(null) }
    var isPermissionChecking by remember { mutableStateOf(false) }
    var pendingToggleValue by remember { mutableStateOf<Boolean?>(null) }

    // 权限检查组件 - 只在开启开关时进行权限检查
    selectedOperation?.let { operation ->
        PermissionAwareOperationSelector(
            selectedOperation = operation,
            onPermissionDialogDismissed = {
                selectedOperation = null
                isPermissionChecking = false

                // 权限检查完成后，检查是否所有权限都已获得
                pendingToggleValue?.let { toggleValue ->
                    if (toggleValue) {
                        // 检查是否所有必需权限都已获得
                        val hasOverlayPermission = com.weinuo.quickcommands22.utils.OverlayPermissionUtil.hasOverlayPermission(context)
                        val hasShizukuPermission = com.weinuo.quickcommands22.shizuku.ShizukuManager.checkShizukuPermission()

                        // 根据提醒类型检查特定权限
                        val hasRequiredPermissions = when (reminderType) {
                            SmartReminderType.FLASHLIGHT_REMINDER -> {
                                // 手电筒提醒还需要相机权限
                                val hasCameraPermission = com.weinuo.quickcommands22.utils.CameraPermissionUtil.hasCameraPermission(context)
                                hasOverlayPermission && hasShizukuPermission && hasCameraPermission
                            }
                            else -> {
                                // 其他提醒只需要悬浮窗和Shizuku权限
                                hasOverlayPermission && hasShizukuPermission
                            }
                        }

                        if (hasRequiredPermissions) {
                            // 所有权限都有，开启功能
                            onToggle(true)
                        }
                        // 如果权限不足，开关保持关闭状态（不调用onToggle）
                    } else {
                        // 关闭操作直接执行
                        onToggle(false)
                    }
                    pendingToggleValue = null
                }
            }
        )
    }

    // 生成配置摘要文本
    val configSummary = remember(config, reminderType, context) {
        if (!config.isConfigured) {
            context.getString(R.string.smart_reminder_tap_to_setup)
        } else {
            generateConfigSummary(reminderType, context)
        }
    }

    Card(
        modifier = modifier
            .fillMaxWidth()
            .clickable { onCardClick() }, // iOS风格：点击整个卡片进入配置/编辑
        shape = RoundedCornerShape(cornerRadius),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceContainerLow
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = elevation
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(paddingValues),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 左侧：信息区域
            Column(
                modifier = Modifier.weight(1f),
                verticalArrangement = Arrangement.spacedBy(cardStyle.contentVerticalSpacing)
            ) {
                // 标题
                Text(
                    text = reminderType.getLocalizedTitle(context),
                    style = titleStyle,
                    color = MaterialTheme.colorScheme.onSurface
                )

                // 状态摘要/引导文字
                Text(
                    text = configSummary,
                    style = contentStyle,
                    color = if (config.isConfigured) {
                        MaterialTheme.colorScheme.primary
                    } else {
                        MaterialTheme.colorScheme.onSurfaceVariant
                    }
                )

                // 功能描述
                Text(
                    text = reminderType.getLocalizedDescription(context),
                    style = smallContentStyle,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }

            // 右侧：开关区域（仅在已配置时显示）
            if (config.isConfigured) {
                if (isPermissionChecking) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(24.dp),
                        strokeWidth = 2.dp,
                        color = MaterialTheme.colorScheme.primary
                    )
                } else {
                    // 使用主题感知的开关组件
                    themeContext.componentFactory.createSwitch()(
                        SwitchConfig(
                            checked = config.isEnabled,
                            enabled = config.canBeEnabled() && !isPermissionChecking,
                            onCheckedChange = { isEnabled ->
                                if (isEnabled) {
                                    // 开启时先检查所有权限
                                    val hasOverlayPermission = com.weinuo.quickcommands22.utils.OverlayPermissionUtil.hasOverlayPermission(context)
                                    val hasShizukuPermission = com.weinuo.quickcommands22.shizuku.ShizukuManager.checkShizukuPermission()

                                    // 根据提醒类型检查特定权限
                                    val hasRequiredPermissions = when (reminderType) {
                                        SmartReminderType.FLASHLIGHT_REMINDER -> {
                                            // 手电筒提醒还需要相机权限
                                            val hasCameraPermission = com.weinuo.quickcommands22.utils.CameraPermissionUtil.hasCameraPermission(context)
                                            hasOverlayPermission && hasShizukuPermission && hasCameraPermission
                                        }
                                        else -> {
                                            // 其他提醒只需要悬浮窗和Shizuku权限
                                            hasOverlayPermission && hasShizukuPermission
                                        }
                                    }

                                    if (hasRequiredPermissions) {
                                        // 所有权限都有，直接开启
                                        onToggle(true)
                                    } else {
                                        // 缺少权限，进行权限检查
                                        isPermissionChecking = true
                                        pendingToggleValue = true
                                        selectedOperation = when (reminderType) {
                                            SmartReminderType.FLASHLIGHT_REMINDER -> SmartReminderOperation.ENABLE_FLASHLIGHT_REMINDER
                                            else -> SmartReminderOperation.ENABLE_REMINDER
                                        }
                                    }
                                } else {
                                    // 关闭时直接调用
                                    onToggle(false)
                                }
                            }
                        )
                    )
                }
            }
        }
    }
}

/**
 * 生成配置摘要文本
 * 根据智慧提醒类型和配置状态生成合适的摘要信息
 */
private fun generateConfigSummary(reminderType: SmartReminderType, context: Context): String {
    return when (reminderType.configType) {
        SmartReminderConfigType.APP_SELECTION -> {
            // 对于应用选择类型，显示已选择的应用数量
            when (reminderType) {
                SmartReminderType.MUSIC_APP_REMINDER -> {
                    val adapter = SmartReminderConfigAdapter(context)
                    val config = adapter.loadMusicAppReminderConfig(reminderType.id)
                    val appCount = config.selectedMusicApps.size
                    context.getString(R.string.smart_reminder_selected_apps_count, appCount)
                }
                SmartReminderType.ADDRESS_REMINDER -> {
                    val adapter = SmartReminderConfigAdapter(context)
                    val config = adapter.loadAddressReminderConfig(reminderType.id)
                    val appCount = config.selectedMapApps.size
                    context.getString(R.string.smart_reminder_selected_apps_count, appCount)
                }
                else -> context.getString(R.string.smart_reminder_configured)
            }
        }
        SmartReminderConfigType.NONE -> {
            // 无需配置的类型显示"可直接使用"
            context.getString(R.string.smart_reminder_ready_to_use)
        }
        else -> {
            // 其他类型显示"已配置"
            context.getString(R.string.smart_reminder_configured)
        }
    }
}




