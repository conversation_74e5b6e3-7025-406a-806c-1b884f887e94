package com.weinuo.quickcommands22.ui.theme.manager

import android.content.Context
import android.util.Log
import android.content.SharedPreferences
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.DisposableEffect
import com.weinuo.quickcommands22.ui.theme.config.BlurConfiguration
import com.weinuo.quickcommands22.ui.theme.config.BlurComponent
import dev.chrisbanes.haze.HazeStyle
import dev.chrisbanes.haze.HazeTint
import dev.chrisbanes.haze.materials.ExperimentalHazeMaterialsApi
import dev.chrisbanes.haze.materials.HazeMaterials
import androidx.compose.material3.MaterialTheme
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.luminance
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.Dp


/**
 * 模糊配置管理器
 *
 * 负责管理应用中所有模糊效果的配置，包括：
 * - 从当前主题提供者获取基础配置
 * - 用户自定义设置的持久化存储
 * - 配置的实时更新
 * - 配置的状态管理
 * - 配置的验证和同步
 */
class BlurConfigurationManager private constructor(private val context: Context) {
    
    companion object {
        private const val PREFS_NAME = "blur_configuration"

        // 模糊效果开关键
        private const val KEY_TOP_BAR_BLUR = "top_bar_blur"
        private const val KEY_BOTTOM_BAR_BLUR = "bottom_bar_blur"
        private const val KEY_DIALOG_BLUR = "dialog_blur"
        private const val KEY_OVERLAY_BLUR = "overlay_blur"

        // 模糊样式类型键
        private const val KEY_TOP_BAR_BLUR_STYLE = "top_bar_blur_style"
        private const val KEY_BOTTOM_BAR_BLUR_STYLE = "bottom_bar_blur_style"
        private const val KEY_DIALOG_BLUR_STYLE = "dialog_blur_style"
        private const val KEY_OVERLAY_BLUR_STYLE = "overlay_blur_style"

        // 预设材质类型键
        private const val KEY_TOP_BAR_PRESET_MATERIAL = "top_bar_preset_material"
        private const val KEY_BOTTOM_BAR_PRESET_MATERIAL = "bottom_bar_preset_material"
        private const val KEY_DIALOG_PRESET_MATERIAL = "dialog_preset_material"
        private const val KEY_OVERLAY_PRESET_MATERIAL = "overlay_preset_material"

        // 模糊强度键
        private const val KEY_TOP_BAR_BLUR_INTENSITY = "top_bar_blur_intensity"
        private const val KEY_BOTTOM_BAR_BLUR_INTENSITY = "bottom_bar_blur_intensity"
        private const val KEY_DIALOG_BLUR_INTENSITY = "dialog_blur_intensity"
        private const val KEY_OVERLAY_BLUR_INTENSITY = "overlay_blur_intensity"

        // 背景透明度键
        private const val KEY_TOP_BAR_BACKGROUND_ALPHA = "top_bar_background_alpha"
        private const val KEY_BOTTOM_BAR_BACKGROUND_ALPHA = "bottom_bar_background_alpha"
        private const val KEY_DIALOG_BACKGROUND_ALPHA = "dialog_background_alpha"
        private const val KEY_OVERLAY_BACKGROUND_ALPHA = "overlay_background_alpha"

        // 模糊半径键
        private const val KEY_TOP_BAR_BLUR_RADIUS = "top_bar_blur_radius"
        private const val KEY_BOTTOM_BAR_BLUR_RADIUS = "bottom_bar_blur_radius"
        private const val KEY_DIALOG_BLUR_RADIUS = "dialog_blur_radius"
        private const val KEY_OVERLAY_BLUR_RADIUS = "overlay_blur_radius"

        // 噪声因子键
        private const val KEY_TOP_BAR_NOISE_FACTOR = "top_bar_noise_factor"
        private const val KEY_BOTTOM_BAR_NOISE_FACTOR = "bottom_bar_noise_factor"
        private const val KEY_DIALOG_NOISE_FACTOR = "dialog_noise_factor"
        private const val KEY_OVERLAY_NOISE_FACTOR = "overlay_noise_factor"

        // 色调强度键
        private const val KEY_TOP_BAR_TINT_INTENSITY = "top_bar_tint_intensity"
        private const val KEY_BOTTOM_BAR_TINT_INTENSITY = "bottom_bar_tint_intensity"
        private const val KEY_DIALOG_TINT_INTENSITY = "dialog_tint_intensity"
        private const val KEY_OVERLAY_TINT_INTENSITY = "overlay_tint_intensity"

        // 兼容性键（保留旧的全局设置）
        private const val KEY_BLUR_INTENSITY = "blur_intensity"
        private const val KEY_BACKGROUND_ALPHA = "background_alpha"
        
        @Volatile
        private var INSTANCE: BlurConfigurationManager? = null
        
        /**
         * 获取模糊配置管理器实例
         */
        fun getInstance(context: Context): BlurConfigurationManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: BlurConfigurationManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    private val prefs: SharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)



    /**
     * 获取当前模糊配置
     *
     * 优先级：
     * 1. 用户自定义设置（如果存在）
     * 2. 当前主题的默认配置
     */
    @Composable
    fun getBlurConfiguration(): BlurConfiguration {
        // 获取当前主题管理器
        val themeManager = remember { ThemeManager.getInstance(context) }
        val currentTheme by themeManager.currentTheme

        // 获取主题的默认模糊配置
        val themeBlurConfig = currentTheme.themeProvider.getBlurConfiguration()

        // 创建一个状态来跟踪配置变化
        val configState = remember { mutableStateOf(loadBlurConfigurationWithTheme(themeBlurConfig)) }

        // 监听SharedPreferences变化
        DisposableEffect(context, themeBlurConfig) {
            val listener = SharedPreferences.OnSharedPreferenceChangeListener { _, _ ->
                configState.value = loadBlurConfigurationWithTheme(themeBlurConfig)
            }
            prefs.registerOnSharedPreferenceChangeListener(listener)

            // 初始加载
            configState.value = loadBlurConfigurationWithTheme(themeBlurConfig)

            onDispose {
                prefs.unregisterOnSharedPreferenceChangeListener(listener)
            }
        }

        return configState.value
    }

    /**
     * 从持久化存储加载模糊配置，并与主题配置合并
     *
     * @param themeConfig 当前主题的默认模糊配置
     * @return 合并后的模糊配置
     */
    private fun loadBlurConfigurationWithTheme(themeConfig: BlurConfiguration): BlurConfiguration {
        return BlurConfiguration(
            // 模糊效果开关
            topBarBlurEnabled = prefs.getBoolean(KEY_TOP_BAR_BLUR, themeConfig.topBarBlurEnabled),
            bottomBarBlurEnabled = prefs.getBoolean(KEY_BOTTOM_BAR_BLUR, themeConfig.bottomBarBlurEnabled),
            dialogBlurEnabled = prefs.getBoolean(KEY_DIALOG_BLUR, themeConfig.dialogBlurEnabled),
            overlayBlurEnabled = prefs.getBoolean(KEY_OVERLAY_BLUR, themeConfig.overlayBlurEnabled),

            // 模糊样式类型
            topBarBlurStyle = prefs.getString(KEY_TOP_BAR_BLUR_STYLE, themeConfig.topBarBlurStyle) ?: themeConfig.topBarBlurStyle,
            bottomBarBlurStyle = prefs.getString(KEY_BOTTOM_BAR_BLUR_STYLE, themeConfig.bottomBarBlurStyle) ?: themeConfig.bottomBarBlurStyle,
            dialogBlurStyle = prefs.getString(KEY_DIALOG_BLUR_STYLE, themeConfig.dialogBlurStyle) ?: themeConfig.dialogBlurStyle,
            overlayBlurStyle = prefs.getString(KEY_OVERLAY_BLUR_STYLE, themeConfig.overlayBlurStyle) ?: themeConfig.overlayBlurStyle,

            // 预设材质类型
            topBarPresetMaterial = prefs.getString(KEY_TOP_BAR_PRESET_MATERIAL, themeConfig.topBarPresetMaterial) ?: themeConfig.topBarPresetMaterial,
            bottomBarPresetMaterial = prefs.getString(KEY_BOTTOM_BAR_PRESET_MATERIAL, themeConfig.bottomBarPresetMaterial) ?: themeConfig.bottomBarPresetMaterial,
            dialogPresetMaterial = prefs.getString(KEY_DIALOG_PRESET_MATERIAL, themeConfig.dialogPresetMaterial) ?: themeConfig.dialogPresetMaterial,
            overlayPresetMaterial = prefs.getString(KEY_OVERLAY_PRESET_MATERIAL, themeConfig.overlayPresetMaterial) ?: themeConfig.overlayPresetMaterial,

            // 模糊强度
            topBarBlurIntensity = prefs.getFloat(KEY_TOP_BAR_BLUR_INTENSITY, themeConfig.topBarBlurIntensity),
            bottomBarBlurIntensity = prefs.getFloat(KEY_BOTTOM_BAR_BLUR_INTENSITY, themeConfig.bottomBarBlurIntensity),
            dialogBlurIntensity = prefs.getFloat(KEY_DIALOG_BLUR_INTENSITY, themeConfig.dialogBlurIntensity),
            overlayBlurIntensity = prefs.getFloat(KEY_OVERLAY_BLUR_INTENSITY, themeConfig.overlayBlurIntensity),

            // 背景透明度
            topBarBackgroundAlpha = prefs.getFloat(KEY_TOP_BAR_BACKGROUND_ALPHA, themeConfig.topBarBackgroundAlpha),
            bottomBarBackgroundAlpha = prefs.getFloat(KEY_BOTTOM_BAR_BACKGROUND_ALPHA, themeConfig.bottomBarBackgroundAlpha),
            dialogBackgroundAlpha = prefs.getFloat(KEY_DIALOG_BACKGROUND_ALPHA, themeConfig.dialogBackgroundAlpha),
            overlayBackgroundAlpha = prefs.getFloat(KEY_OVERLAY_BACKGROUND_ALPHA, themeConfig.overlayBackgroundAlpha),

            // 模糊半径
            topBarBlurRadius = prefs.getFloat(KEY_TOP_BAR_BLUR_RADIUS, themeConfig.topBarBlurRadius.value).dp,
            bottomBarBlurRadius = prefs.getFloat(KEY_BOTTOM_BAR_BLUR_RADIUS, themeConfig.bottomBarBlurRadius.value).dp,
            dialogBlurRadius = prefs.getFloat(KEY_DIALOG_BLUR_RADIUS, themeConfig.dialogBlurRadius.value).dp,
            overlayBlurRadius = prefs.getFloat(KEY_OVERLAY_BLUR_RADIUS, themeConfig.overlayBlurRadius.value).dp,

            // 噪声因子
            topBarNoiseFactor = prefs.getFloat(KEY_TOP_BAR_NOISE_FACTOR, themeConfig.topBarNoiseFactor),
            bottomBarNoiseFactor = prefs.getFloat(KEY_BOTTOM_BAR_NOISE_FACTOR, themeConfig.bottomBarNoiseFactor),
            dialogNoiseFactor = prefs.getFloat(KEY_DIALOG_NOISE_FACTOR, themeConfig.dialogNoiseFactor),
            overlayNoiseFactor = prefs.getFloat(KEY_OVERLAY_NOISE_FACTOR, themeConfig.overlayNoiseFactor),

            // 色调强度
            topBarTintIntensity = prefs.getFloat(KEY_TOP_BAR_TINT_INTENSITY, themeConfig.topBarTintIntensity),
            bottomBarTintIntensity = prefs.getFloat(KEY_BOTTOM_BAR_TINT_INTENSITY, themeConfig.bottomBarTintIntensity),
            dialogTintIntensity = prefs.getFloat(KEY_DIALOG_TINT_INTENSITY, themeConfig.dialogTintIntensity),
            overlayTintIntensity = prefs.getFloat(KEY_OVERLAY_TINT_INTENSITY, themeConfig.overlayTintIntensity),

            // 其他配置
            maxBlurRadius = themeConfig.maxBlurRadius,
            supportedOnDevice = themeConfig.supportedOnDevice
        )
    }


    
    /**
     * 更新顶部栏模糊设置
     */
    fun updateTopBarBlur(enabled: Boolean) {
        updateComponentBlur(BlurComponent.TOP_BAR, enabled)
    }
    
    /**
     * 更新底部栏模糊设置
     */
    fun updateBottomBarBlur(enabled: Boolean) {
        updateComponentBlur(BlurComponent.BOTTOM_BAR, enabled)
    }
    
    /**
     * 更新对话框模糊设置
     */
    fun updateDialogBlur(enabled: Boolean) {
        updateComponentBlur(BlurComponent.DIALOG, enabled)
    }
    

    /**
     * 更新覆盖层模糊设置
     */
    fun updateOverlayBlur(enabled: Boolean) {
        updateComponentBlur(BlurComponent.OVERLAY, enabled)
    }
    
    /**
     * 更新特定组件的模糊设置
     */
    fun updateComponentBlur(component: BlurComponent, enabled: Boolean) {
        // 直接保存到SharedPreferences，下次获取配置时会自动合并
        when (component) {
            BlurComponent.TOP_BAR -> prefs.edit().putBoolean(KEY_TOP_BAR_BLUR, enabled).apply()
            BlurComponent.BOTTOM_BAR -> prefs.edit().putBoolean(KEY_BOTTOM_BAR_BLUR, enabled).apply()
            BlurComponent.DIALOG -> prefs.edit().putBoolean(KEY_DIALOG_BLUR, enabled).apply()
            BlurComponent.OVERLAY -> prefs.edit().putBoolean(KEY_OVERLAY_BLUR, enabled).apply()
        }

        android.util.Log.d("BlurConfigManager", "updateComponentBlur: $component = $enabled")
    }

    /**
     * 更新特定组件的模糊样式类型
     */
    fun updateComponentBlurStyle(component: BlurComponent, style: String) {
        when (component) {
            BlurComponent.TOP_BAR -> prefs.edit().putString(KEY_TOP_BAR_BLUR_STYLE, style).apply()
            BlurComponent.BOTTOM_BAR -> prefs.edit().putString(KEY_BOTTOM_BAR_BLUR_STYLE, style).apply()
            BlurComponent.DIALOG -> prefs.edit().putString(KEY_DIALOG_BLUR_STYLE, style).apply()
            BlurComponent.OVERLAY -> prefs.edit().putString(KEY_OVERLAY_BLUR_STYLE, style).apply()
        }
        android.util.Log.d("BlurConfigManager", "updateComponentBlurStyle: $component = $style")
    }

    /**
     * 更新特定组件的预设材质类型
     */
    fun updateComponentPresetMaterial(component: BlurComponent, material: String) {
        when (component) {
            BlurComponent.TOP_BAR -> prefs.edit().putString(KEY_TOP_BAR_PRESET_MATERIAL, material).apply()
            BlurComponent.BOTTOM_BAR -> prefs.edit().putString(KEY_BOTTOM_BAR_PRESET_MATERIAL, material).apply()
            BlurComponent.DIALOG -> prefs.edit().putString(KEY_DIALOG_PRESET_MATERIAL, material).apply()
            BlurComponent.OVERLAY -> prefs.edit().putString(KEY_OVERLAY_PRESET_MATERIAL, material).apply()
        }
        android.util.Log.d("BlurConfigManager", "updateComponentPresetMaterial: $component = $material")
    }

    /**
     * 更新特定组件的模糊强度
     */
    fun updateComponentBlurIntensity(component: BlurComponent, intensity: Float) {
        val coercedIntensity = intensity.coerceIn(0f, 1f)
        when (component) {
            BlurComponent.TOP_BAR -> prefs.edit().putFloat(KEY_TOP_BAR_BLUR_INTENSITY, coercedIntensity).apply()
            BlurComponent.BOTTOM_BAR -> prefs.edit().putFloat(KEY_BOTTOM_BAR_BLUR_INTENSITY, coercedIntensity).apply()
            BlurComponent.DIALOG -> prefs.edit().putFloat(KEY_DIALOG_BLUR_INTENSITY, coercedIntensity).apply()
            BlurComponent.OVERLAY -> prefs.edit().putFloat(KEY_OVERLAY_BLUR_INTENSITY, coercedIntensity).apply()
        }
        android.util.Log.d("BlurConfigManager", "updateComponentBlurIntensity: $component = $coercedIntensity")
    }

    /**
     * 更新特定组件的背景透明度
     */
    fun updateComponentBackgroundAlpha(component: BlurComponent, alpha: Float) {
        val coercedAlpha = alpha.coerceIn(0f, 1.0f)
        when (component) {
            BlurComponent.TOP_BAR -> prefs.edit().putFloat(KEY_TOP_BAR_BACKGROUND_ALPHA, coercedAlpha).apply()
            BlurComponent.BOTTOM_BAR -> prefs.edit().putFloat(KEY_BOTTOM_BAR_BACKGROUND_ALPHA, coercedAlpha).apply()
            BlurComponent.DIALOG -> prefs.edit().putFloat(KEY_DIALOG_BACKGROUND_ALPHA, coercedAlpha).apply()
            BlurComponent.OVERLAY -> prefs.edit().putFloat(KEY_OVERLAY_BACKGROUND_ALPHA, coercedAlpha).apply()
        }
        android.util.Log.d("BlurConfigManager", "updateComponentBackgroundAlpha: $component = $coercedAlpha")
    }

    /**
     * 更新特定组件的模糊半径
     */
    fun updateComponentBlurRadius(component: BlurComponent, radius: Dp) {
        val coercedRadius = radius.value.coerceIn(5f, 50f)
        when (component) {
            BlurComponent.TOP_BAR -> prefs.edit().putFloat(KEY_TOP_BAR_BLUR_RADIUS, coercedRadius).apply()
            BlurComponent.BOTTOM_BAR -> prefs.edit().putFloat(KEY_BOTTOM_BAR_BLUR_RADIUS, coercedRadius).apply()
            BlurComponent.DIALOG -> prefs.edit().putFloat(KEY_DIALOG_BLUR_RADIUS, coercedRadius).apply()
            BlurComponent.OVERLAY -> prefs.edit().putFloat(KEY_OVERLAY_BLUR_RADIUS, coercedRadius).apply()
        }
        android.util.Log.d("BlurConfigManager", "updateComponentBlurRadius: $component = ${coercedRadius}dp")
    }

    /**
     * 更新特定组件的噪声因子
     */
    fun updateComponentNoiseFactor(component: BlurComponent, noiseFactor: Float) {
        val coercedNoiseFactor = noiseFactor.coerceIn(0f, 1f)
        when (component) {
            BlurComponent.TOP_BAR -> prefs.edit().putFloat(KEY_TOP_BAR_NOISE_FACTOR, coercedNoiseFactor).apply()
            BlurComponent.BOTTOM_BAR -> prefs.edit().putFloat(KEY_BOTTOM_BAR_NOISE_FACTOR, coercedNoiseFactor).apply()
            BlurComponent.DIALOG -> prefs.edit().putFloat(KEY_DIALOG_NOISE_FACTOR, coercedNoiseFactor).apply()
            BlurComponent.OVERLAY -> prefs.edit().putFloat(KEY_OVERLAY_NOISE_FACTOR, coercedNoiseFactor).apply()
        }
        android.util.Log.d("BlurConfigManager", "updateComponentNoiseFactor: $component = $coercedNoiseFactor")
    }

    /**
     * 更新特定组件的色调强度
     */
    fun updateComponentTintIntensity(component: BlurComponent, tintIntensity: Float) {
        val coercedTintIntensity = tintIntensity.coerceIn(0f, 2f)
        when (component) {
            BlurComponent.TOP_BAR -> prefs.edit().putFloat(KEY_TOP_BAR_TINT_INTENSITY, coercedTintIntensity).apply()
            BlurComponent.BOTTOM_BAR -> prefs.edit().putFloat(KEY_BOTTOM_BAR_TINT_INTENSITY, coercedTintIntensity).apply()
            BlurComponent.DIALOG -> prefs.edit().putFloat(KEY_DIALOG_TINT_INTENSITY, coercedTintIntensity).apply()
            BlurComponent.OVERLAY -> prefs.edit().putFloat(KEY_OVERLAY_TINT_INTENSITY, coercedTintIntensity).apply()
        }
        android.util.Log.d("BlurConfigManager", "updateComponentTintIntensity: $component = $coercedTintIntensity")
    }

    /**
     * 更新模糊强度（兼容性方法，更新所有组件）
     */
    fun updateBlurIntensity(intensity: Float) {
        val coercedIntensity = intensity.coerceIn(0f, 1f)
        prefs.edit()
            .putFloat(KEY_TOP_BAR_BLUR_INTENSITY, coercedIntensity)
            .putFloat(KEY_BOTTOM_BAR_BLUR_INTENSITY, coercedIntensity)
            .putFloat(KEY_DIALOG_BLUR_INTENSITY, coercedIntensity)
            .putFloat(KEY_OVERLAY_BLUR_INTENSITY, coercedIntensity)
            .putFloat(KEY_BLUR_INTENSITY, coercedIntensity) // 保留兼容性
            .apply()
        android.util.Log.d("BlurConfigManager", "updateBlurIntensity (all components): $coercedIntensity")
    }

    /**
     * 更新背景透明度（兼容性方法，更新所有组件）
     */
    fun updateBackgroundAlpha(alpha: Float) {
        val coercedAlpha = alpha.coerceIn(0f, 0.5f)
        prefs.edit()
            .putFloat(KEY_TOP_BAR_BACKGROUND_ALPHA, coercedAlpha)
            .putFloat(KEY_BOTTOM_BAR_BACKGROUND_ALPHA, coercedAlpha)
            .putFloat(KEY_DIALOG_BACKGROUND_ALPHA, coercedAlpha)
            .putFloat(KEY_OVERLAY_BACKGROUND_ALPHA, coercedAlpha)
            .putFloat(KEY_BACKGROUND_ALPHA, coercedAlpha) // 保留兼容性
            .apply()
        android.util.Log.d("BlurConfigManager", "updateBackgroundAlpha (all components): $coercedAlpha")
    }

    /**
     * 启用所有模糊效果
     */
    fun enableAllBlurEffects() {
        prefs.edit()
            .putBoolean(KEY_TOP_BAR_BLUR, true)
            .putBoolean(KEY_BOTTOM_BAR_BLUR, true)
            .putBoolean(KEY_DIALOG_BLUR, true)
            .putBoolean(KEY_OVERLAY_BLUR, true)
            .apply()
        Log.d("BlurConfigManager", "启用所有模糊效果")
    }

    /**
     * 禁用所有模糊效果
     */
    fun disableAllBlurEffects() {
        prefs.edit()
            .putBoolean(KEY_TOP_BAR_BLUR, false)
            .putBoolean(KEY_BOTTOM_BAR_BLUR, false)
            .putBoolean(KEY_DIALOG_BLUR, false)
            .putBoolean(KEY_OVERLAY_BLUR, false)
            .apply()
        Log.d("BlurConfigManager", "禁用所有模糊效果")
    }

    /**
     * 重置为主题默认配置
     *
     * 清除所有用户自定义设置，让配置回到主题默认值
     */
    fun resetToThemeDefaults() {
        prefs.edit().clear().apply()
        Log.d("BlurConfigManager", "重置为主题默认配置")
    }



    /**
     * 根据组件配置获取Haze材质
     *
     * @param component 模糊组件类型
     * @return 对应的HazeStyle，根据组件的模糊样式类型返回相应材质
     */
    @OptIn(ExperimentalHazeMaterialsApi::class)
    @Composable
    fun getHazeMaterial(component: BlurComponent): HazeStyle? {
        val config = getBlurConfiguration()
        val blurStyle = config.getComponentBlurStyle(component)

        return when (blurStyle) {
            "preset" -> {
                // 使用预设材质
                val material = config.getComponentPresetMaterial(component)
                when (material) {
                    "ultraThin" -> HazeMaterials.ultraThin()
                    "thin" -> HazeMaterials.thin()
                    "regular" -> HazeMaterials.regular()
                    "thick" -> HazeMaterials.thick()
                    else -> HazeMaterials.regular() // 默认值
                }
            }
            "custom" -> {
                // 使用自定义样式，创建自定义HazeStyle
                getCustomHazeStyle(component)
            }
            else -> HazeMaterials.regular() // 默认值
        }
    }

    /**
     * 创建自定义HazeStyle
     *
     * 根据用户配置的参数创建自定义的模糊样式，包括：
     * - 模糊半径：根据用户设置的强度计算
     * - 背景色：使用Material主题的surface颜色
     * - 色调：根据背景色的亮度自动调整透明度
     * - 噪声因子：固定为0.15f，提供适度的纹理效果
     *
     * @param component 模糊组件类型
     * @return 自定义的HazeStyle
     */
    @Composable
    fun getCustomHazeStyle(component: BlurComponent): HazeStyle {
        val config = getBlurConfiguration()
        val backgroundColor = if (component == BlurComponent.DIALOG) {
            // 对话框使用硬编码的背景颜色
            Color(0xFFF9F9F9)
        } else {
            // 其他组件使用Material主题的surface颜色
            MaterialTheme.colorScheme.surface
        }

        // 获取用户配置的模糊参数
        val blurRadius = config.getActualBlurRadius(component)
        val intensity = config.getComponentBlurIntensity(component)
        val backgroundAlpha = config.getComponentBackgroundAlpha(component)
        val noiseFactor = config.getComponentNoiseFactor(component)
        val tintIntensity = config.getComponentTintIntensity(component)

        // 根据背景色亮度和用户配置的色调强度自动调整tint透明度
        // 亮色背景使用较高透明度，暗色背景使用较低透明度
        val tintAlpha = if (backgroundColor.luminance() >= 0.5f) {
            // 亮色背景：使用用户配置的背景透明度 + 强度调整 * 色调强度
            (backgroundAlpha + intensity * 0.2f * tintIntensity).coerceIn(0.1f, 0.5f)
        } else {
            // 暗色背景：使用较低的透明度 * 色调强度
            (backgroundAlpha + intensity * 0.1f * tintIntensity).coerceIn(0.05f, 0.3f)
        }

        Log.d("BlurConfigurationManager", "创建自定义HazeStyle: component=$component, blurRadius=$blurRadius, intensity=$intensity, tintAlpha=$tintAlpha, noiseFactor=$noiseFactor, tintIntensity=$tintIntensity")

        return HazeStyle(
            backgroundColor = backgroundColor,
            tints = listOf(
                HazeTint(backgroundColor.copy(alpha = tintAlpha))
            ),
            blurRadius = blurRadius,
            noiseFactor = noiseFactor // 使用用户配置的噪声因子
        )
    }

    /**
     * 根据配置获取Haze材质（兼容性方法，使用顶部栏配置）
     *
     * @return 对应的HazeStyle，根据模糊强度返回相应材质，如果是自定义模式则返回默认材质
     */
    @OptIn(ExperimentalHazeMaterialsApi::class)
    @Composable
    fun getHazeMaterial(): HazeStyle {
        return getHazeMaterial(BlurComponent.TOP_BAR) ?: HazeMaterials.regular()
    }

    /**
     * 获取底部导航栏的Haze材质
     *
     * @return 对应的HazeStyle，如果底部栏模糊未启用则返回null
     */
    @OptIn(ExperimentalHazeMaterialsApi::class)
    @Composable
    fun getBottomBarHazeMaterial(): HazeStyle? {
        val config = getBlurConfiguration()
        return if (config.bottomBarBlurEnabled) getHazeMaterial(BlurComponent.BOTTOM_BAR) else null
    }

    /**
     * 获取顶部应用栏的Haze材质
     *
     * @return 对应的HazeStyle，如果顶部栏模糊未启用则返回null
     */
    @OptIn(ExperimentalHazeMaterialsApi::class)
    @Composable
    fun getTopBarHazeMaterial(): HazeStyle? {
        val config = getBlurConfiguration()
        val result = if (config.topBarBlurEnabled) getHazeMaterial(BlurComponent.TOP_BAR) else null
        Log.d("BlurConfigManager", "getTopBarHazeMaterial: enabled=${config.topBarBlurEnabled}, supported=${config.supportedOnDevice}, intensity=${config.getComponentBlurIntensity(BlurComponent.TOP_BAR)}, result=${result != null}")
        return result
    }

    /**
     * 获取对话框的Haze材质
     *
     * @return 对应的HazeStyle，如果对话框模糊未启用则返回null
     */
    @OptIn(ExperimentalHazeMaterialsApi::class)
    @Composable
    fun getDialogHazeMaterial(): HazeStyle? {
        val config = getBlurConfiguration()
        return if (config.dialogBlurEnabled) getHazeMaterial(BlurComponent.DIALOG) else null
    }

    /**
     * 获取覆盖层的Haze材质
     *
     * @return 对应的HazeStyle，如果覆盖层模糊未启用则返回null
     */
    @OptIn(ExperimentalHazeMaterialsApi::class)
    @Composable
    fun getOverlayHazeMaterial(): HazeStyle? {
        val config = getBlurConfiguration()
        return if (config.overlayBlurEnabled) getHazeMaterial(BlurComponent.OVERLAY) else null
    }
}

/**
 * 模糊配置统计信息
 */
data class BlurConfigurationStats(
    val totalComponents: Int,
    val enabledComponents: Int,
    val averageBlurIntensity: Float,
    val isBlurSupported: Boolean
) {
    val enabledPercentage: Float get() = if (totalComponents > 0) enabledComponents.toFloat() / totalComponents else 0f
}
