package com.weinuo.quickcommands22.model

/**
 * 智慧提醒配置数据类
 *
 * 存储单个智慧提醒功能的配置信息，包括：
 * - 基本信息（类型、启用状态）
 * - 配置状态（是否已配置、配置数据）
 * - 运行时状态（最后触发时间等）
 *
 * 设计原则：
 * - 使用原生数据类型，避免序列化问题
 * - 支持所有类型的智慧提醒配置
 * - 便于扩展新的配置字段
 */
data class SmartReminderConfig(
    /**
     * 智慧提醒类型ID
     */
    val reminderTypeId: String,

    /**
     * 是否启用此智慧提醒
     */
    val isEnabled: Boolean = false,

    /**
     * 是否已完成必要配置
     * 对于需要配置的提醒类型，只有配置完成后才能启用
     */
    val isConfigured: Boolean = false,





    /**
     * 创建时间（毫秒时间戳）
     */
    val createdTime: Long = System.currentTimeMillis(),

    /**
     * 最后修改时间（毫秒时间戳）
     */
    val lastModifiedTime: Long = System.currentTimeMillis()
) {

    /**
     * 获取智慧提醒类型
     */
    fun getReminderType(): SmartReminderType? {
        return SmartReminderType.fromId(reminderTypeId)
    }

    /**
     * 检查是否可以启用
     * 只有配置完成的提醒才能启用
     */
    fun canBeEnabled(): Boolean {
        val reminderType = getReminderType() ?: return false
        return when (reminderType.configType) {
            SmartReminderConfigType.NONE -> true
            else -> isConfigured
        }
    }

    /**
     * 检查是否实际可用
     * 需要同时满足：已配置且已启用
     */
    fun isAvailable(): Boolean {
        return canBeEnabled() && isEnabled
    }

    /**
     * 创建更新后的配置（自定义copy方法，自动更新修改时间）
     */
    fun copyWithUpdate(
        reminderTypeId: String = this.reminderTypeId,
        isEnabled: Boolean = this.isEnabled,
        isConfigured: Boolean = this.isConfigured,
        createdTime: Long = this.createdTime,
        lastModifiedTime: Long = System.currentTimeMillis()
    ): SmartReminderConfig {
        return SmartReminderConfig(
            reminderTypeId = reminderTypeId,
            isEnabled = isEnabled,
            isConfigured = isConfigured,
            createdTime = createdTime,
            lastModifiedTime = lastModifiedTime
        )
    }

    companion object {
        /**
         * 创建默认配置
         */
        fun createDefault(reminderType: SmartReminderType): SmartReminderConfig {
            return SmartReminderConfig(
                reminderTypeId = reminderType.id,
                isEnabled = false,
                isConfigured = reminderType.configType == SmartReminderConfigType.NONE
            )
        }
    }
}
