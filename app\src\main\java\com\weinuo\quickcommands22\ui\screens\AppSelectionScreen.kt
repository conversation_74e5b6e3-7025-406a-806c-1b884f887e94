package com.weinuo.quickcommands22.ui.screens

import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.icons.filled.CheckBox
import androidx.compose.material.icons.filled.CheckBoxOutlineBlank
import androidx.compose.material.icons.filled.ExpandLess
import androidx.compose.material.icons.filled.ExpandMore
import androidx.compose.material.icons.filled.IndeterminateCheckBox
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.weinuo.quickcommands22.R
import com.weinuo.quickcommands22.model.SimpleAppInfo
import com.weinuo.quickcommands22.model.AppInfo
import com.weinuo.quickcommands22.utils.AppsCacheManager
import com.weinuo.quickcommands22.ui.components.themed.ThemedSearchTextField
import com.weinuo.quickcommands22.utils.ImageUtils
import com.weinuo.quickcommands22.data.SettingsRepository
import com.weinuo.quickcommands22.ui.theme.manager.ThemeManager
import com.weinuo.quickcommands22.ui.components.skyblue.SkyBlueBackButton
import com.weinuo.quickcommands22.ui.components.skyblue.SkyBlueSaveButton
import kotlinx.coroutines.launch

/**
 * 应用选择模式枚举
 */
enum class AppSelectionMode {
    SINGLE,  // 单选模式
    MULTI    // 多选模式
}

/**
 * 将AppInfo转换为SimpleAppInfo的扩展函数
 */
private fun AppInfo.toSimpleAppInfo(): SimpleAppInfo {
    return SimpleAppInfo(
        packageName = this.packageName,
        appName = this.appName,
        isSystemApp = this.isSystemApp,
        isRunning = this.isRunning,
        icon = this.icon
    )
}



/**
 * 可复用的应用选择界面
 *
 * 提供应用列表选择界面，支持搜索功能和单选/多选模式
 * 使用全屏界面替代对话框，提供更好的用户体验
 * 可以被多个配置提供器复用，避免代码重复
 * 使用通用缓存管理器，按需加载应用列表，避免不必要的电量消耗
 *
 * @param selectionMode 选择模式（单选/多选）
 * @param initialSelectedAppPackageNames 初始选中的应用包名列表（多选模式使用）
 * @param filterType 过滤类型，用于特定应用类型的过滤
 * @param onAppsSelected 应用选择完成回调，返回选中的应用列表
 * @param onDismiss 取消回调
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AppSelectionScreen(
    selectionMode: AppSelectionMode,
    initialSelectedAppPackageNames: List<String> = emptyList(),
    filterType: String? = null,
    onAppsSelected: (List<SimpleAppInfo>) -> Unit,
    onDismiss: () -> Unit
) {
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()
    val focusManager = LocalFocusManager.current

    // 获取主题管理器和设置仓库
    val themeManager = remember { ThemeManager.getInstance(context) }
    val settingsRepository = remember { SettingsRepository(context) }
    val globalSettings by settingsRepository.globalSettings.collectAsState()

    // 主题感知的标题样式
    val themeAwareTitleStyle = if (themeManager.getCurrentThemeId() == "sky_blue") {
        // 天空蓝主题：使用全局设置的标题字重和字体大小
        MaterialTheme.typography.titleLarge.copy(
            fontWeight = when (globalSettings.topAppBarTitleFontWeight) {
                "normal" -> FontWeight.Normal
                "medium" -> FontWeight.Medium
                "bold" -> FontWeight.Bold
                else -> FontWeight.Medium
            },
            fontSize = globalSettings.screenTitleFontSize.sp
        )
    } else {
        // 海洋蓝主题：保持原有样式
        MaterialTheme.typography.titleLarge
    }

    // 状态管理
    var apps by remember { mutableStateOf<List<SimpleAppInfo>>(emptyList()) }
    var isLoading by remember { mutableStateOf(true) }
    var searchQuery by remember { mutableStateOf("") }
    var selectedAppPackageNames by remember { mutableStateOf(initialSelectedAppPackageNames.toSet()) }
    var errorMessage by remember { mutableStateOf<String?>(null) }

    // 分组状态
    var userAppsExpanded by remember { mutableStateOf(true) }
    var systemAppsExpanded by remember { mutableStateOf(false) }

    // 加载应用列表 - 使用通用缓存管理器
    LaunchedEffect(Unit) {
        coroutineScope.launch {
            try {
                // 使用AppsCacheManager加载应用列表
                val (userAppsList, systemAppsList) = AppsCacheManager.getAllApps(context)

                // 合并用户应用和系统应用
                val allApps = userAppsList + systemAppsList

                // 转换为SimpleAppInfo并应用过滤逻辑
                val appList = mutableListOf<SimpleAppInfo>()
                val packageManager = context.packageManager

                for (appInfo in allApps) {
                    val packageName = appInfo.packageName
                    val isSystemApp = appInfo.isSystemApp

                    // 应用特定过滤逻辑
                    val shouldInclude = when (filterType) {
                        "LIVE_WALLPAPER" -> {
                            // 过滤动态壁纸应用
                            try {
                                val serviceIntent = Intent("android.service.wallpaper.WallpaperService")
                                serviceIntent.setPackage(packageName)
                                val services = packageManager.queryIntentServices(serviceIntent, PackageManager.GET_META_DATA)
                                services.isNotEmpty()
                            } catch (e: Exception) {
                                false
                            }
                        }
                        "TASKER_PLUGIN" -> {
                            // 过滤Tasker/Locale插件应用（包含条件和任务插件）
                            try {
                                val taskerIntent = Intent("com.twofortyfouram.locale.intent.action.EDIT_SETTING")
                                taskerIntent.setPackage(packageName)
                                val taskerActivities = packageManager.queryIntentActivities(taskerIntent, PackageManager.GET_META_DATA)

                                val localeIntent = Intent("com.twofortyfouram.locale.intent.action.EDIT_CONDITION")
                                localeIntent.setPackage(packageName)
                                val localeActivities = packageManager.queryIntentActivities(localeIntent, PackageManager.GET_META_DATA)

                                taskerActivities.isNotEmpty() || localeActivities.isNotEmpty()
                            } catch (e: Exception) {
                                false
                            }
                        }
                        "TASKER_ACTION_PLUGIN" -> {
                            // 过滤Tasker/Locale任务插件应用（仅任务插件）
                            try {
                                val taskerIntent = Intent("com.twofortyfouram.locale.intent.action.EDIT_SETTING")
                                taskerIntent.setPackage(packageName)
                                val taskerActivities = packageManager.queryIntentActivities(taskerIntent, PackageManager.GET_META_DATA)
                                taskerActivities.isNotEmpty()
                            } catch (e: Exception) {
                                false
                            }
                        }
                        "TASKER_CONDITION_PLUGIN" -> {
                            // 过滤Tasker/Locale条件插件应用（仅条件插件）
                            try {
                                val localeIntent = Intent("com.twofortyfouram.locale.intent.action.EDIT_CONDITION")
                                localeIntent.setPackage(packageName)
                                val localeActivities = packageManager.queryIntentActivities(localeIntent, PackageManager.GET_META_DATA)
                                localeActivities.isNotEmpty()
                            } catch (e: Exception) {
                                false
                            }
                        }
                        "DIGITAL_ASSISTANT" -> {
                            // 过滤数字助理应用
                            try {
                                val voiceIntent = Intent("android.service.voice.VoiceInteractionService")
                                voiceIntent.setPackage(packageName)
                                val voiceServices = packageManager.queryIntentServices(voiceIntent, PackageManager.GET_META_DATA)

                                val assistIntent = Intent(Intent.ACTION_ASSIST)
                                assistIntent.setPackage(packageName)
                                val assistActivities = packageManager.queryIntentActivities(assistIntent, PackageManager.GET_META_DATA)

                                val voiceSearchIntent = Intent("android.speech.action.RECOGNIZE_SPEECH")
                                voiceSearchIntent.setPackage(packageName)
                                val voiceSearchActivities = packageManager.queryIntentActivities(voiceSearchIntent, PackageManager.GET_META_DATA)

                                val hasVoiceInteractionPermission = try {
                                    val pkgInfo = packageManager.getPackageInfo(packageName, PackageManager.GET_PERMISSIONS)
                                    val permissions = pkgInfo.requestedPermissions?.toList() ?: emptyList()
                                    permissions.contains("android.permission.BIND_VOICE_INTERACTION")
                                } catch (e: Exception) {
                                    false
                                }

                                voiceServices.isNotEmpty() || assistActivities.isNotEmpty() ||
                                voiceSearchActivities.isNotEmpty() || hasVoiceInteractionPermission
                            } catch (e: Exception) {
                                false
                            }
                        }
                        "KEYBOARD" -> {
                            // 过滤键盘应用
                            try {
                                val imeIntent = Intent("android.view.InputMethod")
                                imeIntent.setPackage(packageName)
                                val imeServices = packageManager.queryIntentServices(imeIntent, PackageManager.GET_META_DATA)

                                val hasInputMethodPermission = try {
                                    val pkgInfo = packageManager.getPackageInfo(packageName, PackageManager.GET_PERMISSIONS)
                                    val permissions = pkgInfo.requestedPermissions?.toList() ?: emptyList()
                                    permissions.contains("android.permission.BIND_INPUT_METHOD")
                                } catch (e: Exception) {
                                    false
                                }

                                val inputMethodManager = context.getSystemService(Context.INPUT_METHOD_SERVICE) as android.view.inputmethod.InputMethodManager
                                val enabledInputMethods = inputMethodManager.inputMethodList
                                val isInSystemList = enabledInputMethods.any { it.packageName == packageName }

                                imeServices.isNotEmpty() || hasInputMethodPermission || isInSystemList
                            } catch (e: Exception) {
                                false
                            }
                        }
                        else -> {
                            // 默认：显示所有应用（包括系统应用）
                            true
                        }
                    }

                    if (shouldInclude) {
                        // 直接使用AppRepository提供的AppInfo转换为SimpleAppInfo
                        appList.add(appInfo.toSimpleAppInfo())
                    }
                }

                // 按类型分组，组内按运行状态和名称排序
                val sortedAppList = appList.sortedWith(
                    compareBy<SimpleAppInfo> { it.isSystemApp }
                        .thenByDescending { it.isRunning }
                        .thenBy { it.appName }
                )

                apps = sortedAppList
                errorMessage = null
            } catch (e: Exception) {
                errorMessage = "加载应用失败：${e.message}"
            } finally {
                isLoading = false
            }
        }
    }

    // 过滤应用列表
    val filteredApps = remember(apps, searchQuery) {
        if (searchQuery.isNotBlank()) {
            apps.filter { app ->
                app.appName.contains(searchQuery, ignoreCase = true) ||
                app.packageName.contains(searchQuery, ignoreCase = true)
            }
        } else {
            apps
        }
    }

    // 分组应用列表
    val userApps = remember(filteredApps) {
        filteredApps.filter { !it.isSystemApp }
    }

    val systemApps = remember(filteredApps) {
        filteredApps.filter { it.isSystemApp }
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = when {
                            filterType == "LIVE_WALLPAPER" -> when (selectionMode) {
                                AppSelectionMode.SINGLE -> "选择动态壁纸应用"
                                AppSelectionMode.MULTI -> "选择动态壁纸应用 (${selectedAppPackageNames.size})"
                            }
                            filterType == "DIGITAL_ASSISTANT" -> when (selectionMode) {
                                AppSelectionMode.SINGLE -> "选择数字助理应用"
                                AppSelectionMode.MULTI -> "选择数字助理应用 (${selectedAppPackageNames.size})"
                            }
                            filterType == "KEYBOARD" -> when (selectionMode) {
                                AppSelectionMode.SINGLE -> "选择键盘应用"
                                AppSelectionMode.MULTI -> "选择键盘应用 (${selectedAppPackageNames.size})"
                            }
                            filterType == "TASKER_PLUGIN" -> when (selectionMode) {
                                AppSelectionMode.SINGLE -> "选择Tasker/Locale插件应用"
                                AppSelectionMode.MULTI -> "选择Tasker/Locale插件应用 (${selectedAppPackageNames.size})"
                            }
                            filterType == "TASKER_ACTION_PLUGIN" -> when (selectionMode) {
                                AppSelectionMode.SINGLE -> "选择Tasker/Locale任务插件"
                                AppSelectionMode.MULTI -> "选择Tasker/Locale任务插件 (${selectedAppPackageNames.size})"
                            }
                            filterType == "TASKER_CONDITION_PLUGIN" -> when (selectionMode) {
                                AppSelectionMode.SINGLE -> "选择Tasker/Locale条件插件"
                                AppSelectionMode.MULTI -> "选择Tasker/Locale条件插件 (${selectedAppPackageNames.size})"
                            }
                            else -> when (selectionMode) {
                                AppSelectionMode.SINGLE -> "选择应用"
                                AppSelectionMode.MULTI -> "选择应用 (${selectedAppPackageNames.size})"
                            }
                        },
                        style = themeAwareTitleStyle
                    )
                },
                navigationIcon = {
                    if (themeManager.getCurrentThemeId() == "sky_blue") {
                        // 天空蓝主题：使用专用的返回按钮
                        SkyBlueBackButton(onClick = onDismiss)
                    } else {
                        // 其他主题：使用原有的箭头图标
                        IconButton(onClick = onDismiss) {
                            Icon(
                                imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                                contentDescription = "返回"
                            )
                        }
                    }
                },
                actions = {
                    // 多选模式下显示保存按钮 - 主题感知，始终显示但根据选择状态控制可用性
                    if (selectionMode == AppSelectionMode.MULTI) {
                        val hasSelection = selectedAppPackageNames.isNotEmpty()
                        if (themeManager.getCurrentThemeId() == "sky_blue") {
                            // 天空蓝主题：使用专用的保存按钮（勾号图标）
                            SkyBlueSaveButton(
                                onClick = {
                                    val selectedApps = apps.filter { it.packageName in selectedAppPackageNames }
                                    onAppsSelected(selectedApps)
                                },
                                enabled = hasSelection
                            )
                        } else {
                            // 其他主题：使用原有的文本按钮
                            TextButton(
                                onClick = {
                                    val selectedApps = apps.filter { it.packageName in selectedAppPackageNames }
                                    onAppsSelected(selectedApps)
                                },
                                enabled = hasSelection
                            ) {
                                Text(stringResource(R.string.save))
                            }
                        }
                    }
                }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            // 搜索框
            ThemedSearchTextField(
                searchQuery = searchQuery,
                onSearchQueryChange = {
                    searchQuery = it
                    // 搜索时自动展开所有分组
                    if (it.isNotEmpty()) {
                        userAppsExpanded = true
                        systemAppsExpanded = true
                    }
                },
                onClearSearch = {
                    searchQuery = ""
                    focusManager.clearFocus()
                },
                placeholder = stringResource(R.string.search_apps),
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp)
            )



            when {
                isLoading -> {
                    // 加载状态
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally,
                            verticalArrangement = Arrangement.spacedBy(16.dp)
                        ) {
                            CircularProgressIndicator()
                            Text("正在加载应用...")
                        }
                    }
                }

                errorMessage != null -> {
                    // 错误状态
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally,
                            verticalArrangement = Arrangement.spacedBy(16.dp)
                        ) {
                            Text(
                                text = errorMessage!!,
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.error
                            )
                            Button(
                                onClick = {
                                    isLoading = true
                                    errorMessage = null
                                    // 重新加载应用列表的逻辑会在LaunchedEffect中处理
                                }
                            ) {
                                Text("重试")
                            }
                        }
                    }
                }

                filteredApps.isEmpty() -> {
                    // 空状态
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = when {
                                filterType == "LIVE_WALLPAPER" -> {
                                    if (searchQuery.isBlank()) "没有找到动态壁纸应用" else "没有找到匹配的动态壁纸应用"
                                }
                                filterType == "DIGITAL_ASSISTANT" -> {
                                    if (searchQuery.isBlank()) "没有找到数字助理应用" else "没有找到匹配的数字助理应用"
                                }
                                filterType == "KEYBOARD" -> {
                                    if (searchQuery.isBlank()) "没有找到键盘应用" else "没有找到匹配的键盘应用"
                                }
                                filterType == "TASKER_PLUGIN" -> {
                                    if (searchQuery.isBlank()) "没有找到Tasker/Locale插件应用" else "没有找到匹配的Tasker/Locale插件应用"
                                }
                                filterType == "TASKER_ACTION_PLUGIN" -> {
                                    if (searchQuery.isBlank()) "没有找到Tasker/Locale任务插件" else "没有找到匹配的Tasker/Locale任务插件"
                                }
                                filterType == "TASKER_CONDITION_PLUGIN" -> {
                                    if (searchQuery.isBlank()) "没有找到Tasker/Locale条件插件" else "没有找到匹配的Tasker/Locale条件插件"
                                }
                                else -> {
                                    if (searchQuery.isBlank()) "没有应用" else "没有找到匹配的应用"
                                }
                            },
                            style = MaterialTheme.typography.bodyLarge,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }

                else -> {
                    // 应用列表 - 使用分组显示
                    LazyColumn(
                        modifier = Modifier.weight(1f),
                        contentPadding = PaddingValues(horizontal = 16.dp, vertical = 8.dp),
                        verticalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        // 显示分组还是扁平列表
                        val showGrouped = searchQuery.isBlank()

                        if (showGrouped) {
                            // 分组显示
                            if (userApps.isNotEmpty()) {
                                // 用户应用分组
                                item {
                                    ExpandableGroupHeader(
                                        title = "用户应用",
                                        count = userApps.size,
                                        expanded = userAppsExpanded,
                                        onToggle = { userAppsExpanded = !userAppsExpanded },
                                        selectionMode = selectionMode,
                                        groupApps = userApps,
                                        selectedAppPackageNames = selectedAppPackageNames,
                                        onGroupSelectionToggle = { groupApps ->
                                            // 切换用户应用组的选择状态
                                            val groupPackageNames = groupApps.map { it.packageName }.toSet()
                                            val selectedInGroup = groupPackageNames.intersect(selectedAppPackageNames)
                                            selectedAppPackageNames = if (selectedInGroup.size == groupPackageNames.size) {
                                                // 全部选中，取消全选
                                                selectedAppPackageNames - groupPackageNames
                                            } else {
                                                // 未全选，全选该组
                                                selectedAppPackageNames + groupPackageNames
                                            }
                                        }
                                    )
                                }

                                if (userAppsExpanded) {
                                    items(userApps) { app ->
                                        AppItem(
                                            app = app,
                                            isSelected = app.packageName in selectedAppPackageNames,
                                            selectionMode = selectionMode,
                                            onAppClick = { clickedApp ->
                                                handleAppClick(clickedApp, selectionMode, selectedAppPackageNames, onAppsSelected) { newSelection ->
                                                    selectedAppPackageNames = newSelection
                                                }
                                            }
                                        )
                                    }
                                }
                            }

                            if (systemApps.isNotEmpty()) {
                                // 系统应用分组
                                item {
                                    ExpandableGroupHeader(
                                        title = "系统应用",
                                        count = systemApps.size,
                                        expanded = systemAppsExpanded,
                                        onToggle = { systemAppsExpanded = !systemAppsExpanded },
                                        selectionMode = selectionMode,
                                        groupApps = systemApps,
                                        selectedAppPackageNames = selectedAppPackageNames,
                                        onGroupSelectionToggle = { groupApps ->
                                            // 切换系统应用组的选择状态
                                            val groupPackageNames = groupApps.map { it.packageName }.toSet()
                                            val selectedInGroup = groupPackageNames.intersect(selectedAppPackageNames)
                                            selectedAppPackageNames = if (selectedInGroup.size == groupPackageNames.size) {
                                                // 全部选中，取消全选
                                                selectedAppPackageNames - groupPackageNames
                                            } else {
                                                // 未全选，全选该组
                                                selectedAppPackageNames + groupPackageNames
                                            }
                                        }
                                    )
                                }

                                if (systemAppsExpanded) {
                                    items(systemApps) { app ->
                                        AppItem(
                                            app = app,
                                            isSelected = app.packageName in selectedAppPackageNames,
                                            selectionMode = selectionMode,
                                            onAppClick = { clickedApp ->
                                                handleAppClick(clickedApp, selectionMode, selectedAppPackageNames, onAppsSelected) { newSelection ->
                                                    selectedAppPackageNames = newSelection
                                                }
                                            }
                                        )
                                    }
                                }
                            }
                        } else {
                            // 扁平显示（搜索或筛选时）
                            items(filteredApps) { app ->
                                AppItem(
                                    app = app,
                                    isSelected = app.packageName in selectedAppPackageNames,
                                    selectionMode = selectionMode,
                                    onAppClick = { clickedApp ->
                                        handleAppClick(clickedApp, selectionMode, selectedAppPackageNames, onAppsSelected) { newSelection ->
                                            selectedAppPackageNames = newSelection
                                        }
                                    }
                                )
                            }
                        }
                    }
                }
            }
        }
    }
}

/**
 * 处理应用点击事件的辅助函数
 */
private fun handleAppClick(
    clickedApp: SimpleAppInfo,
    selectionMode: AppSelectionMode,
    selectedAppPackageNames: Set<String>,
    onAppsSelected: (List<SimpleAppInfo>) -> Unit,
    onSelectionChanged: (Set<String>) -> Unit
) {
    when (selectionMode) {
        AppSelectionMode.SINGLE -> {
            // 单选模式：直接返回选中的应用
            onAppsSelected(listOf(clickedApp))
        }
        AppSelectionMode.MULTI -> {
            // 多选模式：切换选中状态
            val newSelection = if (clickedApp.packageName in selectedAppPackageNames) {
                selectedAppPackageNames - clickedApp.packageName
            } else {
                selectedAppPackageNames + clickedApp.packageName
            }
            onSelectionChanged(newSelection)
        }
    }
}

/**
 * 组选择状态枚举
 */
private enum class GroupSelectionState {
    NONE,      // 未选中任何应用
    PARTIAL,   // 部分应用被选中
    ALL        // 全部应用被选中
}

/**
 * 可展开分组标题组件（带全选功能）
 */
@Composable
private fun ExpandableGroupHeader(
    title: String,
    count: Int,
    expanded: Boolean,
    onToggle: () -> Unit,
    // 新增参数：多选模式相关
    selectionMode: AppSelectionMode = AppSelectionMode.SINGLE,
    groupApps: List<SimpleAppInfo> = emptyList(),
    selectedAppPackageNames: Set<String> = emptySet(),
    onGroupSelectionToggle: ((List<SimpleAppInfo>) -> Unit)? = null
) {
    // 计算组选择状态
    val groupSelectionState = when {
        selectionMode != AppSelectionMode.MULTI -> GroupSelectionState.NONE
        groupApps.isEmpty() -> GroupSelectionState.NONE
        else -> {
            val selectedInGroup = groupApps.filter { it.packageName in selectedAppPackageNames }
            when {
                selectedInGroup.isEmpty() -> GroupSelectionState.NONE
                selectedInGroup.size == groupApps.size -> GroupSelectionState.ALL
                else -> GroupSelectionState.PARTIAL
            }
        }
    }

    Surface(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onToggle() },
        shape = RoundedCornerShape(8.dp),
        color = MaterialTheme.colorScheme.secondaryContainer.copy(alpha = 0.3f)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "$title ($count)",
                style = MaterialTheme.typography.titleSmall,
                color = MaterialTheme.colorScheme.secondary,
                modifier = Modifier.weight(1f)
            )

            // 多选模式下显示全选复选框
            if (selectionMode == AppSelectionMode.MULTI && onGroupSelectionToggle != null) {
                IconButton(
                    onClick = {
                        // 切换组内所有应用的选择状态
                        onGroupSelectionToggle(groupApps)
                    },
                    modifier = Modifier.size(40.dp)
                ) {
                    Icon(
                        imageVector = when (groupSelectionState) {
                            GroupSelectionState.NONE -> Icons.Default.CheckBoxOutlineBlank
                            GroupSelectionState.PARTIAL -> Icons.Default.IndeterminateCheckBox
                            GroupSelectionState.ALL -> Icons.Default.CheckBox
                        },
                        contentDescription = when (groupSelectionState) {
                            GroupSelectionState.NONE -> "全选$title"
                            GroupSelectionState.PARTIAL -> "部分选中，点击全选$title"
                            GroupSelectionState.ALL -> "取消全选$title"
                        },
                        tint = MaterialTheme.colorScheme.primary,
                        modifier = Modifier.size(24.dp)
                    )
                }
            }

            Icon(
                imageVector = if (expanded) Icons.Default.ExpandLess else Icons.Default.ExpandMore,
                contentDescription = if (expanded) "收起" else "展开",
                tint = MaterialTheme.colorScheme.secondary
            )
        }
    }
}

/**
 * 应用列表项组件
 *
 * @param app 应用信息
 * @param isSelected 是否选中
 * @param selectionMode 选择模式
 * @param onAppClick 点击回调
 */
@Composable
private fun AppItem(
    app: SimpleAppInfo,
    isSelected: Boolean,
    selectionMode: AppSelectionMode,
    onAppClick: (SimpleAppInfo) -> Unit
) {
    Surface(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onAppClick(app) },
        shape = RoundedCornerShape(12.dp),
        color = if (isSelected) {
            MaterialTheme.colorScheme.primaryContainer
        } else {
            MaterialTheme.colorScheme.surfaceContainerLow
        },

    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 应用图标（与AppRepository保持一致的显示方式）
            if (app.icon != null) {
                Image(
                    bitmap = ImageUtils.safeDrawableToBitmap(app.icon, 96, 96).asImageBitmap(),
                    contentDescription = "${app.appName}图标",
                    modifier = Modifier.size(48.dp),
                    contentScale = ContentScale.Fit
                )
            } else {
                // 图标加载失败时的占位符
                Box(
                    modifier = Modifier
                        .size(48.dp)
                        .background(
                            if (isSelected) {
                                MaterialTheme.colorScheme.primary
                            } else {
                                MaterialTheme.colorScheme.primary.copy(alpha = 0.1f)
                            },
                            CircleShape
                        ),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = app.appName.take(1).uppercase(),
                        style = MaterialTheme.typography.titleMedium,
                        color = if (isSelected) {
                            MaterialTheme.colorScheme.onPrimary
                        } else {
                            MaterialTheme.colorScheme.primary
                        }
                    )
                }
            }

            Spacer(modifier = Modifier.width(16.dp))

            // 应用信息
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = app.appName,
                    style = MaterialTheme.typography.titleMedium,
                    color = if (isSelected) {
                        MaterialTheme.colorScheme.onPrimaryContainer
                    } else {
                        MaterialTheme.colorScheme.onSurface
                    },
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = app.packageName,
                        style = MaterialTheme.typography.bodyMedium,
                        color = if (isSelected) {
                            MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.7f)
                        } else {
                            MaterialTheme.colorScheme.onSurfaceVariant
                        },
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                        modifier = Modifier.weight(1f)
                    )
                    if (app.isRunning) {
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = "正在运行",
                            style = MaterialTheme.typography.labelSmall,
                            color = if (isSelected) {
                                MaterialTheme.colorScheme.onPrimaryContainer
                            } else {
                                MaterialTheme.colorScheme.primary
                            },
                            modifier = Modifier
                                .background(
                                    if (isSelected) {
                                        MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.1f)
                                    } else {
                                        MaterialTheme.colorScheme.primaryContainer
                                    },
                                    RoundedCornerShape(4.dp)
                                )
                                .padding(horizontal = 6.dp, vertical = 2.dp)
                        )
                    }
                }
            }

            // 多选模式下显示选中状态指示器
            if (selectionMode == AppSelectionMode.MULTI) {
                Spacer(modifier = Modifier.width(8.dp))
                if (isSelected) {
                    Icon(
                        imageVector = Icons.Default.Check,
                        contentDescription = "已选中",
                        tint = MaterialTheme.colorScheme.primary,
                        modifier = Modifier.size(24.dp)
                    )
                }
            }
        }
    }
}
