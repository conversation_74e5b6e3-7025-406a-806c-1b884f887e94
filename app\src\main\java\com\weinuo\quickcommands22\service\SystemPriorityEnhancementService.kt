package com.weinuo.quickcommands22.service

import android.accessibilityservice.AccessibilityService
import android.accessibilityservice.AccessibilityServiceInfo
import android.view.accessibility.AccessibilityEvent

/**
 * 系统优先级提升无障碍服务
 *
 * 此服务专门用于提高应用在Android 14及以上系统中的后台存活率
 * 采用最小化资源消耗设计：
 * - 不监听任何无障碍事件（eventTypes = 0）
 * - 不设置任何标志（flags = 0）
 * - 不需要通知（notificationTimeout = 0）
 * - 空实现所有事件处理方法
 * - 不记录任何日志以节省电量
 * - 最小化配置以减少内存占用
 *
 * 注意：此服务仅用于系统优先级提升，不提供任何实际的无障碍功能
 * 如需实际无障碍功能，请创建独立的功能性无障碍服务
 */
class SystemPriorityEnhancementService : AccessibilityService() {

    companion object {
        // 服务实例（用于外部调用）
        @Volatile
        private var instance: SystemPriorityEnhancementService? = null

        fun getInstance(): SystemPriorityEnhancementService? = instance
    }

    override fun onServiceConnected() {
        super.onServiceConnected()
        instance = this
        // 配置服务 - 最小化资源消耗
        configureService()
    }

    override fun onAccessibilityEvent(event: AccessibilityEvent?) {
        // 空实现 - 不处理任何事件以节省CPU和电量
    }

    override fun onInterrupt() {
        // 空实现 - 节省电量
    }

    override fun onDestroy() {
        super.onDestroy()
        instance = null
        // 不记录日志以节省电量
    }

    /**
     * 配置无障碍服务 - 最小化资源消耗设计
     */
    private fun configureService() {
        val info = serviceInfo

        // 最小化资源消耗配置
        info.eventTypes = 0  // 不监听任何无障碍事件
        info.feedbackType = AccessibilityServiceInfo.FEEDBACK_GENERIC
        info.flags = 0  // 不设置任何标志
        info.notificationTimeout = 0  // 不需要通知

        serviceInfo = info
    }
}
