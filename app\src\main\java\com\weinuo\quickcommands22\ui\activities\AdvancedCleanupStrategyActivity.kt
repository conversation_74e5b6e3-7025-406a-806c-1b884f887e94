package com.weinuo.quickcommands22.ui.activities

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import com.weinuo.quickcommands22.data.SettingsRepository
import com.weinuo.quickcommands22.ui.screens.AdvancedCleanupStrategyActivityContent
import com.weinuo.quickcommands22.ui.theme.QuickCommandsTheme

/**
 * 高级清理策略配置界面Activity
 * 
 * 独立Activity，用于配置自定义清理策略，包括规则顺序、参数调整等。
 * 支持新建策略和编辑现有策略两种模式。
 */
class AdvancedCleanupStrategyActivity : ComponentActivity() {
    
    companion object {
        private const val EXTRA_STRATEGY_ID = "strategy_id"
        
        /**
         * 启动新建清理策略界面
         * 
         * @param context 上下文
         */
        fun startForCreate(context: Context) {
            val intent = Intent(context, AdvancedCleanupStrategyActivity::class.java).apply {
                putExtra(EXTRA_STRATEGY_ID, "new")
            }
            context.startActivity(intent)
        }
        
        /**
         * 启动编辑清理策略界面
         * 
         * @param context 上下文
         * @param strategyId 策略ID
         */
        fun startForEdit(context: Context, strategyId: String) {
            val intent = Intent(context, AdvancedCleanupStrategyActivity::class.java).apply {
                putExtra(EXTRA_STRATEGY_ID, strategyId)
            }
            context.startActivity(intent)
        }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 获取传递的参数
        val strategyId = intent.getStringExtra(EXTRA_STRATEGY_ID) ?: "new"
        val settingsRepository = SettingsRepository(this)
        
        val initialStrategy = if (strategyId != "new") {
            settingsRepository.getCustomCleanupStrategy(strategyId)
        } else {
            null
        }
        
        setContent {
            QuickCommandsTheme {
                AdvancedCleanupStrategyActivityContent(
                    initialStrategy = initialStrategy,
                    onStrategyConfigured = { strategy ->
                        // 保存策略并返回
                        settingsRepository.saveCustomCleanupStrategy(strategy)
                        finish()
                    },
                    onNavigateBack = { finish() },
                    onNavigateToAddCleanupRule = {
                        // 启动添加清理规则界面
                        AddCleanupRuleActivity.startForAdd(this)
                    }
                )
            }
        }
    }
}
