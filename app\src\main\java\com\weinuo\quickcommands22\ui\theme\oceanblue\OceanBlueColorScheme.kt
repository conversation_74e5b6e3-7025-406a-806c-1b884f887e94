package com.weinuo.quickcommands22.ui.theme.oceanblue

import androidx.compose.material3.ColorScheme
import androidx.compose.material3.lightColorScheme
import androidx.compose.ui.graphics.Color

/**
 * 海洋蓝主题颜色方案
 *
 * 基于现有颜色系统，实现分层设计风格的颜色配置
 * 特点：深度感强，层次分明，使用阴影表现深度
 */
object OceanBlueColorScheme {

    /**
     * 海洋蓝主题的主要颜色定义
     * 继承自现有的颜色系统，保持一致性
     */
    object Colors {
        // 主要颜色系统 - 深海蓝调
        val primary = Color(0xFF136682) // 深海蓝
        val onPrimary = Color(0xFFFFFFFF) // 白色
        val primaryContainer = Color(0xFFBEE9FF) // 浅蓝容器
        val onPrimaryContainer = Color(0xFF004D65) // 深蓝

        // 次要颜色系统 - 石板灰调
        val secondary = Color(0xFF4D616C) // 石板灰
        val onSecondary = Color(0xFFFFFFFF) // 白色
        val secondaryContainer = Color(0xFFD0E6F2) // 浅蓝灰容器
        val onSecondaryContainer = Color(0xFF354A54) // 深蓝灰

        // 第三颜色系统 - 柔和紫调
        val tertiary = Color(0xFF5D5B7D) // 柔和紫
        val onTertiary = Color(0xFFFFFFFF) // 白色
        val tertiaryContainer = Color(0xFFE3DFFF) // 浅薰衣草容器
        val onTertiaryContainer = Color(0xFF464364) // 深紫

        // 错误颜色系统
        val error = Color(0xFFBA1A1A) // 亮红色
        val onError = Color(0xFFFFFFFF) // 白色
        val errorContainer = Color(0xFFFDDAD6) // 浅粉色
        val onErrorContainer = Color(0xFF410002) // 深红色

        // 表面颜色系统 - 分层设计的核心
        val background = Color(0xFFFAFDFD) // 极浅蓝灰
        val onBackground = Color(0xFF191C1E) // 近黑色
        val surface = Color(0xFFFAFDFD) // 与背景相同
        val onSurface = Color(0xFF191C1E) // 近黑色
        val surfaceVariant = Color(0xFFDCE4E9) // 稍深变体
        val onSurfaceVariant = Color(0xFF40484C) // 深灰

        // 表面容器层次 - 分层设计的关键
        val surfaceDim = Color(0xFFD6DBDE) // 暗化表面
        val surfaceBright = Color(0xFFF6FAFE) // 明亮表面
        val surfaceContainerLowest = Color(0xFFFFFFFF) // 最低层 - 白色
        val surfaceContainerLow = Color(0xFFF0F4F8) // 低层 - 极浅蓝灰
        val surfaceContainer = Color(0xFFEAEEF2) // 中层 - 浅蓝灰
        val surfaceContainerHigh = Color(0xFFE4E9EC) // 高层 - 稍深蓝灰
        val surfaceContainerHighest = Color(0xFFDFE3E7) // 最高层 - 最深浅容器

        // 轮廓和分割线
        val outline = Color(0xFF70787D) // 中灰
        val outlineVariant = Color(0xFFC0C8CD) // 浅灰
        val scrim = Color(0xFF000000) // 黑色

        // 反色系统
        val inverseSurface = Color(0xFF2C3134) // 深色表面
        val inverseOnSurface = Color(0xFFEFF1F3) // 反色表面上的浅色文本
        val inversePrimary = Color(0xFF8CCFF0) // 深色背景上的浅蓝主色

        // 状态颜色 - 保持与现有系统一致
        val runningGreen = Color(0xFF146C2E) // 运行状态绿色
        val runningContainer = Color(0xFFE8F5E8) // 运行状态容器
        val onRunningContainer = Color(0xFF146C2E) // 运行状态容器上的文本

        // 导航颜色
        val unselectedIconGray = Color(0xFF70787D) // 未选中图标的中灰色
    }

    /**
     * 扩展颜色定义
     * 用于分层设计的特殊用途颜色
     */
    object ExtendedColors {
        // 阴影颜色 - 分层设计的核心
        val shadowLight = Color(0x1A000000) // 浅阴影 (10% 透明度)
        val shadowMedium = Color(0x33000000) // 中等阴影 (20% 透明度)
        val shadowDeep = Color(0x4D000000) // 深阴影 (30% 透明度)
        val shadowIntense = Color(0x66000000) // 强阴影 (40% 透明度)

        // 层次边框颜色
        val borderSubtle = Color(0x1A70787D) // 微妙边框
        val borderVisible = Color(0x4D70787D) // 可见边框
        val borderStrong = Color(0x8070787D) // 强边框

        // 交互状态颜色
        val hoverOverlay = Color(0x0D000000) // 悬停覆盖 (5% 透明度)
        val pressedOverlay = Color(0x1A000000) // 按压覆盖 (10% 透明度)
        val focusedOverlay = Color(0x1A136682) // 聚焦覆盖 (主色 10% 透明度)
        val selectedOverlay = Color(0x33136682) // 选中覆盖 (主色 20% 透明度)

        // 分层背景颜色
        val layerBase = Colors.surface // 基础层
        val layerElevated1 = Colors.surfaceContainerLow // 第一层提升
        val layerElevated2 = Colors.surfaceContainer // 第二层提升
        val layerElevated3 = Colors.surfaceContainerHigh // 第三层提升
        val layerElevated4 = Colors.surfaceContainerHighest // 第四层提升
    }

    /**
     * 创建海洋蓝主题的 ColorScheme
     */
    fun createColorScheme(): ColorScheme = lightColorScheme(
        // 主要颜色系统
        primary = Colors.primary,
        onPrimary = Colors.onPrimary,
        primaryContainer = Colors.primaryContainer,
        onPrimaryContainer = Colors.onPrimaryContainer,

        // 次要颜色系统
        secondary = Colors.secondary,
        onSecondary = Colors.onSecondary,
        secondaryContainer = Colors.secondaryContainer,
        onSecondaryContainer = Colors.onSecondaryContainer,

        // 第三颜色系统
        tertiary = Colors.tertiary,
        onTertiary = Colors.onTertiary,
        tertiaryContainer = Colors.tertiaryContainer,
        onTertiaryContainer = Colors.onTertiaryContainer,

        // 错误颜色系统
        error = Colors.error,
        onError = Colors.onError,
        errorContainer = Colors.errorContainer,
        onErrorContainer = Colors.onErrorContainer,

        // 表面颜色系统
        background = Colors.background,
        onBackground = Colors.onBackground,
        surface = Colors.surface,
        onSurface = Colors.onSurface,
        surfaceVariant = Colors.surfaceVariant,
        onSurfaceVariant = Colors.onSurfaceVariant,

        // 表面容器层次
        surfaceDim = Colors.surfaceDim,
        surfaceBright = Colors.surfaceBright,
        surfaceContainerLowest = Colors.surfaceContainerLowest,
        surfaceContainerLow = Colors.surfaceContainerLow,
        surfaceContainer = Colors.surfaceContainer,
        surfaceContainerHigh = Colors.surfaceContainerHigh,
        surfaceContainerHighest = Colors.surfaceContainerHighest,

        // 轮廓和其他
        outline = Colors.outline,
        outlineVariant = Colors.outlineVariant,
        scrim = Colors.scrim,
        inverseSurface = Colors.inverseSurface,
        inverseOnSurface = Colors.inverseOnSurface,
        inversePrimary = Colors.inversePrimary
    )
}
