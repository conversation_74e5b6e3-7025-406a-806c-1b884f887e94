package com.weinuo.quickcommands22.model

import android.util.Log
import java.util.UUID
import java.util.Calendar
import android.graphics.drawable.Drawable

/**
 * 简单应用信息数据类（用于触发条件配置）
 *
 * 注意：此类不实现Parcelable接口，以保持与项目的自定义序列化策略一致。
 * 在Compose状态保存中，应分别保存各个字段而不是直接保存此对象。
 */
data class SimpleAppInfo(
    val packageName: String,
    val appName: String,
    val isSystemApp: Boolean,
    val isRunning: Boolean = false,
    val icon: Drawable? = null
) {
    companion object {
        /**
         * 从字段重建SimpleAppInfo对象的辅助方法
         */
        fun fromFields(packageName: String, appName: String, isSystemApp: Boolean, isRunning: Boolean = false, icon: Drawable? = null): SimpleAppInfo? {
            return if (packageName.isNotEmpty()) {
                SimpleAppInfo(packageName, appName, isSystemApp, isRunning, icon)
            } else {
                null
            }
        }

        // 序列化方法已移除，改用原生数据类型存储
    }
}

/**
 * 共享触发条件列表
 *
 * 此文件定义了应用中可用的触发条件类型。
 * 新的触发条件类型应该在此文件中定义，并注册到 SharedTriggerConditionRegistry 中。
 */

/**
 * 时间条件重复模式
 */
enum class TimeRepeatMode(val displayName: String, val value: String) {
    ONCE("仅一次", "once"),
    DAILY("每天", "daily"),
    WEEKLY("每周", "weekly"),
    BI_WEEKLY("每两周", "bi_weekly"),
    MONTHLY("每月", "monthly"),
    YEARLY("每年", "yearly");

    companion object {
        fun fromValue(value: String): TimeRepeatMode {
            return values().find { it.value == value } ?: ONCE
        }
    }
}

/**
 * 定时触发重复模式
 */
enum class ScheduledRepeatMode(val displayName: String, val value: String) {
    ONCE("仅一次", "once"),
    DAILY("每天", "daily"),
    CUSTOM("自定义", "custom");

    companion object {
        fun fromValue(value: String): ScheduledRepeatMode {
            return values().find { it.value == value } ?: ONCE
        }
    }
}

/**
 * 星期几枚举
 */
enum class DayOfWeek(val displayName: String, val value: String, val calendarValue: Int) {
    MONDAY("周一", "monday", Calendar.MONDAY),
    TUESDAY("周二", "tuesday", Calendar.TUESDAY),
    WEDNESDAY("周三", "wednesday", Calendar.WEDNESDAY),
    THURSDAY("周四", "thursday", Calendar.THURSDAY),
    FRIDAY("周五", "friday", Calendar.FRIDAY),
    SATURDAY("周六", "saturday", Calendar.SATURDAY),
    SUNDAY("周日", "sunday", Calendar.SUNDAY);

    companion object {
        fun fromValue(value: String): DayOfWeek? {
            return values().find { it.value == value }
        }

        fun fromCalendarValue(calendarValue: Int): DayOfWeek? {
            return values().find { it.calendarValue == calendarValue }
        }
    }
}

/**
 * 时间间隔单位
 */
enum class TimeIntervalUnit(val displayName: String, val value: String, val milliseconds: Long) {
    SECONDS("秒", "seconds", 1000L),
    MINUTES("分钟", "minutes", 60 * 1000L),
    HOURS("小时", "hours", 60 * 60 * 1000L);

    companion object {
        fun fromValue(value: String): TimeIntervalUnit {
            return values().find { it.value == value } ?: SECONDS
        }
    }
}

/**
 * 时间条件类型枚举
 * 统一的时间条件类型，整合所有时间相关功能
 */
enum class TimeConditionType(val displayName: String, val value: String) {
    STOPWATCH("秒表", "stopwatch"),
    SUN_EVENT("日出日落", "sun_event"),
    SCHEDULED_TIME("日程时间", "scheduled_time"),
    PERIODIC_TIME("周期时间", "periodic_time"),
    DELAYED_TRIGGER("延迟触发", "delayed_trigger"),
    PERIODIC_TRIGGER("周期触发", "periodic_trigger");

    companion object {
        fun fromValue(value: String): TimeConditionType {
            return values().find { it.value == value } ?: STOPWATCH
        }
    }
}

/**
 * 日出日落事件类型
 */
enum class SunEventType(val displayName: String, val value: String) {
    SUNRISE("日出", "sunrise"),
    SUNSET("日落", "sunset");

    companion object {
        fun fromValue(value: String): SunEventType {
            return values().find { it.value == value } ?: SUNRISE
        }
    }
}

/**
 * 手动触发类型枚举
 * 支持十二种手动触发方式
 */
enum class ManualTriggerType(val displayName: String, val value: String) {
    DYNAMIC_SHORTCUT("动态快捷方式", "dynamic_shortcut"),
    STATIC_SHORTCUT("静态快捷方式", "static_shortcut"),
    DESKTOP_WIDGET("桌面小组件", "desktop_widget"),
    FLOATING_BUTTON("悬浮按钮", "floating_button"),
    FINGERPRINT_GESTURE("指纹手势", "fingerprint_gesture"),
    HOME_BUTTON_LONG_PRESS("主屏幕按钮长按", "home_button_long_press"),
    MEDIA_KEY_PRESS("媒体键按下", "media_key_press"),
    SHORTCUT_OPENING("快捷方式打开", "shortcut_opening"),
    SCREEN_SWIPE("滑动屏幕", "screen_swipe"),
    VOLUME_BUTTON_LONG_PRESS("音量按钮长按", "volume_button_long_press"),
    VOLUME_KEY_PRESS("音量键按下", "volume_key_press"),
    POWER_BUTTON_LONG_PRESS("电源键长按", "power_button_long_press");

    companion object {
        fun fromValue(value: String): ManualTriggerType {
            return values().find { it.value == value } ?: DYNAMIC_SHORTCUT
        }
    }
}

/**
 * 指纹手势类型枚举
 * 支持常见的指纹手势操作
 */
enum class FingerprintGestureType(val displayName: String, val value: String) {
    SWIPE_UP("向上滑动", "swipe_up"),
    SWIPE_DOWN("向下滑动", "swipe_down"),
    SWIPE_LEFT("向左滑动", "swipe_left"),
    SWIPE_RIGHT("向右滑动", "swipe_right"),
    TAP("轻触", "tap"),
    LONG_PRESS("长按", "long_press");

    companion object {
        fun fromValue(value: String): FingerprintGestureType {
            return values().find { it.value == value } ?: TAP
        }
    }
}

/**
 * 媒体键类型枚举
 * 支持常见的媒体键操作
 */
enum class MediaKeyType(val displayName: String, val value: String) {
    PLAY_PAUSE("播放/暂停", "play_pause"),
    NEXT_TRACK("下一首", "next_track"),
    PREVIOUS_TRACK("上一首", "previous_track"),
    VOLUME_UP("音量加", "volume_up"),
    VOLUME_DOWN("音量减", "volume_down");

    companion object {
        fun fromValue(value: String): MediaKeyType {
            return values().find { it.value == value } ?: PLAY_PAUSE
        }
    }
}

/**
 * 滑动屏幕开始区域枚举
 * 定义滑动手势的起始位置
 */
enum class SwipeStartCorner(val displayName: String, val value: String) {
    TOP_LEFT("左上角", "top_left"),
    TOP_RIGHT("右上角", "top_right");

    companion object {
        fun fromValue(value: String): SwipeStartCorner {
            return values().find { it.value == value } ?: TOP_LEFT
        }
    }
}

/**
 * 滑动屏幕方向枚举
 * 定义滑动手势的方向
 */
enum class SwipeDirection(val displayName: String, val value: String) {
    HORIZONTAL("横过", "horizontal"),
    DIAGONAL("对角线", "diagonal"),
    DOWN("下", "down");

    companion object {
        fun fromValue(value: String): SwipeDirection {
            return values().find { it.value == value } ?: HORIZONTAL
        }
    }
}

/**
 * 音量按钮类型枚举
 * 定义音量按钮的类型
 */
enum class VolumeButtonType(val displayName: String, val value: String) {
    VOLUME_UP("音量加", "volume_up"),
    VOLUME_DOWN("音量减", "volume_down"),
    BOTH("音量加+音量减", "both");

    companion object {
        fun fromValue(value: String): VolumeButtonType {
            return values().find { it.value == value } ?: VOLUME_UP
        }
    }
}

/**
 * 触发条件接口
 * 所有触发条件类型都应该实现此接口
 */
interface SharedTriggerCondition {
    val id: String
    val type: String
    val displayName: String

    /**
     * 返回触发条件的简短描述，用于在条件列表中显示
     */
    fun getDescription(): String

    /**
     * 检查当前条件是否与触发条件匹配
     * 用于在条件触发时判断是否应该执行对应的指令
     *
     * @param triggerCondition 触发的条件实例
     * @return 是否匹配
     */
    fun matches(triggerCondition: SharedTriggerCondition): Boolean {
        // 默认实现：只检查类型是否相同
        return this.type == triggerCondition.type
    }
}



/**
 * 电池条件主类型枚举
 * 按功能域分组的层次化设计
 */
enum class BatteryConditionType {
    BATTERY_LEVEL,        // 电池电量
    CHARGING_STATE,       // 充电状态
    BATTERY_TEMPERATURE,  // 电池温度
    POWER_BUTTON,         // 电源键
    POWER_SAVE_MODE       // 省电模式
}

/**
 * 电池电量子类型枚举
 */
enum class BatteryLevelSubType {
    BELOW,        // 低于阈值
    ABOVE,        // 高于阈值
    CHANGED,      // 变化超过阈值
    LOW_WARNING   // 低电量警告(15%以下)
}

/**
 * 充电状态子类型枚举
 */
enum class ChargingSubType {
    STARTED,       // 开始充电
    STOPPED,       // 停止充电
    FULLY_CHARGED  // 充电完成
}

/**
 * 电池温度子类型枚举
 */
enum class TemperatureSubType {
    INCREASED,  // 温度增加
    DECREASED,  // 温度减少
    CHANGED     // 温度变化
}

/**
 * 电源键子类型枚举
 */
enum class PowerButtonSubType {
    PRESSED  // 按下指定次数
}

/**
 * 省电模式子类型枚举
 */
enum class PowerSaveModeSubType {
    ENABLED,   // 启用
    DISABLED   // 禁用
}

/**
 * 重新设计的电池状态条件
 * 采用层次化设计：主类型 + 子类型 + 参数分离
 */
data class BatteryStateCondition(
    override val id: String = UUID.randomUUID().toString(),
    val conditionType: BatteryConditionType = BatteryConditionType.BATTERY_LEVEL,

    // 电池电量相关参数
    val levelSubType: BatteryLevelSubType = BatteryLevelSubType.BELOW,
    val levelThreshold: Int = 20,        // 电量阈值 (0-100)
    val levelChangeThreshold: Int = 5,   // 电量变化阈值 (1-50)

    // 充电状态相关参数
    val chargingSubType: ChargingSubType = ChargingSubType.STARTED,

    // 电池温度相关参数
    val temperatureSubType: TemperatureSubType = TemperatureSubType.CHANGED,
    val temperatureThreshold: Float = 2.0f,  // 温度变化阈值(摄氏度)

    // 电源键相关参数
    val powerButtonSubType: PowerButtonSubType = PowerButtonSubType.PRESSED,
    val powerButtonPressCount: Int = 1,      // 按下次数 (1-10)

    // 省电模式相关参数
    val powerSaveModeSubType: PowerSaveModeSubType = PowerSaveModeSubType.ENABLED
) : SharedTriggerCondition {
    override val type: String = "battery_state"
    override val displayName: String = "电池状态"

    override fun getDescription(): String {
        return when (conditionType) {
            BatteryConditionType.BATTERY_LEVEL -> {
                when (levelSubType) {
                    BatteryLevelSubType.BELOW -> "当电池电量低于 $levelThreshold% 时"
                    BatteryLevelSubType.ABOVE -> "当电池电量高于 $levelThreshold% 时"
                    BatteryLevelSubType.CHANGED -> "当电池电量变化超过 $levelChangeThreshold% 时"
                    BatteryLevelSubType.LOW_WARNING -> "当电池电量过低时"
                }
            }
            BatteryConditionType.CHARGING_STATE -> {
                when (chargingSubType) {
                    ChargingSubType.STARTED -> "当设备开始充电时"
                    ChargingSubType.STOPPED -> "当设备停止充电时"
                    ChargingSubType.FULLY_CHARGED -> "当设备充电完成时"
                }
            }
            BatteryConditionType.BATTERY_TEMPERATURE -> {
                when (temperatureSubType) {
                    TemperatureSubType.INCREASED -> "当电池温度上升超过 ${temperatureThreshold}°C 时"
                    TemperatureSubType.DECREASED -> "当电池温度下降超过 ${temperatureThreshold}°C 时"
                    TemperatureSubType.CHANGED -> "当电池温度变化超过 ${temperatureThreshold}°C 时"
                }
            }
            BatteryConditionType.POWER_BUTTON -> {
                when (powerButtonSubType) {
                    PowerButtonSubType.PRESSED -> "当电源键连续按下 $powerButtonPressCount 次时"
                }
            }
            BatteryConditionType.POWER_SAVE_MODE -> {
                when (powerSaveModeSubType) {
                    PowerSaveModeSubType.ENABLED -> "当省电模式启用时"
                    PowerSaveModeSubType.DISABLED -> "当省电模式禁用时"
                }
            }
        }
    }

    override fun matches(triggerCondition: SharedTriggerCondition): Boolean {
        return triggerCondition is BatteryStateCondition &&
               triggerCondition.conditionType == this.conditionType &&
               when (conditionType) {
                   BatteryConditionType.BATTERY_LEVEL -> {
                       triggerCondition.levelSubType == this.levelSubType &&
                       triggerCondition.levelThreshold == this.levelThreshold &&
                       triggerCondition.levelChangeThreshold == this.levelChangeThreshold
                   }
                   BatteryConditionType.CHARGING_STATE -> {
                       triggerCondition.chargingSubType == this.chargingSubType
                   }
                   BatteryConditionType.BATTERY_TEMPERATURE -> {
                       triggerCondition.temperatureSubType == this.temperatureSubType &&
                       triggerCondition.temperatureThreshold == this.temperatureThreshold
                   }
                   BatteryConditionType.POWER_BUTTON -> {
                       triggerCondition.powerButtonSubType == this.powerButtonSubType &&
                       triggerCondition.powerButtonPressCount == this.powerButtonPressCount
                   }
                   BatteryConditionType.POWER_SAVE_MODE -> {
                       triggerCondition.powerSaveModeSubType == this.powerSaveModeSubType
                   }
               }
    }
}











/**
 * 连接类型枚举
 * 定义所有支持的连接类型，按功能逻辑分组
 */
enum class ConnectionType {
    // 网络连接类型
    WIFI_STATE,        // WiFi连接状态（开启/关闭/连接/断开）
    WIFI_NETWORK,      // WiFi网络切换（特定SSID）
    MOBILE_DATA,       // 移动数据连接状态
    NETWORK_GENERAL,   // 通用网络连接状态
    VPN_STATE,         // VPN连接状态
    ROAMING_STATE,     // 数据漫游状态
    HOTSPOT_STATE,     // 个人热点状态

    // 设备连接类型
    BLUETOOTH_STATE,   // 蓝牙连接状态
    BLUETOOTH_DEVICE,  // 特定蓝牙设备连接
    HEADPHONE,         // 耳机连接状态
    USB_DEVICE,        // USB设备状态

    // 其他连接类型
    IP_ADDRESS,        // IP地址变化
    CELL_TOWER,        // 基站连接状态
    MOBILE_SIGNAL      // 手机信号状态
}

/**
 * 连接子类型枚举
 * 定义每种连接类型的具体状态，按逻辑分组并统一命名规范
 */
enum class ConnectionSubType {
    // WiFi状态子类型（WIFI_STATE）
    WIFI_ENABLED,           // WiFi已开启
    WIFI_DISABLED,          // WiFi已关闭
    WIFI_CONNECTED,         // WiFi已连接
    WIFI_DISCONNECTED,      // WiFi已断开

    // WiFi网络子类型（WIFI_NETWORK）
    WIFI_NETWORK_CHANGED,      // WiFi网络已切换
    WIFI_NETWORK_CONNECTED,    // 连接到指定WiFi网络
    WIFI_NETWORK_DISCONNECTED, // 从指定WiFi网络断开

    // 移动数据子类型（MOBILE_DATA）
    MOBILE_DATA_ENABLED,    // 移动数据已开启
    MOBILE_DATA_DISABLED,   // 移动数据已关闭
    MOBILE_DATA_CONNECTED,  // 移动数据已连接
    MOBILE_DATA_DISCONNECTED, // 移动数据已断开

    // 通用网络子类型（NETWORK_GENERAL）
    NETWORK_CONNECTED,      // 网络已连接（WiFi或移动数据）
    NETWORK_DISCONNECTED,   // 网络已断开

    // VPN连接状态
    VPN_CONNECTED,          // VPN已连接
    VPN_DISCONNECTED,       // VPN已断开

    // 数据漫游状态
    ROAMING_ENABLED,        // 数据漫游已开启
    ROAMING_DISABLED,       // 数据漫游已关闭

    // 个人热点状态
    HOTSPOT_ENABLED,        // 个人热点已开启
    HOTSPOT_DISABLED,       // 个人热点已关闭

    // 蓝牙状态子类型（BLUETOOTH_STATE）
    BLUETOOTH_ENABLED,      // 蓝牙已开启
    BLUETOOTH_DISABLED,     // 蓝牙已关闭
    BLUETOOTH_CONNECTED,    // 蓝牙已连接（任何设备）
    BLUETOOTH_DISCONNECTED, // 蓝牙已断开

    // 特定蓝牙设备子类型（BLUETOOTH_DEVICE）
    BLUETOOTH_DEVICE_CONNECTED,    // 特定蓝牙设备已连接
    BLUETOOTH_DEVICE_DISCONNECTED, // 特定蓝牙设备已断开

    // 耳机连接状态
    HEADPHONE_CONNECTED,    // 耳机已连接
    HEADPHONE_DISCONNECTED, // 耳机已断开

    // USB设备状态
    USB_DEVICE_CONNECTED,   // USB设备已连接
    USB_DEVICE_DISCONNECTED, // USB设备已断开



    // IP地址变化
    IP_ADDRESS_CHANGED,     // IP地址已变化
    IP_ADDRESS_OBTAINED,    // 获得IP地址
    IP_ADDRESS_LOST,        // 失去IP地址

    // 基站连接状态
    CELL_TOWER_ENTERED,     // 进入基站区域
    CELL_TOWER_EXITED,      // 离开基站区域
    CELL_TOWER_CHANGED,     // 基站改变

    // 手机信号状态
    MOBILE_SIGNAL_AVAILABLE,   // 手机信号可用
    MOBILE_SIGNAL_UNAVAILABLE  // 手机信号不可用
}

/**
 * 连接状态条件
 * 统一的连接状态检测功能，整合所有网络、设备、蓝牙等连接相关条件
 *
 * @property id 条件唯一标识
 * @property connectionType 连接类型
 * @property subType 具体状态类型
 * @property specificValue 特定值（如SSID名称、设备名称等）
 * @property deviceAddress 设备地址（如蓝牙设备地址）
 */
data class ConnectionStateCondition(
    override val id: String = UUID.randomUUID().toString(),
    val connectionType: ConnectionType = ConnectionType.NETWORK_GENERAL,
    val subType: ConnectionSubType = ConnectionSubType.NETWORK_CONNECTED,
    val specificValue: String = "", // 特定值（如SSID名称、设备名称等）
    val deviceAddress: String = ""   // 设备地址（如蓝牙设备地址）
) : SharedTriggerCondition {
    override val type: String = "connection_state"
    override val displayName: String = "连接状态"

    override fun getDescription(): String {
        return when (connectionType) {
            ConnectionType.WIFI_STATE -> when (subType) {
                ConnectionSubType.WIFI_ENABLED -> "当WiFi开启时"
                ConnectionSubType.WIFI_DISABLED -> "当WiFi关闭时"
                ConnectionSubType.WIFI_CONNECTED -> "当WiFi连接时"
                ConnectionSubType.WIFI_DISCONNECTED -> "当WiFi断开时"
                else -> "当WiFi状态变化时"
            }
            ConnectionType.WIFI_NETWORK -> when (subType) {
                ConnectionSubType.WIFI_NETWORK_CHANGED -> "当WiFi网络切换时"
                ConnectionSubType.WIFI_NETWORK_CONNECTED -> if (specificValue.isNotEmpty()) "当连接到WiFi网络 $specificValue 时" else "当连接到指定WiFi网络时"
                ConnectionSubType.WIFI_NETWORK_DISCONNECTED -> if (specificValue.isNotEmpty()) "当从WiFi网络 $specificValue 断开时" else "当从指定WiFi网络断开时"
                else -> "当WiFi网络状态变化时"
            }
            ConnectionType.MOBILE_DATA -> when (subType) {
                ConnectionSubType.MOBILE_DATA_ENABLED -> "当移动数据开启时"
                ConnectionSubType.MOBILE_DATA_DISABLED -> "当移动数据关闭时"
                ConnectionSubType.MOBILE_DATA_CONNECTED -> "当移动数据连接时"
                ConnectionSubType.MOBILE_DATA_DISCONNECTED -> "当移动数据断开时"
                else -> "当移动数据状态变化时"
            }
            ConnectionType.NETWORK_GENERAL -> when (subType) {
                ConnectionSubType.NETWORK_CONNECTED -> "当网络连接时"
                ConnectionSubType.NETWORK_DISCONNECTED -> "当网络断开时"
                else -> "当网络连接状态变化时"
            }
            ConnectionType.VPN_STATE -> when (subType) {
                ConnectionSubType.VPN_CONNECTED -> "当VPN连接时"
                ConnectionSubType.VPN_DISCONNECTED -> "当VPN断开时"
                else -> "当VPN连接状态变化时"
            }
            ConnectionType.ROAMING_STATE -> when (subType) {
                ConnectionSubType.ROAMING_ENABLED -> "当数据漫游开启时"
                ConnectionSubType.ROAMING_DISABLED -> "当数据漫游关闭时"
                else -> "当数据漫游状态变化时"
            }
            ConnectionType.HOTSPOT_STATE -> when (subType) {
                ConnectionSubType.HOTSPOT_ENABLED -> "当个人热点开启时"
                ConnectionSubType.HOTSPOT_DISABLED -> "当个人热点关闭时"
                else -> "当个人热点状态变化时"
            }
            ConnectionType.BLUETOOTH_STATE -> when (subType) {
                ConnectionSubType.BLUETOOTH_ENABLED -> "当蓝牙开启时"
                ConnectionSubType.BLUETOOTH_DISABLED -> "当蓝牙关闭时"
                ConnectionSubType.BLUETOOTH_CONNECTED -> "当蓝牙连接时"
                ConnectionSubType.BLUETOOTH_DISCONNECTED -> "当蓝牙断开时"
                else -> "当蓝牙状态变化时"
            }
            ConnectionType.BLUETOOTH_DEVICE -> when (subType) {
                ConnectionSubType.BLUETOOTH_DEVICE_CONNECTED -> if (specificValue.isNotEmpty()) "当蓝牙设备 $specificValue 连接时" else "当特定蓝牙设备连接时"
                ConnectionSubType.BLUETOOTH_DEVICE_DISCONNECTED -> if (specificValue.isNotEmpty()) "当蓝牙设备 $specificValue 断开时" else "当特定蓝牙设备断开时"
                else -> "当特定蓝牙设备状态变化时"
            }
            ConnectionType.HEADPHONE -> when (subType) {
                ConnectionSubType.HEADPHONE_CONNECTED -> "当耳机连接时"
                ConnectionSubType.HEADPHONE_DISCONNECTED -> "当耳机断开时"
                else -> "当耳机连接状态变化时"
            }
            ConnectionType.USB_DEVICE -> when (subType) {
                ConnectionSubType.USB_DEVICE_CONNECTED -> "当USB设备连接时"
                ConnectionSubType.USB_DEVICE_DISCONNECTED -> "当USB设备断开时"
                else -> "当USB设备状态变化时"
            }

            ConnectionType.IP_ADDRESS -> when (subType) {
                ConnectionSubType.IP_ADDRESS_CHANGED -> "当IP地址变化时"
                ConnectionSubType.IP_ADDRESS_OBTAINED -> "当获得IP地址时"
                ConnectionSubType.IP_ADDRESS_LOST -> "当失去IP地址时"
                else -> "当IP地址状态变化时"
            }
            ConnectionType.CELL_TOWER -> when (subType) {
                ConnectionSubType.CELL_TOWER_ENTERED -> if (specificValue.isNotEmpty()) "当进入基站区域 $specificValue 时" else "当进入基站区域时"
                ConnectionSubType.CELL_TOWER_EXITED -> if (specificValue.isNotEmpty()) "当离开基站区域 $specificValue 时" else "当离开基站区域时"
                ConnectionSubType.CELL_TOWER_CHANGED -> "当基站改变时"
                else -> "当基站连接状态变化时"
            }
            ConnectionType.MOBILE_SIGNAL -> when (subType) {
                ConnectionSubType.MOBILE_SIGNAL_AVAILABLE -> "当手机信号可用时"
                ConnectionSubType.MOBILE_SIGNAL_UNAVAILABLE -> "当手机信号不可用时"
                else -> "当手机信号状态变化时"
            }
        }
    }

    override fun matches(triggerCondition: SharedTriggerCondition): Boolean {
        return triggerCondition is ConnectionStateCondition &&
               triggerCondition.connectionType == this.connectionType &&
               triggerCondition.subType == this.subType &&
               triggerCondition.specificValue == this.specificValue &&
               triggerCondition.deviceAddress == this.deviceAddress
    }
}









/**
 * 传感器状态类型枚举
 */
enum class SensorStateType {
    LIGHT_SENSOR,        // 光线传感器
    ORIENTATION_SENSOR,  // 屏幕方向传感器
    SHAKE_SENSOR,        // 摇晃检测
    SLEEP_SENSOR,        // 睡眠检测
    FLIP_SENSOR,         // 设备翻转检测
    PROXIMITY_SENSOR,    // 距离传感器
    ACTIVITY_RECOGNITION // 运动识别
}

/**
 * 设备事件类型枚举
 * 统一的设备级系统事件检测类型，整合17种设备事件监控功能
 */
enum class DeviceEventType(val displayName: String, val value: String) {
    GPS_STATE("GPS状态", "gps_state"),
    LOGCAT_MESSAGE("Logcat消息", "logcat_message"),
    CLIPBOARD_CHANGED("剪贴板变化", "clipboard_changed"),
    SCREEN_STATE("屏幕状态", "screen_state"),
    DOCK_STATE("底座连接", "dock_state"),
    INTENT_RECEIVED("Intent接收", "intent_received"),
    SIM_CARD_STATE("SIM卡状态", "sim_card_state"),
    DARK_THEME_CHANGED("深色主题", "dark_theme_changed"),
    LOGIN_ATTEMPT_FAILED("登录失败", "login_attempt_failed"),
    SYSTEM_SETTING_CHANGED("系统设置", "system_setting_changed"),
    AUTO_SYNC_STATE("自动同步", "auto_sync_state"),
    DEVICE_BOOT_COMPLETED("设备启动", "device_boot_completed"),
    NOTIFICATION_EVENT("通知事件", "notification_event"),
    RINGER_MODE_CHANGED("静音模式", "ringer_mode_changed"),
    MUSIC_PLAYBACK_STATE("音乐播放", "music_playback_state"),
    AIRPLANE_MODE_STATE("飞行模式", "airplane_mode_state"),
    VOLUME_CHANGED("音量变化", "volume_changed"),
    MEMORY_STATE("内存状态", "memory_state");

    companion object {
        fun fromValue(value: String): DeviceEventType {
            return values().find { it.value == value } ?: GPS_STATE
        }
    }
}

/**
 * GPS状态类型枚举
 */
enum class GpsStateType(val displayName: String, val value: String) {
    ENABLED("GPS已启用", "enabled"),
    DISABLED("GPS已禁用", "disabled");

    companion object {
        fun fromValue(value: String): GpsStateType {
            return values().find { it.value == value } ?: ENABLED
        }
    }
}

/**
 * Logcat缓冲区类型枚举
 */
enum class LogcatBufferType(val displayName: String, val value: String) {
    MAIN("主要", "main"),
    SYSTEM("系统", "system"),
    CRASH("崩溃", "crash"),
    KERNEL("内核", "kernel"),
    RADIO("Radio", "radio"),
    EVENTS("事件", "events");

    companion object {
        fun fromValue(value: String): LogcatBufferType {
            return values().find { it.value == value } ?: MAIN
        }
    }
}

/**
 * 屏幕事件类型枚举
 */
enum class ScreenEventType(val displayName: String, val value: String) {
    UNLOCKED("屏幕解锁", "unlocked"),
    ON("屏幕开启", "on"),
    OFF("屏幕关闭", "off"),
    AUTO_ROTATE_ENABLED("自动旋转启用", "auto_rotate_enabled"),
    AUTO_ROTATE_DISABLED("自动旋转禁用", "auto_rotate_disabled");

    companion object {
        fun fromValue(value: String): ScreenEventType {
            return values().find { it.value == value } ?: ON
        }
    }
}

/**
 * 底座状态类型枚举
 */
enum class DockStateType(val displayName: String, val value: String) {
    ANY_DOCK("任何底座", "any_dock"),
    DESK_DOCK("桌面底座", "desk_dock"),
    CAR_DOCK("汽车底座", "car_dock"),
    UNDOCKED("断开底座", "undocked");

    companion object {
        fun fromValue(value: String): DockStateType {
            return values().find { it.value == value } ?: ANY_DOCK
        }
    }
}

/**
 * SIM卡状态类型枚举
 */
enum class SimCardStateType(val displayName: String, val value: String) {
    INSERTED("SIM卡插入", "inserted"),
    REMOVED("SIM卡移除", "removed"),
    STATE_CHANGED("状态变化", "state_changed");

    companion object {
        fun fromValue(value: String): SimCardStateType {
            return values().find { it.value == value } ?: STATE_CHANGED
        }
    }
}

/**
 * 深色主题状态类型枚举
 */
enum class DarkThemeStateType(val displayName: String, val value: String) {
    ENABLED("深色主题启用", "enabled"),
    DISABLED("深色主题禁用", "disabled");

    companion object {
        fun fromValue(value: String): DarkThemeStateType {
            return values().find { it.value == value } ?: ENABLED
        }
    }
}

/**
 * 系统设置类型枚举
 */
enum class SystemSettingType(val displayName: String, val value: String) {
    SYSTEM("System设置", "system"),
    SECURE("Secure设置", "secure"),
    GLOBAL("Global设置", "global");

    companion object {
        fun fromValue(value: String): SystemSettingType {
            return values().find { it.value == value } ?: SYSTEM
        }
    }
}

/**
 * 自动同步状态类型枚举
 */
enum class AutoSyncStateType(val displayName: String, val value: String) {
    ENABLED("自动同步启用", "enabled"),
    DISABLED("自动同步禁用", "disabled");

    companion object {
        fun fromValue(value: String): AutoSyncStateType {
            return values().find { it.value == value } ?: ENABLED
        }
    }
}

/**
 * 通知事件类型枚举
 */
enum class NotificationEventType(val displayName: String, val value: String) {
    RECEIVED("收到通知", "received"),
    REMOVED("通知移除", "removed");

    companion object {
        fun fromValue(value: String): NotificationEventType {
            return values().find { it.value == value } ?: RECEIVED
        }
    }
}

/**
 * 通知应用选择模式枚举
 */
enum class NotificationAppSelectionMode(val displayName: String, val value: String) {
    ANY("任何应用", "any"),
    SELECTED("选择应用", "selected");

    companion object {
        fun fromValue(value: String): NotificationAppSelectionMode {
            return values().find { it.value == value } ?: ANY
        }
    }
}

/**
 * 通知应用包括模式枚举
 */
enum class NotificationAppIncludeMode(val displayName: String, val value: String) {
    INCLUDE("包括", "include"),
    EXCLUDE("排除", "exclude");

    companion object {
        fun fromValue(value: String): NotificationAppIncludeMode {
            return values().find { it.value == value } ?: INCLUDE
        }
    }
}

/**
 * 通知内容匹配模式枚举
 */
enum class NotificationContentMatchMode(val displayName: String, val value: String) {
    ANY("任何", "any"),
    MATCH("匹配", "match"),
    CONTAINS("包括", "contains"),
    NOT_CONTAINS("不包括", "not_contains");

    companion object {
        fun fromValue(value: String): NotificationContentMatchMode {
            return values().find { it.value == value } ?: ANY
        }
    }
}

/**
 * 通知声音模式枚举
 */
enum class NotificationSoundMode(val displayName: String, val value: String) {
    ANY("任何", "any"),
    WITH_SOUND("有声音", "with_sound"),
    WITHOUT_SOUND("无声音", "without_sound");

    companion object {
        fun fromValue(value: String): NotificationSoundMode {
            return values().find { it.value == value } ?: ANY
        }
    }
}

/**
 * 铃声模式类型枚举
 */
enum class RingerModeType(val displayName: String, val value: String) {
    NORMAL("正常模式", "normal"),
    VIBRATE("仅振动", "vibrate"),
    SILENT("完全静音", "silent");

    companion object {
        fun fromValue(value: String): RingerModeType {
            return values().find { it.value == value } ?: NORMAL
        }
    }
}

/**
 * 音乐播放状态类型枚举
 */
enum class MusicPlaybackType(val displayName: String, val value: String) {
    STARTED("开始播放", "started"),
    STOPPED("停止播放", "stopped");

    companion object {
        fun fromValue(value: String): MusicPlaybackType {
            return values().find { it.value == value } ?: STARTED
        }
    }
}

/**
 * 飞行模式状态类型枚举
 */
enum class AirplaneModeType(val displayName: String, val value: String) {
    ENABLED("飞行模式启用", "enabled"),
    DISABLED("飞行模式禁用", "disabled");

    companion object {
        fun fromValue(value: String): AirplaneModeType {
            return values().find { it.value == value } ?: ENABLED
        }
    }
}



/**
 * 内存状态类型枚举
 */
enum class MemoryStateType(val displayName: String, val value: String) {
    BELOW_THRESHOLD("内存低于阈值", "below_threshold"),
    ABOVE_THRESHOLD("内存高于阈值", "above_threshold"),
    CHANGED("内存变化", "changed");

    companion object {
        fun fromValue(value: String): MemoryStateType {
            return values().find { it.value == value } ?: BELOW_THRESHOLD
        }
    }
}

/**
 * 光线阈值类型枚举
 */
enum class LightThresholdType {
    DECREASE_TO,  // 减少到
    INCREASE_TO   // 增加到
}

/**
 * 屏幕方向类型枚举
 */
enum class OrientationType {
    PORTRAIT,   // 竖屏
    LANDSCAPE,  // 横屏
    FACE_UP,    // 朝上
    FACE_DOWN   // 朝下
}

/**
 * 敏感度级别枚举
 */
enum class SensitivityLevel {
    LOW,     // 低
    MEDIUM,  // 中
    HIGH     // 高
}

/**
 * 睡眠状态类型枚举
 */
enum class SleepStateType {
    FALL_ASLEEP,  // 入睡
    WAKE_UP       // 醒来
}

/**
 * 翻转类型枚举
 */
enum class FlipType {
    FACE_UP_TO_DOWN,   // 朝上到朝下
    FACE_DOWN_TO_UP,   // 朝下到朝上
    ANY_TO_FACE_DOWN   // 任何到朝下
}

/**
 * 距离类型枚举
 */
enum class ProximityType {
    NEAR,       // 接近
    FAR,        // 远离
    SLOW_WAVE,  // 慢挥手
    FAST_WAVE   // 快挥手
}

/**
 * 运动类型枚举
 */
enum class ActivityType {
    DRIVING,   // 开车
    CYCLING,   // 骑自行车
    RUNNING,   // 跑步
    WALKING,   // 步行
    STILL      // 静止
}

/**
 * 统一的设备事件条件
 * 整合17种设备级系统事件检测功能，支持可扩展的事件类型管理
 *
 * @property id 条件唯一标识
 * @property eventType 设备事件类型
 * @property gpsStateType GPS状态类型（仅GPS_STATE事件使用）
 * @property logcatComponent Logcat组件名称（仅LOGCAT_MESSAGE事件使用）
 * @property logcatText Logcat匹配文字（仅LOGCAT_MESSAGE事件使用）
 * @property logcatBufferTypes Logcat缓冲区类型列表（仅LOGCAT_MESSAGE事件使用，支持多选）
 * @property logcatCaseSensitive Logcat大小写敏感（仅LOGCAT_MESSAGE事件使用）
 * @property clipboardText 剪贴板匹配文字（仅CLIPBOARD_CHANGED事件使用）
 * @property clipboardUseRegex 剪贴板使用正则表达式（仅CLIPBOARD_CHANGED事件使用）
 * @property clipboardCaseSensitive 剪贴板大小写敏感（仅CLIPBOARD_CHANGED事件使用）
 * @property screenEventType 屏幕事件类型（仅SCREEN_STATE事件使用）
 * @property dockStateType 底座状态类型（仅DOCK_STATE事件使用）
 * @property intentAction Intent动作（仅INTENT_RECEIVED事件使用）
 * @property intentExtraKey Intent Extra键（仅INTENT_RECEIVED事件使用）
 * @property intentExtraValue Intent Extra值（仅INTENT_RECEIVED事件使用）
 * @property simCardStateType SIM卡状态类型（仅SIM_CARD_STATE事件使用）
 * @property darkThemeStateType 深色主题状态类型（仅DARK_THEME_CHANGED事件使用）
 * @property loginFailureThreshold 登录失败次数阈值（仅LOGIN_ATTEMPT_FAILED事件使用）
 * @property systemSettingTypes 系统设置类型列表（仅SYSTEM_SETTING_CHANGED事件使用，支持多选）
 * @property systemSettingKey 系统设置键（仅SYSTEM_SETTING_CHANGED事件使用）
 * @property systemSettingValue 系统设置值（仅SYSTEM_SETTING_CHANGED事件使用）
 * @property systemSettingUseRegex 系统设置使用正则表达式（仅SYSTEM_SETTING_CHANGED事件使用）
 * @property autoSyncStateType 自动同步状态类型（仅AUTO_SYNC_STATE事件使用）
 * @property notificationEventType 通知事件类型（仅NOTIFICATION_EVENT事件使用）
 * @property notificationAppSelectionMode 通知应用选择模式（仅NOTIFICATION_EVENT事件使用）
 * @property notificationSelectedApps 通知选中的应用列表（仅NOTIFICATION_EVENT事件使用）
 * @property notificationAppIncludeMode 通知应用包括模式（仅NOTIFICATION_EVENT事件使用）
 * @property notificationContentMatchMode 通知内容匹配模式（仅NOTIFICATION_EVENT事件使用）
 * @property notificationTitle 通知标题匹配（仅NOTIFICATION_EVENT事件使用）
 * @property notificationText 通知内容匹配（仅NOTIFICATION_EVENT事件使用）
 * @property notificationCaseSensitive 通知大小写敏感（仅NOTIFICATION_EVENT事件使用）
 * @property notificationUseRegex 通知使用正则表达式（仅NOTIFICATION_EVENT事件使用）
 * @property notificationIgnoreOngoing 通知忽略正在进行的通知（仅NOTIFICATION_EVENT事件使用）
 * @property notificationPreventMultipleTrigger 通知防止多重触发（仅NOTIFICATION_EVENT事件使用）
 * @property notificationSoundMode 通知声音模式（仅NOTIFICATION_EVENT事件使用）
 * @property ringerModeType 铃声模式类型（仅RINGER_MODE_CHANGED事件使用）
 * @property musicPlaybackType 音乐播放状态类型（仅MUSIC_PLAYBACK_STATE事件使用）
 * @property airplaneModeType 飞行模式状态类型（仅AIRPLANE_MODE_STATE事件使用）
 * @property volumeStreamType 音量流类型（仅VOLUME_CHANGED事件使用）
 * @property volumeThreshold 音量变化阈值（仅VOLUME_CHANGED事件使用）
 */
data class DeviceEventCondition(
    override val id: String = UUID.randomUUID().toString(),
    val eventType: DeviceEventType = DeviceEventType.GPS_STATE,

    // GPS状态相关参数
    val gpsStateType: GpsStateType = GpsStateType.ENABLED,

    // Logcat消息相关参数
    val logcatComponent: String = "",
    val logcatText: String = "",
    val logcatBufferTypes: List<LogcatBufferType> = listOf(LogcatBufferType.MAIN), // 改为多选
    val logcatCaseSensitive: Boolean = false,

    // 剪贴板变化相关参数
    val clipboardText: String = "",
    val clipboardUseRegex: Boolean = false,
    val clipboardCaseSensitive: Boolean = false, // 新增：剪贴板大小写敏感

    // 屏幕状态相关参数
    val screenEventType: ScreenEventType = ScreenEventType.ON,

    // 底座连接相关参数
    val dockStateType: DockStateType = DockStateType.ANY_DOCK,

    // Intent接收相关参数
    val intentAction: String = "",
    val intentExtraKey: String = "",
    val intentExtraValue: String = "",

    // SIM卡状态相关参数
    val simCardStateType: SimCardStateType = SimCardStateType.STATE_CHANGED,

    // 深色主题相关参数
    val darkThemeStateType: DarkThemeStateType = DarkThemeStateType.ENABLED,

    // 登录失败相关参数
    val loginFailureThreshold: Int = 3,

    // 系统设置相关参数
    val systemSettingTypes: List<SystemSettingType> = listOf(SystemSettingType.SYSTEM), // 改为多选
    val systemSettingKey: String = "",
    val systemSettingValue: String = "",
    val systemSettingUseRegex: Boolean = false, // 新增：系统设置使用正则表达式

    // 自动同步相关参数
    val autoSyncStateType: AutoSyncStateType = AutoSyncStateType.ENABLED,

    // 通知事件相关参数
    val notificationEventType: NotificationEventType = NotificationEventType.RECEIVED, // 改为收到通知
    val notificationAppSelectionMode: NotificationAppSelectionMode = NotificationAppSelectionMode.ANY, // 新增：应用选择模式
    val notificationSelectedApps: List<String> = emptyList(), // 新增：选中的应用包名列表
    val notificationAppIncludeMode: NotificationAppIncludeMode = NotificationAppIncludeMode.INCLUDE, // 新增：包括还是排除
    val notificationContentMatchMode: NotificationContentMatchMode = NotificationContentMatchMode.ANY, // 新增：匹配内容模式
    val notificationTitle: String = "",
    val notificationText: String = "",
    val notificationCaseSensitive: Boolean = false, // 新增：通知大小写敏感
    val notificationUseRegex: Boolean = false, // 新增：通知使用正则表达式
    val notificationIgnoreOngoing: Boolean = false, // 新增：忽略正在进行的通知
    val notificationPreventMultipleTrigger: Boolean = false, // 新增：防止多重触发
    val notificationSoundMode: NotificationSoundMode = NotificationSoundMode.ANY, // 新增：通知声音模式

    // 铃声模式相关参数
    val ringerModeType: RingerModeType = RingerModeType.NORMAL,

    // 音乐播放相关参数
    val musicPlaybackType: MusicPlaybackType = MusicPlaybackType.STARTED,

    // 飞行模式相关参数
    val airplaneModeType: AirplaneModeType = AirplaneModeType.ENABLED,

    // 音量变化相关参数
    val volumeStreamType: VolumeStreamType = VolumeStreamType.MEDIA_MUSIC,
    val volumeThreshold: Int = 5,

    // 内存状态相关参数
    val memoryStateType: MemoryStateType = MemoryStateType.BELOW_THRESHOLD,
    val memoryThreshold: Int = 2,           // 内存阈值
    val isPercentageMode: Boolean = false,  // 百分比模式
    val memoryChangeThreshold: Int = 10,    // 变化阈值

    // 检测频率配置
    val checkFrequency: ConditionCheckFrequency = ConditionCheckFrequency.BALANCED, // 检测频率
    val enableCustomCheckFrequency: Boolean = false, // 启用自定义检测频率
    val customCheckFrequencySeconds: Int = 5, // 自定义检测频率（秒）

    // 内存检测模式配置
    val memoryCheckMode: MemoryCheckMode = MemoryCheckMode.TRADITIONAL, // 内存检测模式

    // 事件驱动配置
    val triggerOnAppForeground: Boolean = true, // 应用进入前台时触发
    val triggerOnAppLaunch: Boolean = true, // 应用启动时触发
    val triggerOnMemoryPressure: Boolean = true, // 系统内存压力时触发
    val appLaunchDelaySeconds: Int = 10, // 应用启动后延迟检测时间
    val foregroundDelaySeconds: Int = 5, // 前台切换后延迟检测时间
    val enableSmartDelay: Boolean = true, // 启用智能延迟
    val monitorDurationSeconds: Int = 30, // 连续监控时长
    val monitorIntervalSeconds: Int = 3, // 监控期间检测间隔
    val cooldownSeconds: Int = 60, // 冷却期时长

    // 自适应配置
    val adaptiveStrategy: AdaptiveStrategy = AdaptiveStrategy.BALANCED, // 自适应策略
    val enableMemoryPressureAdaptation: Boolean = true, // 内存压力感知
    val enableAppActivityAdaptation: Boolean = true, // 应用活动感知
    val memoryAbundantFrequency: Int = 120, // 内存充足时频率
    val memoryTightFrequency: Int = 5, // 内存紧张时频率

    // 智能学习配置
    val enableLearning: Boolean = true, // 启用智能学习
    val minSamplesForPrediction: Int = 5, // 最小学习样本数
    val confidenceThreshold: Float = 0.7f, // 置信度阈值
    val stabilityCheckInterval: Int = 2, // 稳定性检测间隔
    val stabilityThresholdMB: Int = 50, // 稳定性阈值
    val requiredStableChecks: Int = 3, // 连续稳定次数
    val maxHistoryRecords: Int = 30, // 最大历史记录数
    val dataRetentionDays: Int = 30, // 数据保留天数

    // 混合模式配置
    val enableEventDriven: Boolean = true, // 启用事件驱动策略
    val enableAdaptive: Boolean = true, // 启用自适应策略
    val enableIntelligent: Boolean = false, // 启用智能学习策略
    val eventDrivenWeight: Float = 0.4f, // 事件驱动权重
    val adaptiveWeight: Float = 0.4f, // 自适应权重
    val intelligentWeight: Float = 0.2f // 智能学习权重
) : SharedTriggerCondition {
    override val type: String = "device_event"
    override val displayName: String = "设备事件"

    override fun getDescription(): String {
        return when (eventType) {
            DeviceEventType.GPS_STATE -> "当${gpsStateType.displayName}时"
            DeviceEventType.LOGCAT_MESSAGE -> {
                val componentDesc = if (logcatComponent.isNotEmpty()) "组件[$logcatComponent]" else "任何组件"
                val textDesc = if (logcatText.isNotEmpty()) "包含[$logcatText]" else "任何内容"
                val bufferDesc = if (logcatBufferTypes.size == 1) logcatBufferTypes[0].displayName else "${logcatBufferTypes.size}种缓冲区"
                "当${componentDesc}的${bufferDesc}日志${textDesc}时"
            }
            DeviceEventType.CLIPBOARD_CHANGED -> {
                if (clipboardText.isNotEmpty()) {
                    val matchType = if (clipboardUseRegex) "正则匹配" else "包含"
                    "当剪贴板内容${matchType}[$clipboardText]时"
                } else {
                    "当剪贴板内容变化时"
                }
            }
            DeviceEventType.SCREEN_STATE -> "当${screenEventType.displayName}时"
            DeviceEventType.DOCK_STATE -> "当${dockStateType.displayName}时"
            DeviceEventType.INTENT_RECEIVED -> {
                val actionDesc = if (intentAction.isNotEmpty()) "动作[$intentAction]" else "任何动作"
                "当接收到${actionDesc}的Intent时"
            }
            DeviceEventType.SIM_CARD_STATE -> "当${simCardStateType.displayName}时"
            DeviceEventType.DARK_THEME_CHANGED -> "当${darkThemeStateType.displayName}时"
            DeviceEventType.LOGIN_ATTEMPT_FAILED -> "当登录失败次数达到${loginFailureThreshold}次时"
            DeviceEventType.SYSTEM_SETTING_CHANGED -> {
                val keyDesc = if (systemSettingKey.isNotEmpty()) "键[$systemSettingKey]" else "任何设置"
                val typeDesc = if (systemSettingTypes.size == 1) systemSettingTypes[0].displayName else "${systemSettingTypes.size}种设置类型"
                "当${typeDesc}的${keyDesc}变化时"
            }
            DeviceEventType.AUTO_SYNC_STATE -> "当${autoSyncStateType.displayName}时"
            DeviceEventType.DEVICE_BOOT_COMPLETED -> "当设备启动完成时"
            DeviceEventType.NOTIFICATION_EVENT -> {
                val appDesc = when (notificationAppSelectionMode) {
                    NotificationAppSelectionMode.ANY -> "任何应用"
                    NotificationAppSelectionMode.SELECTED -> {
                        if (notificationSelectedApps.isNotEmpty()) {
                            val modeDesc = if (notificationAppIncludeMode == NotificationAppIncludeMode.INCLUDE) "包括" else "排除"
                            "${modeDesc}${notificationSelectedApps.size}个应用"
                        } else "选择的应用"
                    }
                }
                "当${appDesc}${notificationEventType.displayName}时"
            }
            DeviceEventType.RINGER_MODE_CHANGED -> "当切换到${ringerModeType.displayName}时"
            DeviceEventType.MUSIC_PLAYBACK_STATE -> "当音乐${musicPlaybackType.displayName}时"
            DeviceEventType.AIRPLANE_MODE_STATE -> "当${airplaneModeType.displayName}时"
            DeviceEventType.VOLUME_CHANGED -> "当${volumeStreamType.displayName}变化超过${volumeThreshold}%时"
            DeviceEventType.MEMORY_STATE -> when (memoryStateType) {
                MemoryStateType.BELOW_THRESHOLD -> {
                    val unit = if (isPercentageMode) "%" else "GB"
                    "当可用内存低于${memoryThreshold}${unit}时"
                }
                MemoryStateType.ABOVE_THRESHOLD -> {
                    val unit = if (isPercentageMode) "%" else "GB"
                    "当可用内存高于${memoryThreshold}${unit}时"
                }
                MemoryStateType.CHANGED -> "当内存变化超过${memoryChangeThreshold}%时"
            }
        }
    }

    override fun matches(triggerCondition: SharedTriggerCondition): Boolean {
        return triggerCondition is DeviceEventCondition &&
               triggerCondition.eventType == this.eventType &&
               when (eventType) {
                   DeviceEventType.GPS_STATE -> triggerCondition.gpsStateType == this.gpsStateType
                   DeviceEventType.LOGCAT_MESSAGE ->
                       triggerCondition.logcatComponent == this.logcatComponent &&
                       triggerCondition.logcatText == this.logcatText &&
                       triggerCondition.logcatBufferTypes == this.logcatBufferTypes &&
                       triggerCondition.logcatCaseSensitive == this.logcatCaseSensitive
                   DeviceEventType.CLIPBOARD_CHANGED ->
                       triggerCondition.clipboardText == this.clipboardText &&
                       triggerCondition.clipboardUseRegex == this.clipboardUseRegex &&
                       triggerCondition.clipboardCaseSensitive == this.clipboardCaseSensitive
                   DeviceEventType.SCREEN_STATE -> triggerCondition.screenEventType == this.screenEventType
                   DeviceEventType.DOCK_STATE -> triggerCondition.dockStateType == this.dockStateType
                   DeviceEventType.INTENT_RECEIVED ->
                       triggerCondition.intentAction == this.intentAction &&
                       triggerCondition.intentExtraKey == this.intentExtraKey &&
                       triggerCondition.intentExtraValue == this.intentExtraValue
                   DeviceEventType.SIM_CARD_STATE -> triggerCondition.simCardStateType == this.simCardStateType
                   DeviceEventType.DARK_THEME_CHANGED -> triggerCondition.darkThemeStateType == this.darkThemeStateType
                   DeviceEventType.LOGIN_ATTEMPT_FAILED -> triggerCondition.loginFailureThreshold == this.loginFailureThreshold
                   DeviceEventType.SYSTEM_SETTING_CHANGED ->
                       triggerCondition.systemSettingTypes == this.systemSettingTypes &&
                       triggerCondition.systemSettingKey == this.systemSettingKey &&
                       triggerCondition.systemSettingValue == this.systemSettingValue &&
                       triggerCondition.systemSettingUseRegex == this.systemSettingUseRegex
                   DeviceEventType.AUTO_SYNC_STATE -> triggerCondition.autoSyncStateType == this.autoSyncStateType
                   DeviceEventType.DEVICE_BOOT_COMPLETED -> true // 无额外参数
                   DeviceEventType.NOTIFICATION_EVENT ->
                       triggerCondition.notificationEventType == this.notificationEventType &&
                       triggerCondition.notificationAppSelectionMode == this.notificationAppSelectionMode &&
                       triggerCondition.notificationSelectedApps == this.notificationSelectedApps &&
                       triggerCondition.notificationAppIncludeMode == this.notificationAppIncludeMode &&
                       triggerCondition.notificationContentMatchMode == this.notificationContentMatchMode &&
                       triggerCondition.notificationTitle == this.notificationTitle &&
                       triggerCondition.notificationText == this.notificationText &&
                       triggerCondition.notificationCaseSensitive == this.notificationCaseSensitive &&
                       triggerCondition.notificationUseRegex == this.notificationUseRegex &&
                       triggerCondition.notificationIgnoreOngoing == this.notificationIgnoreOngoing &&
                       triggerCondition.notificationPreventMultipleTrigger == this.notificationPreventMultipleTrigger &&
                       triggerCondition.notificationSoundMode == this.notificationSoundMode
                   DeviceEventType.RINGER_MODE_CHANGED -> triggerCondition.ringerModeType == this.ringerModeType
                   DeviceEventType.MUSIC_PLAYBACK_STATE -> triggerCondition.musicPlaybackType == this.musicPlaybackType
                   DeviceEventType.AIRPLANE_MODE_STATE -> triggerCondition.airplaneModeType == this.airplaneModeType
                   DeviceEventType.VOLUME_CHANGED ->
                       triggerCondition.volumeStreamType == this.volumeStreamType &&
                       triggerCondition.volumeThreshold == this.volumeThreshold
                   DeviceEventType.MEMORY_STATE ->
                       triggerCondition.memoryStateType == this.memoryStateType &&
                       triggerCondition.memoryThreshold == this.memoryThreshold &&
                       triggerCondition.isPercentageMode == this.isPercentageMode &&
                       triggerCondition.memoryChangeThreshold == this.memoryChangeThreshold
               }
    }
}

/**
 * 传感器状态条件
 * 统一的传感器状态检测功能，支持7种传感器类型的检测
 *
 * @property id 条件唯一标识
 * @property sensorType 传感器类型
 * @property thresholdType 光线阈值类型（仅光线传感器使用）
 * @property luxValue 光照强度值（仅光线传感器使用）
 * @property orientationType 屏幕方向类型（仅屏幕方向传感器使用）
 * @property sensitivity 敏感度级别（摇晃检测使用）
 * @property shakeThreshold 摇晃阈值（摇晃检测使用）
 * @property sleepStateType 睡眠状态类型（仅睡眠检测使用）
 * @property flipType 翻转类型（仅设备翻转检测使用）
 * @property proximityType 距离类型（仅距离传感器使用）
 * @property activityType 运动类型（仅运动识别使用）
 */
data class SensorStateCondition(
    override val id: String = UUID.randomUUID().toString(),
    val sensorType: SensorStateType = SensorStateType.LIGHT_SENSOR,
    // 光线传感器参数
    val thresholdType: LightThresholdType = LightThresholdType.DECREASE_TO,
    val luxValue: Float = 10.0f,
    // 屏幕方向传感器参数
    val orientationType: OrientationType = OrientationType.PORTRAIT,
    // 摇晃检测参数
    val sensitivity: SensitivityLevel = SensitivityLevel.MEDIUM,
    val shakeThreshold: Float = 12.0f,
    // 睡眠检测参数
    val sleepStateType: SleepStateType = SleepStateType.FALL_ASLEEP,
    // 设备翻转检测参数
    val flipType: FlipType = FlipType.FACE_UP_TO_DOWN,
    // 距离传感器参数
    val proximityType: ProximityType = ProximityType.NEAR,
    // 运动识别参数
    val activityType: ActivityType = ActivityType.WALKING
) : SharedTriggerCondition {
    override val type: String = "sensor_state"
    override val displayName: String = "传感器状态"

    override fun getDescription(): String {
        return when (sensorType) {
            SensorStateType.LIGHT_SENSOR -> {
                val action = when (thresholdType) {
                    LightThresholdType.DECREASE_TO -> "减少到"
                    LightThresholdType.INCREASE_TO -> "增加到"
                }
                "当环境光照${action}${luxValue}lux时"
            }
            SensorStateType.ORIENTATION_SENSOR -> {
                val orientation = when (orientationType) {
                    OrientationType.PORTRAIT -> "竖屏"
                    OrientationType.LANDSCAPE -> "横屏"
                    OrientationType.FACE_UP -> "朝上"
                    OrientationType.FACE_DOWN -> "朝下"
                }
                "当屏幕方向为${orientation}时"
            }
            SensorStateType.SHAKE_SENSOR -> {
                val level = when (sensitivity) {
                    SensitivityLevel.LOW -> "低"
                    SensitivityLevel.MEDIUM -> "中"
                    SensitivityLevel.HIGH -> "高"
                }
                "当检测到摇晃时（${level}敏感度）"
            }
            SensorStateType.SLEEP_SENSOR -> {
                val state = when (sleepStateType) {
                    SleepStateType.FALL_ASLEEP -> "入睡"
                    SleepStateType.WAKE_UP -> "醒来"
                }
                "当用户${state}时"
            }
            SensorStateType.FLIP_SENSOR -> {
                val flip = when (flipType) {
                    FlipType.FACE_UP_TO_DOWN -> "朝上翻转到朝下"
                    FlipType.FACE_DOWN_TO_UP -> "朝下翻转到朝上"
                    FlipType.ANY_TO_FACE_DOWN -> "翻转到朝下"
                }
                "当设备${flip}时"
            }
            SensorStateType.PROXIMITY_SENSOR -> {
                val proximity = when (proximityType) {
                    ProximityType.NEAR -> "接近"
                    ProximityType.FAR -> "远离"
                    ProximityType.SLOW_WAVE -> "慢挥手"
                    ProximityType.FAST_WAVE -> "快挥手"
                }
                "当检测到${proximity}时"
            }
            SensorStateType.ACTIVITY_RECOGNITION -> {
                val activity = when (activityType) {
                    ActivityType.DRIVING -> "开车"
                    ActivityType.CYCLING -> "骑自行车"
                    ActivityType.RUNNING -> "跑步"
                    ActivityType.WALKING -> "步行"
                    ActivityType.STILL -> "静止"
                }
                "当用户${activity}时"
            }
        }
    }

    override fun matches(triggerCondition: SharedTriggerCondition): Boolean {
        return triggerCondition is SensorStateCondition &&
               triggerCondition.sensorType == this.sensorType &&
               when (sensorType) {
                   SensorStateType.LIGHT_SENSOR ->
                       triggerCondition.thresholdType == this.thresholdType &&
                       triggerCondition.luxValue == this.luxValue
                   SensorStateType.ORIENTATION_SENSOR ->
                       triggerCondition.orientationType == this.orientationType
                   SensorStateType.SHAKE_SENSOR ->
                       triggerCondition.sensitivity == this.sensitivity
                   SensorStateType.SLEEP_SENSOR ->
                       triggerCondition.sleepStateType == this.sleepStateType
                   SensorStateType.FLIP_SENSOR ->
                       triggerCondition.flipType == this.flipType
                   SensorStateType.PROXIMITY_SENSOR ->
                       triggerCondition.proximityType == this.proximityType
                   SensorStateType.ACTIVITY_RECOGNITION ->
                       triggerCondition.activityType == this.activityType
               }
    }
}







/**
 * 统一时间条件
 * 整合秒表、日出日落、日程时间、周期时间、延迟触发、周期触发等所有时间相关功能
 *
 * @property id 条件唯一标识
 * @property timeConditionType 时间条件类型
 * @property stopwatchHours 秒表小时数（秒表类型使用）
 * @property stopwatchMinutes 秒表分钟数（秒表类型使用）
 * @property stopwatchSeconds 秒表秒数（秒表类型使用）
 * @property sunEventType 日出日落事件类型（日出日落类型使用）
 * @property latitude 纬度（日出日落类型使用，可选）
 * @property longitude 经度（日出日落类型使用，可选）
 * @property year 年份（日程时间类型使用）
 * @property month 月份（日程时间类型使用）
 * @property day 日期（日程时间类型使用）
 * @property hour 小时（日程时间、周期时间类型使用）
 * @property minute 分钟（日程时间、周期时间类型使用）
 * @property timeRepeatMode 时间重复模式（日程时间类型使用）
 * @property scheduledRepeatMode 定时重复模式（周期时间类型使用）
 * @property selectedDays 选择的星期几（周期时间类型使用）
 * @property interval 时间间隔（延迟触发、周期触发类型使用）
 * @property unit 时间单位（延迟触发、周期触发类型使用）
 * @property startTime 开始时间（秒表、延迟触发、周期触发类型使用）
 */
data class TimeBasedCondition(
    override val id: String = UUID.randomUUID().toString(),
    val timeConditionType: TimeConditionType = TimeConditionType.STOPWATCH,

    // 秒表参数
    val stopwatchHours: Int = 0,
    val stopwatchMinutes: Int = 0,
    val stopwatchSeconds: Int = 30,

    // 日出日落参数
    val sunEventType: SunEventType = SunEventType.SUNRISE,
    val latitude: Double? = null,  // null表示使用系统位置
    val longitude: Double? = null, // null表示使用系统位置

    // 日程时间参数
    val year: Int = Calendar.getInstance().get(Calendar.YEAR),
    val month: Int = Calendar.getInstance().get(Calendar.MONTH) + 1,
    val day: Int = Calendar.getInstance().get(Calendar.DAY_OF_MONTH),
    val hour: Int = 8,
    val minute: Int = 0,
    val timeRepeatMode: TimeRepeatMode = TimeRepeatMode.ONCE,

    // 周期时间参数
    val scheduledRepeatMode: ScheduledRepeatMode = ScheduledRepeatMode.ONCE,
    val selectedDays: Set<DayOfWeek> = emptySet(),

    // 延迟触发、周期触发参数
    val interval: Int = 30,
    val unit: TimeIntervalUnit = TimeIntervalUnit.SECONDS,
    val startTime: Long = System.currentTimeMillis()
) : SharedTriggerCondition {
    override val type: String = "time_based"
    override val displayName: String = "时间条件"

    override fun getDescription(): String {
        return when (timeConditionType) {
            TimeConditionType.STOPWATCH -> {
                val totalSeconds = stopwatchHours * 3600 + stopwatchMinutes * 60 + stopwatchSeconds
                when {
                    stopwatchHours > 0 -> "${stopwatchHours}小时${stopwatchMinutes}分${stopwatchSeconds}秒后"
                    stopwatchMinutes > 0 -> "${stopwatchMinutes}分${stopwatchSeconds}秒后"
                    else -> "${stopwatchSeconds}秒后"
                }
            }
            TimeConditionType.SUN_EVENT -> {
                val eventName = when (sunEventType) {
                    SunEventType.SUNRISE -> "日出"
                    SunEventType.SUNSET -> "日落"
                }
                if (latitude != null && longitude != null) {
                    "${eventName}时（${String.format("%.2f", latitude)}, ${String.format("%.2f", longitude)}）"
                } else {
                    "${eventName}时"
                }
            }
            TimeConditionType.SCHEDULED_TIME -> {
                val timeStr = "${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}"
                val dateStr = "${year}年${month}月${day}日"
                when (timeRepeatMode) {
                    TimeRepeatMode.ONCE -> "${dateStr} ${timeStr}"
                    TimeRepeatMode.DAILY -> "${dateStr} ${timeStr} 起每天 ${timeStr}"
                    TimeRepeatMode.WEEKLY -> {
                        val calendar = Calendar.getInstance().apply {
                            set(year, month - 1, day)
                        }
                        val dayOfWeek = when (calendar.get(Calendar.DAY_OF_WEEK)) {
                            Calendar.SUNDAY -> "周日"
                            Calendar.MONDAY -> "周一"
                            Calendar.TUESDAY -> "周二"
                            Calendar.WEDNESDAY -> "周三"
                            Calendar.THURSDAY -> "周四"
                            Calendar.FRIDAY -> "周五"
                            Calendar.SATURDAY -> "周六"
                            else -> "未知"
                        }
                        "${dateStr} ${timeStr} 起每${dayOfWeek} ${timeStr}"
                    }
                    else -> "${dateStr} ${timeStr} 起${timeRepeatMode.displayName} ${timeStr}"
                }
            }
            TimeConditionType.PERIODIC_TIME -> {
                val timeStr = "${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}"
                when (scheduledRepeatMode) {
                    ScheduledRepeatMode.ONCE -> "在 ${timeStr}"
                    ScheduledRepeatMode.DAILY -> "每天 ${timeStr}"
                    ScheduledRepeatMode.CUSTOM -> {
                        if (selectedDays.isNotEmpty()) {
                            val dayNames = selectedDays.map { it.displayName }.joinToString("、")
                            "每${dayNames} ${timeStr}"
                        } else {
                            "自定义时间 ${timeStr}"
                        }
                    }
                }
            }
            TimeConditionType.DELAYED_TRIGGER -> {
                "${interval}${unit.displayName}后"
            }
            TimeConditionType.PERIODIC_TRIGGER -> {
                "每隔${interval}${unit.displayName}"
            }
        }
    }

    override fun matches(triggerCondition: SharedTriggerCondition): Boolean {
        return triggerCondition is TimeBasedCondition &&
               triggerCondition.timeConditionType == this.timeConditionType &&
               when (timeConditionType) {
                   TimeConditionType.STOPWATCH -> {
                       triggerCondition.stopwatchHours == this.stopwatchHours &&
                       triggerCondition.stopwatchMinutes == this.stopwatchMinutes &&
                       triggerCondition.stopwatchSeconds == this.stopwatchSeconds
                   }
                   TimeConditionType.SUN_EVENT -> {
                       triggerCondition.sunEventType == this.sunEventType &&
                       triggerCondition.latitude == this.latitude &&
                       triggerCondition.longitude == this.longitude
                   }
                   TimeConditionType.SCHEDULED_TIME -> {
                       triggerCondition.year == this.year &&
                       triggerCondition.month == this.month &&
                       triggerCondition.day == this.day &&
                       triggerCondition.hour == this.hour &&
                       triggerCondition.minute == this.minute &&
                       triggerCondition.timeRepeatMode == this.timeRepeatMode
                   }
                   TimeConditionType.PERIODIC_TIME -> {
                       triggerCondition.hour == this.hour &&
                       triggerCondition.minute == this.minute &&
                       triggerCondition.scheduledRepeatMode == this.scheduledRepeatMode &&
                       triggerCondition.selectedDays == this.selectedDays
                   }
                   TimeConditionType.DELAYED_TRIGGER -> {
                       triggerCondition.interval == this.interval &&
                       triggerCondition.unit == this.unit
                   }
                   TimeConditionType.PERIODIC_TRIGGER -> {
                       triggerCondition.interval == this.interval &&
                       triggerCondition.unit == this.unit
                   }
               }
    }

    /**
     * 重置开始时间（用于秒表、延迟触发、周期触发）
     */
    fun resetStartTime(): TimeBasedCondition {
        return this.copy(startTime = System.currentTimeMillis())
    }
}

/**
 * 应用状态类型枚举
 */
enum class AppStateType {
    FOREGROUND,      // 应用进入前台（替代AppForegroundCondition）
    BACKGROUND,      // 应用进入后台（替代AppBackgroundCondition）
    BACKGROUND_TIME_EXCEEDED, // 后台时间超过阈值（新增）
    STATE_CHANGED,   // 应用状态变化（前台↔后台）
    LAUNCHED,        // 应用启动
    CLOSED,          // 应用关闭
    INSTALLED,       // 应用安装（新增）
    UNINSTALLED,     // 应用删除（新增）
    UPDATED,         // 应用更新（新增）
    INTERFACE_CLICK, // 界面点击（新增）
    SCREEN_CONTENT,  // 屏幕内容（新增）
    TASKER_LOCALE_PLUGIN_CONDITION // Tasker/Locale插件条件状态
}

/**
 * 应用状态类别枚举
 * 用于对应用状态进行分类管理
 */
enum class AppStateCategoryType {
    STATE_CHANGE,        // 状态变化类：前台、后台、状态变化、后台时间超过阈值
    LIFECYCLE,           // 生命周期类：启动、关闭
    PACKAGE_MANAGEMENT,  // 应用管理类：安装、删除、更新
    INTERFACE_INTERACTION, // 界面交互类：界面点击、屏幕内容（新增）
    TASKER_LOCALE_PLUGIN // Tasker/Locale插件条件
}

/**
 * 应用检测模式枚举
 * 定义检测范围
 */
enum class AppDetectionMode(val displayName: String) {
    ANY_APP("任何应用"),           // 监控所有应用
    SELECTED_APPS("指定应用")      // 只监控选中的应用
}

/**
 * 应用状态触发模式枚举
 * 定义应用状态条件的触发方式（适用于所有应用状态类型）
 */
enum class AppStateTriggerMode(val displayName: String) {
    ANY_APP("任意应用"),      // 选中的应用中任意一个满足条件就触发
    ALL_APPS("所有应用")      // 选中的应用全部都满足条件才触发
}



/**
 * 屏幕内容触发模式枚举
 */
enum class ScreenContentTriggerMode(val displayName: String) {
    APPEAR("出现时触发"),
    DISAPPEAR("消失时触发")
}

/**
 * 界面交互类型枚举
 * 定义界面交互的具体类型（点击或长按）
 */
enum class InterfaceInteractionType(val displayName: String) {
    CLICK("点击"),
    LONG_CLICK("长按")
}

/**
 * 屏幕内容匹配类型枚举
 * 定义屏幕内容的匹配方式（文本内容或查看ID）
 */
enum class ScreenContentMatchType(val displayName: String) {
    TEXT_CONTENT("文本内容"),
    VIEW_ID("查看ID")
}

/**
 * 统一的应用状态条件
 * 整合了应用进入前台、进入后台等多种状态检测，支持层次化配置
 * 所有状态类型都支持多选应用和触发模式配置
 */
data class AppStateCondition(
    override val id: String = UUID.randomUUID().toString(),
    val categoryType: AppStateCategoryType = AppStateCategoryType.STATE_CHANGE,
    val stateType: AppStateType = AppStateType.STATE_CHANGED,
    val detectionMode: AppDetectionMode = AppDetectionMode.ANY_APP,

    // 应用选择相关字段（适用于所有状态类型）
    val selectedApps: List<SimpleAppInfo> = emptyList(), // 选择的应用列表（多选）
    val appStateTriggerMode: AppStateTriggerMode = AppStateTriggerMode.ANY_APP, // 应用状态触发模式

    // 单选应用字段（保留用于特定场景）
    val targetPackageName: String = "", // 单选应用包名
    val targetAppName: String = "", // 单选应用名称

    // 后台时间相关字段（仅当stateType为BACKGROUND_TIME_EXCEEDED时使用）
    val backgroundTimeThresholdMinutes: Int = 5, // 后台时间阈值（分钟）
    val skipForegroundApp: Boolean = true, // 跳过前台应用
    val skipMusicPlayingApp: Boolean = true, // 跳过音乐播放应用
    val skipVpnApp: Boolean = false, // 跳过VPN应用
    val selectedVpnApps: List<SimpleAppInfo> = emptyList(), // 选择的VPN应用
    val autoIncludeNewApps: Boolean = false, // 自动包含新安装应用

    // 界面交互相关字段（仅当stateType为INTERFACE_CLICK或SCREEN_CONTENT时使用）
    val clickTargetText: String = "", // 界面点击目标文本
    val screenContentText: String = "", // 屏幕内容文本
    val screenContentTriggerMode: ScreenContentTriggerMode = ScreenContentTriggerMode.APPEAR, // 屏幕内容触发模式
    val caseSensitive: Boolean = false, // 是否区分大小写
    val useRegex: Boolean = false, // 是否使用正则表达式

    // 新增界面交互字段
    val interfaceInteractionType: InterfaceInteractionType = InterfaceInteractionType.CLICK, // 界面交互类型（点击或长按）
    val screenContentMatchType: ScreenContentMatchType = ScreenContentMatchType.TEXT_CONTENT, // 屏幕内容匹配类型
    val includeOverlayLayers: Boolean = false, // 是否包括叠加层

    // Tasker/Locale插件相关字段
    val pluginAction: String = "com.twofortyfouram.locale.intent.action.QUERY_CONDITION",
    val pluginExtras: String = "",
    val expectedState: TaskerLocaleConditionState = TaskerLocaleConditionState.SATISFIED,
    val checkInterval: Int = 30, // 默认30秒检查一次
    val timeoutSeconds: Int = 10, // 默认10秒超时

    // 检测频率配置
    val checkFrequency: ConditionCheckFrequency = ConditionCheckFrequency.BALANCED, // 检测频率
    val enableCustomCheckFrequency: Boolean = false, // 启用自定义检测频率
    val customCheckFrequencySeconds: Int = 5, // 自定义检测频率（秒）
    val backgroundTimeCheckFrequency: ConditionCheckFrequency = ConditionCheckFrequency.SLOW, // 后台时间检测频率
    val enableCustomBackgroundTimeCheckFrequency: Boolean = false, // 启用自定义后台时间检测频率
    val customBackgroundTimeCheckFrequencySeconds: Int = 30 // 自定义后台时间检测频率（秒）
) : SharedTriggerCondition {
    override val type: String = "app_state"
    override val displayName: String = "应用状态"

    override fun getDescription(): String {
        return when (stateType) {
            AppStateType.BACKGROUND_TIME_EXCEEDED -> {
                val appDesc = if (selectedApps.isEmpty()) {
                    "未选择应用"
                } else if (selectedApps.size == 1) {
                    selectedApps.first().appName
                } else {
                    "${selectedApps.size}个应用"
                }
                val triggerDesc = when (appStateTriggerMode) {
                    AppStateTriggerMode.ANY_APP -> "任意应用"
                    AppStateTriggerMode.ALL_APPS -> "所有应用"
                }
                val skipOptions = mutableListOf<String>()
                if (skipForegroundApp) skipOptions.add("跳过前台")
                if (skipMusicPlayingApp) skipOptions.add("跳过音乐")
                if (skipVpnApp) skipOptions.add("跳过VPN")
                if (selectedVpnApps.isNotEmpty()) {
                    skipOptions.add("VPN应用:${selectedVpnApps.size}个")
                }
                val skipText = if (skipOptions.isNotEmpty()) "(${skipOptions.joinToString(", ")})" else ""
                "当${appDesc}中${triggerDesc}后台时间超过${backgroundTimeThresholdMinutes}分钟时 $skipText"
            }
            AppStateType.INTERFACE_CLICK -> {
                val appDesc = if (targetPackageName.isEmpty()) "任何应用" else targetAppName
                val textDesc = if (clickTargetText.isNotEmpty()) "「$clickTargetText」" else "指定文本"
                val interactionDesc = when (interfaceInteractionType) {
                    InterfaceInteractionType.CLICK -> "点击"
                    InterfaceInteractionType.LONG_CLICK -> "长按"
                }
                val overlayDesc = if (includeOverlayLayers) "（包括叠加层）" else ""
                "当在${appDesc}中${interactionDesc}${textDesc}时${overlayDesc}"
            }
            AppStateType.SCREEN_CONTENT -> {
                val appDesc = if (targetPackageName.isEmpty()) "任何应用" else targetAppName
                val contentDesc = if (screenContentText.isNotEmpty()) "「$screenContentText」" else "指定内容"
                val matchTypeDesc = when (screenContentMatchType) {
                    ScreenContentMatchType.TEXT_CONTENT -> "文本"
                    ScreenContentMatchType.VIEW_ID -> "ID"
                }
                val triggerDesc = when (screenContentTriggerMode) {
                    ScreenContentTriggerMode.APPEAR -> "出现"
                    ScreenContentTriggerMode.DISAPPEAR -> "消失"
                }
                val overlayDesc = if (includeOverlayLayers) "（包括叠加层）" else ""
                "当在${appDesc}中${matchTypeDesc}${contentDesc}${triggerDesc}时${overlayDesc}"
            }
            AppStateType.TASKER_LOCALE_PLUGIN_CONDITION -> {
                val pluginName = if (targetAppName.isNotEmpty()) targetAppName else targetPackageName
                if (pluginName.isNotEmpty()) {
                    "当插件 $pluginName ${expectedState.displayName}时"
                } else {
                    "Tasker/Locale插件条件"
                }
            }
            else -> {
                // 生成应用描述
                val appDesc = when (detectionMode) {
                    AppDetectionMode.ANY_APP -> "任何应用"
                    AppDetectionMode.SELECTED_APPS -> {
                        if (selectedApps.isEmpty()) {
                            // 兼容旧版本的单选模式
                            if (targetPackageName.isEmpty()) "任何应用" else targetAppName
                        } else {
                            val triggerDesc = when (appStateTriggerMode) {
                                AppStateTriggerMode.ANY_APP -> "任意应用"
                                AppStateTriggerMode.ALL_APPS -> "所有应用"
                            }
                            if (selectedApps.size == 1) {
                                "${selectedApps.first().appName}（$triggerDesc）"
                            } else {
                                "${selectedApps.size}个应用中$triggerDesc"
                            }
                        }
                    }
                }

                when (stateType) {
                    AppStateType.FOREGROUND -> "当${appDesc}进入前台时"
                    AppStateType.BACKGROUND -> "当${appDesc}进入后台时"
                    AppStateType.STATE_CHANGED -> "当${appDesc}状态变化时"
                    AppStateType.LAUNCHED -> "当${appDesc}启动时"
                    AppStateType.CLOSED -> "当${appDesc}关闭时"
                    AppStateType.INSTALLED -> "当${appDesc}安装时"
                    AppStateType.UNINSTALLED -> "当${appDesc}删除时"
                    AppStateType.UPDATED -> "当${appDesc}更新时"
                    AppStateType.INTERFACE_CLICK -> "界面点击条件"
                    AppStateType.SCREEN_CONTENT -> "屏幕内容条件"
                    else -> "应用状态条件"
                }
            }
        }
    }

    override fun matches(triggerCondition: SharedTriggerCondition): Boolean {
        return triggerCondition is AppStateCondition &&
               triggerCondition.categoryType == this.categoryType &&
               triggerCondition.stateType == this.stateType &&
               when (stateType) {
                   AppStateType.BACKGROUND_TIME_EXCEEDED -> {
                       // 后台时间条件需要匹配应用列表、阈值和触发模式
                       triggerCondition.selectedApps.map { it.packageName }.toSet() ==
                           this.selectedApps.map { it.packageName }.toSet() &&
                       triggerCondition.backgroundTimeThresholdMinutes == this.backgroundTimeThresholdMinutes &&
                       triggerCondition.appStateTriggerMode == this.appStateTriggerMode &&
                       triggerCondition.skipForegroundApp == this.skipForegroundApp &&
                       triggerCondition.skipMusicPlayingApp == this.skipMusicPlayingApp &&
                       triggerCondition.skipVpnApp == this.skipVpnApp
                   }
                   AppStateType.INTERFACE_CLICK -> {
                       // 界面点击条件需要匹配目标应用和点击文本
                       triggerCondition.detectionMode == this.detectionMode &&
                       (this.detectionMode == AppDetectionMode.ANY_APP ||
                        triggerCondition.targetPackageName == this.targetPackageName) &&
                       triggerCondition.clickTargetText == this.clickTargetText &&
                       triggerCondition.caseSensitive == this.caseSensitive &&
                       triggerCondition.useRegex == this.useRegex &&
                       triggerCondition.interfaceInteractionType == this.interfaceInteractionType &&
                       triggerCondition.includeOverlayLayers == this.includeOverlayLayers
                   }
                   AppStateType.SCREEN_CONTENT -> {
                       // 屏幕内容条件需要匹配目标应用、内容文本和触发模式
                       triggerCondition.detectionMode == this.detectionMode &&
                       (this.detectionMode == AppDetectionMode.ANY_APP ||
                        triggerCondition.targetPackageName == this.targetPackageName) &&
                       triggerCondition.screenContentText == this.screenContentText &&
                       triggerCondition.screenContentTriggerMode == this.screenContentTriggerMode &&
                       triggerCondition.caseSensitive == this.caseSensitive &&
                       triggerCondition.useRegex == this.useRegex &&
                       triggerCondition.screenContentMatchType == this.screenContentMatchType &&
                       triggerCondition.includeOverlayLayers == this.includeOverlayLayers
                   }
                   AppStateType.TASKER_LOCALE_PLUGIN_CONDITION -> {
                       // Tasker/Locale插件条件需要匹配插件相关字段
                       triggerCondition.pluginAction == this.pluginAction &&
                       triggerCondition.pluginExtras == this.pluginExtras &&
                       triggerCondition.expectedState == this.expectedState
                   }
                   else -> {
                       // 其他应用状态条件需要匹配检测模式、应用选择和触发模式
                       triggerCondition.detectionMode == this.detectionMode &&
                       triggerCondition.appStateTriggerMode == this.appStateTriggerMode &&
                       when (this.detectionMode) {
                           AppDetectionMode.ANY_APP -> true // 任何应用模式不需要匹配具体应用
                           AppDetectionMode.SELECTED_APPS -> {
                               // 指定应用模式需要匹配应用列表
                               if (this.selectedApps.isNotEmpty()) {
                                   // 新版本多选模式
                                   triggerCondition.selectedApps.map { it.packageName }.toSet() ==
                                       this.selectedApps.map { it.packageName }.toSet()
                               } else {
                                   // 兼容旧版本单选模式
                                   triggerCondition.targetPackageName == this.targetPackageName
                               }
                           }
                       }
                   }
               }
    }
}

/**
 * 通信状态类型枚举
 */
enum class CommunicationStateType {
    SMS_SENT,        // 发送短信
    SMS_RECEIVED,    // 收到短信
    OUTGOING_CALL,   // 拨出电话
    INCOMING_CALL,   // 接到呼叫
    CALL_ENDED,      // 呼叫结束
    MISSED_CALL,     // 未接来电
    CALL_ACTIVE,     // 通话中
    DIAL_NUMBER      // 拨打电话号码
}

/**
 * 联系人筛选类型枚举
 */
enum class ContactFilterType {
    ANY_CONTACT,      // 任何联系人
    NO_CONTACT,       // 没有联系人（陌生号码）
    SPECIFIC_CONTACTS, // 指定联系人
    CONTACT_GROUP,    // 联系人分组
    ANY_NUMBER,       // 任何号码
    SPECIFIC_NUMBER   // 指定号码
}

/**
 * 联系人筛选模式枚举
 */
enum class ContactFilterMode {
    INCLUDE,  // 包含
    EXCLUDE   // 排除
}

/**
 * 通信状态条件
 * 统一的通信状态检测功能，支持通话和短信状态监控
 *
 * @property id 条件唯一标识
 * @property stateType 通信状态类型
 * @property filterType 联系人筛选类型
 * @property filterMode 筛选模式（包含/排除）
 * @property selectedContactIds 选中的联系人ID列表
 * @property selectedContactNames 选中的联系人名称列表（用于显示）
 * @property selectedGroupId 选中的联系人分组ID
 * @property selectedGroupName 选中的联系人分组名称（用于显示）
 * @property specificNumber 指定的电话号码
 */
data class CommunicationStateCondition(
    override val id: String = UUID.randomUUID().toString(),
    val stateType: CommunicationStateType = CommunicationStateType.SMS_RECEIVED,
    val filterType: ContactFilterType = ContactFilterType.ANY_CONTACT,
    val filterMode: ContactFilterMode = ContactFilterMode.INCLUDE,
    val selectedContactIds: List<String> = emptyList(),
    val selectedContactNames: List<String> = emptyList(),
    val selectedGroupId: String = "",
    val selectedGroupName: String = "",
    val specificNumber: String = ""
) : SharedTriggerCondition {
    override val type: String = "communication_state"
    override val displayName: String = "通信状态"

    override fun getDescription(): String {
        val stateDesc = when (stateType) {
            CommunicationStateType.SMS_SENT -> "发送短信"
            CommunicationStateType.SMS_RECEIVED -> "收到短信"
            CommunicationStateType.OUTGOING_CALL -> "拨出电话"
            CommunicationStateType.INCOMING_CALL -> "接到呼叫"
            CommunicationStateType.CALL_ENDED -> "呼叫结束"
            CommunicationStateType.MISSED_CALL -> "未接来电"
            CommunicationStateType.CALL_ACTIVE -> "通话中"
            CommunicationStateType.DIAL_NUMBER -> "拨打电话号码"
        }

        val filterDesc = when (filterType) {
            ContactFilterType.ANY_CONTACT -> "任何联系人"
            ContactFilterType.NO_CONTACT -> "陌生号码"
            ContactFilterType.SPECIFIC_CONTACTS -> {
                if (selectedContactNames.isNotEmpty()) {
                    val names = selectedContactNames.take(3).joinToString("、")
                    val suffix = if (selectedContactNames.size > 3) "等${selectedContactNames.size}个联系人" else ""
                    "$names$suffix"
                } else {
                    "指定联系人"
                }
            }
            ContactFilterType.CONTACT_GROUP -> {
                if (selectedGroupName.isNotEmpty()) selectedGroupName else "指定分组"
            }
            ContactFilterType.ANY_NUMBER -> "任何号码"
            ContactFilterType.SPECIFIC_NUMBER -> {
                if (specificNumber.isNotEmpty()) specificNumber else "指定号码"
            }
        }

        val modeDesc = when (filterMode) {
            ContactFilterMode.INCLUDE -> "来自"
            ContactFilterMode.EXCLUDE -> "排除"
        }

        return when (filterType) {
            ContactFilterType.ANY_CONTACT, ContactFilterType.ANY_NUMBER -> "当$stateDesc 时"
            ContactFilterType.NO_CONTACT -> "当$stateDesc（$filterDesc）时"
            else -> "当$stateDesc（$modeDesc $filterDesc）时"
        }
    }

    override fun matches(triggerCondition: SharedTriggerCondition): Boolean {
        return triggerCondition is CommunicationStateCondition &&
               triggerCondition.stateType == this.stateType &&
               triggerCondition.filterType == this.filterType &&
               triggerCondition.filterMode == this.filterMode &&
               triggerCondition.selectedContactIds == this.selectedContactIds &&
               triggerCondition.selectedGroupId == this.selectedGroupId &&
               triggerCondition.specificNumber == this.specificNumber
    }
}

/**
 * 手动触发条件
 * 支持十二种手动触发方式：动态快捷方式、静态快捷方式、桌面小组件、悬浮按钮、指纹手势、主屏幕按钮长按、媒体键按下、快捷方式打开、滑动屏幕、音量按钮长按、音量键按下、电源键长按
 *
 * @property id 条件唯一标识
 * @property triggerType 触发方式类型
 * @property slotIndex 槽位索引（静态快捷方式和桌面小组件使用）
 * @property buttonText 悬浮按钮文字（悬浮按钮使用）
 * @property shortcutName 动态快捷方式名称（动态快捷方式使用）
 * @property buttonSize 悬浮按钮大小（dp，范围20-200）
 * @property buttonAlpha 悬浮按钮透明度（范围0.1-1.0）
 * @property fingerprintGestureType 指纹手势类型（指纹手势使用）
 * @property homeButtonLongPressThreshold 主屏幕按钮长按阈值（毫秒，主屏幕按钮长按使用）
 * @property mediaKeyType 媒体键类型（媒体键按下使用）
 * @property mediaKeyPressCount 媒体键按下次数（1-3次，媒体键按下使用）
 * @property mediaKeyPressTimeout 媒体键多次按下的超时时间（毫秒，媒体键按下使用）
 * @property shortcutOpeningName 快捷方式打开名称（快捷方式打开使用）
 * @property swipeStartCorner 滑动开始区域（滑动屏幕使用）
 * @property swipeDirection 滑动方向（滑动屏幕使用）
 * @property volumeButtonType 音量按钮类型（音量按钮长按使用）
 * @property volumeButtonLongPressThreshold 音量按钮长按阈值（毫秒，音量按钮长按使用）
 * @property volumeKeyType 音量键类型（音量键按下使用）
 * @property volumeKeyPreserveVolume 音量键按下时是否保留原音量（音量键按下使用）
 * @property powerButtonLongPressThreshold 电源键长按阈值（毫秒，电源键长按使用）
 */
data class ManualTriggerCondition(
    override val id: String = UUID.randomUUID().toString(),
    val triggerType: ManualTriggerType = ManualTriggerType.DYNAMIC_SHORTCUT,
    val slotIndex: Int = 0, // 槽位索引（静态快捷方式和桌面小组件使用）
    val buttonText: String = "", // 悬浮按钮文字（悬浮按钮使用）
    val shortcutName: String = "", // 动态快捷方式名称（动态快捷方式使用）
    val buttonSize: Int = 60, // 悬浮按钮大小（dp，范围20-200）
    val buttonAlpha: Float = 0.8f, // 悬浮按钮透明度（范围0.1-1.0）
    val fingerprintGestureType: FingerprintGestureType = FingerprintGestureType.TAP, // 指纹手势类型（指纹手势使用）
    val homeButtonLongPressThreshold: Long = 1000L, // 主屏幕按钮长按阈值（毫秒）
    val mediaKeyType: MediaKeyType = MediaKeyType.PLAY_PAUSE, // 媒体键类型
    val mediaKeyPressCount: Int = 1, // 媒体键按下次数（1-3次）
    val mediaKeyPressTimeout: Long = 1000L, // 媒体键多次按下的超时时间（毫秒）
    val shortcutOpeningName: String = "", // 快捷方式打开名称（快捷方式打开使用）
    val swipeStartCorner: SwipeStartCorner = SwipeStartCorner.TOP_LEFT, // 滑动开始区域（滑动屏幕使用）
    val swipeDirection: SwipeDirection = SwipeDirection.HORIZONTAL, // 滑动方向（滑动屏幕使用）
    val volumeButtonType: VolumeButtonType = VolumeButtonType.VOLUME_UP, // 音量按钮类型（音量按钮长按使用）
    val volumeButtonLongPressThreshold: Long = 1000L, // 音量按钮长按阈值（毫秒）
    val volumeKeyType: VolumeButtonType = VolumeButtonType.VOLUME_UP, // 音量键类型（音量键按下使用）
    val volumeKeyPreserveVolume: Boolean = false, // 音量键按下时是否保留原音量（音量键按下使用）
    val powerButtonLongPressThreshold: Long = 1000L // 电源键长按阈值（毫秒）
) : SharedTriggerCondition {
    override val type: String = "manual_trigger"
    override val displayName: String = "手动触发"

    override fun getDescription(): String {
        return when (triggerType) {
            ManualTriggerType.DYNAMIC_SHORTCUT -> {
                val name = if (shortcutName.isNotEmpty()) shortcutName else "动态快捷方式"
                "通过动态快捷方式「$name」触发"
            }
            ManualTriggerType.STATIC_SHORTCUT -> {
                "通过静态快捷方式槽位 ${slotIndex + 1} 触发"
            }
            ManualTriggerType.DESKTOP_WIDGET -> {
                "通过桌面小组件槽位 ${slotIndex + 1} 触发"
            }
            ManualTriggerType.FLOATING_BUTTON -> {
                val text = if (buttonText.isNotEmpty()) "「$buttonText」" else ""
                "通过悬浮按钮${text}触发"
            }
            ManualTriggerType.FINGERPRINT_GESTURE -> {
                "通过指纹手势「${fingerprintGestureType.displayName}」触发"
            }
            ManualTriggerType.HOME_BUTTON_LONG_PRESS -> {
                "通过主屏幕按钮长按（${homeButtonLongPressThreshold}ms）触发"
            }
            ManualTriggerType.MEDIA_KEY_PRESS -> {
                val countDesc = if (mediaKeyPressCount > 1) "${mediaKeyPressCount}次" else ""
                "通过媒体键「${mediaKeyType.displayName}」${countDesc}按下触发"
            }
            ManualTriggerType.SHORTCUT_OPENING -> {
                val name = if (shortcutOpeningName.isNotEmpty()) shortcutOpeningName else "快捷方式"
                "通过快捷方式「$name」打开触发"
            }
            ManualTriggerType.SCREEN_SWIPE -> {
                "通过从${swipeStartCorner.displayName}${swipeDirection.displayName}滑动触发"
            }
            ManualTriggerType.VOLUME_BUTTON_LONG_PRESS -> {
                "通过${volumeButtonType.displayName}长按（${volumeButtonLongPressThreshold}ms）触发"
            }
            ManualTriggerType.VOLUME_KEY_PRESS -> {
                val preserveDesc = if (volumeKeyPreserveVolume) "（保留原音量）" else "（更新音量）"
                "通过${volumeKeyType.displayName}按下${preserveDesc}触发"
            }
            ManualTriggerType.POWER_BUTTON_LONG_PRESS -> {
                "通过电源键长按（${powerButtonLongPressThreshold}ms）触发"
            }
        }
    }

    override fun matches(triggerCondition: SharedTriggerCondition): Boolean {
        return triggerCondition is ManualTriggerCondition &&
               triggerCondition.triggerType == this.triggerType &&
               when (triggerType) {
                   ManualTriggerType.DYNAMIC_SHORTCUT -> triggerCondition.shortcutName == this.shortcutName
                   ManualTriggerType.STATIC_SHORTCUT, ManualTriggerType.DESKTOP_WIDGET ->
                       triggerCondition.slotIndex == this.slotIndex
                   ManualTriggerType.FLOATING_BUTTON ->
                       triggerCondition.buttonText == this.buttonText
                   ManualTriggerType.FINGERPRINT_GESTURE ->
                       triggerCondition.fingerprintGestureType == this.fingerprintGestureType
                   ManualTriggerType.HOME_BUTTON_LONG_PRESS ->
                       triggerCondition.homeButtonLongPressThreshold == this.homeButtonLongPressThreshold
                   ManualTriggerType.MEDIA_KEY_PRESS ->
                       triggerCondition.mediaKeyType == this.mediaKeyType &&
                       triggerCondition.mediaKeyPressCount == this.mediaKeyPressCount &&
                       triggerCondition.mediaKeyPressTimeout == this.mediaKeyPressTimeout
                   ManualTriggerType.SHORTCUT_OPENING ->
                       triggerCondition.shortcutOpeningName == this.shortcutOpeningName
                   ManualTriggerType.SCREEN_SWIPE ->
                       triggerCondition.swipeStartCorner == this.swipeStartCorner &&
                       triggerCondition.swipeDirection == this.swipeDirection
                   ManualTriggerType.VOLUME_BUTTON_LONG_PRESS ->
                       triggerCondition.volumeButtonType == this.volumeButtonType &&
                       triggerCondition.volumeButtonLongPressThreshold == this.volumeButtonLongPressThreshold
                   ManualTriggerType.VOLUME_KEY_PRESS ->
                       triggerCondition.volumeKeyType == this.volumeKeyType &&
                       triggerCondition.volumeKeyPreserveVolume == this.volumeKeyPreserveVolume
                   ManualTriggerType.POWER_BUTTON_LONG_PRESS ->
                       triggerCondition.powerButtonLongPressThreshold == this.powerButtonLongPressThreshold
               }
    }
}

/**
 * Tasker/Locale插件条件状态类型
 */
enum class TaskerLocaleConditionState(val displayName: String) {
    SATISFIED("条件满足"),
    UNSATISFIED("条件不满足"),
    UNKNOWN("状态未知")
}



/**
 * 触发条件类型
 */
data class SharedTriggerConditionType(
    val type: String,
    val displayName: String,
    val createDefaultCondition: () -> SharedTriggerCondition,
    val createConditionWithId: (String) -> SharedTriggerCondition,
    val createConditionFromJson: ((String, Map<String, Any>) -> SharedTriggerCondition)? = null
)

/**
 * 触发条件注册表
 * 用于注册和获取可用的触发条件类型
 */
object SharedTriggerConditionRegistry {
    private val conditionTypes = mutableListOf<SharedTriggerConditionType>()

    init {

        // 注册统一的应用状态条件
        register(
            type = "app_state",
            displayName = "应用状态",
            createDefaultCondition = { AppStateCondition() },
            createConditionWithId = { id -> AppStateCondition(id) },
            createConditionFromJson = { id, params ->
                val categoryTypeValue = params["categoryType"] as? String ?: "STATE_CHANGE"
                val categoryType = try {
                    AppStateCategoryType.valueOf(categoryTypeValue)
                } catch (e: IllegalArgumentException) {
                    Log.w("SharedTriggerConditionRegistry", "Invalid categoryType: $categoryTypeValue, using default")
                    AppStateCategoryType.STATE_CHANGE
                }

                val stateTypeValue = params["stateType"] as? String ?: "STATE_CHANGED"
                val stateType = try {
                    AppStateType.valueOf(stateTypeValue)
                } catch (e: IllegalArgumentException) {
                    Log.w("SharedTriggerConditionRegistry", "Invalid stateType: $stateTypeValue, using default")
                    AppStateType.STATE_CHANGED
                }

                val detectionModeValue = params["detectionMode"] as? String ?: "ANY_APP"
                val detectionMode = try {
                    AppDetectionMode.valueOf(detectionModeValue)
                } catch (e: IllegalArgumentException) {
                    Log.w("SharedTriggerConditionRegistry", "Invalid detectionMode: $detectionModeValue, using default")
                    AppDetectionMode.ANY_APP
                }

                // 解析新的应用状态触发模式
                val appStateTriggerModeValue = params["appStateTriggerMode"] as? String ?: "ANY_APP"
                val appStateTriggerMode = try {
                    AppStateTriggerMode.valueOf(appStateTriggerModeValue)
                } catch (e: IllegalArgumentException) {
                    Log.w("SharedTriggerConditionRegistry", "Invalid appStateTriggerMode: $appStateTriggerModeValue, using default")
                    AppStateTriggerMode.ANY_APP
                }

                val targetPackageName = params["targetPackageName"] as? String ?: ""
                val targetAppName = params["targetAppName"] as? String ?: ""

                // JSON反序列化已移除，现在使用原生数据类型存储
                val selectedApps = emptyList<SimpleAppInfo>() // 通过存储适配器加载
                val backgroundTimeThresholdMinutes = (params["backgroundTimeThresholdMinutes"] as? Number)?.toInt() ?: 5
                // triggerMode字段已删除，使用appStateTriggerMode替代
                val skipForegroundApp = params["skipForegroundApp"] as? Boolean ?: true
                val skipMusicPlayingApp = params["skipMusicPlayingApp"] as? Boolean ?: true
                val skipVpnApp = params["skipVpnApp"] as? Boolean ?: false
                // JSON反序列化已移除，现在使用原生数据类型存储
                val selectedVpnApps = emptyList<SimpleAppInfo>() // 通过存储适配器加载
                val autoIncludeNewApps = params["autoIncludeNewApps"] as? Boolean ?: false

                // Tasker/Locale插件相关字段
                val pluginAction = params["pluginAction"] as? String ?: "com.twofortyfouram.locale.intent.action.QUERY_CONDITION"
                val pluginExtras = params["pluginExtras"] as? String ?: ""

                val expectedStateValue = params["expectedState"] as? String ?: "SATISFIED"
                val expectedState = try {
                    TaskerLocaleConditionState.valueOf(expectedStateValue)
                } catch (e: IllegalArgumentException) {
                    Log.w("SharedTriggerConditionRegistry", "Invalid expectedState: $expectedStateValue, using default")
                    TaskerLocaleConditionState.SATISFIED
                }

                val checkInterval = (params["checkInterval"] as? Number)?.toInt() ?: 30
                val timeoutSeconds = (params["timeoutSeconds"] as? Number)?.toInt() ?: 10

                // 界面交互相关字段
                val clickTargetText = params["clickTargetText"] as? String ?: ""
                val screenContentText = params["screenContentText"] as? String ?: ""
                val screenContentTriggerModeValue = params["screenContentTriggerMode"] as? String ?: "APPEAR"
                val screenContentTriggerMode = try {
                    ScreenContentTriggerMode.valueOf(screenContentTriggerModeValue)
                } catch (e: IllegalArgumentException) {
                    Log.w("SharedTriggerConditionRegistry", "Invalid screenContentTriggerMode: $screenContentTriggerModeValue, using default")
                    ScreenContentTriggerMode.APPEAR
                }
                val caseSensitive = params["caseSensitive"] as? Boolean ?: false
                val useRegex = params["useRegex"] as? Boolean ?: false

                // 新增界面交互字段
                val interfaceInteractionTypeValue = params["interfaceInteractionType"] as? String ?: "CLICK"
                val interfaceInteractionType = try {
                    InterfaceInteractionType.valueOf(interfaceInteractionTypeValue)
                } catch (e: IllegalArgumentException) {
                    Log.w("SharedTriggerConditionRegistry", "Invalid interfaceInteractionType: $interfaceInteractionTypeValue, using default")
                    InterfaceInteractionType.CLICK
                }

                val screenContentMatchTypeValue = params["screenContentMatchType"] as? String ?: "TEXT_CONTENT"
                val screenContentMatchType = try {
                    ScreenContentMatchType.valueOf(screenContentMatchTypeValue)
                } catch (e: IllegalArgumentException) {
                    Log.w("SharedTriggerConditionRegistry", "Invalid screenContentMatchType: $screenContentMatchTypeValue, using default")
                    ScreenContentMatchType.TEXT_CONTENT
                }

                val includeOverlayLayers = params["includeOverlayLayers"] as? Boolean ?: false

                AppStateCondition(
                    id = id,
                    categoryType = categoryType,
                    stateType = stateType,
                    detectionMode = detectionMode,
                    selectedApps = selectedApps,
                    appStateTriggerMode = appStateTriggerMode,
                    targetPackageName = targetPackageName,
                    targetAppName = targetAppName,
                    backgroundTimeThresholdMinutes = backgroundTimeThresholdMinutes,
                    skipForegroundApp = skipForegroundApp,
                    skipMusicPlayingApp = skipMusicPlayingApp,
                    skipVpnApp = skipVpnApp,
                    selectedVpnApps = selectedVpnApps,
                    autoIncludeNewApps = autoIncludeNewApps,
                    clickTargetText = clickTargetText,
                    screenContentText = screenContentText,
                    screenContentTriggerMode = screenContentTriggerMode,
                    caseSensitive = caseSensitive,
                    useRegex = useRegex,
                    interfaceInteractionType = interfaceInteractionType,
                    screenContentMatchType = screenContentMatchType,
                    includeOverlayLayers = includeOverlayLayers,
                    pluginAction = pluginAction,
                    pluginExtras = pluginExtras,
                    expectedState = expectedState,
                    checkInterval = checkInterval,
                    timeoutSeconds = timeoutSeconds
                )
            }
        )

        // 注册重新设计的电池状态条件
        register(
            type = "battery_state",
            displayName = "电池状态",
            createDefaultCondition = { BatteryStateCondition() },
            createConditionWithId = { id -> BatteryStateCondition(id) },
            createConditionFromJson = { id, params ->
                // 主类型
                val conditionTypeValue = params["conditionType"] as? String ?: "BATTERY_LEVEL"
                val conditionType = BatteryConditionType.valueOf(conditionTypeValue)

                // 电池电量相关参数
                val levelSubTypeValue = params["levelSubType"] as? String ?: "BELOW"
                val levelSubType = BatteryLevelSubType.valueOf(levelSubTypeValue)
                val levelThreshold = params["levelThreshold"] as? Int ?: 20
                val levelChangeThreshold = params["levelChangeThreshold"] as? Int ?: 5

                // 充电状态相关参数
                val chargingSubTypeValue = params["chargingSubType"] as? String ?: "STARTED"
                val chargingSubType = ChargingSubType.valueOf(chargingSubTypeValue)

                // 电池温度相关参数
                val temperatureSubTypeValue = params["temperatureSubType"] as? String ?: "CHANGED"
                val temperatureSubType = TemperatureSubType.valueOf(temperatureSubTypeValue)
                val temperatureThreshold = (params["temperatureThreshold"] as? Number)?.toFloat() ?: 2.0f

                // 电源键相关参数
                val powerButtonSubTypeValue = params["powerButtonSubType"] as? String ?: "PRESSED"
                val powerButtonSubType = PowerButtonSubType.valueOf(powerButtonSubTypeValue)
                val powerButtonPressCount = params["powerButtonPressCount"] as? Int ?: 1

                // 省电模式相关参数
                val powerSaveModeSubTypeValue = params["powerSaveModeSubType"] as? String ?: "ENABLED"
                val powerSaveModeSubType = PowerSaveModeSubType.valueOf(powerSaveModeSubTypeValue)

                BatteryStateCondition(
                    id = id,
                    conditionType = conditionType,
                    levelSubType = levelSubType,
                    levelThreshold = levelThreshold,
                    levelChangeThreshold = levelChangeThreshold,
                    chargingSubType = chargingSubType,
                    temperatureSubType = temperatureSubType,
                    temperatureThreshold = temperatureThreshold,
                    powerButtonSubType = powerButtonSubType,
                    powerButtonPressCount = powerButtonPressCount,
                    powerSaveModeSubType = powerSaveModeSubType
                )
            }
        )

        // 注册统一的硬件状态条件

        register(
            type = "device_event",
            displayName = "设备事件",
            createDefaultCondition = { DeviceEventCondition() },
            createConditionWithId = { id -> DeviceEventCondition(id) },
            createConditionFromJson = { id, params ->
                val eventTypeValue = params["eventType"] as? String ?: "GPS_STATE"
                val eventType = DeviceEventType.fromValue(eventTypeValue)

                DeviceEventCondition(
                    id = id,
                    eventType = eventType,
                    gpsStateType = GpsStateType.fromValue(params["gpsStateType"] as? String ?: "enabled"),
                    logcatComponent = params["logcatComponent"] as? String ?: "",
                    logcatText = params["logcatText"] as? String ?: "",
                    logcatBufferTypes = (params["logcatBufferTypes"] as? List<String>)?.map { LogcatBufferType.fromValue(it) } ?: listOf(LogcatBufferType.MAIN),
                    logcatCaseSensitive = params["logcatCaseSensitive"] as? Boolean ?: false,
                    clipboardText = params["clipboardText"] as? String ?: "",
                    clipboardUseRegex = params["clipboardUseRegex"] as? Boolean ?: false,
                    clipboardCaseSensitive = params["clipboardCaseSensitive"] as? Boolean ?: false,
                    screenEventType = ScreenEventType.fromValue(params["screenEventType"] as? String ?: "on"),
                    dockStateType = DockStateType.fromValue(params["dockStateType"] as? String ?: "any_dock"),
                    intentAction = params["intentAction"] as? String ?: "",
                    intentExtraKey = params["intentExtraKey"] as? String ?: "",
                    intentExtraValue = params["intentExtraValue"] as? String ?: "",
                    simCardStateType = SimCardStateType.fromValue(params["simCardStateType"] as? String ?: "state_changed"),
                    darkThemeStateType = DarkThemeStateType.fromValue(params["darkThemeStateType"] as? String ?: "enabled"),
                    loginFailureThreshold = params["loginFailureThreshold"] as? Int ?: 3,
                    systemSettingTypes = (params["systemSettingTypes"] as? List<String>)?.map { SystemSettingType.fromValue(it) } ?: listOf(SystemSettingType.SYSTEM),
                    systemSettingKey = params["systemSettingKey"] as? String ?: "",
                    systemSettingValue = params["systemSettingValue"] as? String ?: "",
                    systemSettingUseRegex = params["systemSettingUseRegex"] as? Boolean ?: false,
                    autoSyncStateType = AutoSyncStateType.fromValue(params["autoSyncStateType"] as? String ?: "enabled"),
                    notificationEventType = NotificationEventType.fromValue(params["notificationEventType"] as? String ?: "received"),
                    notificationAppSelectionMode = NotificationAppSelectionMode.fromValue(params["notificationAppSelectionMode"] as? String ?: "any"),
                    notificationSelectedApps = params["notificationSelectedApps"] as? List<String> ?: emptyList(),
                    notificationAppIncludeMode = NotificationAppIncludeMode.fromValue(params["notificationAppIncludeMode"] as? String ?: "include"),
                    notificationContentMatchMode = NotificationContentMatchMode.fromValue(params["notificationContentMatchMode"] as? String ?: "any"),
                    notificationTitle = params["notificationTitle"] as? String ?: "",
                    notificationText = params["notificationText"] as? String ?: "",
                    notificationCaseSensitive = params["notificationCaseSensitive"] as? Boolean ?: false,
                    notificationUseRegex = params["notificationUseRegex"] as? Boolean ?: false,
                    notificationIgnoreOngoing = params["notificationIgnoreOngoing"] as? Boolean ?: false,
                    notificationPreventMultipleTrigger = params["notificationPreventMultipleTrigger"] as? Boolean ?: false,
                    notificationSoundMode = NotificationSoundMode.fromValue(params["notificationSoundMode"] as? String ?: "any"),
                    ringerModeType = RingerModeType.fromValue(params["ringerModeType"] as? String ?: "normal"),
                    musicPlaybackType = MusicPlaybackType.fromValue(params["musicPlaybackType"] as? String ?: "started"),
                    airplaneModeType = AirplaneModeType.fromValue(params["airplaneModeType"] as? String ?: "enabled"),
                    volumeStreamType = try {
                        VolumeStreamType.valueOf(params["volumeStreamType"] as? String ?: "MEDIA_MUSIC")
                    } catch (e: Exception) {
                        VolumeStreamType.MEDIA_MUSIC
                    },
                    volumeThreshold = params["volumeThreshold"] as? Int ?: 5,
                    memoryStateType = try {
                        MemoryStateType.valueOf(params["memoryStateType"] as? String ?: "BELOW_THRESHOLD")
                    } catch (e: Exception) {
                        MemoryStateType.BELOW_THRESHOLD
                    },
                    memoryThreshold = params["memoryThreshold"] as? Int ?: 2,
                    isPercentageMode = params["isPercentageMode"] as? Boolean ?: false,
                    memoryChangeThreshold = params["memoryChangeThreshold"] as? Int ?: 10
                )
            }
        )









        // 注册连接状态条件
        register(
            type = "connection_state",
            displayName = "连接状态",
            createDefaultCondition = { ConnectionStateCondition() },
            createConditionWithId = { id -> ConnectionStateCondition(id) },
            createConditionFromJson = { id, params ->
                val connectionTypeValue = params["connectionType"] as? String ?: "NETWORK_GENERAL"
                val connectionType = ConnectionType.valueOf(connectionTypeValue)
                val subTypeValue = params["subType"] as? String ?: "NETWORK_CONNECTED"
                val subType = ConnectionSubType.valueOf(subTypeValue)
                val specificValue = params["specificValue"] as? String ?: ""
                val deviceAddress = params["deviceAddress"] as? String ?: ""
                ConnectionStateCondition(id, connectionType, subType, specificValue, deviceAddress)
            }
        )








        // 注册传感器状态条件
        register(
            type = "sensor_state",
            displayName = "传感器状态",
            createDefaultCondition = { SensorStateCondition() },
            createConditionWithId = { id -> SensorStateCondition(id) },
            createConditionFromJson = { id, params ->
                val sensorTypeValue = params["sensorType"] as? String ?: "LIGHT_SENSOR"
                val sensorType = SensorStateType.valueOf(sensorTypeValue)

                val thresholdTypeValue = params["thresholdType"] as? String ?: "DECREASE_TO"
                val thresholdType = LightThresholdType.valueOf(thresholdTypeValue)
                val luxValue = (params["luxValue"] as? Number)?.toFloat() ?: 10.0f

                val orientationTypeValue = params["orientationType"] as? String ?: "PORTRAIT"
                val orientationType = OrientationType.valueOf(orientationTypeValue)

                val sensitivityValue = params["sensitivity"] as? String ?: "MEDIUM"
                val sensitivity = SensitivityLevel.valueOf(sensitivityValue)
                val shakeThreshold = (params["shakeThreshold"] as? Number)?.toFloat() ?: 12.0f

                val sleepStateTypeValue = params["sleepStateType"] as? String ?: "FALL_ASLEEP"
                val sleepStateType = SleepStateType.valueOf(sleepStateTypeValue)

                val flipTypeValue = params["flipType"] as? String ?: "FACE_UP_TO_DOWN"
                val flipType = FlipType.valueOf(flipTypeValue)

                val proximityTypeValue = params["proximityType"] as? String ?: "NEAR"
                val proximityType = ProximityType.valueOf(proximityTypeValue)

                val activityTypeValue = params["activityType"] as? String ?: "WALKING"
                val activityType = ActivityType.valueOf(activityTypeValue)

                SensorStateCondition(
                    id, sensorType, thresholdType, luxValue, orientationType,
                    sensitivity, shakeThreshold, sleepStateType, flipType,
                    proximityType, activityType
                )
            }
        )





        // 注册通信状态条件
        register(
            type = "communication_state",
            displayName = "通信状态",
            createDefaultCondition = { CommunicationStateCondition() },
            createConditionWithId = { id -> CommunicationStateCondition(id) },
            createConditionFromJson = { id, params ->
                val stateTypeValue = params["stateType"] as? String ?: "SMS_RECEIVED"
                val stateType = CommunicationStateType.valueOf(stateTypeValue)
                val filterTypeValue = params["filterType"] as? String ?: "ANY_CONTACT"
                val filterType = ContactFilterType.valueOf(filterTypeValue)
                val filterModeValue = params["filterMode"] as? String ?: "INCLUDE"
                val filterMode = ContactFilterMode.valueOf(filterModeValue)
                val selectedContactIds = params["selectedContactIds"] as? List<String> ?: emptyList()
                val selectedContactNames = params["selectedContactNames"] as? List<String> ?: emptyList()
                val selectedGroupId = params["selectedGroupId"] as? String ?: ""
                val selectedGroupName = params["selectedGroupName"] as? String ?: ""
                val specificNumber = params["specificNumber"] as? String ?: ""
                CommunicationStateCondition(
                    id = id,
                    stateType = stateType,
                    filterType = filterType,
                    filterMode = filterMode,
                    selectedContactIds = selectedContactIds,
                    selectedContactNames = selectedContactNames,
                    selectedGroupId = selectedGroupId,
                    selectedGroupName = selectedGroupName,
                    specificNumber = specificNumber
                )
            }
        )

        // 注册统一时间条件
        register(
            type = "time_based",
            displayName = "时间条件",
            createDefaultCondition = { TimeBasedCondition() },
            createConditionWithId = { id -> TimeBasedCondition(id) },
            createConditionFromJson = { id, params ->
                val timeConditionTypeValue = params["timeConditionType"] as? String ?: "STOPWATCH"
                val timeConditionType = TimeConditionType.fromValue(timeConditionTypeValue)

                // 秒表参数
                val stopwatchHours = (params["stopwatchHours"] as? Number)?.toInt() ?: 0
                val stopwatchMinutes = (params["stopwatchMinutes"] as? Number)?.toInt() ?: 0
                val stopwatchSeconds = (params["stopwatchSeconds"] as? Number)?.toInt() ?: 30

                // 日出日落参数
                val sunEventTypeValue = params["sunEventType"] as? String ?: "SUNRISE"
                val sunEventType = SunEventType.fromValue(sunEventTypeValue)
                val latitude = (params["latitude"] as? Number)?.toDouble()
                val longitude = (params["longitude"] as? Number)?.toDouble()

                // 日程时间参数
                val currentCalendar = Calendar.getInstance()
                val year = (params["year"] as? Number)?.toInt() ?: currentCalendar.get(Calendar.YEAR)
                val month = (params["month"] as? Number)?.toInt() ?: (currentCalendar.get(Calendar.MONTH) + 1)
                val day = (params["day"] as? Number)?.toInt() ?: currentCalendar.get(Calendar.DAY_OF_MONTH)
                val hour = (params["hour"] as? Number)?.toInt() ?: 8
                val minute = (params["minute"] as? Number)?.toInt() ?: 0
                val timeRepeatModeValue = params["timeRepeatMode"] as? String ?: "once"
                val timeRepeatMode = TimeRepeatMode.fromValue(timeRepeatModeValue)

                // 周期时间参数
                val scheduledRepeatModeValue = params["scheduledRepeatMode"] as? String ?: "once"
                val scheduledRepeatMode = ScheduledRepeatMode.fromValue(scheduledRepeatModeValue)
                val selectedDaysValues = params["selectedDays"] as? List<String> ?: emptyList()
                val selectedDays = selectedDaysValues.mapNotNull { DayOfWeek.fromValue(it) }.toSet()

                // 延迟触发、周期触发参数
                val interval = (params["interval"] as? Number)?.toInt() ?: 30
                val unitValue = params["unit"] as? String ?: "seconds"
                val unit = TimeIntervalUnit.fromValue(unitValue)
                val startTime = (params["startTime"] as? Number)?.toLong() ?: System.currentTimeMillis()

                TimeBasedCondition(
                    id = id,
                    timeConditionType = timeConditionType,
                    stopwatchHours = stopwatchHours,
                    stopwatchMinutes = stopwatchMinutes,
                    stopwatchSeconds = stopwatchSeconds,
                    sunEventType = sunEventType,
                    latitude = latitude,
                    longitude = longitude,
                    year = year,
                    month = month,
                    day = day,
                    hour = hour,
                    minute = minute,
                    timeRepeatMode = timeRepeatMode,
                    scheduledRepeatMode = scheduledRepeatMode,
                    selectedDays = selectedDays,
                    interval = interval,
                    unit = unit,
                    startTime = startTime
                )
            }
        )

        // 注册手动触发条件
        register(
            type = "manual_trigger",
            displayName = "手动触发",
            createDefaultCondition = { ManualTriggerCondition() },
            createConditionWithId = { id -> ManualTriggerCondition(id) },
            createConditionFromJson = { id, params ->
                val triggerTypeValue = params["triggerType"] as? String ?: "DYNAMIC_SHORTCUT"
                val triggerType = try {
                    // 首先尝试按枚举名称解析
                    ManualTriggerType.valueOf(triggerTypeValue)
                } catch (e: IllegalArgumentException) {
                    // 如果失败，尝试按value解析
                    ManualTriggerType.fromValue(triggerTypeValue)
                }
                val slotIndex = (params["slotIndex"] as? Number)?.toInt() ?: 0
                val buttonText = params["buttonText"] as? String ?: ""
                val shortcutName = params["shortcutName"] as? String ?: ""
                val buttonSize = (params["buttonSize"] as? Number)?.toInt() ?: 60
                val buttonAlpha = (params["buttonAlpha"] as? Number)?.toFloat() ?: 0.8f
                val fingerprintGestureTypeValue = params["fingerprintGestureType"] as? String ?: "TAP"
                val fingerprintGestureType = try {
                    // 首先尝试按枚举名称解析
                    FingerprintGestureType.valueOf(fingerprintGestureTypeValue)
                } catch (e: IllegalArgumentException) {
                    // 如果失败，尝试按value解析
                    FingerprintGestureType.fromValue(fingerprintGestureTypeValue)
                }

                val homeButtonLongPressThreshold = (params["homeButtonLongPressThreshold"] as? Number)?.toLong() ?: 1000L

                val mediaKeyTypeValue = params["mediaKeyType"] as? String ?: "PLAY_PAUSE"
                val mediaKeyType = try {
                    MediaKeyType.valueOf(mediaKeyTypeValue)
                } catch (e: IllegalArgumentException) {
                    MediaKeyType.fromValue(mediaKeyTypeValue)
                }
                val mediaKeyPressCount = (params["mediaKeyPressCount"] as? Number)?.toInt() ?: 1
                val mediaKeyPressTimeout = (params["mediaKeyPressTimeout"] as? Number)?.toLong() ?: 1000L

                val shortcutOpeningName = params["shortcutOpeningName"] as? String ?: ""

                val swipeStartCornerValue = params["swipeStartCorner"] as? String ?: "TOP_LEFT"
                val swipeStartCorner = try {
                    SwipeStartCorner.valueOf(swipeStartCornerValue)
                } catch (e: IllegalArgumentException) {
                    SwipeStartCorner.fromValue(swipeStartCornerValue)
                }

                val swipeDirectionValue = params["swipeDirection"] as? String ?: "HORIZONTAL"
                val swipeDirection = try {
                    SwipeDirection.valueOf(swipeDirectionValue)
                } catch (e: IllegalArgumentException) {
                    SwipeDirection.fromValue(swipeDirectionValue)
                }

                val volumeButtonTypeValue = params["volumeButtonType"] as? String ?: "VOLUME_UP"
                val volumeButtonType = try {
                    VolumeButtonType.valueOf(volumeButtonTypeValue)
                } catch (e: IllegalArgumentException) {
                    VolumeButtonType.fromValue(volumeButtonTypeValue)
                }
                val volumeButtonLongPressThreshold = (params["volumeButtonLongPressThreshold"] as? Number)?.toLong() ?: 1000L

                val volumeKeyTypeValue = params["volumeKeyType"] as? String ?: "VOLUME_UP"
                val volumeKeyType = try {
                    VolumeButtonType.valueOf(volumeKeyTypeValue)
                } catch (e: IllegalArgumentException) {
                    VolumeButtonType.fromValue(volumeKeyTypeValue)
                }
                val volumeKeyPreserveVolume = params["volumeKeyPreserveVolume"] as? Boolean ?: false

                val powerButtonLongPressThreshold = (params["powerButtonLongPressThreshold"] as? Number)?.toLong() ?: 1000L

                ManualTriggerCondition(
                    id = id,
                    triggerType = triggerType,
                    slotIndex = slotIndex,
                    buttonText = buttonText,
                    shortcutName = shortcutName,
                    buttonSize = buttonSize,
                    buttonAlpha = buttonAlpha,
                    fingerprintGestureType = fingerprintGestureType,
                    homeButtonLongPressThreshold = homeButtonLongPressThreshold,
                    mediaKeyType = mediaKeyType,
                    mediaKeyPressCount = mediaKeyPressCount,
                    mediaKeyPressTimeout = mediaKeyPressTimeout,
                    shortcutOpeningName = shortcutOpeningName,
                    swipeStartCorner = swipeStartCorner,
                    swipeDirection = swipeDirection,
                    volumeButtonType = volumeButtonType,
                    volumeButtonLongPressThreshold = volumeButtonLongPressThreshold,
                    volumeKeyType = volumeKeyType,
                    volumeKeyPreserveVolume = volumeKeyPreserveVolume,
                    powerButtonLongPressThreshold = powerButtonLongPressThreshold
                )
            }
        )



    }

    /**
     * 注册新的触发条件类型
     */
    fun register(
        type: String,
        displayName: String,
        createDefaultCondition: () -> SharedTriggerCondition,
        createConditionWithId: (String) -> SharedTriggerCondition,
        createConditionFromJson: ((String, Map<String, Any>) -> SharedTriggerCondition)? = null
    ) {
        conditionTypes.add(
            SharedTriggerConditionType(
                type = type,
                displayName = displayName,
                createDefaultCondition = createDefaultCondition,
                createConditionWithId = createConditionWithId,
                createConditionFromJson = createConditionFromJson
            )
        )
    }

    /**
     * 获取所有注册的触发条件类型
     */
    fun getConditionTypes(): List<SharedTriggerConditionType> = conditionTypes

    /**
     * 根据类型获取触发条件类型
     */
    fun getConditionTypeByType(type: String): SharedTriggerConditionType? {
        return conditionTypes.find { it.type == type }
    }

    /**
     * 创建带有指定ID的条件实例
     *
     * @param type 条件类型
     * @param id 条件ID
     * @return 创建的条件实例，如果类型不存在则返回null
     */
    fun createConditionWithId(type: String, id: String): SharedTriggerCondition? {
        val conditionType = getConditionTypeByType(type) ?: return null
        return conditionType.createConditionWithId(id)
    }

    /**
     * 从JSON数据创建条件实例
     *
     * @param type 条件类型
     * @param id 条件ID
     * @param params 条件参数
     * @return 创建的条件实例，如果类型不存在则返回null
     */
    fun createConditionFromJson(type: String, id: String, params: Map<String, Any>): SharedTriggerCondition? {
        val conditionType = getConditionTypeByType(type) ?: return null
        return conditionType.createConditionFromJson?.invoke(id, params)
            ?: conditionType.createConditionWithId(id)
    }
}











































