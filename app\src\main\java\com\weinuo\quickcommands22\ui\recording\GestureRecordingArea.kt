package com.weinuo.quickcommands22.ui.recording

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.input.pointer.*
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.weinuo.quickcommands22.model.TouchEvent
import kotlinx.coroutines.delay

/**
 * 手势录制区域组件
 * 提供触摸事件捕获和可视化反馈
 */
@Composable
fun GestureRecordingArea(
    isRecording: Boolean,
    onStartRecording: () -> Unit,
    onStopRecording: () -> Unit,
    onTouchEvent: (TouchEvent) -> Unit,
    modifier: Modifier = Modifier
) {
    // 存储所有已完成的手势轨迹
    var completedGestures by remember { mutableStateOf(listOf<List<Offset>>()) }
    // 当前正在进行的手势轨迹
    var currentGesturePoints by remember { mutableStateOf(listOf<Offset>()) }
    var currentTouch by remember { mutableStateOf<Offset?>(null) }
    var showInstructions by remember { mutableStateOf(true) }
    var hasUserInteracted by remember { mutableStateOf(false) }
    var gestureStartPoint by remember { mutableStateOf<Offset?>(null) }
    var isCurrentlyDragging by remember { mutableStateOf(false) }

    // 当开始录制时重置交互状态
    LaunchedEffect(isRecording) {
        if (isRecording) {
            hasUserInteracted = false
            showInstructions = true
        }
    }

    Box(
        modifier = modifier
            .background(Color.Transparent)
    ) {
        // 录制区域画布
        Canvas(
            modifier = Modifier
                .fillMaxSize()
                .pointerInput(isRecording) {
                    if (isRecording) {
                        // 使用awaitPointerEventScope来处理更复杂的触摸事件
                        awaitPointerEventScope {
                            while (true) {
                                val event = awaitPointerEvent()

                                when (event.type) {
                                    PointerEventType.Press -> {
                                        // 触摸开始
                                        val offset = event.changes.first().position
                                        hasUserInteracted = true
                                        showInstructions = false

                                        gestureStartPoint = offset
                                        currentTouch = offset
                                        // 开始新的手势，清空当前手势轨迹
                                        currentGesturePoints = listOf(offset)
                                        isCurrentlyDragging = false
                                    }

                                    PointerEventType.Move -> {
                                        // 触摸移动
                                        val offset = event.changes.first().position
                                        gestureStartPoint?.let { startOffset ->
                                            val distance = (offset - startOffset).getDistance()

                                            // 如果移动距离超过阈值，认为是滑动
                                            if (distance > 20f && !isCurrentlyDragging) {
                                                isCurrentlyDragging = true
                                            }

                                            if (isCurrentlyDragging) {
                                                currentTouch = offset
                                                // 只在拖拽时添加轨迹点
                                                currentGesturePoints = currentGesturePoints + offset
                                            }
                                        }
                                    }

                                    PointerEventType.Release -> {
                                        // 触摸结束
                                        gestureStartPoint?.let { startOffset ->
                                            val endOffset = event.changes.first().position
                                            val distance = (endOffset - startOffset).getDistance()

                                            if (distance <= 20f) {
                                                // 移动距离很小，认为是点击
                                                val position = com.weinuo.quickcommands22.model.TouchPosition(
                                                    startX = startOffset.x / size.width,
                                                    startY = startOffset.y / size.height
                                                )

                                                onTouchEvent(
                                                    TouchEvent(
                                                        type = com.weinuo.quickcommands22.model.TouchEventType.TAP,
                                                        position = position
                                                    )
                                                )

                                                // 点击不需要保存轨迹，只显示一个点的效果
                                                completedGestures = completedGestures + listOf(listOf(startOffset))
                                            } else {
                                                // 移动距离较大，认为是滑动
                                                val position = com.weinuo.quickcommands22.model.TouchPosition(
                                                    startX = startOffset.x / size.width,
                                                    startY = startOffset.y / size.height,
                                                    endX = endOffset.x / size.width,
                                                    endY = endOffset.y / size.height
                                                )

                                                onTouchEvent(
                                                    TouchEvent(
                                                        type = com.weinuo.quickcommands22.model.TouchEventType.SWIPE,
                                                        position = position,
                                                        duration = 300L
                                                    )
                                                )

                                                // 保存滑动轨迹
                                                completedGestures = completedGestures + listOf(currentGesturePoints)
                                            }
                                        }

                                        // 重置状态
                                        gestureStartPoint = null
                                        currentTouch = null
                                        currentGesturePoints = emptyList()
                                        isCurrentlyDragging = false
                                    }
                                }
                            }
                        }
                    }
                }
        ) {
            // 绘制所有已完成的手势轨迹
            drawCompletedGestures(completedGestures)
            // 绘制当前正在进行的手势轨迹
            drawCurrentGesture(currentGesturePoints, currentTouch)
        }

        // 中央提示区域（仅在需要时显示）
        if (showInstructions) {
            Box(
                modifier = Modifier.align(Alignment.Center),
                contentAlignment = Alignment.Center
            ) {
                if (!isRecording) {
                    // 未录制状态的提示
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.Center
                    ) {
                        Text(
                            text = "点击开始录制手势操作",
                            color = Color.White,
                            fontSize = 20.sp,
                            fontWeight = FontWeight.Medium
                        )

                        Spacer(modifier = Modifier.height(24.dp))

                        Button(
                            onClick = onStartRecording,
                            colors = ButtonDefaults.buttonColors(
                                containerColor = Color.Red,
                                contentColor = Color.White
                            ),
                            modifier = Modifier.size(width = 140.dp, height = 56.dp)
                        ) {
                            Text(
                                text = "开始录制",
                                fontSize = 18.sp,
                                fontWeight = FontWeight.Bold
                            )
                        }
                    }
                } else if (!hasUserInteracted) {
                    // 录制状态但用户还未开始操作的提示
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.Center
                    ) {
                        Text(
                            text = "在屏幕上进行手势操作",
                            color = Color.White,
                            fontSize = 18.sp,
                            fontWeight = FontWeight.Medium
                        )

                        Spacer(modifier = Modifier.height(8.dp))

                        Text(
                            text = "开始操作后此提示将自动消失",
                            color = Color.White.copy(alpha = 0.7f),
                            fontSize = 14.sp
                        )
                    }
                }
            }
        }

        // 底部停止录制按钮（仅在录制时显示）
        if (isRecording) {
            Box(
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .padding(bottom = 48.dp)
            ) {
                Button(
                    onClick = onStopRecording,
                    colors = ButtonDefaults.buttonColors(
                        containerColor = Color.Red.copy(alpha = 0.8f),
                        contentColor = Color.White
                    ),
                    modifier = Modifier.size(width = 140.dp, height = 56.dp)
                ) {
                    Text(
                        text = "停止录制",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold
                    )
                }
            }
        }

    }

    // 清除触摸轨迹的效果
    LaunchedEffect(isRecording) {
        if (!isRecording) {
            delay(1000)
            completedGestures = emptyList()
            currentGesturePoints = emptyList()
            showInstructions = true
            hasUserInteracted = false
            gestureStartPoint = null
            currentTouch = null
            isCurrentlyDragging = false
        }
    }
}

/**
 * 绘制所有已完成的手势轨迹
 */
private fun DrawScope.drawCompletedGestures(completedGestures: List<List<Offset>>) {
    completedGestures.forEach { gesturePoints ->
        drawSingleGesture(gesturePoints, alpha = 0.6f)
    }
}

/**
 * 绘制当前正在进行的手势轨迹
 */
private fun DrawScope.drawCurrentGesture(
    currentGesturePoints: List<Offset>,
    currentTouch: Offset?
) {
    if (currentGesturePoints.isNotEmpty()) {
        drawSingleGesture(currentGesturePoints, alpha = 1.0f)
    }

    // 绘制当前触摸点
    currentTouch?.let { point ->
        drawCircle(
            color = Color.Red,
            radius = 16.dp.toPx(),
            center = point
        )
        drawCircle(
            color = Color.White,
            radius = 8.dp.toPx(),
            center = point
        )
    }
}

/**
 * 绘制单个手势轨迹
 */
private fun DrawScope.drawSingleGesture(
    gesturePoints: List<Offset>,
    alpha: Float
) {
    if (gesturePoints.isEmpty()) return

    if (gesturePoints.size == 1) {
        // 单点（点击）
        val point = gesturePoints.first()
        drawCircle(
            color = Color.Red.copy(alpha = alpha),
            radius = 12.dp.toPx(),
            center = point
        )
        drawCircle(
            color = Color.White.copy(alpha = alpha),
            radius = 6.dp.toPx(),
            center = point
        )
    } else {
        // 多点（滑动轨迹）
        // 绘制轨迹线
        for (i in 1 until gesturePoints.size) {
            val start = gesturePoints[i - 1]
            val end = gesturePoints[i]
            val lineAlpha = (i.toFloat() / gesturePoints.size) * 0.8f + 0.2f

            drawLine(
                color = Color.Red.copy(alpha = alpha * lineAlpha),
                start = start,
                end = end,
                strokeWidth = 8.dp.toPx()
            )
        }

        // 绘制起始点
        val startPoint = gesturePoints.first()
        drawCircle(
            color = Color.Green.copy(alpha = alpha),
            radius = 10.dp.toPx(),
            center = startPoint
        )

        // 绘制结束点
        val endPoint = gesturePoints.last()
        drawCircle(
            color = Color.Blue.copy(alpha = alpha),
            radius = 10.dp.toPx(),
            center = endPoint
        )
    }
}


