package com.weinuo.quickcommands22.service

import android.content.Context
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.KeyEvent
import com.weinuo.quickcommands22.model.ManualTriggerCondition
import com.weinuo.quickcommands22.model.ManualTriggerType
import com.weinuo.quickcommands22.model.MediaKeyType
import com.weinuo.quickcommands22.repository.ConditionRepository
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * 媒体键按下检测器
 *
 * 负责检测媒体键的按下操作，支持多次按下计数和超时检测。
 * 支持的媒体键类型包括：播放/暂停、下一首、上一首、音量加、音量减。
 *
 * 功能特性：
 * - 检测各种媒体键按下事件
 * - 支持1-3次按下计数
 * - 可配置的超时时间
 * - 防止误触发机制
 * - 与条件评估器集成
 *
 * @param context Android上下文
 * @param conditionRepository 条件仓库，用于获取相关条件
 */
class MediaKeyPressDetector(
    private val context: Context,
    private val conditionRepository: ConditionRepository
) {

    companion object {
        private const val TAG = "MediaKeyPressDetector"

        // 默认超时时间（毫秒）
        private const val DEFAULT_PRESS_TIMEOUT = 1000L

        // 最小超时时间
        private const val MIN_PRESS_TIMEOUT = 500L

        // 最大超时时间
        private const val MAX_PRESS_TIMEOUT = 5000L
    }

    // 主线程Handler，用于处理超时检测
    private val mainHandler = Handler(Looper.getMainLooper())

    // 协程作用域
    private val coroutineScope = CoroutineScope(Dispatchers.Main)

    // 已注册的媒体键按下条件
    private val registeredConditions = mutableSetOf<ManualTriggerCondition>()

    // 按键计数器 - 每个条件对应一个计数器
    private val pressCounters = mutableMapOf<String, Int>()

    // 超时任务 - 每个条件对应一个超时任务
    private val timeoutTasks = mutableMapOf<String, Runnable>()

    /**
     * 媒体键按下回调接口
     */
    interface MediaKeyPressCallback {
        fun onMediaKeyPress(condition: ManualTriggerCondition)
    }

    // 注册的回调
    private val callbacks = mutableSetOf<MediaKeyPressCallback>()

    /**
     * 注册媒体键按下回调
     */
    fun registerCallback(callback: MediaKeyPressCallback) {
        callbacks.add(callback)
        Log.d(TAG, "注册媒体键按下回调，当前回调数量: ${callbacks.size}")
    }

    /**
     * 取消注册媒体键按下回调
     */
    fun unregisterCallback(callback: MediaKeyPressCallback) {
        callbacks.remove(callback)
        Log.d(TAG, "取消注册媒体键按下回调，当前回调数量: ${callbacks.size}")
    }

    /**
     * 处理媒体键事件
     *
     * @param keyCode 按键代码
     * @param event 按键事件
     * @return 是否处理了该事件
     */
    fun onMediaKeyEvent(keyCode: Int, event: KeyEvent): Boolean {
        // 只处理按键释放事件，避免重复触发
        if (event.action != KeyEvent.ACTION_UP) {
            return false
        }

        val mediaKeyType = getMediaKeyType(keyCode) ?: return false

        Log.d(TAG, "检测到媒体键按下: ${mediaKeyType.displayName}")

        // 加载相关条件
        loadMediaKeyPressConditions()

        // 处理匹配的条件
        val matchingConditions = registeredConditions.filter { it.mediaKeyType == mediaKeyType }
        matchingConditions.forEach { condition ->
            handleMediaKeyPress(condition)
        }

        return matchingConditions.isNotEmpty()
    }

    /**
     * 根据按键代码获取媒体键类型
     */
    private fun getMediaKeyType(keyCode: Int): MediaKeyType? {
        return when (keyCode) {
            KeyEvent.KEYCODE_MEDIA_PLAY_PAUSE -> MediaKeyType.PLAY_PAUSE
            KeyEvent.KEYCODE_MEDIA_NEXT -> MediaKeyType.NEXT_TRACK
            KeyEvent.KEYCODE_MEDIA_PREVIOUS -> MediaKeyType.PREVIOUS_TRACK
            KeyEvent.KEYCODE_VOLUME_UP -> MediaKeyType.VOLUME_UP
            KeyEvent.KEYCODE_VOLUME_DOWN -> MediaKeyType.VOLUME_DOWN
            else -> null
        }
    }

    /**
     * 处理媒体键按下
     */
    private fun handleMediaKeyPress(condition: ManualTriggerCondition) {
        val conditionId = condition.id
        val currentCount = pressCounters.getOrDefault(conditionId, 0) + 1
        pressCounters[conditionId] = currentCount

        Log.d(TAG, "媒体键按下计数: $currentCount/${condition.mediaKeyPressCount} (条件: $conditionId)")

        // 取消之前的超时任务
        timeoutTasks[conditionId]?.let { task ->
            mainHandler.removeCallbacks(task)
        }

        // 检查是否达到目标按下次数
        if (currentCount >= condition.mediaKeyPressCount) {
            // 达到目标次数，触发条件
            triggerMediaKeyCondition(condition)
            resetConditionCounter(conditionId)
        } else {
            // 未达到目标次数，设置超时任务
            val timeout = condition.mediaKeyPressTimeout.coerceIn(
                MIN_PRESS_TIMEOUT,
                MAX_PRESS_TIMEOUT
            )

            val timeoutTask = Runnable {
                Log.d(TAG, "媒体键按下超时，重置计数器 (条件: $conditionId)")
                resetConditionCounter(conditionId)
            }

            timeoutTasks[conditionId] = timeoutTask
            mainHandler.postDelayed(timeoutTask, timeout)
        }
    }

    /**
     * 重置条件计数器
     */
    private fun resetConditionCounter(conditionId: String) {
        pressCounters.remove(conditionId)
        timeoutTasks[conditionId]?.let { task ->
            mainHandler.removeCallbacks(task)
        }
        timeoutTasks.remove(conditionId)
    }

    /**
     * 加载媒体键按下条件
     */
    private fun loadMediaKeyPressConditions() {
        coroutineScope.launch {
            try {
                val allConditions = conditionRepository.loadAllConditions()
                val mediaKeyConditions = allConditions.filterIsInstance<ManualTriggerCondition>()
                    .filter { it.triggerType == ManualTriggerType.MEDIA_KEY_PRESS }

                registeredConditions.clear()
                registeredConditions.addAll(mediaKeyConditions)

                Log.d(TAG, "加载了 ${registeredConditions.size} 个媒体键按下条件")
            } catch (e: Exception) {
                Log.e(TAG, "加载媒体键按下条件失败", e)
            }
        }
    }

    /**
     * 触发媒体键条件
     */
    private fun triggerMediaKeyCondition(condition: ManualTriggerCondition) {
        Log.d(TAG, "触发媒体键按下条件: ${condition.id} (${condition.mediaKeyType.displayName}, ${condition.mediaKeyPressCount}次)")

        // 通知所有注册的回调
        callbacks.forEach { callback ->
            try {
                callback.onMediaKeyPress(condition)
            } catch (e: Exception) {
                Log.e(TAG, "媒体键按下回调执行失败", e)
            }
        }
    }

    /**
     * 清理资源
     */
    fun cleanup() {
        // 取消所有待执行的超时任务
        timeoutTasks.values.forEach { task ->
            mainHandler.removeCallbacks(task)
        }
        timeoutTasks.clear()

        // 清理状态
        pressCounters.clear()
        registeredConditions.clear()
        callbacks.clear()

        Log.d(TAG, "媒体键按下检测器已清理")
    }
}
