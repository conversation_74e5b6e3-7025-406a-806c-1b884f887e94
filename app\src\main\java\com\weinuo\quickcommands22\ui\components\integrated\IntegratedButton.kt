package com.weinuo.quickcommands22.ui.components.integrated

import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.collectIsPressedAsState
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.weinuo.quickcommands22.ui.theme.config.ButtonConfig
import com.weinuo.quickcommands22.ui.theme.config.IconPosition
import com.weinuo.quickcommands22.ui.theme.manager.ThemeManager

/**
 * 整合设计风格的按钮组件
 *
 * 特点：
 * - 无阴影设计
 * - 大圆角
 * - 流畅的交互动画
 * - 统一的视觉体验
 * - 支持多种样式变体
 */
@Composable
fun IntegratedButton(
    config: ButtonConfig,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    val themeManager = remember { ThemeManager.getInstance(context) }
    val styleConfig = themeManager.currentThemeProvider.value.getStyleConfiguration()
    
    // 交互状态
    val interactionSource = remember { MutableInteractionSource() }
    val isPressed by interactionSource.collectIsPressedAsState()
    
    // 动画状态
    val animatedScale by animateFloatAsState(
        targetValue = if (isPressed) 0.98f else 1f,
        animationSpec = tween(durationMillis = 150),
        label = "button_scale"
    )

    Button(
        onClick = config.onClick,
        modifier = modifier
            .scale(animatedScale)
            .height(48.dp),
        enabled = config.enabled,
        shape = RoundedCornerShape(styleConfig.cornerRadius.medium),
        colors = ButtonDefaults.buttonColors(
            containerColor = config.backgroundColor ?: MaterialTheme.colorScheme.primary,
            contentColor = config.contentColor ?: MaterialTheme.colorScheme.onPrimary,
            disabledContainerColor = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.12f),
            disabledContentColor = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.38f)
        ),
        elevation = ButtonDefaults.buttonElevation(
            defaultElevation = 0.dp, // 整合设计不使用阴影
            pressedElevation = 0.dp,
            focusedElevation = 0.dp,
            hoveredElevation = 0.dp,
            disabledElevation = 0.dp
        ),
        border = config.border as? BorderStroke,
        contentPadding = PaddingValues(
            horizontal = styleConfig.spacing.medium,
            vertical = styleConfig.spacing.small
        ),
        interactionSource = interactionSource
    ) {
        ButtonContent(
            text = config.text,
            icon = config.icon,
            iconPosition = config.iconPosition
        )
    }
}

/**
 * 整合设计风格的轮廓按钮
 */
@Composable
fun IntegratedOutlinedButton(
    config: ButtonConfig,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    val themeManager = remember { ThemeManager.getInstance(context) }
    val styleConfig = themeManager.currentThemeProvider.value.getStyleConfiguration()
    
    // 交互状态
    val interactionSource = remember { MutableInteractionSource() }
    val isPressed by interactionSource.collectIsPressedAsState()
    
    // 动画状态
    val animatedScale by animateFloatAsState(
        targetValue = if (isPressed) 0.98f else 1f,
        animationSpec = tween(durationMillis = 150),
        label = "outlined_button_scale"
    )

    OutlinedButton(
        onClick = config.onClick,
        modifier = modifier
            .scale(animatedScale)
            .height(48.dp),
        enabled = config.enabled,
        shape = RoundedCornerShape(styleConfig.cornerRadius.medium),
        colors = ButtonDefaults.outlinedButtonColors(
            containerColor = Color.Transparent,
            contentColor = config.contentColor ?: MaterialTheme.colorScheme.primary,
            disabledContainerColor = Color.Transparent,
            disabledContentColor = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.38f)
        ),
        elevation = ButtonDefaults.buttonElevation(defaultElevation = 0.dp),
        border = BorderStroke(
            width = 1.dp,
            color = config.contentColor ?: MaterialTheme.colorScheme.outline
        ),
        contentPadding = PaddingValues(
            horizontal = styleConfig.spacing.medium,
            vertical = styleConfig.spacing.small
        ),
        interactionSource = interactionSource
    ) {
        ButtonContent(
            text = config.text,
            icon = config.icon,
            iconPosition = config.iconPosition
        )
    }
}

/**
 * 整合设计风格的文本按钮
 */
@Composable
fun IntegratedTextButton(
    config: ButtonConfig,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    val themeManager = remember { ThemeManager.getInstance(context) }
    val styleConfig = themeManager.currentThemeProvider.value.getStyleConfiguration()
    
    // 交互状态
    val interactionSource = remember { MutableInteractionSource() }
    val isPressed by interactionSource.collectIsPressedAsState()
    
    // 动画状态
    val animatedScale by animateFloatAsState(
        targetValue = if (isPressed) 0.98f else 1f,
        animationSpec = tween(durationMillis = 150),
        label = "text_button_scale"
    )

    TextButton(
        onClick = config.onClick,
        modifier = modifier
            .scale(animatedScale)
            .height(40.dp), // 文本按钮使用较小的高度
        enabled = config.enabled,
        shape = RoundedCornerShape(styleConfig.cornerRadius.medium),
        colors = ButtonDefaults.textButtonColors(
            containerColor = Color.Transparent,
            contentColor = config.contentColor ?: MaterialTheme.colorScheme.primary,
            disabledContainerColor = Color.Transparent,
            disabledContentColor = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.38f)
        ),
        elevation = ButtonDefaults.buttonElevation(defaultElevation = 0.dp),
        contentPadding = PaddingValues(
            horizontal = styleConfig.spacing.medium,
            vertical = styleConfig.spacing.small
        ),
        interactionSource = interactionSource
    ) {
        ButtonContent(
            text = config.text,
            icon = config.icon,
            iconPosition = config.iconPosition
        )
    }
}

/**
 * 整合设计风格的填充色调按钮
 */
@Composable
fun IntegratedFilledTonalButton(
    config: ButtonConfig,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    val themeManager = remember { ThemeManager.getInstance(context) }
    val styleConfig = themeManager.currentThemeProvider.value.getStyleConfiguration()
    
    // 交互状态
    val interactionSource = remember { MutableInteractionSource() }
    val isPressed by interactionSource.collectIsPressedAsState()
    
    // 动画状态
    val animatedScale by animateFloatAsState(
        targetValue = if (isPressed) 0.98f else 1f,
        animationSpec = tween(durationMillis = 150),
        label = "filled_tonal_button_scale"
    )

    FilledTonalButton(
        onClick = config.onClick,
        modifier = modifier
            .scale(animatedScale)
            .height(48.dp),
        enabled = config.enabled,
        shape = RoundedCornerShape(styleConfig.cornerRadius.medium),
        colors = ButtonDefaults.filledTonalButtonColors(
            containerColor = config.backgroundColor ?: MaterialTheme.colorScheme.secondaryContainer,
            contentColor = config.contentColor ?: MaterialTheme.colorScheme.onSecondaryContainer,
            disabledContainerColor = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.12f),
            disabledContentColor = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.38f)
        ),
        elevation = ButtonDefaults.filledTonalButtonElevation(defaultElevation = 0.dp),
        contentPadding = PaddingValues(
            horizontal = styleConfig.spacing.medium,
            vertical = styleConfig.spacing.small
        ),
        interactionSource = interactionSource
    ) {
        ButtonContent(
            text = config.text,
            icon = config.icon,
            iconPosition = config.iconPosition
        )
    }
}

/**
 * 按钮内容组合
 */
@Composable
private fun ButtonContent(
    text: String,
    icon: ImageVector? = null,
    iconPosition: IconPosition = IconPosition.START
) {
    when {
        icon == null -> {
            // 仅文本
            Text(
                text = text,
                style = MaterialTheme.typography.labelLarge.copy(
                    fontWeight = FontWeight.Medium
                ),
                maxLines = 1
            )
        }
        iconPosition == IconPosition.START -> {
            // 图标在前
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.Center
            ) {
                Icon(
                    imageVector = icon,
                    contentDescription = null,
                    modifier = Modifier.size(18.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = text,
                    style = MaterialTheme.typography.labelLarge.copy(
                        fontWeight = FontWeight.Medium
                    ),
                    maxLines = 1
                )
            }
        }
        iconPosition == IconPosition.END -> {
            // 图标在后
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.Center
            ) {
                Text(
                    text = text,
                    style = MaterialTheme.typography.labelLarge.copy(
                        fontWeight = FontWeight.Medium
                    ),
                    maxLines = 1
                )
                Spacer(modifier = Modifier.width(8.dp))
                Icon(
                    imageVector = icon,
                    contentDescription = null,
                    modifier = Modifier.size(18.dp)
                )
            }
        }
        iconPosition == IconPosition.TOP -> {
            // 图标在上
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                Icon(
                    imageVector = icon,
                    contentDescription = null,
                    modifier = Modifier.size(18.dp)
                )
                Spacer(modifier = Modifier.height(4.dp))
                Text(
                    text = text,
                    style = MaterialTheme.typography.labelSmall.copy(
                        fontWeight = FontWeight.Medium
                    ),
                    maxLines = 1
                )
            }
        }
    }
}

// IconPosition 枚举已在 ComponentConfigs.kt 中定义
