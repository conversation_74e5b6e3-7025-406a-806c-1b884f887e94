package com.weinuo.quickcommands22.ui.theme.manager

import android.content.Context
import android.util.Log
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.util.concurrent.atomic.AtomicLong
import java.util.concurrent.atomic.AtomicInteger

/**
 * 主题性能监控器
 * 
 * 监控主题系统的性能指标，确保：
 * 1. 主题切换时间在可接受范围内
 * 2. 内存使用量不会无限增长
 * 3. 后台运行时资源消耗最小
 */
object ThemePerformanceMonitor {
    
    private const val TAG = "ThemePerformanceMonitor"
    private const val MAX_THEME_SWITCH_TIME_MS = 100L // 主题切换最大允许时间
    private const val MAX_MEMORY_INCREASE_MB = 5L // 主题系统最大内存增量
    
    // 性能计数器
    private val themeSwitchCount = AtomicInteger(0)
    private val totalSwitchTime = AtomicLong(0L)
    private val cacheHitCount = AtomicInteger(0)
    private val cacheMissCount = AtomicInteger(0)
    private val backgroundCleanupCount = AtomicInteger(0)
    
    // 内存监控
    private var initialMemoryUsage = 0L
    private var isMonitoringEnabled = false
    
    /**
     * 开始性能监控
     */
    fun startMonitoring(context: Context) {
        if (isMonitoringEnabled) return
        
        isMonitoringEnabled = true
        initialMemoryUsage = getCurrentMemoryUsage()
        
        Log.d(TAG, "开始主题性能监控，初始内存使用: ${initialMemoryUsage / 1024 / 1024}MB")
        
        // 定期输出性能报告
        CoroutineScope(Dispatchers.Default).launch {
            while (isMonitoringEnabled) {
                kotlinx.coroutines.delay(60000) // 每分钟输出一次
                logPerformanceReport()
            }
        }
    }
    
    /**
     * 停止性能监控
     */
    fun stopMonitoring() {
        isMonitoringEnabled = false
        Log.d(TAG, "停止主题性能监控")
    }
    
    /**
     * 记录主题切换性能
     */
    fun recordThemeSwitch(switchTimeMs: Long) {
        themeSwitchCount.incrementAndGet()
        totalSwitchTime.addAndGet(switchTimeMs)
        
        if (switchTimeMs > MAX_THEME_SWITCH_TIME_MS) {
            Log.w(TAG, "主题切换时间过长: ${switchTimeMs}ms (最大允许: ${MAX_THEME_SWITCH_TIME_MS}ms)")
        }
        
        Log.d(TAG, "主题切换完成，耗时: ${switchTimeMs}ms")
    }
    
    /**
     * 记录缓存命中
     */
    fun recordCacheHit() {
        cacheHitCount.incrementAndGet()
    }
    
    /**
     * 记录缓存未命中
     */
    fun recordCacheMiss() {
        cacheMissCount.incrementAndGet()
    }
    
    /**
     * 记录后台清理
     */
    fun recordBackgroundCleanup() {
        backgroundCleanupCount.incrementAndGet()
        Log.d(TAG, "执行后台清理，总次数: ${backgroundCleanupCount.get()}")
    }
    
    /**
     * 检查内存使用情况
     */
    fun checkMemoryUsage(): MemoryCheckResult {
        val currentMemory = getCurrentMemoryUsage()
        val memoryIncrease = currentMemory - initialMemoryUsage
        val memoryIncreaseMB = memoryIncrease / 1024 / 1024
        
        val isWithinLimit = memoryIncreaseMB <= MAX_MEMORY_INCREASE_MB
        
        if (!isWithinLimit) {
            Log.w(TAG, "主题系统内存使用超限: ${memoryIncreaseMB}MB (最大允许: ${MAX_MEMORY_INCREASE_MB}MB)")
        }
        
        return MemoryCheckResult(
            currentMemoryMB = currentMemory / 1024 / 1024,
            initialMemoryMB = initialMemoryUsage / 1024 / 1024,
            increaseMB = memoryIncreaseMB,
            isWithinLimit = isWithinLimit
        )
    }
    
    /**
     * 获取性能统计
     */
    fun getPerformanceStats(): ThemePerformanceStats {
        val switchCount = themeSwitchCount.get()
        val avgSwitchTime = if (switchCount > 0) {
            totalSwitchTime.get().toDouble() / switchCount
        } else 0.0
        
        val totalCacheRequests = cacheHitCount.get() + cacheMissCount.get()
        val cacheHitRate = if (totalCacheRequests > 0) {
            (cacheHitCount.get().toDouble() / totalCacheRequests) * 100
        } else 0.0
        
        val memoryCheck = checkMemoryUsage()
        
        return ThemePerformanceStats(
            themeSwitchCount = switchCount,
            averageSwitchTimeMs = avgSwitchTime,
            cacheHitRate = cacheHitRate,
            backgroundCleanupCount = backgroundCleanupCount.get(),
            memoryUsageMB = memoryCheck.currentMemoryMB,
            memoryIncreaseMB = memoryCheck.increaseMB,
            isPerformanceHealthy = avgSwitchTime <= MAX_THEME_SWITCH_TIME_MS && 
                                 memoryCheck.isWithinLimit
        )
    }
    
    /**
     * 输出性能报告
     */
    private fun logPerformanceReport() {
        if (!isMonitoringEnabled) return
        
        val stats = getPerformanceStats()
        
        Log.i(TAG, """
            |主题性能报告:
            |  主题切换次数: ${stats.themeSwitchCount}
            |  平均切换时间: ${String.format("%.2f", stats.averageSwitchTimeMs)}ms
            |  缓存命中率: ${String.format("%.1f", stats.cacheHitRate)}%
            |  后台清理次数: ${stats.backgroundCleanupCount}
            |  当前内存使用: ${stats.memoryUsageMB}MB
            |  内存增量: ${stats.memoryIncreaseMB}MB
            |  性能状态: ${if (stats.isPerformanceHealthy) "健康" else "需要优化"}
        """.trimMargin())
    }
    
    /**
     * 重置性能统计
     */
    fun resetStats() {
        themeSwitchCount.set(0)
        totalSwitchTime.set(0L)
        cacheHitCount.set(0)
        cacheMissCount.set(0)
        backgroundCleanupCount.set(0)
        initialMemoryUsage = getCurrentMemoryUsage()
        
        Log.d(TAG, "性能统计已重置")
    }
    
    /**
     * 获取当前内存使用量
     */
    private fun getCurrentMemoryUsage(): Long {
        val runtime = Runtime.getRuntime()
        return runtime.totalMemory() - runtime.freeMemory()
    }
    
    /**
     * 检查性能是否健康
     */
    fun isPerformanceHealthy(): Boolean {
        return getPerformanceStats().isPerformanceHealthy
    }
    
    /**
     * 获取性能建议
     */
    fun getPerformanceRecommendations(): List<String> {
        val stats = getPerformanceStats()
        val recommendations = mutableListOf<String>()
        
        if (stats.averageSwitchTimeMs > MAX_THEME_SWITCH_TIME_MS) {
            recommendations.add("主题切换时间过长，建议优化主题初始化逻辑")
        }
        
        if (stats.cacheHitRate < 80.0) {
            recommendations.add("缓存命中率较低，建议调整缓存策略")
        }
        
        if (stats.memoryIncreaseMB > MAX_MEMORY_INCREASE_MB) {
            recommendations.add("内存使用增长过多，建议增加后台清理频率")
        }
        
        if (stats.backgroundCleanupCount == 0 && stats.themeSwitchCount > 5) {
            recommendations.add("建议启用后台清理以优化内存使用")
        }
        
        if (recommendations.isEmpty()) {
            recommendations.add("性能状态良好，无需优化")
        }
        
        return recommendations
    }
}

/**
 * 内存检查结果
 */
data class MemoryCheckResult(
    val currentMemoryMB: Long,
    val initialMemoryMB: Long,
    val increaseMB: Long,
    val isWithinLimit: Boolean
)

/**
 * 主题性能统计
 */
data class ThemePerformanceStats(
    val themeSwitchCount: Int,
    val averageSwitchTimeMs: Double,
    val cacheHitRate: Double,
    val backgroundCleanupCount: Int,
    val memoryUsageMB: Long,
    val memoryIncreaseMB: Long,
    val isPerformanceHealthy: Boolean
)
