package com.weinuo.quickcommands22.floating

import android.content.Context
import android.graphics.PixelFormat
import android.util.Log
import android.view.Gravity
import android.view.MotionEvent
import android.view.View
import android.view.WindowManager
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.*
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.platform.LocalContext
import com.weinuo.quickcommands22.data.SettingsRepository
import com.weinuo.quickcommands22.ui.components.WaterBallAdvancedColorSchemes
import com.weinuo.quickcommands22.ui.theme.manager.LocalThemeContext

/**
 * 悬浮加速球组件
 *
 * 实现静止状态的加速球UI，包括：
 * - 内存占用百分比显示
 * - 进度弧线绘制
 * - 纯黑色水球背景
 * - 拖动功能
 */
class FloatingAcceleratorBall(
    private val context: Context,
    private val onMemoryCleanup: () -> Unit
) {
    companion object {
        private const val TAG = "FloatingAcceleratorBall"
        private const val BALL_SIZE_DP = 40
    }

    private var windowManager: WindowManager? = null
    private var ballContainer: View? = null
    private var isShowing = false
    private var memoryPercentage by mutableStateOf(0)

    /**
     * 显示悬浮加速球
     */
    fun show(): Boolean {
        if (isShowing) {
            Log.w(TAG, "悬浮加速球已显示")
            return true
        }

        try {
            windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
            createBallContainer()

            val params = createLayoutParams()
            windowManager?.addView(ballContainer, params)

            isShowing = true
            Log.d(TAG, "悬浮加速球已显示")
            return true
        } catch (e: Exception) {
            Log.e(TAG, "显示悬浮加速球失败", e)
            return false
        }
    }

    /**
     * 隐藏悬浮加速球
     */
    fun hide() {
        if (!isShowing) return

        try {
            ballContainer?.let { view ->
                windowManager?.removeView(view)
            }
            isShowing = false
            Log.d(TAG, "悬浮加速球已隐藏")
        } catch (e: Exception) {
            Log.e(TAG, "隐藏悬浮加速球失败", e)
        }
    }

    /**
     * 更新内存占用百分比
     */
    fun updateMemoryPercentage(percentage: Int) {
        memoryPercentage = percentage.coerceIn(0, 100)
        (ballContainer as? AcceleratorBallView)?.updateMemoryPercentage(memoryPercentage)
    }

    /**
     * 获取当前位置
     */
    fun getCurrentPosition(): Pair<Int, Int> {
        ballContainer?.let { view ->
            val params = view.layoutParams as? WindowManager.LayoutParams
            return Pair(params?.x ?: 100, params?.y ?: 100)
        }
        return Pair(100, 100)
    }

    /**
     * 创建悬浮球容器
     */
    private fun createBallContainer() {
        ballContainer = AcceleratorBallView(context).apply {
            // 设置点击监听器 - 点击直接播放动画和加速
            setOnClickListener {
                // 通知用户交互
                (this as? AcceleratorBallView)?.onUserInteraction()
                // 开始膨胀和动画
                startExpandAndAnimation()
                // 执行内存清理
                onMemoryCleanup()
            }
        }

        // 添加拖动功能（保留拖动能力用于移动位置）
        addDragFunctionality()
    }

    /**
     * 开始膨胀和动画
     */
    private fun startExpandAndAnimation() {
        ballContainer?.let { view ->
            val params = view.layoutParams as WindowManager.LayoutParams
            val originalSize = (BALL_SIZE_DP * context.resources.displayMetrics.density).toInt()
            val expandedSize = (originalSize * 2.5f).toInt()

            // 膨胀到150%
            params.width = expandedSize
            params.height = expandedSize
            // 调整位置保持中心不变
            params.x -= (expandedSize - originalSize) / 2
            params.y -= (expandedSize - originalSize) / 2
            windowManager?.updateViewLayout(view, params)

            // 切换到动画模式
            (view as? AcceleratorBallView)?.startAnimation(memoryPercentage)

            // 3秒后恢复原状
            view.postDelayed({
                // 停止动画
                (view as? AcceleratorBallView)?.stopAnimation()

                // 恢复原始大小
                params.width = originalSize
                params.height = originalSize
                params.x += (expandedSize - originalSize) / 2
                params.y += (expandedSize - originalSize) / 2
                windowManager?.updateViewLayout(view, params)
            }, 3000)
        }
    }

    /**
     * 创建布局参数
     */
    private fun createLayoutParams(): WindowManager.LayoutParams {
        val density = context.resources.displayMetrics.density
        val sizeInPx = (BALL_SIZE_DP * density).toInt()

        return WindowManager.LayoutParams(
            sizeInPx,
            sizeInPx,
            WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY,
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
            PixelFormat.TRANSLUCENT
        ).apply {
            gravity = Gravity.TOP or Gravity.START
            x = 100
            y = 100
        }
    }

    /**
     * 添加拖动功能（仅用于移动位置）
     */
    private fun addDragFunctionality() {
        var initialX = 0
        var initialY = 0
        var initialTouchX = 0f
        var initialTouchY = 0f
        var isDragging = false

        ballContainer?.setOnTouchListener { view, event ->
            val params = view.layoutParams as WindowManager.LayoutParams

            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    initialX = params.x
                    initialY = params.y
                    initialTouchX = event.rawX
                    initialTouchY = event.rawY
                    isDragging = false
                    // 通知用户交互
                    (view as? AcceleratorBallView)?.onUserInteraction()
                    true
                }
                MotionEvent.ACTION_MOVE -> {
                    val deltaX = event.rawX - initialTouchX
                    val deltaY = event.rawY - initialTouchY

                    if (!isDragging && (Math.abs(deltaX) > 10 || Math.abs(deltaY) > 10)) {
                        isDragging = true
                    }

                    if (isDragging) {
                        params.x = initialX + deltaX.toInt()
                        params.y = initialY + deltaY.toInt()
                        windowManager?.updateViewLayout(view, params)
                    }
                    true
                }
                MotionEvent.ACTION_UP -> {
                    if (!isDragging) {
                        // 点击事件 - 触发点击监听器
                        view.performClick()
                    }
                    isDragging = false
                    true
                }
                else -> false
            }
        }
    }
}

/**
 * 加速球内容组件
 */
@Composable
private fun AcceleratorBallContent(
    memoryPercentage: Int,
    onCleanup: () -> Unit
) {
    Box(
        modifier = Modifier
            .size(80.dp)
            .clip(CircleShape)
            .background(Color.Black), // 纯黑色背景
        contentAlignment = Alignment.Center
    ) {
        // 获取弧线颜色（反向映射水球颜色）
        val arcColor = getArcColorForMemoryPercentage(memoryPercentage)

        // 绘制进度弧线和内容
        Canvas(
            modifier = Modifier.fillMaxSize()
        ) {
            drawAcceleratorBall(
                memoryPercentage = memoryPercentage,
                size = size,
                arcColor = arcColor
            )
        }

        // 内存占用百分比文字
        Text(
            text = "${memoryPercentage}%",
            color = Color.White,
            fontSize = 14.sp,
            fontWeight = FontWeight.Bold
        )
    }
}

/**
 * 绘制加速球
 */
private fun DrawScope.drawAcceleratorBall(
    memoryPercentage: Int,
    size: androidx.compose.ui.geometry.Size,
    arcColor: Color
) {
    val center = androidx.compose.ui.geometry.Offset(size.width / 2, size.height / 2)
    val radius = minOf(size.width, size.height) / 2 - 4.dp.toPx()

    // 计算弧线长度（根据内存占用百分比）
    val sweepAngle = (memoryPercentage / 100f) * 360f

    // 绘制进度弧线
    if (sweepAngle > 0) {
        drawArc(
            color = arcColor,
            startAngle = -90f, // 从顶部开始
            sweepAngle = sweepAngle,
            useCenter = false,
            style = Stroke(width = 3.dp.toPx()),
            topLeft = androidx.compose.ui.geometry.Offset(
                center.x - radius,
                center.y - radius
            ),
            size = androidx.compose.ui.geometry.Size(radius * 2, radius * 2)
        )
    }
}

/**
 * 根据内存占用百分比获取弧线颜色
 * 实现与水球颜色的反向映射，支持主题感知和高级材质
 */
@Composable
private fun getArcColorForMemoryPercentage(memoryPercentage: Int): Color {
    // 获取主题上下文和设置
    val themeContext = LocalThemeContext.current
    val context = LocalContext.current
    val settingsRepository = remember { SettingsRepository(context) }
    val globalSettings by settingsRepository.globalSettings.collectAsState()

    // 判断是否启用高级材质效果（仅在天空蓝主题下有效）
    val isAdvancedMaterialEnabled = remember(globalSettings.waterBallAdvancedMaterialEnabled, themeContext.theme.id) {
        globalSettings.waterBallAdvancedMaterialEnabled && themeContext.theme.id == "sky_blue"
    }

    return when {
        // 内存占用低（0-20%）-> 对应水球高分数颜色
        memoryPercentage <= 20 -> {
            if (isAdvancedMaterialEnabled) {
                // 使用高级材质的优秀等级颜色
                WaterBallAdvancedColorSchemes.getColorSetForScore(100).gradientEnd
            } else {
                // 使用主题品牌色
                themeContext.colorScheme.primary
            }
        }
        // 内存占用中等（21-60%）-> 对应水球中等分数颜色
        memoryPercentage <= 60 -> {
            if (isAdvancedMaterialEnabled) {
                // 使用高级材质的良好等级颜色
                WaterBallAdvancedColorSchemes.getColorSetForScore(70).gradientEnd
            } else {
                // 根据主题选择橙色
                when (themeContext.theme.id) {
                    "ocean_blue" -> Color(0xFFFF9800) // MD3标准橙色
                    "sky_blue" -> Color(0xFFFF8C00)   // iOS风格橙色
                    else -> Color(0xFFFF9800)         // 默认MD3橙色
                }
            }
        }
        // 内存占用高（61-100%）-> 对应水球低分数颜色
        else -> {
            if (isAdvancedMaterialEnabled) {
                // 使用高级材质的需要优化等级颜色
                WaterBallAdvancedColorSchemes.getColorSetForScore(30).gradientEnd
            } else {
                // 使用主题错误色
                themeContext.colorScheme.error
            }
        }
    }
}
