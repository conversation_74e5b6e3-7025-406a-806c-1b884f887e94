package com.weinuo.quickcommands22.ui.components.skyblue

import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ripple
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.dp

/**
 * 天空蓝主题专用开关组件 - iOS风格设计
 *
 * 特点：
 * - 采用iOS风格的设计语言
 * - 开启状态：天空蓝色轨道，白色圆形拇指
 * - 关闭状态：天空蓝色轨道，白色圆形拇指（拇指位于左侧）
 * - 椭圆形轨道，圆形拇指
 * - 平滑的动画过渡
 * - 拇指带有轻微阴影效果
 */
@Composable
fun SkyBlueSwitch(
    checked: Boolean,
    onCheckedChange: ((Boolean) -> Unit)?,
    modifier: Modifier = Modifier,
    enabled: Boolean = true
) {
    // iOS风格的颜色定义
    val skyBlueColor = Color(0xFF0A59F7)        // 天空蓝色轨道（开启状态）
    val uncheckedTrackColor = Color(0xFFE5E5EA) // 浅灰色轨道（关闭状态）
    val thumbColor = Color.White                 // 白色拇指（开启和关闭状态都是白色）
    val disabledTrackColor = Color(0xFFE5E5EA)  // 禁用状态轨道颜色
    val disabledThumbColor = Color.White        // 禁用状态拇指颜色

    // iOS Switch尺寸 (调整宽度为37dp，高度为22dp)
    val switchWidth = 37.dp
    val switchHeight = 22.dp
    val thumbSize = 17.dp
    val thumbInset = 2.dp  // 拇指与轨道边缘的内边距
    val trackCornerRadius = switchHeight / 2
    val thumbCornerRadius = thumbSize / 2

    // 动画状态
    val animationSpec = tween<Float>(
        durationMillis = 200,
        easing = FastOutSlowInEasing
    )

    val thumbOffset by animateFloatAsState(
        targetValue = if (checked) 1f else 0f,
        animationSpec = animationSpec,
        label = "thumb_offset"
    )

    val trackColor by animateColorAsState(
        targetValue = when {
            !enabled -> disabledTrackColor
            checked -> skyBlueColor      // 开启状态使用天空蓝色
            else -> uncheckedTrackColor  // 关闭状态使用浅灰色
        },
        animationSpec = tween(200),
        label = "track_color"
    )

    val currentThumbColor by animateColorAsState(
        targetValue = if (enabled) thumbColor else disabledThumbColor,
        animationSpec = tween(200),
        label = "thumb_color"
    )

    // 交互源
    val interactionSource = remember { MutableInteractionSource() }

    Box(
        modifier = modifier
            .size(width = switchWidth, height = switchHeight)
            .clip(RoundedCornerShape(trackCornerRadius))
            .background(trackColor)
            .clickable(
                interactionSource = interactionSource,
                indication = ripple(bounded = true, radius = switchWidth / 2),
                enabled = enabled,
                onClick = { onCheckedChange?.invoke(!checked) }
            ),
        contentAlignment = Alignment.CenterStart
    ) {
        // 拇指
        val density = LocalDensity.current
        val thumbOffsetPx = with(density) {
            (switchWidth - thumbSize - thumbInset * 2).toPx() * thumbOffset + thumbInset.toPx()
        }

        Box(
            modifier = Modifier
                .offset(x = with(density) { thumbOffsetPx.toDp() })
                .size(thumbSize)
                .shadow(
                    elevation = 2.dp,
                    shape = RoundedCornerShape(thumbCornerRadius),
                    clip = false
                )
                .background(
                    color = currentThumbColor,
                    shape = RoundedCornerShape(thumbCornerRadius)
                )
        )
    }
}
