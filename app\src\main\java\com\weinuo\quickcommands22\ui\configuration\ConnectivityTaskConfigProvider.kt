package com.weinuo.quickcommands22.ui.configuration

import android.app.Activity
import android.content.Context
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import com.weinuo.quickcommands22.R
import com.weinuo.quickcommands22.model.*
import com.weinuo.quickcommands22.navigation.Screen
import com.weinuo.quickcommands22.storage.UIStateStorageManager
import com.weinuo.quickcommands22.ui.activities.AppSelectionActivity
import com.weinuo.quickcommands22.ui.components.ConfigurationCardItem
import com.weinuo.quickcommands22.ui.screens.LocalNavController

/**
 * 连接任务配置数据提供器
 *
 * 提供连接任务的配置项列表，支持11种连接操作类型：
 * - WiFi控制
 * - 移动数据控制
 * - 蓝牙控制
 * - 热点控制
 * - 飞行模式控制
 * - 自动同步控制
 * - Android Wear控制
 * - 发送Intent
 * - 同步账号
 * - 检查连接
 * - 保存网络状态
 * - 恢复网络状态
 *
 * 每个配置项都有独立的配置界面组件，支持权限检查和参数配置。
 */
object ConnectivityTaskConfigProvider {

    /**
     * 获取连接任务配置项列表
     *
     * @param context 上下文，用于获取字符串资源
     * @return 连接任务配置项列表
     */
    fun getConfigurationItems(context: Context): List<ConfigurationCardItem<ConnectivityOperation>> {
        return listOf(
            // WiFi控制配置项
            ConfigurationCardItem(
                id = "wifi_control",
                title = context.getString(R.string.connectivity_wifi_control),
                description = context.getString(R.string.connectivity_wifi_control_description),
                operationType = ConnectivityOperation.WIFI_CONTROL,
                permissionRequired = true,
                content = { operation, onComplete ->
                    WifiControlConfigContent(operation, onComplete)
                }
            ),

            // 移动数据控制配置项
            ConfigurationCardItem(
                id = "mobile_data_control",
                title = context.getString(R.string.connectivity_mobile_data_control),
                description = context.getString(R.string.connectivity_mobile_data_control_description),
                operationType = ConnectivityOperation.MOBILE_DATA_CONTROL,
                permissionRequired = true,
                content = { operation, onComplete ->
                    MobileDataControlConfigContent(operation, onComplete)
                }
            ),

            // 热点控制配置项
            ConfigurationCardItem(
                id = "hotspot_control",
                title = context.getString(R.string.connectivity_hotspot_control),
                description = context.getString(R.string.connectivity_hotspot_control_description),
                operationType = ConnectivityOperation.HOTSPOT_CONTROL,
                permissionRequired = true,
                content = { operation, onComplete ->
                    HotspotControlConfigContent(operation, onComplete)
                }
            ),

            // 飞行模式控制配置项
            ConfigurationCardItem(
                id = "airplane_mode_control",
                title = context.getString(R.string.connectivity_airplane_mode_control),
                description = context.getString(R.string.connectivity_airplane_mode_control_description),
                operationType = ConnectivityOperation.AIRPLANE_MODE_CONTROL,
                permissionRequired = true,
                content = { operation, onComplete ->
                    AirplaneModeControlConfigContent(operation, onComplete)
                }
            ),

            // 自动同步控制配置项
            ConfigurationCardItem(
                id = "auto_sync_control",
                title = "自动同步控制",
                description = "控制自动同步的开启、关闭或切换",
                operationType = ConnectivityOperation.AUTO_SYNC_CONTROL,
                permissionRequired = true,
                content = { operation, onComplete ->
                    AutoSyncControlConfigContent(operation, onComplete)
                }
            ),



            // 发送Intent配置项
            ConfigurationCardItem(
                id = "send_intent",
                title = context.getString(R.string.connectivity_send_intent),
                description = context.getString(R.string.connectivity_send_intent_description),
                operationType = ConnectivityOperation.SEND_INTENT,
                permissionRequired = false,
                content = { operation, onComplete ->
                    SendIntentConfigContent(operation, onComplete)
                }
            ),

            // 同步账号配置项
            ConfigurationCardItem(
                id = "sync_account",
                title = "同步账号",
                description = "同步指定类型的账号数据",
                operationType = ConnectivityOperation.SYNC_ACCOUNT,
                permissionRequired = true,
                content = { operation, onComplete ->
                    SyncAccountConfigContent(operation, onComplete)
                }
            ),

            // 检查连接配置项
            ConfigurationCardItem(
                id = "check_connection",
                title = "检查连接",
                description = "检查指定网址的连通性",
                operationType = ConnectivityOperation.CHECK_CONNECTION,
                permissionRequired = true,
                content = { operation, onComplete ->
                    CheckConnectionConfigContent(operation, onComplete)
                }
            ),

            // 保存网络状态配置项
            ConfigurationCardItem(
                id = "network_state_save",
                title = "保存网络状态",
                description = "保存当前WiFi和移动数据的状态，用于后续恢复",
                operationType = ConnectivityOperation.NETWORK_STATE_SAVE,
                permissionRequired = true,
                content = { operation, onComplete ->
                    NetworkStateSaveConfigContent(operation, onComplete)
                }
            ),

            // 恢复网络状态配置项
            ConfigurationCardItem(
                id = "network_state_restore",
                title = "恢复网络状态",
                description = "恢复之前保存的WiFi和移动数据状态",
                operationType = ConnectivityOperation.NETWORK_STATE_RESTORE,
                permissionRequired = true,
                content = { operation, onComplete ->
                    NetworkStateRestoreConfigContent(operation, onComplete)
                },
                editableContent = { operation, initialConfig, onComplete ->
                    NetworkStateRestoreConfigContent(operation, onComplete, initialConfig as? ConnectivityTask)
                }
            )
        )
    }
}

/**
 * WiFi控制配置内容组件
 *
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
fun WifiControlConfigContent(
    operation: ConnectivityOperation,
    onComplete: (Any) -> Unit
) {

    var selectedSwitchOperation by rememberSaveable { mutableStateOf(SwitchOperation.TOGGLE) }

    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "配置WiFi控制操作",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        // 开关操作选择
        Text(
            text = "开关操作",
            style = MaterialTheme.typography.bodyMedium
        )

        SwitchOperation.values().forEach { switchOp ->
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .selectable(
                        selected = (selectedSwitchOperation == switchOp),
                        onClick = { selectedSwitchOperation = switchOp }
                    ),
                verticalAlignment = Alignment.CenterVertically
            ) {
                RadioButton(
                    selected = (selectedSwitchOperation == switchOp),
                    onClick = { selectedSwitchOperation = switchOp }
                )
                Text(
                    text = when (switchOp) {
                        SwitchOperation.ENABLE -> "开启"
                        SwitchOperation.DISABLE -> "关闭"
                        SwitchOperation.TOGGLE -> "切换"
                    },
                    modifier = Modifier.padding(start = 8.dp)
                )
            }
        }

        // 确认按钮
        Button(
            onClick = {
                val task = ConnectivityTask(
                    operation = operation,
                    wifiOperation = selectedSwitchOperation
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 移动数据控制配置内容组件
 *
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
fun MobileDataControlConfigContent(
    operation: ConnectivityOperation,
    onComplete: (Any) -> Unit
) {

    var selectedSwitchOperation by rememberSaveable { mutableStateOf(SwitchOperation.TOGGLE) }

    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "配置移动数据控制操作",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        // 开关操作选择
        Text(
            text = "开关操作",
            style = MaterialTheme.typography.bodyMedium
        )

        SwitchOperation.values().forEach { switchOp ->
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .selectable(
                        selected = (selectedSwitchOperation == switchOp),
                        onClick = { selectedSwitchOperation = switchOp }
                    ),
                verticalAlignment = Alignment.CenterVertically
            ) {
                RadioButton(
                    selected = (selectedSwitchOperation == switchOp),
                    onClick = { selectedSwitchOperation = switchOp }
                )
                Text(
                    text = when (switchOp) {
                        SwitchOperation.ENABLE -> "开启"
                        SwitchOperation.DISABLE -> "关闭"
                        SwitchOperation.TOGGLE -> "切换"
                    },
                    modifier = Modifier.padding(start = 8.dp)
                )
            }
        }

        // 确认按钮
        Button(
            onClick = {
                val task = ConnectivityTask(
                    operation = operation,
                    mobileDataOperation = selectedSwitchOperation
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 热点控制配置内容组件
 *
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
fun HotspotControlConfigContent(
    operation: ConnectivityOperation,
    onComplete: (Any) -> Unit
) {

    var selectedSwitchOperation by rememberSaveable { mutableStateOf(SwitchOperation.TOGGLE) }

    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "配置热点控制操作",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        // 开关操作选择 - 使用与移动数据控制相同的UI模式，避免嵌套卡片
        Text(
            text = "开关操作",
            style = MaterialTheme.typography.bodyMedium
        )

        SwitchOperation.values().forEach { switchOp ->
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .selectable(
                        selected = (selectedSwitchOperation == switchOp),
                        onClick = { selectedSwitchOperation = switchOp }
                    ),
                verticalAlignment = Alignment.CenterVertically
            ) {
                RadioButton(
                    selected = (selectedSwitchOperation == switchOp),
                    onClick = { selectedSwitchOperation = switchOp }
                )
                Text(
                    text = switchOp.displayName,
                    modifier = Modifier.padding(start = 8.dp)
                )
            }
        }

        // 确认按钮
        Button(
            onClick = {
                val task = ConnectivityTask(
                    operation = operation,
                    hotspotOperation = selectedSwitchOperation
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 飞行模式控制配置内容组件
 *
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
fun AirplaneModeControlConfigContent(
    operation: ConnectivityOperation,
    onComplete: (Any) -> Unit
) {

    var selectedSwitchOperation by rememberSaveable { mutableStateOf(SwitchOperation.TOGGLE) }

    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "配置飞行模式控制操作",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        // 开关操作选择 - 使用与移动数据控制相同的UI模式，避免嵌套卡片
        Text(
            text = "开关操作",
            style = MaterialTheme.typography.bodyMedium
        )

        SwitchOperation.values().forEach { switchOp ->
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .selectable(
                        selected = (selectedSwitchOperation == switchOp),
                        onClick = { selectedSwitchOperation = switchOp }
                    ),
                verticalAlignment = Alignment.CenterVertically
            ) {
                RadioButton(
                    selected = (selectedSwitchOperation == switchOp),
                    onClick = { selectedSwitchOperation = switchOp }
                )
                Text(
                    text = switchOp.displayName,
                    modifier = Modifier.padding(start = 8.dp)
                )
            }
        }

        // 确认按钮
        Button(
            onClick = {
                val task = ConnectivityTask(
                    operation = operation,
                    airplaneModeOperation = selectedSwitchOperation
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 自动同步控制配置内容组件
 *
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
fun AutoSyncControlConfigContent(
    operation: ConnectivityOperation,
    onComplete: (Any) -> Unit
) {

    var selectedSwitchOperation by rememberSaveable { mutableStateOf(SwitchOperation.TOGGLE) }

    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "配置自动同步控制操作",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        // 开关操作选择 - 使用与移动数据控制相同的UI模式，避免嵌套卡片
        Text(
            text = "开关操作",
            style = MaterialTheme.typography.bodyMedium
        )

        SwitchOperation.values().forEach { switchOp ->
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .selectable(
                        selected = (selectedSwitchOperation == switchOp),
                        onClick = { selectedSwitchOperation = switchOp }
                    ),
                verticalAlignment = Alignment.CenterVertically
            ) {
                RadioButton(
                    selected = (selectedSwitchOperation == switchOp),
                    onClick = { selectedSwitchOperation = switchOp }
                )
                Text(
                    text = switchOp.displayName,
                    modifier = Modifier.padding(start = 8.dp)
                )
            }
        }

        // 确认按钮
        Button(
            onClick = {
                val task = ConnectivityTask(
                    operation = operation,
                    autoSyncOperation = selectedSwitchOperation
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}



/**
 * 发送Intent配置内容组件
 *
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
fun SendIntentConfigContent(
    operation: ConnectivityOperation,
    onComplete: (Any) -> Unit
) {

    var targetType by rememberSaveable { mutableStateOf(IntentTargetType.ACTIVITY) }
    var intentAction by rememberSaveable { mutableStateOf("") }
    // 分别保存SimpleAppInfo的各个字段，避免自定义Saver的复杂性
    var selectedAppPackageName by rememberSaveable { mutableStateOf("") }
    var selectedAppName by rememberSaveable { mutableStateOf("") }
    var selectedAppIsSystemApp by rememberSaveable { mutableStateOf(false) }
    var intentClass by rememberSaveable { mutableStateOf("") }
    var intentData by rememberSaveable { mutableStateOf("") }
    var intentCategory by rememberSaveable { mutableStateOf("") }
    var intentMimeType by rememberSaveable { mutableStateOf("") }

    // 根据保存的字段重建SimpleAppInfo对象
    val selectedApp = if (selectedAppPackageName.isNotEmpty()) {
        SimpleAppInfo(
            packageName = selectedAppPackageName,
            appName = selectedAppName,
            isSystemApp = selectedAppIsSystemApp
        )
    } else {
        null
    }

    val context = LocalContext.current
    val uiStateManager = remember { UIStateStorageManager(context) }

    // 应用选择ActivityResultLauncher
    val appSelectionLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            val resultKey = result.data?.getStringExtra(AppSelectionActivity.RESULT_SELECTED_APPS)
            if (resultKey != null) {
                val selectedApps = uiStateManager.loadAppListState(resultKey, "selected_apps")
                if (selectedApps.isNotEmpty()) {
                    val app = selectedApps.first()
                    selectedAppPackageName = app.packageName
                    selectedAppName = app.appName
                    selectedAppIsSystemApp = app.isSystemApp
                }
                // 清理临时存储
                uiStateManager.clearAppListState(resultKey, "selected_apps")
            }
        }
    }

    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "配置Intent发送",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        // Intent目标类型选择 - 避免嵌套卡片
        Text(
            text = "Intent目标类型",
            style = MaterialTheme.typography.bodyMedium
        )

        IntentTargetType.values().forEach { intentType ->
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .selectable(
                        selected = (targetType == intentType),
                        onClick = { targetType = intentType }
                    ),
                verticalAlignment = Alignment.CenterVertically
            ) {
                RadioButton(
                    selected = (targetType == intentType),
                    onClick = { targetType = intentType }
                )
                Text(
                    text = when (intentType) {
                        IntentTargetType.ACTIVITY -> "Activity"
                        IntentTargetType.BROADCAST -> "广播"
                        IntentTargetType.SERVICE -> "服务"
                    },
                    modifier = Modifier.padding(start = 8.dp)
                )
            }
        }

        // Intent配置字段
        OutlinedTextField(
            value = intentAction,
            onValueChange = { intentAction = it },
            label = { Text("Action") },
            modifier = Modifier.fillMaxWidth(),
            placeholder = { Text("android.intent.action.VIEW") }
        )

        // 应用选择
        Text(
            text = "选择应用",
            style = MaterialTheme.typography.titleMedium
        )

        OutlinedButton(
            onClick = {
                val resultKey = "connectivity_app_selection_${System.currentTimeMillis()}"
                val intent = AppSelectionActivity.createSingleSelectionIntent(
                    context = context,
                    resultKey = resultKey
                )
                appSelectionLauncher.launch(intent)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text(
                text = if (selectedApp != null) {
                    "已选择: ${selectedApp.appName}"
                } else {
                    "点击选择应用"
                }
            )
        }

        if (selectedApp != null) {
            Text(
                text = "包名: ${selectedApp.packageName}",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }

        OutlinedTextField(
            value = intentClass,
            onValueChange = { intentClass = it },
            label = { Text("类名") },
            modifier = Modifier.fillMaxWidth(),
            placeholder = { Text("com.example.app.MainActivity") }
        )

        OutlinedTextField(
            value = intentData,
            onValueChange = { intentData = it },
            label = { Text("数据URI") },
            modifier = Modifier.fillMaxWidth(),
            placeholder = { Text("https://example.com") }
        )

        OutlinedTextField(
            value = intentCategory,
            onValueChange = { intentCategory = it },
            label = { Text("类别") },
            modifier = Modifier.fillMaxWidth(),
            placeholder = { Text("android.intent.category.DEFAULT") }
        )

        OutlinedTextField(
            value = intentMimeType,
            onValueChange = { intentMimeType = it },
            label = { Text("MIME类型") },
            modifier = Modifier.fillMaxWidth(),
            placeholder = { Text("text/plain") }
        )

        // 确认按钮
        Button(
            onClick = {
                val task = ConnectivityTask(
                    operation = operation,
                    intentTargetType = targetType,
                    intentAction = intentAction,
                    intentPackageName = selectedApp?.packageName ?: "",
                    intentClassName = intentClass,
                    intentDataUrl = intentData,
                    intentMimeType = intentMimeType
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 同步账号配置内容组件
 *
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
fun SyncAccountConfigContent(
    operation: ConnectivityOperation,
    onComplete: (Any) -> Unit
) {
    val navController = LocalNavController.current

    // 分别保存AccountInfo的各个字段，避免自定义Saver的复杂性
    var selectedAccountName by rememberSaveable { mutableStateOf("") }
    var selectedAccountType by rememberSaveable { mutableStateOf("") }
    var waitForCompletion by rememberSaveable { mutableStateOf(false) }

    // 根据保存的字段重建AccountInfo对象
    val selectedAccount = if (selectedAccountName.isNotEmpty()) {
        com.weinuo.quickcommands22.ui.screens.AccountInfo(
            name = selectedAccountName,
            type = selectedAccountType
        )
    } else {
        null
    }

    // 监听账号选择结果
    LaunchedEffect(Unit) {
        val savedStateHandle = navController?.currentBackStackEntry?.savedStateHandle
        val selectedAccounts = savedStateHandle?.get<List<com.weinuo.quickcommands22.ui.screens.AccountInfo>>("selected_accounts")
        if (selectedAccounts != null && selectedAccounts.isNotEmpty()) {
            val account = selectedAccounts.first()
            selectedAccountName = account.name
            selectedAccountType = account.type
            savedStateHandle.remove<List<com.weinuo.quickcommands22.ui.screens.AccountInfo>>("selected_accounts")
        }
    }

    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "配置账号同步",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        // 账号选择
        Text(
            text = "选择账号",
            style = MaterialTheme.typography.titleMedium
        )

        OutlinedButton(
            onClick = {
                if (navController != null) {
                    val route = Screen.AccountSelection.createSingleSelectionRoute()
                    navController.navigate(route)
                }
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text(
                text = if (selectedAccount != null) {
                    "已选择: ${selectedAccount.displayName}"
                } else {
                    "点击选择账号"
                }
            )
        }

        if (selectedAccount != null) {
            Text(
                text = "账号类型: ${selectedAccount.type}",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }

        // 完成后才能后续动作复选框
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Checkbox(
                checked = waitForCompletion,
                onCheckedChange = { waitForCompletion = it }
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = "完成后才能后续动作",
                style = MaterialTheme.typography.bodyMedium
            )
        }

        // 确认按钮
        Button(
            onClick = {
                val task = ConnectivityTask(
                    operation = operation,
                    accountType = selectedAccount?.type ?: "",
                    waitForCompletion = waitForCompletion
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth(),
            enabled = selectedAccount != null
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 检查连接配置内容组件
 *
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
fun CheckConnectionConfigContent(
    operation: ConnectivityOperation,
    onComplete: (Any) -> Unit
) {

    var checkUrl by rememberSaveable { mutableStateOf("") }
    var timeoutMilliseconds by rememberSaveable { mutableStateOf("10000") }
    var waitForCompletion by rememberSaveable { mutableStateOf(false) }

    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "配置连接检查",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        OutlinedTextField(
            value = checkUrl,
            onValueChange = { checkUrl = it },
            label = { Text("检查URL") },
            modifier = Modifier.fillMaxWidth(),
            placeholder = { Text("https://m.baidu.com") }
        )

        OutlinedTextField(
            value = timeoutMilliseconds,
            onValueChange = { timeoutMilliseconds = it },
            label = { Text("超时时间（毫秒）") },
            modifier = Modifier.fillMaxWidth(),
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
            placeholder = { Text("10000") }
        )

        // 完成后才能后续动作复选框
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Checkbox(
                checked = waitForCompletion,
                onCheckedChange = { waitForCompletion = it }
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = "完成后才能后续动作",
                style = MaterialTheme.typography.bodyMedium
            )
        }

        // 确认按钮
        Button(
            onClick = {
                val timeout = timeoutMilliseconds.toIntOrNull() ?: 10000
                val task = ConnectivityTask(
                    operation = operation,
                    checkUrl = checkUrl,
                    checkTimeout = timeout,
                    waitForCompletion = waitForCompletion
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 保存网络状态配置内容组件
 *
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
fun NetworkStateSaveConfigContent(
    operation: ConnectivityOperation,
    onComplete: (Any) -> Unit
) {
    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "保存当前网络状态",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        Text(
            text = "此操作将保存当前WiFi和移动数据的开启/关闭状态，用于后续的网络状态恢复操作。",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        Text(
            text = "注意：每次保存操作都会覆盖之前保存的状态。",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        // 确认按钮
        Button(
            onClick = {
                val task = ConnectivityTask(
                    operation = operation
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 恢复网络状态配置内容组件
 *
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
fun NetworkStateRestoreConfigContent(
    operation: ConnectivityOperation,
    onComplete: (Any) -> Unit,
    initialTask: ConnectivityTask? = null
) {
    // 自定义恢复命令配置
    var useCustomRestoreCommands by rememberSaveable { mutableStateOf(initialTask?.useCustomRestoreCommands ?: false) }
    var customWifiEnableCommand by rememberSaveable { mutableStateOf(initialTask?.customWifiEnableCommand ?: "") }
    var customWifiDisableCommand by rememberSaveable { mutableStateOf(initialTask?.customWifiDisableCommand ?: "") }
    var customMobileDataEnableCommand by rememberSaveable { mutableStateOf(initialTask?.customMobileDataEnableCommand ?: "") }
    var customMobileDataDisableCommand by rememberSaveable { mutableStateOf(initialTask?.customMobileDataDisableCommand ?: "") }

    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "恢复保存的网络状态",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        Text(
            text = "此操作将恢复之前保存的WiFi和移动数据状态。如果没有保存的状态或状态已过期，则跳过恢复操作。",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        Text(
            text = "注意：恢复操作执行后，保存的状态数据将被自动清除。",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        // 自定义恢复命令配置区域
        CustomRestoreCommandsSection(
            useCustomRestoreCommands = useCustomRestoreCommands,
            onUseCustomRestoreCommandsChange = { useCustomRestoreCommands = it },
            customWifiEnableCommand = customWifiEnableCommand,
            onCustomWifiEnableCommandChange = { customWifiEnableCommand = it },
            customWifiDisableCommand = customWifiDisableCommand,
            onCustomWifiDisableCommandChange = { customWifiDisableCommand = it },
            customMobileDataEnableCommand = customMobileDataEnableCommand,
            onCustomMobileDataEnableCommandChange = { customMobileDataEnableCommand = it },
            customMobileDataDisableCommand = customMobileDataDisableCommand,
            onCustomMobileDataDisableCommandChange = { customMobileDataDisableCommand = it }
        )

        // 确认按钮
        Button(
            onClick = {
                // 预处理命令
                val effectiveWifiEnableCommand = if (useCustomRestoreCommands && customWifiEnableCommand.isNotBlank()) {
                    customWifiEnableCommand
                } else {
                    "svc wifi enable"
                }
                val effectiveWifiDisableCommand = if (useCustomRestoreCommands && customWifiDisableCommand.isNotBlank()) {
                    customWifiDisableCommand
                } else {
                    "svc wifi disable"
                }
                val effectiveMobileDataEnableCommand = if (useCustomRestoreCommands && customMobileDataEnableCommand.isNotBlank()) {
                    customMobileDataEnableCommand
                } else {
                    "svc data enable"
                }
                val effectiveMobileDataDisableCommand = if (useCustomRestoreCommands && customMobileDataDisableCommand.isNotBlank()) {
                    customMobileDataDisableCommand
                } else {
                    "svc data disable"
                }

                val task = ConnectivityTask(
                    operation = operation,
                    useCustomRestoreCommands = useCustomRestoreCommands,
                    customWifiEnableCommand = customWifiEnableCommand,
                    customWifiDisableCommand = customWifiDisableCommand,
                    customMobileDataEnableCommand = customMobileDataEnableCommand,
                    customMobileDataDisableCommand = customMobileDataDisableCommand,
                    effectiveWifiEnableCommand = effectiveWifiEnableCommand,
                    effectiveWifiDisableCommand = effectiveWifiDisableCommand,
                    effectiveMobileDataEnableCommand = effectiveMobileDataEnableCommand,
                    effectiveMobileDataDisableCommand = effectiveMobileDataDisableCommand
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 自定义恢复命令配置区域组件
 */
@Composable
private fun CustomRestoreCommandsSection(
    useCustomRestoreCommands: Boolean,
    onUseCustomRestoreCommandsChange: (Boolean) -> Unit,
    customWifiEnableCommand: String,
    onCustomWifiEnableCommandChange: (String) -> Unit,
    customWifiDisableCommand: String,
    onCustomWifiDisableCommandChange: (String) -> Unit,
    customMobileDataEnableCommand: String,
    onCustomMobileDataEnableCommandChange: (String) -> Unit,
    customMobileDataDisableCommand: String,
    onCustomMobileDataDisableCommandChange: (String) -> Unit
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        // 启用自定义恢复命令选项
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Checkbox(
                checked = useCustomRestoreCommands,
                onCheckedChange = onUseCustomRestoreCommandsChange
            )
            Spacer(modifier = Modifier.width(8.dp))
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = "使用自定义恢复命令",
                    style = MaterialTheme.typography.bodyMedium
                )
                Text(
                    text = "自定义网络状态恢复时使用的命令",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }

        // 自定义命令配置（条件显示）
        if (useCustomRestoreCommands) {
            Column(
                verticalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                Text(
                    text = "恢复命令设置",
                    style = MaterialTheme.typography.titleMedium
                )

                Text(
                    text = "设置恢复网络状态时使用的具体命令，留空将使用默认命令",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )

                // WiFi开启命令
                OutlinedTextField(
                    value = customWifiEnableCommand,
                    onValueChange = onCustomWifiEnableCommandChange,
                    label = { Text("WiFi开启命令") },
                    placeholder = { Text("svc wifi enable") },
                    supportingText = {
                        Text("默认：svc wifi enable")
                    },
                    modifier = Modifier.fillMaxWidth(),
                    singleLine = true
                )

                // WiFi关闭命令
                OutlinedTextField(
                    value = customWifiDisableCommand,
                    onValueChange = onCustomWifiDisableCommandChange,
                    label = { Text("WiFi关闭命令") },
                    placeholder = { Text("svc wifi disable") },
                    supportingText = {
                        Text("默认：svc wifi disable")
                    },
                    modifier = Modifier.fillMaxWidth(),
                    singleLine = true
                )

                // 移动数据开启命令
                OutlinedTextField(
                    value = customMobileDataEnableCommand,
                    onValueChange = onCustomMobileDataEnableCommandChange,
                    label = { Text("移动数据开启命令") },
                    placeholder = { Text("svc data enable") },
                    supportingText = {
                        Text("默认：svc data enable")
                    },
                    modifier = Modifier.fillMaxWidth(),
                    singleLine = true
                )

                // 移动数据关闭命令
                OutlinedTextField(
                    value = customMobileDataDisableCommand,
                    onValueChange = onCustomMobileDataDisableCommandChange,
                    label = { Text("移动数据关闭命令") },
                    placeholder = { Text("svc data disable") },
                    supportingText = {
                        Text("默认：svc data disable")
                    },
                    modifier = Modifier.fillMaxWidth(),
                    singleLine = true
                )

                Text(
                    text = "提示：命令将通过Shizuku执行，请确保命令的正确性和安全性。",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}


