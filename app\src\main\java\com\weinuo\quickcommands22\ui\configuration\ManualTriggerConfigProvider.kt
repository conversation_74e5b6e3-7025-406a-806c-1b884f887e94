package com.weinuo.quickcommands22.ui.configuration

import android.content.Context
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import com.weinuo.quickcommands22.R
import com.weinuo.quickcommands22.model.*
import com.weinuo.quickcommands22.ui.components.ConfigurationCardItem

/**
 * 手动触发配置数据提供器
 *
 * 提供手动触发相关的配置项，包括动态快捷方式、静态快捷方式、桌面小组件、悬浮按钮。
 * 使用通用组件架构，支持高度复用和扩展。
 */
object ManualTriggerConfigProvider {

    /**
     * 获取手动触发配置项列表
     *
     * @param context 上下文，用于获取字符串资源
     * @return 手动触发配置项列表
     */
    fun getConfigurationItems(context: Context): List<ConfigurationCardItem<ManualTriggerType>> {
        return listOf(
            // 动态快捷方式配置项
            ConfigurationCardItem(
                id = "dynamic_shortcut",
                title = context.getString(R.string.manual_dynamic_shortcut),
                description = context.getString(R.string.manual_dynamic_shortcut_description),
                operationType = ManualTriggerType.DYNAMIC_SHORTCUT,
                permissionRequired = false,
                content = { type, onComplete ->
                    DynamicShortcutConfigContent(type, onComplete)
                }
            ),

            // 静态快捷方式配置项
            ConfigurationCardItem(
                id = "static_shortcut",
                title = context.getString(R.string.manual_static_shortcut),
                description = context.getString(R.string.manual_static_shortcut_description),
                operationType = ManualTriggerType.STATIC_SHORTCUT,
                permissionRequired = false,
                content = { type, onComplete ->
                    StaticShortcutConfigContent(type, onComplete)
                }
            ),

            // 桌面小组件配置项
            ConfigurationCardItem(
                id = "desktop_widget",
                title = "桌面小组件",
                description = "通过桌面小组件触发",
                operationType = ManualTriggerType.DESKTOP_WIDGET,
                permissionRequired = false,
                content = { type, onComplete ->
                    DesktopWidgetConfigContent(type, onComplete)
                }
            ),

            // 悬浮按钮配置项
            ConfigurationCardItem(
                id = "floating_button",
                title = "悬浮按钮",
                description = "通过悬浮按钮触发",
                operationType = ManualTriggerType.FLOATING_BUTTON,
                permissionRequired = true, // 悬浮按钮需要悬浮窗权限
                content = { type, onComplete ->
                    FloatingButtonConfigContent(type, onComplete)
                }
            ),

            // 指纹手势配置项
            ConfigurationCardItem(
                id = "fingerprint_gesture",
                title = context.getString(R.string.manual_fingerprint_gesture),
                description = context.getString(R.string.manual_fingerprint_gesture_description),
                operationType = ManualTriggerType.FINGERPRINT_GESTURE,
                permissionRequired = true, // 指纹手势需要手势识别无障碍服务
                content = { type, onComplete ->
                    FingerprintGestureConfigContent(type, onComplete)
                }
            ),

            // 主屏幕按钮长按配置项
            ConfigurationCardItem(
                id = "home_button_long_press",
                title = context.getString(R.string.manual_home_button_long_press),
                description = context.getString(R.string.manual_home_button_long_press_description),
                operationType = ManualTriggerType.HOME_BUTTON_LONG_PRESS,
                permissionRequired = true, // 需要辅助服务和语音输入应用权限
                content = { type, onComplete ->
                    HomeButtonLongPressConfigContent(type, onComplete)
                }
            ),

            // 媒体键按下配置项
            ConfigurationCardItem(
                id = "media_key_press",
                title = context.getString(R.string.manual_media_key_press),
                description = context.getString(R.string.manual_media_key_press_description),
                operationType = ManualTriggerType.MEDIA_KEY_PRESS,
                permissionRequired = false, // 媒体键监听不需要特殊权限
                content = { type, onComplete ->
                    MediaKeyPressConfigContent(type, onComplete)
                }
            ),

            // 快捷方式打开配置项
            ConfigurationCardItem(
                id = "shortcut_opening",
                title = "快捷方式打开",
                description = "本触发条件会为本指令打开一个应用的快捷方式。当特定的快捷方式被打开时，将触发条件。此条件使你可以通过其他支持打开快捷方式的应用或桌面来将其调用。",
                operationType = ManualTriggerType.SHORTCUT_OPENING,
                permissionRequired = false,
                content = { type, onComplete ->
                    ShortcutOpeningConfigContent(type, onComplete)
                }
            ),

            // 滑动屏幕配置项
            ConfigurationCardItem(
                id = "screen_swipe",
                title = "滑动屏幕",
                description = "可选滑动开始区域是左上角还是右上角，还可选横过还是对角线还是下",
                operationType = ManualTriggerType.SCREEN_SWIPE,
                permissionRequired = true, // 需要无障碍服务权限
                content = { type, onComplete ->
                    ScreenSwipeConfigContent(type, onComplete)
                }
            ),

            // 音量按钮长按配置项
            ConfigurationCardItem(
                id = "volume_button_long_press",
                title = "音量按钮长按",
                description = "通过音量按钮长按触发",
                operationType = ManualTriggerType.VOLUME_BUTTON_LONG_PRESS,
                permissionRequired = true, // 需要无障碍服务权限
                content = { type, onComplete ->
                    VolumeButtonLongPressConfigContent(type, onComplete)
                }
            ),

            // 音量键按下配置项
            ConfigurationCardItem(
                id = "volume_key_press",
                title = context.getString(R.string.manual_volume_key_press),
                description = context.getString(R.string.manual_volume_key_press_description),
                operationType = ManualTriggerType.VOLUME_KEY_PRESS,
                permissionRequired = true, // 需要无障碍服务权限
                content = { type, onComplete ->
                    VolumeKeyPressConfigContent(type, onComplete)
                }
            ),

            // 电源键长按配置项
            ConfigurationCardItem(
                id = "power_button_long_press",
                title = "电源键长按",
                description = "通过电源键长按触发",
                operationType = ManualTriggerType.POWER_BUTTON_LONG_PRESS,
                permissionRequired = true, // 需要无障碍服务权限
                content = { type, onComplete ->
                    PowerButtonLongPressConfigContent(type, onComplete)
                }
            )
        )
    }
}

/**
 * 动态快捷方式配置内容组件
 *
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun DynamicShortcutConfigContent(type: ManualTriggerType, onComplete: (Any) -> Unit) {

    var shortcutName by rememberSaveable { mutableStateOf("") }

    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "配置动态快捷方式",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        OutlinedTextField(
            value = shortcutName,
            onValueChange = { shortcutName = it },
            label = { Text("快捷方式名称") },
            placeholder = { Text("输入快捷方式名称（可选）") },
            modifier = Modifier.fillMaxWidth()
        )

        Text(
            text = "如果不填写名称，将使用默认名称「动态快捷方式」",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        Button(
            onClick = {
                val condition = ManualTriggerCondition(
                    triggerType = type,
                    shortcutName = shortcutName.takeIf { it.isNotBlank() } ?: ""
                )
                onComplete(condition)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 静态快捷方式配置内容组件
 *
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun StaticShortcutConfigContent(type: ManualTriggerType, onComplete: (Any) -> Unit) {
    var slotIndex by rememberSaveable { mutableStateOf(0) }

    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "配置静态快捷方式",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        Text(
            text = "选择槽位",
            style = MaterialTheme.typography.bodyMedium
        )

        // 槽位选择
        Column {
            for (i in 0..3) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .selectable(
                            selected = slotIndex == i,
                            onClick = { slotIndex = i }
                        )
                        .padding(vertical = 8.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = slotIndex == i,
                        onClick = { slotIndex = i }
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("槽位 ${i + 1}")
                }
            }
        }

        Button(
            onClick = {
                val condition = ManualTriggerCondition(
                    triggerType = type,
                    slotIndex = slotIndex
                )
                onComplete(condition)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 桌面小组件配置内容组件
 *
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun DesktopWidgetConfigContent(type: ManualTriggerType, onComplete: (Any) -> Unit) {

    var slotIndex by rememberSaveable { mutableStateOf(0) }

    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "配置桌面小组件",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        Text(
            text = "选择槽位",
            style = MaterialTheme.typography.bodyMedium
        )

        // 槽位选择
        Column {
            for (i in 0..3) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .selectable(
                            selected = slotIndex == i,
                            onClick = { slotIndex = i }
                        )
                        .padding(vertical = 8.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = slotIndex == i,
                        onClick = { slotIndex = i }
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("槽位 ${i + 1}")
                }
            }
        }

        Button(
            onClick = {
                val condition = ManualTriggerCondition(
                    triggerType = type,
                    slotIndex = slotIndex
                )
                onComplete(condition)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 悬浮按钮配置内容组件
 *
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun FloatingButtonConfigContent(type: ManualTriggerType, onComplete: (Any) -> Unit) {
    var buttonText by rememberSaveable { mutableStateOf("") }
    var buttonSize by rememberSaveable { mutableStateOf("60") }
    var buttonAlpha by rememberSaveable { mutableStateOf("0.8") }

    // 输入验证
    val buttonSizeError = buttonSize.toIntOrNull()?.let { it < 20 || it > 200 } ?: true
    val buttonAlphaError = buttonAlpha.toFloatOrNull()?.let { it < 0.1f || it > 1.0f } ?: true
    val hasError = buttonSizeError || buttonAlphaError

    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "配置悬浮按钮",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        OutlinedTextField(
            value = buttonText,
            onValueChange = { buttonText = it },
            label = { Text("按钮文字") },
            placeholder = { Text("输入按钮文字（可选）") },
            modifier = Modifier.fillMaxWidth()
        )

        OutlinedTextField(
            value = buttonSize,
            onValueChange = { buttonSize = it },
            label = { Text("按钮大小 (dp)") },
            placeholder = { Text("20-200") },
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
            isError = buttonSizeError,
            modifier = Modifier.fillMaxWidth()
        )

        OutlinedTextField(
            value = buttonAlpha,
            onValueChange = { buttonAlpha = it },
            label = { Text("透明度") },
            placeholder = { Text("0.1-1.0") },
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
            isError = buttonAlphaError,
            modifier = Modifier.fillMaxWidth()
        )

        if (hasError) {
            Text(
                text = "请输入有效的参数值（大小：20-200dp，透明度：0.1-1.0）",
                color = MaterialTheme.colorScheme.error,
                style = MaterialTheme.typography.bodySmall
            )
        } else {
            Text(
                text = "建议大小：40-100dp，透明度：0.8为最佳视觉效果",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }

        Button(
            onClick = {
                val condition = ManualTriggerCondition(
                    triggerType = type,
                    buttonText = buttonText,
                    buttonSize = buttonSize.toIntOrNull() ?: 60,
                    buttonAlpha = buttonAlpha.toFloatOrNull() ?: 0.8f
                )
                onComplete(condition)
            },
            enabled = !hasError,
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 指纹手势配置内容组件
 *
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun FingerprintGestureConfigContent(type: ManualTriggerType, onComplete: (Any) -> Unit) {
    var selectedGestureType by rememberSaveable { mutableStateOf(FingerprintGestureType.TAP) }

    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "配置指纹手势",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        Text(
            text = "选择要识别的指纹手势类型：",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        // 手势类型选择
        FingerprintGestureType.values().forEach { gestureType ->
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable { selectedGestureType = gestureType }
                    .padding(vertical = 8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                RadioButton(
                    selected = selectedGestureType == gestureType,
                    onClick = { selectedGestureType = gestureType }
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = gestureType.displayName,
                    style = MaterialTheme.typography.bodyMedium
                )
            }
        }

        // 提示信息
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.5f)
            )
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "注意事项：",
                    style = MaterialTheme.typography.titleSmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "• 需要启用「快捷指令-手势识别服务」无障碍服务\n" +
                          "• 需要Android 8.0及以上版本\n" +
                          "• 设备必须支持指纹传感器\n" +
                          "• 部分手势可能因设备而异",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }

        Button(
            onClick = {
                val condition = ManualTriggerCondition(
                    triggerType = type,
                    fingerprintGestureType = selectedGestureType
                )
                onComplete(condition)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 主屏幕按钮长按配置内容组件
 *
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun HomeButtonLongPressConfigContent(type: ManualTriggerType, onComplete: (Any) -> Unit) {
    var longPressThreshold by rememberSaveable { mutableStateOf("1000") }

    // 输入验证
    val thresholdError = longPressThreshold.toLongOrNull()?.let { it < 500L || it > 5000L } ?: true
    val hasError = thresholdError

    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "配置主屏幕按钮长按",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        OutlinedTextField(
            value = longPressThreshold,
            onValueChange = { longPressThreshold = it },
            label = { Text("长按阈值 (毫秒)") },
            placeholder = { Text("500-5000") },
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
            isError = thresholdError,
            modifier = Modifier.fillMaxWidth()
        )

        if (hasError) {
            Text(
                text = "请输入有效的长按阈值（500-5000毫秒）",
                color = MaterialTheme.colorScheme.error,
                style = MaterialTheme.typography.bodySmall
            )
        } else {
            Text(
                text = "建议阈值：1000-2000毫秒为最佳用户体验",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }

        // 权限要求说明
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.5f)
            )
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "权限要求：",
                    style = MaterialTheme.typography.titleSmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "• 需要启用辅助服务权限\n" +
                          "• 需要将本应用设置为默认语音输入应用\n" +
                          "• 可能需要录音权限以确保应用持续运行\n" +
                          "• 部分设备可能需要额外的系统权限",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }

        Button(
            onClick = {
                val condition = ManualTriggerCondition(
                    triggerType = type,
                    homeButtonLongPressThreshold = longPressThreshold.toLongOrNull() ?: 1000L
                )
                onComplete(condition)
            },
            enabled = !hasError,
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 媒体键按下配置内容组件
 *
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun MediaKeyPressConfigContent(type: ManualTriggerType, onComplete: (Any) -> Unit) {
    var selectedMediaKeyType by rememberSaveable { mutableStateOf(MediaKeyType.PLAY_PAUSE) }
    var pressCount by rememberSaveable { mutableStateOf(1) }
    var pressTimeout by rememberSaveable { mutableStateOf("1000") }

    // 输入验证
    val timeoutError = pressTimeout.toLongOrNull()?.let { it < 500L || it > 5000L } ?: true
    val hasError = timeoutError

    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "配置媒体键按下",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        Text(
            text = "选择媒体键类型：",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        // 媒体键类型选择
        MediaKeyType.values().forEach { keyType ->
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable { selectedMediaKeyType = keyType }
                    .padding(vertical = 8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                RadioButton(
                    selected = selectedMediaKeyType == keyType,
                    onClick = { selectedMediaKeyType = keyType }
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = keyType.displayName,
                    style = MaterialTheme.typography.bodyMedium
                )
            }
        }

        Text(
            text = "按下次数：",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        // 按下次数选择
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            for (count in 1..3) {
                Row(
                    modifier = Modifier
                        .clickable { pressCount = count }
                        .padding(vertical = 8.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = pressCount == count,
                        onClick = { pressCount = count }
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("${count}次")
                }
            }
        }

        OutlinedTextField(
            value = pressTimeout,
            onValueChange = { pressTimeout = it },
            label = { Text("超时时间 (毫秒)") },
            placeholder = { Text("500-5000") },
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
            isError = timeoutError,
            modifier = Modifier.fillMaxWidth()
        )

        if (hasError) {
            Text(
                text = "请输入有效的超时时间（500-5000毫秒）",
                color = MaterialTheme.colorScheme.error,
                style = MaterialTheme.typography.bodySmall
            )
        } else {
            Text(
                text = "超时时间用于多次按下检测，建议1000-2000毫秒",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }

        // 功能说明
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.5f)
            )
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "使用说明：",
                    style = MaterialTheme.typography.titleSmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "• 支持有线和蓝牙耳机的媒体键\n" +
                          "• 多次按下需要在超时时间内完成\n" +
                          "• 某些设备可能对特定媒体键支持有限\n" +
                          "• 建议先测试确认设备兼容性",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }

        Button(
            onClick = {
                val condition = ManualTriggerCondition(
                    triggerType = type,
                    mediaKeyType = selectedMediaKeyType,
                    mediaKeyPressCount = pressCount,
                    mediaKeyPressTimeout = pressTimeout.toLongOrNull() ?: 1000L
                )
                onComplete(condition)
            },
            enabled = !hasError,
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 快捷方式打开配置内容组件
 *
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun ShortcutOpeningConfigContent(type: ManualTriggerType, onComplete: (Any) -> Unit) {
    var shortcutName by rememberSaveable { mutableStateOf("") }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "配置快捷方式打开触发条件",
            style = MaterialTheme.typography.titleMedium
        )

        Text(
            text = "本触发条件会为本指令打开一个应用的快捷方式。当特定的快捷方式被打开时，将触发条件。此条件使你可以通过其他支持打开快捷方式的应用或桌面来将其调用。",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        OutlinedTextField(
            value = shortcutName,
            onValueChange = { shortcutName = it },
            label = { Text("快捷方式名称") },
            placeholder = { Text("请输入快捷方式名称") },
            modifier = Modifier.fillMaxWidth()
        )

        Button(
            onClick = {
                val condition = ManualTriggerCondition(
                    triggerType = type,
                    shortcutOpeningName = shortcutName
                )
                onComplete(condition)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 滑动屏幕配置内容组件
 *
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun ScreenSwipeConfigContent(type: ManualTriggerType, onComplete: (Any) -> Unit) {
    var selectedStartCorner by rememberSaveable { mutableStateOf(SwipeStartCorner.TOP_LEFT) }
    var selectedDirection by rememberSaveable { mutableStateOf(SwipeDirection.HORIZONTAL) }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "配置滑动屏幕触发条件",
            style = MaterialTheme.typography.titleMedium
        )

        Text(
            text = "可选滑动开始区域是左上角还是右上角，还可选横过还是对角线还是下",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        // 滑动开始区域选择
        Text(
            text = "滑动开始区域",
            style = MaterialTheme.typography.titleSmall
        )

        SwipeStartCorner.values().forEach { corner ->
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable { selectedStartCorner = corner }
                    .padding(vertical = 8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                RadioButton(
                    selected = selectedStartCorner == corner,
                    onClick = { selectedStartCorner = corner }
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(text = corner.displayName)
            }
        }

        // 滑动方向选择
        Text(
            text = "滑动方向",
            style = MaterialTheme.typography.titleSmall
        )

        SwipeDirection.values().forEach { direction ->
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable { selectedDirection = direction }
                    .padding(vertical = 8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                RadioButton(
                    selected = selectedDirection == direction,
                    onClick = { selectedDirection = direction }
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(text = direction.displayName)
            }
        }

        Button(
            onClick = {
                val condition = ManualTriggerCondition(
                    triggerType = type,
                    swipeStartCorner = selectedStartCorner,
                    swipeDirection = selectedDirection
                )
                onComplete(condition)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 音量按钮长按配置内容组件
 *
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun VolumeButtonLongPressConfigContent(type: ManualTriggerType, onComplete: (Any) -> Unit) {
    var selectedVolumeButtonType by rememberSaveable { mutableStateOf(VolumeButtonType.VOLUME_UP) }
    var longPressThreshold by rememberSaveable { mutableStateOf("1000") }

    // 验证输入
    val hasError = longPressThreshold.toLongOrNull() == null || longPressThreshold.toLongOrNull()!! < 100

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "配置音量按钮长按触发条件",
            style = MaterialTheme.typography.titleMedium
        )

        Text(
            text = "通过音量按钮长按触发",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        // 音量按钮类型选择
        Text(
            text = "音量按钮类型",
            style = MaterialTheme.typography.titleSmall
        )

        VolumeButtonType.values().forEach { buttonType ->
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable { selectedVolumeButtonType = buttonType }
                    .padding(vertical = 8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                RadioButton(
                    selected = selectedVolumeButtonType == buttonType,
                    onClick = { selectedVolumeButtonType = buttonType }
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(text = buttonType.displayName)
            }
        }

        OutlinedTextField(
            value = longPressThreshold,
            onValueChange = { longPressThreshold = it },
            label = { Text("长按阈值 (毫秒)") },
            placeholder = { Text("1000") },
            isError = hasError,
            supportingText = if (hasError) {
                { Text("请输入有效的长按阈值（≥100毫秒）") }
            } else null,
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
            modifier = Modifier.fillMaxWidth()
        )

        Button(
            onClick = {
                val condition = ManualTriggerCondition(
                    triggerType = type,
                    volumeButtonType = selectedVolumeButtonType,
                    volumeButtonLongPressThreshold = longPressThreshold.toLongOrNull() ?: 1000L
                )
                onComplete(condition)
            },
            enabled = !hasError,
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 电源键长按配置内容组件
 *
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun PowerButtonLongPressConfigContent(type: ManualTriggerType, onComplete: (Any) -> Unit) {
    var longPressThreshold by rememberSaveable { mutableStateOf("1000") }

    // 验证输入
    val hasError = longPressThreshold.toLongOrNull() == null || longPressThreshold.toLongOrNull()!! < 100

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "配置电源键长按触发条件",
            style = MaterialTheme.typography.titleMedium
        )

        Text(
            text = "通过电源键长按触发",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        OutlinedTextField(
            value = longPressThreshold,
            onValueChange = { longPressThreshold = it },
            label = { Text("长按阈值 (毫秒)") },
            placeholder = { Text("1000") },
            isError = hasError,
            supportingText = if (hasError) {
                { Text("请输入有效的长按阈值（≥100毫秒）") }
            } else null,
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
            modifier = Modifier.fillMaxWidth()
        )

        Button(
            onClick = {
                val condition = ManualTriggerCondition(
                    triggerType = type,
                    powerButtonLongPressThreshold = longPressThreshold.toLongOrNull() ?: 1000L
                )
                onComplete(condition)
            },
            enabled = !hasError,
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 音量键按下配置内容组件
 *
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun VolumeKeyPressConfigContent(type: ManualTriggerType, onComplete: (Any) -> Unit) {
    var selectedVolumeKeyType by rememberSaveable { mutableStateOf(VolumeButtonType.VOLUME_UP) }
    var preserveVolume by rememberSaveable { mutableStateOf(false) }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "音量键按下配置",
            style = MaterialTheme.typography.titleMedium
        )

        Text(
            text = "选择要监听的音量键类型",
            style = MaterialTheme.typography.bodyMedium
        )

        VolumeButtonType.values().forEach { buttonType ->
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable { selectedVolumeKeyType = buttonType }
                    .padding(vertical = 8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                RadioButton(
                    selected = selectedVolumeKeyType == buttonType,
                    onClick = { selectedVolumeKeyType = buttonType }
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(text = buttonType.displayName)
            }
        }

        Text(
            text = "音量处理方式",
            style = MaterialTheme.typography.bodyMedium
        )

        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Checkbox(
                checked = preserveVolume,
                onCheckedChange = { preserveVolume = it }
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text("保留原音量（不实际改变音量）")
        }

        if (!preserveVolume) {
            Text(
                text = "注意：取消勾选时，按下音量键会正常调节音量",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }

        Button(
            onClick = {
                val condition = ManualTriggerCondition(
                    triggerType = type,
                    volumeKeyType = selectedVolumeKeyType,
                    volumeKeyPreserveVolume = preserveVolume
                )
                onComplete(condition)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}