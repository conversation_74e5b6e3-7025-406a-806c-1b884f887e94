package com.weinuo.quickcommands22.utils

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.Environment
import android.provider.Settings
import android.util.Log
import androidx.compose.runtime.Composable
import androidx.compose.ui.platform.LocalContext
import com.weinuo.quickcommands22.ui.components.ScrollableAlertDialog

/**
 * 存储权限工具类，用于处理存储权限相关操作
 */
object StoragePermissionUtil {
    private const val TAG = "StoragePermissionUtil"

    /**
     * 检查是否有管理外部存储的权限
     * 对于 Android 11 及以上版本，需要 MANAGE_EXTERNAL_STORAGE 权限
     * 对于 Android 10 及以下版本，使用传统的存储权限
     */
    fun hasStoragePermission(context: Context): <PERSON><PERSON><PERSON> {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            // Android 11 及以上使用 MANAGE_EXTERNAL_STORAGE 权限
            Environment.isExternalStorageManager()
        } else {
            // Android 10 及以下使用传统存储权限
            true // 在 AndroidManifest 中已经声明了 WRITE_EXTERNAL_STORAGE 权限，且 maxSdkVersion="29"
        }
    }

    /**
     * 打开存储权限设置页面
     * 对于 Android 11 及以上版本，打开 MANAGE_EXTERNAL_STORAGE 权限设置页面
     */
    fun openStoragePermissionSettings(context: Context) {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                // Android 11 及以上打开 MANAGE_EXTERNAL_STORAGE 权限设置页面
                val intent = Intent(Settings.ACTION_MANAGE_APP_ALL_FILES_ACCESS_PERMISSION).apply {
                    data = Uri.parse("package:${context.packageName}")
                    addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                }
                context.startActivity(intent)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error opening storage permission settings", e)
            // 如果上面的方法失败，尝试打开应用详情页面
            try {
                val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                    data = Uri.parse("package:${context.packageName}")
                    addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                }
                context.startActivity(intent)
            } catch (e2: Exception) {
                Log.e(TAG, "Error opening app details settings", e2)
            }
        }
    }

    /**
     * 显示存储权限引导对话框
     * @param onDismiss 关闭回调
     * @param onConfirm 确认回调
     */
    @Composable
    fun ShowStoragePermissionDialog(
        onDismiss: () -> Unit,
        onConfirm: () -> Unit = {}
    ) {
        val context = LocalContext.current

        val permissionDescription = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            """
                此功能需要存储管理权限才能正常工作。

                Android 11及以上版本需要"所有文件访问权限"来管理外部存储：
                • 读取和写入文件
                • 访问应用外部存储目录
                • 管理媒体文件

                授权步骤：
                1. 点击下方"前往设置"按钮
                2. 开启"允许访问所有文件"开关
                3. 返回应用重试

                这个权限仅用于您主动触发的文件操作，不会在后台自动使用。
            """.trimIndent()
        } else {
            """
                此功能需要存储权限才能正常工作。

                应用需要存储权限来：
                • 读取和写入文件
                • 访问外部存储目录
                • 管理媒体文件

                该权限已在应用清单中声明，通常会自动授予。
                如果遇到问题，请检查应用权限设置。
            """.trimIndent()
        }

        ScrollableAlertDialog(
            onDismissRequest = onDismiss,
            title = "需要存储权限",
            message = permissionDescription,
            confirmText = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) "前往设置" else "确定",
            onConfirm = {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                    openStoragePermissionSettings(context)
                }
                onConfirm()
                onDismiss()
            },
            dismissText = "取消",
            onDismiss = onDismiss
        )
    }

    /**
     * 显示存储权限引导对话框（简化版本）
     * @param context 上下文
     */
    fun showStoragePermissionDialog(context: Context) {
        // 这个方法主要用于非Compose环境
        // 在Compose环境中应该使用ShowStoragePermissionDialog
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            openStoragePermissionSettings(context)
        }
    }

    /**
     * 检查是否需要申请存储权限
     * @param context 上下文
     * @return 是否需要申请权限
     */
    fun needsStoragePermission(context: Context): Boolean {
        return !hasStoragePermission(context)
    }

    /**
     * 获取存储权限状态描述
     * @param context 上下文
     * @return 权限状态描述
     */
    fun getStoragePermissionStatusDescription(context: Context): String {
        return if (hasStoragePermission(context)) {
            "存储权限已授予"
        } else {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                "需要所有文件访问权限"
            } else {
                "需要存储权限"
            }
        }
    }
}
