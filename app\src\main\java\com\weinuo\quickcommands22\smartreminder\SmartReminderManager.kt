package com.weinuo.quickcommands22.smartreminder

import android.content.Context
import android.util.Log
import com.weinuo.quickcommands22.model.SmartReminderConfig
import com.weinuo.quickcommands22.model.SmartReminderType
import com.weinuo.quickcommands22.storage.SmartReminderConfigAdapter
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch

/**
 * 智慧提醒管理器
 *
 * 统一管理所有智慧提醒功能的生命周期，包括：
 * - 提醒功能的启动和停止
 * - 配置变更的监听和响应
 * - 提醒事件的处理和分发
 * - 与前台服务的集成
 *
 * 设计原则：
 * - 高可扩展性：便于添加新的提醒类型
 * - 模块化管理：每种提醒类型独立管理
 * - 统一接口：提供一致的管理接口
 * - 性能优化：按需启动，避免资源浪费
 */
class SmartReminderManager(
    private val context: Context
) {

    companion object {
        private const val TAG = "SmartReminderManager"
    }

    private val configAdapter = SmartReminderConfigAdapter(context)
    private val managerScope = CoroutineScope(SupervisorJob() + Dispatchers.Default)

    // 各种提醒处理器
    private var screenRotationReminderHandler: ScreenRotationReminderHandler? = null
    private var flashlightReminderHandler: FlashlightReminderHandler? = null
    private var newAppReminderHandler: NewAppReminderHandler? = null
    private var musicAppReminderHandler: MusicAppReminderHandler? = null
    private var shoppingAppReminderHandler: ShoppingAppReminderHandler? = null
    private var appLinkReminderHandler: AppLinkReminderHandler? = null
    private var shareUrlReminderHandler: ShareUrlReminderHandler? = null
    private var addressReminderHandler: AddressReminderHandler? = null

    // 当前活跃的配置
    private var activeConfigs = mutableMapOf<String, SmartReminderConfig>()

    /**
     * 启动智慧提醒管理器
     */
    fun startManager() {
        managerScope.launch {
            try {
                // 加载所有配置
                val configs = configAdapter.loadAllConfigs()
                activeConfigs.putAll(configs)

                // 启动已启用的提醒功能
                startEnabledReminders()
            } catch (e: Exception) {
                Log.e(TAG, "Error starting SmartReminderManager", e)
            }
        }
    }

    /**
     * 停止智慧提醒管理器
     */
    fun stopManager() {
        try {
            // 停止所有提醒处理器
            screenRotationReminderHandler?.stop()
            screenRotationReminderHandler = null

            flashlightReminderHandler?.stopMonitoring()
            flashlightReminderHandler = null

            newAppReminderHandler?.stopMonitoring()
            newAppReminderHandler = null

            musicAppReminderHandler?.stopMonitoring()
            musicAppReminderHandler = null

            shoppingAppReminderHandler?.stopMonitoring()
            shoppingAppReminderHandler = null

            appLinkReminderHandler?.stopMonitoring()
            appLinkReminderHandler = null

            // 清空活跃配置
            activeConfigs.clear()
        } catch (e: Exception) {
            Log.e(TAG, "Error stopping SmartReminderManager", e)
        }
    }

    /**
     * 更新提醒配置
     */
    fun updateReminderConfig(config: SmartReminderConfig) {
        managerScope.launch {
            try {
                // 保存配置
                configAdapter.saveConfig(config)

                // 更新本地缓存
                activeConfigs[config.reminderTypeId] = config

                // 重新启动相关的提醒功能
                restartReminder(config.reminderTypeId)
            } catch (e: Exception) {
                Log.e(TAG, "Error updating reminder config: ${config.reminderTypeId}", e)
            }
        }
    }

    /**
     * 获取提醒配置
     */
    fun getReminderConfig(reminderTypeId: String): SmartReminderConfig? {
        return activeConfigs[reminderTypeId]
    }

    /**
     * 启动已启用的提醒功能
     */
    private fun startEnabledReminders() {
        Log.d(TAG, "Starting enabled reminders, total configs: ${activeConfigs.size}")
        activeConfigs.values.forEach { config ->
            Log.d(TAG, "Checking config: ${config.reminderTypeId}, isAvailable: ${config.isAvailable()}, isEnabled: ${config.isEnabled}, isConfigured: ${config.isConfigured}")
            if (config.isAvailable()) {
                Log.d(TAG, "Starting reminder: ${config.reminderTypeId}")
                startReminder(config.reminderTypeId)
            } else {
                Log.d(TAG, "Skipping reminder: ${config.reminderTypeId} (not available)")
            }
        }
    }

    /**
     * 启动特定的提醒功能
     */
    private fun startReminder(reminderTypeId: String) {
        val reminderType = SmartReminderType.fromId(reminderTypeId)
        if (reminderType == null) {
            Log.w(TAG, "Unknown reminder type: $reminderTypeId")
            return
        }

        when (reminderType) {
            SmartReminderType.SCREEN_ROTATION_REMINDER -> {
                startScreenRotationReminder()
            }
            SmartReminderType.FLASHLIGHT_REMINDER -> {
                startFlashlightReminder()
            }
            SmartReminderType.NEW_APP_REMINDER -> {
                startNewAppReminder()
            }
            SmartReminderType.MUSIC_APP_REMINDER -> {
                startMusicAppReminder()
            }
            SmartReminderType.SHOPPING_APP_REMINDER -> {
                startShoppingAppReminder()
            }
            SmartReminderType.APP_LINK_REMINDER -> {
                startAppLinkReminder()
            }
            SmartReminderType.SHARE_URL_REMINDER -> {
                startShareUrlReminder()
            }
            SmartReminderType.ADDRESS_REMINDER -> {
                startAddressReminder()
            }
        }
    }

    /**
     * 停止特定的提醒功能
     */
    private fun stopReminder(reminderTypeId: String) {
        val reminderType = SmartReminderType.fromId(reminderTypeId)
        if (reminderType == null) {
            Log.w(TAG, "Unknown reminder type: $reminderTypeId")
            return
        }

        when (reminderType) {
            SmartReminderType.SCREEN_ROTATION_REMINDER -> {
                stopScreenRotationReminder()
            }
            SmartReminderType.FLASHLIGHT_REMINDER -> {
                stopFlashlightReminder()
            }
            SmartReminderType.NEW_APP_REMINDER -> {
                stopNewAppReminder()
            }
            SmartReminderType.MUSIC_APP_REMINDER -> {
                stopMusicAppReminder()
            }
            SmartReminderType.SHOPPING_APP_REMINDER -> {
                stopShoppingAppReminder()
            }
            SmartReminderType.APP_LINK_REMINDER -> {
                stopAppLinkReminder()
            }
            SmartReminderType.SHARE_URL_REMINDER -> {
                stopShareUrlReminder()
            }
            SmartReminderType.ADDRESS_REMINDER -> {
                stopAddressReminder()
            }
        }
    }

    /**
     * 重新启动特定的提醒功能
     */
    private fun restartReminder(reminderTypeId: String) {
        stopReminder(reminderTypeId)

        val config = activeConfigs[reminderTypeId]
        if (config?.isAvailable() == true) {
            startReminder(reminderTypeId)
        }
    }

    /**
     * 启动屏幕旋转提醒
     */
    private fun startScreenRotationReminder() {
        if (screenRotationReminderHandler == null) {
            screenRotationReminderHandler = ScreenRotationReminderHandler(
                context = context,
                onReminderTriggered = { eventData ->
                    handleReminderTriggered(SmartReminderType.SCREEN_ROTATION_REMINDER.id, eventData)
                }
            )
        }
        screenRotationReminderHandler?.start()
    }

    /**
     * 停止屏幕旋转提醒
     */
    private fun stopScreenRotationReminder() {
        screenRotationReminderHandler?.stop()
    }

    /**
     * 启动手电筒提醒
     */
    private fun startFlashlightReminder() {
        if (flashlightReminderHandler == null) {
            flashlightReminderHandler = FlashlightReminderHandler(
                context = context,
                onReminderTriggered = { eventData ->
                    handleReminderTriggered(SmartReminderType.FLASHLIGHT_REMINDER.id, eventData)
                }
            )
        }
        flashlightReminderHandler?.startMonitoring()
    }

    /**
     * 停止手电筒提醒
     */
    private fun stopFlashlightReminder() {
        flashlightReminderHandler?.stopMonitoring()
    }

    /**
     * 启动新应用提醒
     */
    private fun startNewAppReminder() {
        if (newAppReminderHandler == null) {
            newAppReminderHandler = NewAppReminderHandler(
                context = context,
                onReminderTriggered = { eventData ->
                    handleReminderTriggered(SmartReminderType.NEW_APP_REMINDER.id, eventData)
                }
            )
        }
        newAppReminderHandler?.startMonitoring()
    }

    /**
     * 停止新应用提醒
     */
    private fun stopNewAppReminder() {
        newAppReminderHandler?.stopMonitoring()
    }

    /**
     * 启动音乐应用提醒
     */
    private fun startMusicAppReminder() {
        if (musicAppReminderHandler == null) {
            musicAppReminderHandler = MusicAppReminderHandler(
                context = context,
                onReminderTriggered = { eventData ->
                    handleReminderTriggered(SmartReminderType.MUSIC_APP_REMINDER.id, eventData)
                }
            )
        }
        musicAppReminderHandler?.startMonitoring()
    }

    /**
     * 停止音乐应用提醒
     */
    private fun stopMusicAppReminder() {
        musicAppReminderHandler?.stopMonitoring()
    }

    /**
     * 启动购物应用提醒
     */
    private fun startShoppingAppReminder() {
        if (shoppingAppReminderHandler == null) {
            shoppingAppReminderHandler = ShoppingAppReminderHandler(
                context = context,
                onReminderTriggered = { eventData ->
                    handleReminderTriggered(SmartReminderType.SHOPPING_APP_REMINDER.id, eventData)
                }
            )
        }
        shoppingAppReminderHandler?.startMonitoring()
    }

    /**
     * 停止购物应用提醒
     */
    private fun stopShoppingAppReminder() {
        shoppingAppReminderHandler?.stopMonitoring()
    }

    /**
     * 启动应用链接提醒
     */
    private fun startAppLinkReminder() {
        if (appLinkReminderHandler == null) {
            appLinkReminderHandler = AppLinkReminderHandler(
                context = context,
                onReminderTriggered = { eventData ->
                    handleReminderTriggered(SmartReminderType.APP_LINK_REMINDER.id, eventData)
                }
            )
        }
        appLinkReminderHandler?.startMonitoring()
    }

    /**
     * 停止应用链接提醒
     */
    private fun stopAppLinkReminder() {
        appLinkReminderHandler?.stopMonitoring()
    }

    /**
     * 启动分享网址提醒
     */
    private fun startShareUrlReminder() {
        if (shareUrlReminderHandler == null) {
            shareUrlReminderHandler = ShareUrlReminderHandler(
                context = context,
                onReminderTriggered = { eventData ->
                    handleReminderTriggered(SmartReminderType.SHARE_URL_REMINDER.id, eventData)
                }
            )
        }
        shareUrlReminderHandler?.startMonitoring()
    }

    /**
     * 停止分享网址提醒
     */
    private fun stopShareUrlReminder() {
        shareUrlReminderHandler?.stopMonitoring()
    }

    /**
     * 处理提醒触发事件
     */
    private fun handleReminderTriggered(reminderTypeId: String, eventData: Map<String, Any>) {
        managerScope.launch {
            try {
                Log.d(TAG, "Smart reminder triggered: $reminderTypeId")
                // 提醒触发处理逻辑已简化，移除统计功能以节省电量
            } catch (e: Exception) {
                Log.e(TAG, "Error handling reminder trigger: $reminderTypeId", e)
            }
        }
    }

    /**
     * 重新加载所有配置
     */
    fun reloadConfigs() {
        managerScope.launch {
            try {
                // 停止所有当前的提醒
                activeConfigs.keys.forEach { reminderTypeId ->
                    stopReminder(reminderTypeId)
                }

                // 重新加载配置
                val configs = configAdapter.loadAllConfigs()
                activeConfigs.clear()
                activeConfigs.putAll(configs)

                // 重新启动已启用的提醒
                startEnabledReminders()
            } catch (e: Exception) {
                Log.e(TAG, "Error reloading configs", e)
            }
        }
    }

    /**
     * 启动地址提醒
     */
    private fun startAddressReminder() {
        if (addressReminderHandler == null) {
            addressReminderHandler = AddressReminderHandler(
                context = context,
                onReminderTriggered = { eventData ->
                    handleReminderTriggered(SmartReminderType.ADDRESS_REMINDER.id, eventData)
                }
            )
        }
        addressReminderHandler?.startMonitoring()
    }

    /**
     * 停止地址提醒
     */
    private fun stopAddressReminder() {
        addressReminderHandler?.stopMonitoring()
    }

    /**
     * 检查特定提醒是否正在运行
     */
    fun isReminderActive(reminderTypeId: String): Boolean {
        val config = activeConfigs[reminderTypeId]
        return config?.isAvailable() == true
    }
}
