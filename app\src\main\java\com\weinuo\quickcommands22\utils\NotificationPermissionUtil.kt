package com.weinuo.quickcommands22.utils

import android.Manifest
import android.app.NotificationManager
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Build
import android.provider.Settings
import androidx.core.content.ContextCompat
import androidx.compose.runtime.*
import androidx.compose.ui.platform.LocalContext
import com.weinuo.quickcommands22.ui.components.ScrollableAlertDialog

/**
 * 通知权限工具类
 * 管理通知相关权限的检查、申请和引导
 * 适用于通知发送、通知监听、气泡通知等功能
 */
object NotificationPermissionUtil {

    /**
     * 检查是否拥有发送通知权限
     * Android 13+ (API 33+) 需要POST_NOTIFICATIONS权限
     * @param context 上下文
     * @return 是否拥有发送通知权限
     */
    fun hasPostNotificationPermission(context: Context): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            ContextCompat.checkSelfPermission(
                context,
                Manifest.permission.POST_NOTIFICATIONS
            ) == PackageManager.PERMISSION_GRANTED
        } else {
            // Android 13以下版本不需要此权限
            true
        }
    }

    /**
     * 检查是否拥有通知使用权限（通知监听）
     * 用于清除通知、通知交互等功能
     * @param context 上下文
     * @return 是否拥有通知使用权限
     */
    fun hasNotificationListenerPermission(context: Context): Boolean {
        return DeviceEventPermissionUtil.hasNotificationListenerPermission(context)
    }



    /**
     * 检查是否允许显示气泡通知
     * Android 10+ (API 29+) 支持气泡通知
     * @param context 上下文
     * @return 是否允许显示气泡通知
     */
    fun areBubblesAllowed(context: Context): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            try {
                val bubblesAllowed = notificationManager.areBubblesAllowed()
                val notificationsEnabled = notificationManager.areNotificationsEnabled()

                android.util.Log.d("NotificationPermissionUtil",
                    "Bubble permission check: bubblesAllowed=$bubblesAllowed, notificationsEnabled=$notificationsEnabled")

                // 气泡通知需要通知权限也被启用
                bubblesAllowed && notificationsEnabled
            } catch (e: Exception) {
                android.util.Log.e("NotificationPermissionUtil", "Error checking bubble permission", e)
                // 如果方法调用失败，返回false
                false
            }
        } else {
            // Android 10以下版本不支持气泡通知
            false
        }
    }

    /**
     * 检查通知是否已启用
     * @param context 上下文
     * @return 通知是否已启用
     */
    fun areNotificationsEnabled(context: Context): Boolean {
        val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        return notificationManager.areNotificationsEnabled()
    }

    /**
     * 获取需要申请的通知权限列表
     * @return 权限列表
     */
    fun getRequiredNotificationPermissions(): Array<String> {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            arrayOf(Manifest.permission.POST_NOTIFICATIONS)
        } else {
            emptyArray()
        }
    }

    /**
     * 申请通知权限
     * @param launcher 权限申请启动器
     */
    fun requestNotificationPermissions(launcher: androidx.activity.result.ActivityResultLauncher<Array<String>>) {
        val permissions = getRequiredNotificationPermissions()
        if (permissions.isNotEmpty()) {
            launcher.launch(permissions)
        }
    }

    /**
     * 引导用户开启通知使用权限
     * 跳转到系统设置的通知使用权限页面
     * @param context 上下文
     */
    fun requestNotificationListenerPermission(context: Context) {
        DeviceEventPermissionUtil.requestNotificationListenerPermission(context)
    }





    /**
     * 引导用户开启气泡通知权限
     * 跳转到系统设置的气泡通知权限页面
     * @param context 上下文
     */
    fun requestBubblePermission(context: Context) {
        try {
            android.util.Log.d("NotificationPermissionUtil", "Requesting bubble permission for package: ${context.packageName}")

            val intent = Intent().apply {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                    // 首先尝试直接跳转到气泡通知设置页面
                    action = Settings.ACTION_APP_NOTIFICATION_BUBBLE_SETTINGS
                    putExtra(Settings.EXTRA_APP_PACKAGE, context.packageName)
                    flags = Intent.FLAG_ACTIVITY_NEW_TASK

                    android.util.Log.d("NotificationPermissionUtil", "Using ACTION_APP_NOTIFICATION_BUBBLE_SETTINGS")
                } else {
                    // Android 10以下版本跳转到通知设置页面
                    action = Settings.ACTION_APP_NOTIFICATION_SETTINGS
                    putExtra(Settings.EXTRA_APP_PACKAGE, context.packageName)
                    flags = Intent.FLAG_ACTIVITY_NEW_TASK

                    android.util.Log.d("NotificationPermissionUtil", "Using ACTION_APP_NOTIFICATION_SETTINGS (Android < 10)")
                }
            }

            // 检查Intent是否可以被处理
            if (intent.resolveActivity(context.packageManager) != null) {
                context.startActivity(intent)
                android.util.Log.d("NotificationPermissionUtil", "Successfully started bubble permission activity")
            } else {
                android.util.Log.w("NotificationPermissionUtil", "No activity found for bubble permission intent, falling back")
                throw Exception("No activity found for bubble permission intent")
            }
        } catch (e: Exception) {
            android.util.Log.e("NotificationPermissionUtil", "Error opening bubble permission settings", e)
            // 如果无法打开气泡通知设置，回退到通用通知设置
            openNotificationSettings(context)
        }
    }



    /**
     * 打开通知设置页面
     * @param context 上下文
     */
    fun openNotificationSettings(context: Context) {
        try {
            val intent = Intent().apply {
                when {
                    Build.VERSION.SDK_INT >= Build.VERSION_CODES.O -> {
                        action = Settings.ACTION_APP_NOTIFICATION_SETTINGS
                        putExtra(Settings.EXTRA_APP_PACKAGE, context.packageName)
                    }
                    else -> {
                        action = Settings.ACTION_APPLICATION_DETAILS_SETTINGS
                        data = android.net.Uri.parse("package:${context.packageName}")
                    }
                }
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }
            context.startActivity(intent)
        } catch (e: Exception) {
            // 如果无法打开应用通知设置，尝试打开通用设置
            try {
                val intent = Intent(Settings.ACTION_SETTINGS).apply {
                    flags = Intent.FLAG_ACTIVITY_NEW_TASK
                }
                context.startActivity(intent)
            } catch (e2: Exception) {
                // 忽略异常，用户需要手动到设置中开启
            }
        }
    }

    /**
     * 通知权限说明对话框
     * @param onDismiss 关闭回调
     * @param onConfirm 确认回调
     */
    @Composable
    fun NotificationPermissionRationaleDialog(
        onDismiss: () -> Unit,
        onConfirm: () -> Unit
    ) {
        val context = LocalContext.current

        ScrollableAlertDialog(
            onDismissRequest = onDismiss,
            title = "通知权限说明",
            message = "此功能需要通知权限才能正常工作：\n\n" +
                    "• 发送通知权限：用于显示系统通知\n" +
                    "• 通知使用权限：用于清除和交互通知\n\n" +
                    "请在系统设置中授权相应权限。",
            confirmText = "前往设置",
            onConfirm = {
                onConfirm()
                openNotificationSettings(context)
            },
            dismissText = "取消",
            onDismiss = onDismiss
        )
    }

    /**
     * 通知使用权限说明对话框
     * @param onDismiss 关闭回调
     * @param onConfirm 确认回调
     */
    @Composable
    fun NotificationListenerPermissionRationaleDialog(
        onDismiss: () -> Unit,
        onConfirm: () -> Unit
    ) {
        DeviceEventPermissionUtil.NotificationListenerPermissionRationaleDialog(
            onDismiss = onDismiss,
            onConfirm = onConfirm
        )
    }



    /**
     * 气泡通知权限说明对话框
     * @param onDismiss 关闭回调
     * @param onConfirm 确认回调
     */
    @Composable
    fun BubblePermissionRationaleDialog(
        onDismiss: () -> Unit,
        onConfirm: () -> Unit
    ) {
        val context = LocalContext.current

        ScrollableAlertDialog(
            onDismissRequest = onDismiss,
            title = "气泡通知权限说明",
            message = "此功能需要气泡通知权限才能正常工作：\n\n" +
                    "• 用于显示气泡样式的通知\n" +
                    "• 气泡通知可以浮动在其他应用上方\n" +
                    "• 提供更便捷的通知交互体验\n\n" +
                    "请在系统设置中允许此应用显示气泡通知。\n\n" +
                    "注意：首次显示气泡通知时，系统会自动弹出权限对话框。",
            confirmText = "前往设置",
            onConfirm = {
                onConfirm()
                requestBubblePermission(context)
            },
            dismissText = "取消",
            onDismiss = onDismiss
        )
    }
}
