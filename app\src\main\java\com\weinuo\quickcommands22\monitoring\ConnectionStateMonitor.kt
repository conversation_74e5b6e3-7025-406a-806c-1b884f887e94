package com.weinuo.quickcommands22.monitoring

import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothDevice
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.net.ConnectivityManager
import android.net.Network
import android.net.NetworkCapabilities
import android.net.NetworkRequest
import android.net.wifi.WifiManager
import android.os.Build
import android.telephony.TelephonyManager
import android.util.Log
import com.weinuo.quickcommands22.model.*
import kotlinx.coroutines.*

/**
 * 连接状态监听器
 * 监听WiFi、蓝牙、移动数据、网络连接等状态变化，触发相应的条件
 *
 * 支持的监听功能：
 * - WiFi状态变化（开启/关闭、连接/断开、特定网络）
 * - 蓝牙状态变化（开启/关闭、设备连接/断开）
 * - 移动数据状态变化（连接/断开、漫游状态）
 * - 通用网络连接状态
 * - 手机信号状态
 */
class ConnectionStateMonitor(
    private val context: Context,
    private val onConditionTriggered: (ConnectionStateCondition, Map<String, Any>) -> Unit
) {

    companion object {
        private const val TAG = "ConnectionStateMonitor"
    }

    // 协程作用域
    private val monitorScope = CoroutineScope(Dispatchers.Default + SupervisorJob())

    // 监听器状态
    private var isMonitoring = false

    // 注册的条件列表
    private val registeredConditions = mutableListOf<ConnectionStateCondition>()

    // 系统服务
    private val wifiManager: WifiManager? by lazy {
        context.applicationContext.getSystemService(Context.WIFI_SERVICE) as? WifiManager
    }
    private val connectivityManager: ConnectivityManager by lazy {
        context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
    }
    private val telephonyManager: TelephonyManager by lazy {
        context.getSystemService(Context.TELEPHONY_SERVICE) as TelephonyManager
    }

    // 广播接收器
    private var wifiReceiver: BroadcastReceiver? = null
    private var bluetoothReceiver: BroadcastReceiver? = null
    private var connectivityReceiver: BroadcastReceiver? = null

    // 网络回调
    private var networkCallback: ConnectivityManager.NetworkCallback? = null

    // 状态缓存
    private var lastWifiState: Int = WifiManager.WIFI_STATE_UNKNOWN
    private var lastBluetoothState: Int = BluetoothAdapter.STATE_OFF
    private var lastNetworkConnected: Boolean = false
    private var lastMobileDataConnected: Boolean = false
    private var lastConnectedWifiSsid: String = ""

    /**
     * 注册连接状态条件
     */
    fun registerCondition(condition: ConnectionStateCondition) {
        synchronized(registeredConditions) {
            if (!registeredConditions.any { it.id == condition.id }) {
                registeredConditions.add(condition)
                Log.d(TAG, "Registered connection condition: ${condition.getDescription()}")
            }
        }
    }

    /**
     * 取消注册连接状态条件
     */
    fun unregisterCondition(conditionId: String) {
        synchronized(registeredConditions) {
            registeredConditions.removeAll { it.id == conditionId }
            Log.d(TAG, "Unregistered connection condition: $conditionId")
        }
    }

    /**
     * 清除所有注册的条件
     */
    fun clearAllConditions() {
        synchronized(registeredConditions) {
            registeredConditions.clear()
            Log.d(TAG, "Cleared all connection conditions")
        }
    }

    /**
     * 开始监听连接状态
     */
    fun startMonitoring() {
        if (isMonitoring) {
            Log.d(TAG, "Connection monitoring already started")
            return
        }

        try {
            setupWifiMonitoring()
            setupBluetoothMonitoring()
            setupNetworkMonitoring()

            // 初始化状态缓存
            initializeStateCache()

            isMonitoring = true
            Log.d(TAG, "Connection monitoring started successfully")

        } catch (e: Exception) {
            Log.e(TAG, "Error starting connection monitoring", e)
            stopMonitoring()
        }
    }

    /**
     * 停止监听连接状态
     */
    fun stopMonitoring() {
        if (!isMonitoring) {
            Log.d(TAG, "Connection monitoring already stopped")
            return
        }

        try {
            // 取消注册广播接收器
            wifiReceiver?.let { context.unregisterReceiver(it) }
            bluetoothReceiver?.let { context.unregisterReceiver(it) }
            connectivityReceiver?.let { context.unregisterReceiver(it) }

            // 取消网络回调
            networkCallback?.let { connectivityManager.unregisterNetworkCallback(it) }

            wifiReceiver = null
            bluetoothReceiver = null
            connectivityReceiver = null
            networkCallback = null

            isMonitoring = false
            Log.d(TAG, "Connection monitoring stopped")

        } catch (e: Exception) {
            Log.e(TAG, "Error stopping connection monitoring", e)
        }
    }

    /**
     * 初始化状态缓存
     */
    private fun initializeStateCache() {
        try {
            // 初始化WiFi状态
            wifiManager?.let { wifi ->
                lastWifiState = wifi.wifiState
                if (wifi.isWifiEnabled) {
                    val wifiInfo = wifi.connectionInfo
                    lastConnectedWifiSsid = wifiInfo?.ssid?.removeSurrounding("\"") ?: ""
                }
            }

            // 初始化蓝牙状态
            val bluetoothAdapter = BluetoothAdapter.getDefaultAdapter()
            lastBluetoothState = bluetoothAdapter?.state ?: BluetoothAdapter.STATE_OFF

            // 初始化网络状态
            val activeNetwork = connectivityManager.activeNetwork
            lastNetworkConnected = activeNetwork != null

            val networkCapabilities = activeNetwork?.let { connectivityManager.getNetworkCapabilities(it) }
            lastMobileDataConnected = networkCapabilities?.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) == true

            Log.d(TAG, "State cache initialized - WiFi: $lastWifiState, Bluetooth: $lastBluetoothState, Network: $lastNetworkConnected, Mobile: $lastMobileDataConnected")

        } catch (e: Exception) {
            Log.e(TAG, "Error initializing state cache", e)
        }
    }

    /**
     * 设置WiFi监听
     */
    private fun setupWifiMonitoring() {
        wifiReceiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context, intent: Intent) {
                monitorScope.launch {
                    handleWifiStateChange(intent)
                }
            }
        }

        val wifiFilter = IntentFilter().apply {
            addAction(WifiManager.WIFI_STATE_CHANGED_ACTION)
            addAction(WifiManager.NETWORK_STATE_CHANGED_ACTION)
            addAction(WifiManager.SUPPLICANT_CONNECTION_CHANGE_ACTION)
        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            context.registerReceiver(wifiReceiver, wifiFilter, Context.RECEIVER_EXPORTED)
        } else {
            context.registerReceiver(wifiReceiver, wifiFilter)
        }

        Log.d(TAG, "WiFi monitoring setup completed")
    }

    /**
     * 设置蓝牙监听
     */
    private fun setupBluetoothMonitoring() {
        bluetoothReceiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context, intent: Intent) {
                monitorScope.launch {
                    handleBluetoothStateChange(intent)
                }
            }
        }

        val bluetoothFilter = IntentFilter().apply {
            addAction(BluetoothAdapter.ACTION_STATE_CHANGED)
            addAction(BluetoothDevice.ACTION_ACL_CONNECTED)
            addAction(BluetoothDevice.ACTION_ACL_DISCONNECTED)
        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            context.registerReceiver(bluetoothReceiver, bluetoothFilter, Context.RECEIVER_EXPORTED)
        } else {
            context.registerReceiver(bluetoothReceiver, bluetoothFilter)
        }
    }

    /**
     * 设置网络监听
     */
    private fun setupNetworkMonitoring() {
        // 使用NetworkCallback监听网络变化
        networkCallback = object : ConnectivityManager.NetworkCallback() {
            override fun onAvailable(network: Network) {
                monitorScope.launch {
                    handleNetworkAvailable(network)
                }
            }

            override fun onLost(network: Network) {
                monitorScope.launch {
                    handleNetworkLost(network)
                }
            }

            override fun onCapabilitiesChanged(network: Network, networkCapabilities: NetworkCapabilities) {
                monitorScope.launch {
                    handleNetworkCapabilitiesChanged(network, networkCapabilities)
                }
            }
        }

        val networkRequest = NetworkRequest.Builder()
            .addCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
            .build()

        connectivityManager.registerNetworkCallback(networkRequest, networkCallback!!)

        Log.d(TAG, "Network monitoring setup completed")
    }

    /**
     * 处理WiFi状态变化
     */
    private suspend fun handleWifiStateChange(intent: Intent) {
        try {
            val action = intent.action
            Log.d(TAG, "WiFi state changed: $action")

            when (action) {
                WifiManager.WIFI_STATE_CHANGED_ACTION -> {
                    val wifiState = intent.getIntExtra(WifiManager.EXTRA_WIFI_STATE, WifiManager.WIFI_STATE_UNKNOWN)
                    handleWifiStateChanged(wifiState)
                }
                WifiManager.NETWORK_STATE_CHANGED_ACTION -> {
                    val networkInfo = intent.getParcelableExtra<android.net.NetworkInfo>(WifiManager.EXTRA_NETWORK_INFO)
                    handleWifiNetworkStateChanged(networkInfo)
                }
                WifiManager.SUPPLICANT_CONNECTION_CHANGE_ACTION -> {
                    val connected = intent.getBooleanExtra(WifiManager.EXTRA_SUPPLICANT_CONNECTED, false)
                    handleWifiSupplicantConnectionChange(connected)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error handling WiFi state change", e)
        }
    }

    /**
     * 处理WiFi开关状态变化
     */
    private suspend fun handleWifiStateChanged(wifiState: Int) {
        if (wifiState == lastWifiState) return

        Log.d(TAG, "WiFi state changed: $lastWifiState -> $wifiState")

        val matchingConditions = registeredConditions.filter { condition ->
            condition.connectionType == ConnectionType.WIFI_STATE &&
            when (condition.subType) {
                ConnectionSubType.WIFI_ENABLED -> wifiState == WifiManager.WIFI_STATE_ENABLED
                ConnectionSubType.WIFI_DISABLED -> wifiState == WifiManager.WIFI_STATE_DISABLED
                else -> false
            }
        }

        for (condition in matchingConditions) {
            val eventData = mapOf(
                "wifiState" to wifiState,
                "previousState" to lastWifiState,
                "timestamp" to System.currentTimeMillis()
            )
            onConditionTriggered(condition, eventData)
        }

        lastWifiState = wifiState
    }

    /**
     * 处理WiFi网络连接状态变化
     */
    private suspend fun handleWifiNetworkStateChanged(networkInfo: android.net.NetworkInfo?) {
        if (networkInfo == null) return

        val isConnected = networkInfo.isConnected
        val currentSsid = wifiManager?.connectionInfo?.ssid?.removeSurrounding("\"") ?: ""

        Log.d(TAG, "WiFi network state changed - Connected: $isConnected, SSID: $currentSsid")

        val matchingConditions = registeredConditions.filter { condition ->
            when (condition.connectionType) {
                ConnectionType.WIFI_STATE -> {
                    when (condition.subType) {
                        ConnectionSubType.WIFI_CONNECTED -> isConnected
                        ConnectionSubType.WIFI_DISCONNECTED -> !isConnected
                        else -> false
                    }
                }
                ConnectionType.WIFI_NETWORK -> {
                    isConnected && (condition.specificValue.isEmpty() || condition.specificValue == currentSsid)
                }
                else -> false
            }
        }

        for (condition in matchingConditions) {
            val eventData = mapOf(
                "connected" to isConnected,
                "ssid" to currentSsid,
                "previousSsid" to lastConnectedWifiSsid,
                "timestamp" to System.currentTimeMillis()
            )
            onConditionTriggered(condition, eventData)
        }

        if (isConnected) {
            lastConnectedWifiSsid = currentSsid
        }
    }

    /**
     * 处理WiFi Supplicant连接变化
     */
    private suspend fun handleWifiSupplicantConnectionChange(connected: Boolean) {
        Log.d(TAG, "WiFi supplicant connection changed: $connected")

        val matchingConditions = registeredConditions.filter { condition ->
            condition.connectionType == ConnectionType.WIFI_STATE &&
            when (condition.subType) {
                ConnectionSubType.WIFI_CONNECTED -> connected
                ConnectionSubType.WIFI_DISCONNECTED -> !connected
                else -> false
            }
        }

        for (condition in matchingConditions) {
            val eventData = mapOf(
                "supplicantConnected" to connected,
                "timestamp" to System.currentTimeMillis()
            )
            onConditionTriggered(condition, eventData)
        }
    }

    /**
     * 处理蓝牙状态变化
     */
    private suspend fun handleBluetoothStateChange(intent: Intent) {
        try {
            val action = intent.action
            Log.d(TAG, "Bluetooth state changed: $action")

            when (action) {
                BluetoothAdapter.ACTION_STATE_CHANGED -> {
                    val state = intent.getIntExtra(BluetoothAdapter.EXTRA_STATE, BluetoothAdapter.ERROR)
                    handleBluetoothAdapterStateChanged(state)
                }
                BluetoothDevice.ACTION_ACL_CONNECTED -> {
                    val device = intent.getParcelableExtra<BluetoothDevice>(BluetoothDevice.EXTRA_DEVICE)
                    handleBluetoothDeviceConnected(device)
                }
                BluetoothDevice.ACTION_ACL_DISCONNECTED -> {
                    val device = intent.getParcelableExtra<BluetoothDevice>(BluetoothDevice.EXTRA_DEVICE)
                    handleBluetoothDeviceDisconnected(device)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error handling Bluetooth state change", e)
        }
    }

    /**
     * 处理蓝牙适配器状态变化
     */
    private suspend fun handleBluetoothAdapterStateChanged(state: Int) {
        if (state == lastBluetoothState) return

        Log.d(TAG, "Bluetooth adapter state changed: $lastBluetoothState -> $state")

        val matchingConditions = registeredConditions.filter { condition ->
            condition.connectionType == ConnectionType.BLUETOOTH_STATE &&
            when (condition.subType) {
                ConnectionSubType.BLUETOOTH_ENABLED -> state == BluetoothAdapter.STATE_ON
                ConnectionSubType.BLUETOOTH_DISABLED -> state == BluetoothAdapter.STATE_OFF
                else -> false
            }
        }

        for (condition in matchingConditions) {
            val eventData = mapOf(
                "bluetoothState" to state,
                "previousState" to lastBluetoothState,
                "timestamp" to System.currentTimeMillis()
            )
            onConditionTriggered(condition, eventData)
        }

        lastBluetoothState = state
    }

    /**
     * 处理蓝牙设备连接
     */
    private suspend fun handleBluetoothDeviceConnected(device: BluetoothDevice?) {
        if (device == null) return

        Log.d(TAG, "Bluetooth device connected: ${device.name} (${device.address})")

        val matchingConditions = registeredConditions.filter { condition ->
            condition.connectionType == ConnectionType.BLUETOOTH_DEVICE &&
            condition.subType == ConnectionSubType.BLUETOOTH_DEVICE_CONNECTED &&
            (condition.deviceAddress.isEmpty() || condition.deviceAddress == device.address) &&
            (condition.specificValue.isEmpty() || condition.specificValue == device.name)
        }

        for (condition in matchingConditions) {
            val eventData = mapOf(
                "deviceName" to (device.name ?: "Unknown"),
                "deviceAddress" to device.address,
                "timestamp" to System.currentTimeMillis()
            )
            onConditionTriggered(condition, eventData)
        }
    }

    /**
     * 处理蓝牙设备断开
     */
    private suspend fun handleBluetoothDeviceDisconnected(device: BluetoothDevice?) {
        if (device == null) return

        Log.d(TAG, "Bluetooth device disconnected: ${device.name} (${device.address})")

        val matchingConditions = registeredConditions.filter { condition ->
            condition.connectionType == ConnectionType.BLUETOOTH_DEVICE &&
            condition.subType == ConnectionSubType.BLUETOOTH_DEVICE_DISCONNECTED &&
            (condition.deviceAddress.isEmpty() || condition.deviceAddress == device.address) &&
            (condition.specificValue.isEmpty() || condition.specificValue == device.name)
        }

        for (condition in matchingConditions) {
            val eventData = mapOf(
                "deviceName" to (device.name ?: "Unknown"),
                "deviceAddress" to device.address,
                "timestamp" to System.currentTimeMillis()
            )
            onConditionTriggered(condition, eventData)
        }
    }

    /**
     * 处理网络可用
     */
    private suspend fun handleNetworkAvailable(network: Network) {
        Log.d(TAG, "Network available: $network")

        val networkCapabilities = connectivityManager.getNetworkCapabilities(network)
        val isConnected = true
        val isMobile = networkCapabilities?.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) == true
        val isWifi = networkCapabilities?.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) == true

        // 更新状态缓存
        lastNetworkConnected = true
        if (isMobile) {
            lastMobileDataConnected = true
        }

        val matchingConditions = registeredConditions.filter { condition ->
            when (condition.connectionType) {
                ConnectionType.NETWORK_GENERAL -> {
                    condition.subType == ConnectionSubType.NETWORK_CONNECTED
                }
                ConnectionType.MOBILE_DATA -> {
                    isMobile && condition.subType == ConnectionSubType.MOBILE_DATA_CONNECTED
                }
                ConnectionType.ROAMING_STATE -> {
                    isMobile && telephonyManager.isNetworkRoaming &&
                    condition.subType == ConnectionSubType.ROAMING_ENABLED
                }
                else -> false
            }
        }

        for (condition in matchingConditions) {
            val eventData = mapOf(
                "networkType" to when {
                    isWifi -> "WiFi"
                    isMobile -> "Mobile"
                    else -> "Other"
                },
                "isRoaming" to telephonyManager.isNetworkRoaming,
                "timestamp" to System.currentTimeMillis()
            )
            onConditionTriggered(condition, eventData)
        }
    }

    /**
     * 处理网络丢失
     */
    private suspend fun handleNetworkLost(network: Network) {
        Log.d(TAG, "Network lost: $network")

        // 检查是否还有其他网络连接
        val activeNetwork = connectivityManager.activeNetwork
        val stillConnected = activeNetwork != null

        // 更新状态缓存
        lastNetworkConnected = stillConnected

        // 检查移动数据是否断开
        val activeCapabilities = activeNetwork?.let { connectivityManager.getNetworkCapabilities(it) }
        val stillHasMobile = activeCapabilities?.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) == true

        if (!stillHasMobile) {
            lastMobileDataConnected = false
        }

        val matchingConditions = registeredConditions.filter { condition ->
            when (condition.connectionType) {
                ConnectionType.NETWORK_GENERAL -> {
                    !stillConnected && condition.subType == ConnectionSubType.NETWORK_DISCONNECTED
                }
                ConnectionType.MOBILE_DATA -> {
                    !stillHasMobile && condition.subType == ConnectionSubType.MOBILE_DATA_DISCONNECTED
                }
                else -> false
            }
        }

        for (condition in matchingConditions) {
            val eventData = mapOf(
                "stillConnected" to stillConnected,
                "stillHasMobile" to stillHasMobile,
                "timestamp" to System.currentTimeMillis()
            )
            onConditionTriggered(condition, eventData)
        }
    }

    /**
     * 处理网络能力变化
     */
    private suspend fun handleNetworkCapabilitiesChanged(network: Network, networkCapabilities: NetworkCapabilities) {
        Log.d(TAG, "Network capabilities changed: $network")

        val isMobile = networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR)
        val isRoaming = telephonyManager.isNetworkRoaming

        // 检查漫游状态变化
        if (isMobile) {
            val matchingConditions = registeredConditions.filter { condition ->
                condition.connectionType == ConnectionType.ROAMING_STATE &&
                when (condition.subType) {
                    ConnectionSubType.ROAMING_ENABLED -> isRoaming
                    ConnectionSubType.ROAMING_DISABLED -> !isRoaming
                    else -> false
                }
            }

            for (condition in matchingConditions) {
                val eventData = mapOf(
                    "isRoaming" to isRoaming,
                    "timestamp" to System.currentTimeMillis()
                )
                onConditionTriggered(condition, eventData)
            }
        }
    }

    /**
     * 获取监听状态
     */
    fun isMonitoring(): Boolean = isMonitoring

    /**
     * 获取注册的条件数量
     */
    fun getRegisteredConditionCount(): Int = registeredConditions.size

    /**
     * 获取当前WiFi状态
     */
    fun getCurrentWifiState(): Int = lastWifiState

    /**
     * 获取当前蓝牙状态
     */
    fun getCurrentBluetoothState(): Int = lastBluetoothState

    /**
     * 获取当前网络连接状态
     */
    fun isCurrentlyNetworkConnected(): Boolean = lastNetworkConnected

    /**
     * 获取当前移动数据连接状态
     */
    fun isCurrentlyMobileDataConnected(): Boolean = lastMobileDataConnected

    /**
     * 获取当前连接的WiFi SSID
     */
    fun getCurrentWifiSsid(): String = lastConnectedWifiSsid

    /**
     * 手动触发状态检查（用于调试和测试）
     */
    fun triggerStateCheck() {
        monitorScope.launch {
            try {
                Log.d(TAG, "Manual state check triggered")

                // 重新初始化状态缓存并检查变化
                val oldWifiState = lastWifiState
                val oldBluetoothState = lastBluetoothState
                val oldNetworkConnected = lastNetworkConnected
                val oldMobileDataConnected = lastMobileDataConnected

                initializeStateCache()

                // 如果状态有变化，触发相应的处理
                if (lastWifiState != oldWifiState) {
                    handleWifiStateChanged(lastWifiState)
                }
                if (lastBluetoothState != oldBluetoothState) {
                    handleBluetoothAdapterStateChanged(lastBluetoothState)
                }
                if (lastNetworkConnected != oldNetworkConnected) {
                    if (lastNetworkConnected) {
                        connectivityManager.activeNetwork?.let { handleNetworkAvailable(it) }
                    } else {
                        // 模拟网络丢失事件
                        Log.d(TAG, "Network disconnected during manual check")
                    }
                }

            } catch (e: Exception) {
                Log.e(TAG, "Error during manual state check", e)
            }
        }
    }

    /**
     * 清理资源
     */
    fun cleanup() {
        stopMonitoring()
        monitorScope.cancel()
        registeredConditions.clear()
        Log.d(TAG, "Connection monitor cleaned up")
    }
}
