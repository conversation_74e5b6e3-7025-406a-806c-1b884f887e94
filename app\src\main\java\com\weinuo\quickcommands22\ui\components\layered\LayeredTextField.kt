package com.weinuo.quickcommands22.ui.components.layered

import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import com.weinuo.quickcommands22.ui.theme.config.TextFieldConfig

/**
 * 分层设计风格的文本输入框
 *
 * 特点：
 * - 使用标准Material 3 OutlinedTextField设计
 * - 简洁清晰的视觉分离
 * - 遵循Material Design 3规范
 * - 保持与old项目一致的简洁风格
 */
@Composable
fun LayeredTextField(
    config: TextFieldConfig,
    modifier: Modifier = Modifier
) {
    OutlinedTextField(
        value = config.value,
        onValueChange = config.onValueChange,
        modifier = modifier,
        enabled = config.enabled,
        label = config.label?.let { { Text(it) } },
        placeholder = config.placeholder?.let { { Text(it) } },
        leadingIcon = config.leadingIcon?.let { { Icon(it, contentDescription = null) } },
        trailingIcon = config.trailingIcon?.let {
            {
                IconButton(onClick = config.onTrailingIconClick ?: {}) {
                    Icon(it, contentDescription = null)
                }
            }
        },
        supportingText = config.supportingText?.let { { Text(it) } },
        isError = config.isError,
        singleLine = config.singleLine,
        maxLines = config.maxLines
    )
}


