package com.weinuo.quickcommands22.ui.activities

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.runtime.Composable
import com.weinuo.quickcommands22.utils.ContactsHelper
import com.weinuo.quickcommands22.ui.screens.ContactSelectionMode
import com.weinuo.quickcommands22.ui.screens.ContactSelectionScreen
import com.weinuo.quickcommands22.ui.theme.QuickCommandsTheme
import com.weinuo.quickcommands22.storage.UIStateStorageManager

/**
 * 联系人选择Activity
 * 
 * 提供独立的联系人选择界面，支持单选和多选模式
 * 替代原有的NavController导航方式，提供更好的用户体验
 */
class ContactSelectionActivity : ComponentActivity() {
    
    companion object {
        private const val EXTRA_SELECTION_MODE = "selection_mode"
        private const val EXTRA_SELECTED_CONTACT_IDS = "selected_contact_ids"
        private const val EXTRA_RESULT_KEY = "result_key"
        const val RESULT_SELECTED_CONTACTS = "selected_contacts"

        /**
         * 启动单选联系人界面
         */
        fun startForSingleSelection(
            context: Context,
            resultKey: String = "selected_contacts"
        ) {
            val intent = Intent(context, ContactSelectionActivity::class.java).apply {
                putExtra(EXTRA_SELECTION_MODE, ContactSelectionMode.SINGLE.name)
                putExtra(EXTRA_RESULT_KEY, resultKey)
            }
            context.startActivity(intent)
        }

        /**
         * 启动多选联系人界面
         */
        fun startForMultiSelection(
            context: Context,
            selectedContactIds: List<String> = emptyList(),
            resultKey: String = "selected_contacts"
        ) {
            val intent = Intent(context, ContactSelectionActivity::class.java).apply {
                putExtra(EXTRA_SELECTION_MODE, ContactSelectionMode.MULTI.name)
                putStringArrayListExtra(EXTRA_SELECTED_CONTACT_IDS, ArrayList(selectedContactIds))
                putExtra(EXTRA_RESULT_KEY, resultKey)
            }
            context.startActivity(intent)
        }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 获取传入的参数
        val selectionModeString = intent.getStringExtra(EXTRA_SELECTION_MODE) ?: ContactSelectionMode.SINGLE.name
        val selectedContactIds = intent.getStringArrayListExtra(EXTRA_SELECTED_CONTACT_IDS) ?: arrayListOf()
        val resultKey = intent.getStringExtra(EXTRA_RESULT_KEY) ?: "selected_contacts"

        val selectionMode = try {
            ContactSelectionMode.valueOf(selectionModeString)
        } catch (e: IllegalArgumentException) {
            ContactSelectionMode.SINGLE
        }
        
        setContent {
            QuickCommandsTheme {
                ContactSelectionActivityContent(
                    selectionMode = selectionMode,
                    initialSelectedContactIds = selectedContactIds,
                    onContactsSelected = { selectedContacts ->
                        finishWithResult(selectedContacts)
                    },
                    onFinish = { finish() }
                )
            }
        }
    }
    
    /**
     * 返回选择结果并结束Activity
     */
    private fun finishWithResult(selectedContacts: List<ContactsHelper.ContactInfo>) {
        val resultKey = intent.getStringExtra(EXTRA_RESULT_KEY) ?: "selected_contacts"

        // 使用UIStateStorageManager存储联系人信息
        val uiStateManager = UIStateStorageManager(this)

        // 将联系人信息转换为简单的字符串存储
        val contactIds = selectedContacts.map { it.id }
        val contactNames = selectedContacts.map { it.name }
        val contactPhones = selectedContacts.map { it.phoneNumber }

        // 存储联系人数据
        uiStateManager.saveSimpleState(resultKey, "contact_count", selectedContacts.size)
        contactIds.forEachIndexed { index, id ->
            uiStateManager.saveSimpleState(resultKey, "contact_${index}_id", id)
        }
        contactNames.forEachIndexed { index, name ->
            uiStateManager.saveSimpleState(resultKey, "contact_${index}_name", name)
        }
        contactPhones.forEachIndexed { index, phone ->
            uiStateManager.saveSimpleState(resultKey, "contact_${index}_phone", phone)
        }

        val intent = Intent().apply {
            putExtra(RESULT_SELECTED_CONTACTS, resultKey)
        }
        setResult(Activity.RESULT_OK, intent)
        finish()
    }
}

/**
 * 联系人选择Activity的内容组件
 * 
 * 不依赖NavController的可复用组件，可以在Activity中使用
 */
@Composable
fun ContactSelectionActivityContent(
    selectionMode: ContactSelectionMode,
    initialSelectedContactIds: List<String> = emptyList(),
    onContactsSelected: (List<ContactsHelper.ContactInfo>) -> Unit,
    onFinish: () -> Unit
) {
    ContactSelectionScreen(
        selectionMode = selectionMode,
        initialSelectedContactIds = initialSelectedContactIds,
        onContactsSelected = onContactsSelected,
        onDismiss = onFinish
    )
}
