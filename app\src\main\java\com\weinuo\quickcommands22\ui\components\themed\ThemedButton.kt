package com.weinuo.quickcommands22.ui.components.themed

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.material3.ButtonDefaults
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.graphics.vector.ImageVector
import com.weinuo.quickcommands22.ui.theme.config.ButtonConfig
import com.weinuo.quickcommands22.ui.theme.config.IconPosition
import com.weinuo.quickcommands22.ui.theme.manager.LocalThemeContext

/**
 * 主题感知的按钮组件
 *
 * 根据当前主题自动选择合适的实现：
 * - 海洋蓝主题：使用分层设计风格（LayeredButton）- 带阴影效果
 * - 天空蓝主题：使用整合设计风格（IntegratedButton）- 无阴影，大圆角
 * - 未来主题：可以使用各自独有的实现
 *
 * 支持图标按钮、文本按钮等多种变体，
 * 以及不同的图标位置（前、后、上）。
 */
@Composable
fun ThemedButton(
    config: ButtonConfig,
    modifier: Modifier = Modifier
) {
    val themeContext = LocalThemeContext.current
    
    // 使用当前主题的组件工厂创建按钮
    themeContext.componentFactory.createButton()(
        config.copy(modifier = modifier)
    )
}

/**
 * 主题感知的按钮组件 - 便捷版本
 *
 * 提供更简洁的API，自动处理常见的配置
 */
@Composable
fun ThemedButton(
    onClick: () -> Unit,
    text: String,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    icon: ImageVector? = null,
    iconPosition: IconPosition = IconPosition.START,
    backgroundColor: Color? = null,
    contentColor: Color? = null,
    border: BorderStroke? = null,
    shape: Shape? = null,
    contentPadding: PaddingValues = ButtonDefaults.ContentPadding
) {
    val config = ButtonConfig(
        onClick = onClick,
        text = text,
        modifier = modifier,
        enabled = enabled,
        icon = icon,
        iconPosition = iconPosition,
        backgroundColor = backgroundColor,
        contentColor = contentColor,
        border = border,
        shape = shape,
        contentPadding = contentPadding
    )
    
    // 使用主题感知组件
    ThemedButton(config = config)
}

/**
 * 主题感知的简单按钮 - 最简版本
 *
 * 只需要文本和点击事件的最简单版本
 */
@Composable
fun ThemedButton(
    onClick: () -> Unit,
    text: String,
    modifier: Modifier = Modifier
) {
    ThemedButton(
        onClick = onClick,
        text = text,
        modifier = modifier,
        enabled = true
    )
}

/**
 * 主题感知的图标按钮 - 专用版本
 *
 * 专门用于带图标的按钮
 */
@Composable
fun ThemedIconButton(
    onClick: () -> Unit,
    text: String,
    icon: ImageVector,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    iconPosition: IconPosition = IconPosition.START
) {
    ThemedButton(
        onClick = onClick,
        text = text,
        icon = icon,
        iconPosition = iconPosition,
        modifier = modifier,
        enabled = enabled
    )
}
