package com.weinuo.quickcommands22.ui.screens

import androidx.compose.runtime.Composable
import androidx.navigation.NavController
import com.weinuo.quickcommands22.data.SettingsRepository
import com.weinuo.quickcommands22.data.QuickCommandRepository
import com.weinuo.quickcommands22.shortcut.ShortcutManager
import com.weinuo.quickcommands22.utils.ExperimentalFeatureDetector

/**
 * 界面工厂接口
 *
 * 定义创建主题特定界面实现的统一接口
 * 每个主题可以提供完全不同的界面实现，实现高度的视觉和功能差异化
 */
interface ScreenFactory {
    /**
     * 创建快捷指令界面
     *
     * @param navController 导航控制器
     * @param quickCommandRepository 快捷指令仓库
     * @param shortcutManager 快捷方式管理器
     * @return 返回一个Composable函数
     */
    @Composable
    fun createQuickCommandsScreen(
        navController: NavController,
        quickCommandRepository: QuickCommandRepository = QuickCommandRepository.getInstance(navController.context),
        shortcutManager: ShortcutManager = ShortcutManager(navController.context)
    )

    /**
     * 创建全局设置界面
     *
     * @param settingsRepository 设置仓库
     * @param experimentalFeatureDetector 实验性功能检测器
     * @return 返回一个Composable函数
     */
    @Composable
    fun createGlobalSettingsScreen(
        settingsRepository: SettingsRepository,
        experimentalFeatureDetector: ExperimentalFeatureDetector? = null
    )

    /**
     * 创建命令模板界面
     *
     * @param navController 导航控制器
     * @param quickCommandRepository 快捷指令仓库
     * @return 返回一个Composable函数
     */
    @Composable
    fun createCommandTemplatesScreen(
        navController: NavController,
        quickCommandRepository: QuickCommandRepository
    )

    /**
     * 创建智能提醒界面
     *
     * @param navController 导航控制器
     * @return 返回一个Composable函数
     */
    @Composable
    fun createSmartRemindersScreen(navController: NavController)

    /**
     * 获取界面工厂的版本
     *
     * @return 工厂版本，用于兼容性检查
     */
    fun getVersion(): String {
        return "1.0.0"
    }

    /**
     * 检查是否支持特定界面类型
     *
     * @param screenType 界面类型
     * @return 是否支持该界面
     */
    fun supportsScreen(screenType: ScreenType): Boolean {
        return true // 默认支持所有界面
    }
}

/**
 * 界面类型枚举
 *
 * 定义应用中所有可用的界面类型
 */
enum class ScreenType {
    /**
     * 快捷指令界面
     */
    QUICK_COMMANDS,

    /**
     * 全局设置界面
     */
    GLOBAL_SETTINGS,

    /**
     * 命令模板界面
     */
    COMMAND_TEMPLATES,

    /**
     * 智能提醒界面
     */
    SMART_REMINDERS,

    /**
     * 快捷指令表单界面
     */
    QUICK_COMMAND_FORM,

    /**
     * 详细配置界面
     */
    DETAIL_CONFIGURATION,

    /**
     * 应用选择界面
     */
    APP_SELECTION,

    /**
     * 联系人选择界面
     */
    CONTACT_SELECTION,

    /**
     * 铃声选择界面
     */
    RINGTONE_SELECTION,

    /**
     * 账户选择界面
     */
    ACCOUNT_SELECTION,

    /**
     * 分享目标选择界面
     */
    SHARE_TARGET_SELECTION,

    /**
     * 秒表选择界面
     */
    STOPWATCH_SELECTION,

    /**
     * 内存学习数据界面
     */
    MEMORY_LEARNING_DATA,

    /**
     * 高级内存配置界面
     */
    ADVANCED_MEMORY_CONFIG,

    /**
     * 应用重要性管理界面
     */
    APP_IMPORTANCE_MANAGEMENT,

    /**
     * 高级清理策略界面
     */
    ADVANCED_CLEANUP_STRATEGY,

    /**
     * 添加清理规则界面
     */
    ADD_CLEANUP_RULE,

    /**
     * 自定义应用平台配置界面
     */
    CUSTOM_APP_PLATFORM_CONFIG,

    /**
     * 自定义购物平台配置界面
     */
    CUSTOM_SHOPPING_PLATFORM_CONFIG,

    /**
     * 智能提醒详细配置界面
     */
    SMART_REMINDER_DETAIL_CONFIG,

    /**
     * 统一配置界面
     */
    UNIFIED_CONFIGURATION,

    /**
     * 详细配置界面（新版）
     */
    DETAILED_CONFIGURATION
}
