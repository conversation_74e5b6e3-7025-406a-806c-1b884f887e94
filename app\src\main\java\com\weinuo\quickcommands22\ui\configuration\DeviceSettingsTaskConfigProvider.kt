package com.weinuo.quickcommands22.ui.configuration

import android.content.Context
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.selection.selectableGroup
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import com.weinuo.quickcommands22.R
import com.weinuo.quickcommands22.model.*
import com.weinuo.quickcommands22.navigation.Screen
import com.weinuo.quickcommands22.storage.UIStateStorageManager
import com.weinuo.quickcommands22.ui.components.ConfigurationCardItem
import com.weinuo.quickcommands22.ui.screens.LocalNavController
import com.weinuo.quickcommands22.utils.FilePickerUtil

/**
 * 设备设置任务配置数据提供器
 *
 * 提供设备设置任务的配置项，包括反色、字体大小、屏幕自动旋转、
 * 无障碍服务、显示密度、沉浸模式、深色主题、演示模式、环境显示、
 * 省电模式、系统设置、壁纸设置、屏幕锁定、数字助理、键盘设置、驾驶模式等。
 *
 * 使用通用组件复用配置系统，支持高度可扩展的配置界面。
 */
object DeviceSettingsTaskConfigProvider {

    /**
     * 获取设备设置任务配置项列表
     *
     * @param context 上下文，用于获取字符串资源
     * @return 设备设置任务配置项列表
     */
    fun getConfigurationItems(context: Context): List<ConfigurationCardItem<DeviceSettingsOperation>> {
        return listOf(
            // 反色配置项
            ConfigurationCardItem(
                id = "invert_colors",
                title = context.getString(R.string.device_settings_invert_colors),
                description = context.getString(R.string.device_settings_invert_colors_description),
                operationType = DeviceSettingsOperation.INVERT_COLORS,
                permissionRequired = true,
                content = { operation, onComplete ->
                    InvertColorsConfigContent(operation, onComplete)
                }
            ),

            // 字体大小配置项
            ConfigurationCardItem(
                id = "font_size",
                title = context.getString(R.string.device_settings_font_size),
                description = context.getString(R.string.device_settings_font_size_description),
                operationType = DeviceSettingsOperation.FONT_SIZE,
                permissionRequired = true,
                content = { operation, onComplete ->
                    FontSizeConfigContent(operation, onComplete)
                }
            ),

            // 进入屏保模式配置项
            ConfigurationCardItem(
                id = "enter_screensaver",
                title = context.getString(R.string.device_settings_enter_screensaver),
                description = context.getString(R.string.device_settings_enter_screensaver_description),
                operationType = DeviceSettingsOperation.ENTER_SCREENSAVER,
                permissionRequired = true,
                content = { operation, onComplete ->
                    SimpleDeviceSettingsConfigContent(operation, onComplete)
                }
            ),

            // 屏幕自动旋转配置项
            ConfigurationCardItem(
                id = "auto_rotate",
                title = context.getString(R.string.device_settings_auto_rotate),
                description = context.getString(R.string.device_settings_auto_rotate_description),
                operationType = DeviceSettingsOperation.AUTO_ROTATE,
                permissionRequired = true,
                content = { operation, onComplete ->
                    AutoRotateConfigContent(operation, onComplete)
                }
            ),

            // 无障碍服务配置项
            ConfigurationCardItem(
                id = "accessibility_service",
                title = context.getString(R.string.device_settings_accessibility_service),
                description = context.getString(R.string.device_settings_accessibility_service_description),
                operationType = DeviceSettingsOperation.ACCESSIBILITY_SERVICE,
                permissionRequired = true,
                content = { operation, onComplete ->
                    AccessibilityServiceConfigContent(operation, onComplete)
                }
            ),

            // 显示密度配置项
            ConfigurationCardItem(
                id = "display_density",
                title = context.getString(R.string.device_settings_display_density),
                description = context.getString(R.string.device_settings_display_density_description),
                operationType = DeviceSettingsOperation.DISPLAY_DENSITY,
                permissionRequired = true,
                content = { operation, onComplete ->
                    DisplayDensityConfigContent(operation, onComplete)
                }
            ),

            // 沉浸模式配置项
            ConfigurationCardItem(
                id = "immersive_mode",
                title = context.getString(R.string.device_settings_immersive_mode),
                description = context.getString(R.string.device_settings_immersive_mode_description),
                operationType = DeviceSettingsOperation.IMMERSIVE_MODE,
                permissionRequired = true,
                content = { operation, onComplete ->
                    ImmersiveModeConfigContent(operation, onComplete)
                }
            ),

            // 深色主题配置项
            ConfigurationCardItem(
                id = "dark_theme",
                title = "深色主题",
                description = "控制系统深色主题模式",
                operationType = DeviceSettingsOperation.DARK_THEME,
                permissionRequired = true,
                content = { operation, onComplete ->
                    DarkThemeConfigContent(operation, onComplete)
                }
            ),

            // 演示模式配置项
            ConfigurationCardItem(
                id = "demo_mode",
                title = "演示模式",
                description = "控制系统演示模式",
                operationType = DeviceSettingsOperation.DEMO_MODE,
                permissionRequired = true,
                content = { operation, onComplete ->
                    DemoModeConfigContent(operation, onComplete)
                }
            ),

            // 环境显示配置项
            ConfigurationCardItem(
                id = "ambient_display",
                title = "环境显示",
                description = "配置环境显示模式",
                operationType = DeviceSettingsOperation.AMBIENT_DISPLAY,
                permissionRequired = true,
                content = { operation, onComplete ->
                    AmbientDisplayConfigContent(operation, onComplete)
                }
            ),

            // 省电模式配置项
            ConfigurationCardItem(
                id = "power_save_mode",
                title = "省电模式",
                description = "控制系统省电模式",
                operationType = DeviceSettingsOperation.POWER_SAVE_MODE,
                permissionRequired = true,
                content = { operation, onComplete ->
                    PowerSaveModeConfigContent(operation, onComplete)
                }
            ),

            // 系统设置配置项
            ConfigurationCardItem(
                id = "system_settings",
                title = "系统设置",
                description = "修改系统设置表中的键值对",
                operationType = DeviceSettingsOperation.SYSTEM_SETTINGS,
                permissionRequired = true,
                content = { operation, onComplete ->
                    SystemSettingsConfigContent(operation, onComplete)
                }
            ),

            // 设置壁纸配置项
            ConfigurationCardItem(
                id = "set_wallpaper",
                title = "设置壁纸",
                description = "设置系统壁纸",
                operationType = DeviceSettingsOperation.SET_WALLPAPER,
                permissionRequired = true,
                content = { operation, onComplete ->
                    SetWallpaperConfigContent(operation, onComplete)
                }
            ),

            // 屏幕锁定配置项
            ConfigurationCardItem(
                id = "screen_lock",
                title = "设置屏幕锁定",
                description = "控制屏幕锁定功能",
                operationType = DeviceSettingsOperation.SCREEN_LOCK,
                permissionRequired = true,
                content = { operation, onComplete ->
                    ScreenLockConfigContent(operation, onComplete)
                }
            ),

            // 数字助理配置项
            ConfigurationCardItem(
                id = "digital_assistant",
                title = "设置数字助理",
                description = "设置默认数字助理应用",
                operationType = DeviceSettingsOperation.DIGITAL_ASSISTANT,
                permissionRequired = true,
                content = { operation, onComplete ->
                    DigitalAssistantConfigContent(operation, onComplete)
                }
            ),

            // 默认键盘配置项
            ConfigurationCardItem(
                id = "default_keyboard",
                title = "键盘-设置默认值",
                description = "设置默认输入法键盘",
                operationType = DeviceSettingsOperation.DEFAULT_KEYBOARD,
                permissionRequired = true,
                content = { operation, onComplete ->
                    DefaultKeyboardConfigContent(operation, onComplete)
                }
            ),

            // 键盘提示配置项
            ConfigurationCardItem(
                id = "keyboard_hint",
                title = context.getString(R.string.device_settings_keyboard_hint),
                description = context.getString(R.string.device_settings_keyboard_hint_description),
                operationType = DeviceSettingsOperation.KEYBOARD_HINT,
                permissionRequired = true,
                content = { operation, onComplete ->
                    SimpleDeviceSettingsConfigContent(operation, onComplete)
                }
            ),

            // 驾驶模式配置项
            ConfigurationCardItem(
                id = "driving_mode",
                title = context.getString(R.string.device_settings_driving_mode),
                description = context.getString(R.string.device_settings_driving_mode_description),
                operationType = DeviceSettingsOperation.DRIVING_MODE,
                permissionRequired = true,
                content = { operation, onComplete ->
                    DrivingModeConfigContent(operation, onComplete)
                }
            )
        )
    }
}

/**
 * 反色配置内容组件
 */
@Composable
private fun InvertColorsConfigContent(
    operation: DeviceSettingsOperation,
    onComplete: (Any) -> Unit
) {
    var selectedOperation by rememberSaveable { mutableStateOf(TriStateOperation.TOGGLE) }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "反色操作",
            style = MaterialTheme.typography.titleMedium
        )

        // 三态操作选择器
        TriStateOperationSelector(
            selectedOperation = selectedOperation,
            onOperationSelected = { selectedOperation = it }
        )

        // 确认按钮
        Button(
            onClick = {
                val task = DeviceSettingsTask(
                    operation = operation,
                    invertColorsOperation = selectedOperation
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 字体大小配置内容组件
 */
@Composable
private fun FontSizeConfigContent(
    operation: DeviceSettingsOperation,
    onComplete: (Any) -> Unit
) {
    var fontSizePercentage by rememberSaveable { mutableStateOf("100") }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "字体大小设置",
            style = MaterialTheme.typography.titleMedium
        )

        OutlinedTextField(
            value = fontSizePercentage,
            onValueChange = { fontSizePercentage = it },
            label = { Text("字体大小百分比") },
            placeholder = { Text("100") },
            modifier = Modifier.fillMaxWidth(),
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number)
        )

        // 确认按钮
        Button(
            onClick = {
                val task = DeviceSettingsTask(
                    operation = operation,
                    fontSizePercentage = fontSizePercentage.toIntOrNull() ?: 100
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 屏幕自动旋转配置内容组件
 */
@Composable
private fun AutoRotateConfigContent(
    operation: DeviceSettingsOperation,
    onComplete: (Any) -> Unit
) {
    var selectedOperation by rememberSaveable { mutableStateOf(TriStateOperation.TOGGLE) }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "自动旋转操作",
            style = MaterialTheme.typography.titleMedium
        )

        // 三态操作选择器
        TriStateOperationSelector(
            selectedOperation = selectedOperation,
            onOperationSelected = { selectedOperation = it }
        )

        // 确认按钮
        Button(
            onClick = {
                val task = DeviceSettingsTask(
                    operation = operation,
                    autoRotateOperation = selectedOperation
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 通用简单设备设置配置内容组件
 * 用于不需要额外参数的设备设置操作类型
 */
@Composable
private fun SimpleDeviceSettingsConfigContent(
    operation: DeviceSettingsOperation,
    onComplete: (Any) -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "该操作无需额外配置参数",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        // 确认按钮
        Button(
            onClick = {
                val task = DeviceSettingsTask(
                    operation = operation
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 无障碍服务配置内容组件
 */
@Composable
private fun AccessibilityServiceConfigContent(
    operation: DeviceSettingsOperation,
    onComplete: (Any) -> Unit
) {
    var selectedOperation by rememberSaveable { mutableStateOf(TriStateOperation.TOGGLE) }
    // 分别保存SimpleAppInfo的各个字段，避免自定义Saver的复杂性
    var selectedServicePackageName by rememberSaveable { mutableStateOf("") }
    var selectedServiceName by rememberSaveable { mutableStateOf("") }
    var selectedServiceIsSystemApp by rememberSaveable { mutableStateOf(false) }

    // 根据保存的字段重建SimpleAppInfo对象
    val selectedServiceApp = if (selectedServicePackageName.isNotEmpty()) {
        SimpleAppInfo(
            packageName = selectedServicePackageName,
            appName = selectedServiceName,
            isSystemApp = selectedServiceIsSystemApp
        )
    } else {
        null
    }

    val navController = LocalNavController.current
    val context = LocalContext.current

    // 监听应用选择结果（使用原生数据类型存储）
    LaunchedEffect(Unit) {
        val savedStateHandle = navController?.currentBackStackEntry?.savedStateHandle
        val navigationKey = savedStateHandle?.get<String>("selected_apps_navigation_key")

        if (navigationKey != null) {
            val uiStateManager = UIStateStorageManager(context)
            val selectedAppsResult = uiStateManager.loadAppListState(navigationKey, "selected_apps")

            if (selectedAppsResult.isNotEmpty()) {
                val app = selectedAppsResult.first()
                selectedServicePackageName = app.packageName
                selectedServiceName = app.appName
                selectedServiceIsSystemApp = app.isSystemApp
            }

            // 清除结果，避免重复处理
            savedStateHandle.remove<String>("selected_apps_navigation_key")
            uiStateManager.clearAppListState(navigationKey, "selected_apps")
        }
    }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "无障碍服务设置",
            style = MaterialTheme.typography.titleMedium
        )

        // 三态操作选择器
        TriStateOperationSelector(
            selectedOperation = selectedOperation,
            onOperationSelected = { selectedOperation = it }
        )

        Text(
            text = "选择无障碍服务应用",
            style = MaterialTheme.typography.titleMedium
        )

        OutlinedButton(
            onClick = {
                if (navController != null) {
                    val route = Screen.AppSelection.createSingleSelectionRoute()
                    navController.navigate(route)
                }
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text(
                text = if (selectedServiceApp != null) {
                    "已选择: ${selectedServiceApp.appName}"
                } else {
                    "点击选择无障碍服务应用"
                }
            )
        }

        if (selectedServiceApp != null) {
            Text(
                text = "包名: ${selectedServiceApp.packageName}",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }

        // 确认按钮
        Button(
            onClick = {
                val task = DeviceSettingsTask(
                    operation = operation,
                    accessibilityServiceOperation = selectedOperation,
                    accessibilityServicePackage = selectedServiceApp?.packageName ?: ""
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth(),
            enabled = selectedServiceApp != null
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 显示密度配置内容组件
 */
@Composable
private fun DisplayDensityConfigContent(
    operation: DeviceSettingsOperation,
    onComplete: (Any) -> Unit
) {
    var densityPercentage by rememberSaveable { mutableStateOf("100") }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "显示密度设置",
            style = MaterialTheme.typography.titleMedium
        )

        OutlinedTextField(
            value = densityPercentage,
            onValueChange = { densityPercentage = it },
            label = { Text("显示密度百分比") },
            placeholder = { Text("100") },
            modifier = Modifier.fillMaxWidth(),
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number)
        )

        // 确认按钮
        Button(
            onClick = {
                val task = DeviceSettingsTask(
                    operation = operation,
                    displayDensityPercentage = densityPercentage.toIntOrNull() ?: 100
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 沉浸模式配置内容组件
 */
@Composable
private fun ImmersiveModeConfigContent(
    operation: DeviceSettingsOperation,
    onComplete: (Any) -> Unit
) {
    var selectedType by rememberSaveable { mutableStateOf(ImmersiveModeType.FULL_IMMERSIVE) }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "沉浸模式设置",
            style = MaterialTheme.typography.titleMedium
        )

        // 沉浸模式类型选择器
        ImmersiveModeTypeSelector(
            selectedType = selectedType,
            onTypeSelected = { selectedType = it }
        )

        // 确认按钮
        Button(
            onClick = {
                val task = DeviceSettingsTask(
                    operation = operation,
                    immersiveModeType = selectedType
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 深色主题配置内容组件
 */
@Composable
private fun DarkThemeConfigContent(
    operation: DeviceSettingsOperation,
    onComplete: (Any) -> Unit
) {
    var selectedOperation by rememberSaveable { mutableStateOf(TriStateOperation.TOGGLE) }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "深色主题操作",
            style = MaterialTheme.typography.titleMedium
        )

        // 三态操作选择器
        TriStateOperationSelector(
            selectedOperation = selectedOperation,
            onOperationSelected = { selectedOperation = it }
        )

        // 确认按钮
        Button(
            onClick = {
                val task = DeviceSettingsTask(
                    operation = operation,
                    darkThemeOperation = selectedOperation
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 演示模式配置内容组件
 */
@Composable
private fun DemoModeConfigContent(
    operation: DeviceSettingsOperation,
    onComplete: (Any) -> Unit
) {
    var selectedOperation by rememberSaveable { mutableStateOf(TriStateOperation.TOGGLE) }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "演示模式操作",
            style = MaterialTheme.typography.titleMedium
        )

        // 三态操作选择器
        TriStateOperationSelector(
            selectedOperation = selectedOperation,
            onOperationSelected = { selectedOperation = it }
        )

        // 确认按钮
        Button(
            onClick = {
                val task = DeviceSettingsTask(
                    operation = operation,
                    demoModeOperation = selectedOperation
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 环境显示配置内容组件
 */
@Composable
private fun AmbientDisplayConfigContent(
    operation: DeviceSettingsOperation,
    onComplete: (Any) -> Unit
) {
    var selectedMode by rememberSaveable { mutableStateOf(AmbientDisplayMode.NOTIFICATION_WAKE) }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "环境显示模式",
            style = MaterialTheme.typography.titleMedium
        )

        // 环境显示模式选择器
        AmbientDisplayModeSelector(
            selectedMode = selectedMode,
            onModeSelected = { selectedMode = it }
        )

        // 确认按钮
        Button(
            onClick = {
                val task = DeviceSettingsTask(
                    operation = operation,
                    ambientDisplayMode = selectedMode
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 省电模式配置内容组件
 */
@Composable
private fun PowerSaveModeConfigContent(
    operation: DeviceSettingsOperation,
    onComplete: (Any) -> Unit
) {
    var selectedOperation by rememberSaveable { mutableStateOf(TriStateOperation.TOGGLE) }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "省电模式操作",
            style = MaterialTheme.typography.titleMedium
        )

        // 三态操作选择器
        TriStateOperationSelector(
            selectedOperation = selectedOperation,
            onOperationSelected = { selectedOperation = it }
        )

        // 确认按钮
        Button(
            onClick = {
                val task = DeviceSettingsTask(
                    operation = operation,
                    powerSaveModeOperation = selectedOperation
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 驾驶模式配置内容组件
 */
@Composable
private fun DrivingModeConfigContent(
    operation: DeviceSettingsOperation,
    onComplete: (Any) -> Unit
) {
    var selectedOperation by rememberSaveable { mutableStateOf(TriStateOperation.TOGGLE) }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "驾驶模式操作",
            style = MaterialTheme.typography.titleMedium
        )

        // 三态操作选择器
        TriStateOperationSelector(
            selectedOperation = selectedOperation,
            onOperationSelected = { selectedOperation = it }
        )

        // 确认按钮
        Button(
            onClick = {
                val task = DeviceSettingsTask(
                    operation = operation,
                    drivingModeOperation = selectedOperation
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 系统设置配置内容组件
 */
@Composable
private fun SystemSettingsConfigContent(
    operation: DeviceSettingsOperation,
    onComplete: (Any) -> Unit
) {
    var selectedTable by rememberSaveable { mutableStateOf(SystemSettingsTable.SYSTEM) }
    var settingsKey by rememberSaveable { mutableStateOf("") }
    var settingsValue by rememberSaveable { mutableStateOf("") }
    var selectedValueType by rememberSaveable { mutableStateOf(SystemSettingsValueType.STRING) }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "系统设置配置",
            style = MaterialTheme.typography.titleMedium
        )

        // 系统设置表选择器
        SystemSettingsTableSelector(
            selectedTable = selectedTable,
            onTableSelected = { selectedTable = it }
        )

        OutlinedTextField(
            value = settingsKey,
            onValueChange = { settingsKey = it },
            label = { Text("设置键名") },
            placeholder = { Text("例如: screen_brightness") },
            modifier = Modifier.fillMaxWidth()
        )

        OutlinedTextField(
            value = settingsValue,
            onValueChange = { settingsValue = it },
            label = { Text("设置值") },
            placeholder = { Text("例如: 128") },
            modifier = Modifier.fillMaxWidth()
        )

        // 值类型选择器
        SystemSettingsValueTypeSelector(
            selectedType = selectedValueType,
            onTypeSelected = { selectedValueType = it }
        )

        // 确认按钮
        Button(
            onClick = {
                val task = DeviceSettingsTask(
                    operation = operation,
                    systemSettingsTable = selectedTable,
                    systemSettingsKey = settingsKey,
                    systemSettingsValue = settingsValue,
                    systemSettingsValueType = selectedValueType
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 设置壁纸配置内容组件
 */
@Composable
private fun SetWallpaperConfigContent(
    operation: DeviceSettingsOperation,
    onComplete: (Any) -> Unit
) {
    val context = LocalContext.current

    var selectedType by rememberSaveable { mutableStateOf(WallpaperType.IMAGE) }
    var imagePath by rememberSaveable { mutableStateOf("") }
    var selectedLocation by rememberSaveable { mutableStateOf(WallpaperLocation.HOME_SCREEN) }
    var selectedFileName by rememberSaveable { mutableStateOf("") }

    // 动态壁纸应用选择相关状态
    var selectedLiveWallpaperPackageName by rememberSaveable { mutableStateOf("") }
    var selectedLiveWallpaperName by rememberSaveable { mutableStateOf("") }
    var selectedLiveWallpaperIsSystemApp by rememberSaveable { mutableStateOf(false) }

    // 根据保存的字段重建SimpleAppInfo对象
    val selectedLiveWallpaperApp = if (selectedLiveWallpaperPackageName.isNotEmpty()) {
        SimpleAppInfo(
            packageName = selectedLiveWallpaperPackageName,
            appName = selectedLiveWallpaperName,
            isSystemApp = selectedLiveWallpaperIsSystemApp
        )
    } else {
        null
    }

    val navController = LocalNavController.current

    // 监听应用选择结果（使用原生数据类型存储）
    LaunchedEffect(Unit) {
        val savedStateHandle = navController?.currentBackStackEntry?.savedStateHandle
        val navigationKey = savedStateHandle?.get<String>("selected_apps_navigation_key")

        if (navigationKey != null) {
            val uiStateManager = UIStateStorageManager(context)
            val selectedAppsResult = uiStateManager.loadAppListState(navigationKey, "selected_apps")

            if (selectedAppsResult.isNotEmpty()) {
                // 自动切换到动态壁纸选项
                selectedType = WallpaperType.LIVE_WALLPAPER
                // 更新选中的应用
                val app = selectedAppsResult.first()
                selectedLiveWallpaperPackageName = app.packageName
                selectedLiveWallpaperName = app.appName
                selectedLiveWallpaperIsSystemApp = app.isSystemApp
            }

            // 清除结果，避免重复处理
            savedStateHandle.remove<String>("selected_apps_navigation_key")
            uiStateManager.clearAppListState(navigationKey, "selected_apps")
        }
    }



    // 文件选择器启动器
    val fileLauncher = FilePickerUtil.rememberFilePickerLauncher { uri ->
        uri?.let {
            // 尝试获取真实路径，如果失败则使用URI字符串
            val realPath = FilePickerUtil.getRealPath(context, it)
            imagePath = realPath ?: it.toString()
            // 使用显示路径仅用于UI显示
            selectedFileName = FilePickerUtil.getDisplayPath(context, it)
        }
    }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "壁纸设置",
            style = MaterialTheme.typography.titleMedium
        )

        // 壁纸类型选择器
        WallpaperTypeSelector(
            selectedType = selectedType,
            onTypeSelected = { selectedType = it }
        )

        if (selectedType == WallpaperType.IMAGE) {
            Text(
                text = "选择壁纸图片",
                style = MaterialTheme.typography.bodyMedium
            )

            // 文件选择按钮
            Button(
                onClick = {
                    FilePickerUtil.launchFilePicker(
                        fileLauncher,
                        arrayOf("image/*")
                    )
                },
                modifier = Modifier.fillMaxWidth()
            ) {
                Text(
                    if (selectedFileName.isEmpty()) {
                        "选择图片文件"
                    } else {
                        "已选择: $selectedFileName"
                    }
                )
            }

            // 显示选择的文件路径（用于调试和确认）
            if (imagePath.isNotEmpty()) {
                Text(
                    text = "文件路径: $imagePath",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }

        if (selectedType == WallpaperType.LIVE_WALLPAPER) {
            Text(
                text = "选择动态壁纸应用",
                style = MaterialTheme.typography.bodyMedium
            )

            OutlinedButton(
                onClick = {
                    if (navController != null) {
                        val route = Screen.AppSelection.createLiveWallpaperSingleSelectionRoute()
                        navController.navigate(route)
                    }
                },
                modifier = Modifier.fillMaxWidth()
            ) {
                Text(
                    text = if (selectedLiveWallpaperApp != null) {
                        "已选择: ${selectedLiveWallpaperApp.appName}"
                    } else {
                        "点击选择动态壁纸应用"
                    }
                )
            }

            if (selectedLiveWallpaperApp != null) {
                Text(
                    text = "包名: ${selectedLiveWallpaperApp.packageName}",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }

            // 添加说明文字
            Text(
                text = "不幸的是，Android无法在没有用户交互的情况下设置动态壁纸，所以这个任务将会带您进入预览屏幕",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.7f),
                modifier = Modifier.padding(top = 8.dp)
            )
        }

        // 壁纸位置选择器
        WallpaperLocationSelector(
            selectedLocation = selectedLocation,
            onLocationSelected = { selectedLocation = it }
        )

        // 确认按钮
        Button(
            onClick = {
                val task = DeviceSettingsTask(
                    operation = operation,
                    wallpaperType = selectedType,
                    wallpaperImagePath = imagePath,
                    wallpaperLocation = selectedLocation,
                    liveWallpaperPackage = selectedLiveWallpaperApp?.packageName ?: ""
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth(),
            enabled = when (selectedType) {
                WallpaperType.IMAGE -> imagePath.isNotEmpty()
                WallpaperType.LIVE_WALLPAPER -> selectedLiveWallpaperApp != null
            }
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 屏幕锁定配置内容组件
 */
@Composable
private fun ScreenLockConfigContent(
    operation: DeviceSettingsOperation,
    onComplete: (Any) -> Unit
) {
    var selectedOperation by rememberSaveable { mutableStateOf(TriStateOperation.ENABLE) }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "屏幕锁定操作",
            style = MaterialTheme.typography.titleMedium
        )

        // 三态操作选择器
        TriStateOperationSelector(
            selectedOperation = selectedOperation,
            onOperationSelected = { selectedOperation = it }
        )

        // 确认按钮
        Button(
            onClick = {
                val task = DeviceSettingsTask(
                    operation = operation,
                    screenLockOperation = selectedOperation
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 数字助理配置内容组件
 */
@Composable
private fun DigitalAssistantConfigContent(
    operation: DeviceSettingsOperation,
    onComplete: (Any) -> Unit
) {
    // 分别保存SimpleAppInfo的各个字段，避免自定义Saver的复杂性
    var selectedAssistantPackageName by rememberSaveable { mutableStateOf("") }
    var selectedAssistantName by rememberSaveable { mutableStateOf("") }
    var selectedAssistantIsSystemApp by rememberSaveable { mutableStateOf(false) }

    // 根据保存的字段重建SimpleAppInfo对象
    val selectedAssistantApp = if (selectedAssistantPackageName.isNotEmpty()) {
        SimpleAppInfo(
            packageName = selectedAssistantPackageName,
            appName = selectedAssistantName,
            isSystemApp = selectedAssistantIsSystemApp
        )
    } else {
        null
    }

    val navController = LocalNavController.current
    val context = LocalContext.current

    // 监听应用选择结果（使用原生数据类型存储）
    LaunchedEffect(Unit) {
        val savedStateHandle = navController?.currentBackStackEntry?.savedStateHandle
        val navigationKey = savedStateHandle?.get<String>("selected_apps_navigation_key")

        if (navigationKey != null) {
            val uiStateManager = UIStateStorageManager(context)
            val selectedAppsResult = uiStateManager.loadAppListState(navigationKey, "selected_apps")

            if (selectedAppsResult.isNotEmpty()) {
                val app = selectedAppsResult.first()
                selectedAssistantPackageName = app.packageName
                selectedAssistantName = app.appName
                selectedAssistantIsSystemApp = app.isSystemApp
            }

            // 清除结果，避免重复处理
            savedStateHandle.remove<String>("selected_apps_navigation_key")
            uiStateManager.clearAppListState(navigationKey, "selected_apps")
        }
    }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "数字助理设置",
            style = MaterialTheme.typography.titleMedium
        )

        Text(
            text = "选择数字助理应用",
            style = MaterialTheme.typography.titleMedium
        )

        OutlinedButton(
            onClick = {
                if (navController != null) {
                    val route = Screen.AppSelection.createDigitalAssistantSingleSelectionRoute()
                    navController.navigate(route)
                }
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text(
                text = if (selectedAssistantApp != null) {
                    "已选择: ${selectedAssistantApp.appName}"
                } else {
                    "点击选择数字助理应用"
                }
            )
        }

        if (selectedAssistantApp != null) {
            Text(
                text = "包名: ${selectedAssistantApp.packageName}",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }

        // 确认按钮
        Button(
            onClick = {
                val task = DeviceSettingsTask(
                    operation = operation,
                    digitalAssistantPackage = selectedAssistantApp?.packageName ?: ""
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth(),
            enabled = selectedAssistantApp != null
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 默认键盘配置内容组件
 */
@Composable
private fun DefaultKeyboardConfigContent(
    operation: DeviceSettingsOperation,
    onComplete: (Any) -> Unit
) {
    // 分别保存SimpleAppInfo的各个字段，避免自定义Saver的复杂性
    var selectedKeyboardPackageName by rememberSaveable { mutableStateOf("") }
    var selectedKeyboardName by rememberSaveable { mutableStateOf("") }
    var selectedKeyboardIsSystemApp by rememberSaveable { mutableStateOf(false) }

    // 根据保存的字段重建SimpleAppInfo对象
    val selectedKeyboardApp = if (selectedKeyboardPackageName.isNotEmpty()) {
        SimpleAppInfo(
            packageName = selectedKeyboardPackageName,
            appName = selectedKeyboardName,
            isSystemApp = selectedKeyboardIsSystemApp
        )
    } else {
        null
    }

    val navController = LocalNavController.current
    val context = LocalContext.current

    // 监听应用选择结果（使用原生数据类型存储）
    LaunchedEffect(Unit) {
        val savedStateHandle = navController?.currentBackStackEntry?.savedStateHandle
        val navigationKey = savedStateHandle?.get<String>("selected_apps_navigation_key")

        if (navigationKey != null) {
            val uiStateManager = UIStateStorageManager(context)
            val selectedAppsResult = uiStateManager.loadAppListState(navigationKey, "selected_apps")

            if (selectedAppsResult.isNotEmpty()) {
                val app = selectedAppsResult.first()
                selectedKeyboardPackageName = app.packageName
                selectedKeyboardName = app.appName
                selectedKeyboardIsSystemApp = app.isSystemApp
            }

            // 清除结果，避免重复处理
            savedStateHandle.remove<String>("selected_apps_navigation_key")
            uiStateManager.clearAppListState(navigationKey, "selected_apps")
        }
    }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "默认键盘设置",
            style = MaterialTheme.typography.titleMedium
        )

        Text(
            text = "选择键盘应用",
            style = MaterialTheme.typography.titleMedium
        )

        OutlinedButton(
            onClick = {
                if (navController != null) {
                    val route = Screen.AppSelection.createKeyboardSingleSelectionRoute()
                    navController.navigate(route)
                }
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text(
                text = if (selectedKeyboardApp != null) {
                    "已选择: ${selectedKeyboardApp.appName}"
                } else {
                    "点击选择键盘应用"
                }
            )
        }

        if (selectedKeyboardApp != null) {
            Text(
                text = "包名: ${selectedKeyboardApp.packageName}",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }

        // 确认按钮
        Button(
            onClick = {
                val task = DeviceSettingsTask(
                    operation = operation,
                    keyboardPackage = selectedKeyboardApp?.packageName ?: ""
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth(),
            enabled = selectedKeyboardApp != null
        ) {
            Text("确认配置")
        }
    }
}

// ==================== 辅助选择器组件 ====================

/**
 * 三态操作选择器组件
 */
@Composable
private fun TriStateOperationSelector(
    selectedOperation: TriStateOperation,
    onOperationSelected: (TriStateOperation) -> Unit
) {
    Column {
        Text(
            text = "操作类型",
            style = MaterialTheme.typography.bodyMedium,
            modifier = Modifier.padding(bottom = 8.dp)
        )

        Column(modifier = Modifier.selectableGroup()) {
            TriStateOperation.values().forEach { operation ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clickable(
                            interactionSource = remember { MutableInteractionSource() },
                            indication = null // 移除涟漪效果
                        ) { onOperationSelected(operation) }
                        .padding(vertical = 4.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = selectedOperation == operation,
                        onClick = { onOperationSelected(operation) }
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = operation.displayName,
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            }
        }
    }
}

/**
 * 沉浸模式类型选择器组件
 */
@Composable
private fun ImmersiveModeTypeSelector(
    selectedType: ImmersiveModeType,
    onTypeSelected: (ImmersiveModeType) -> Unit
) {
    Column {
        Text(
            text = "沉浸模式类型",
            style = MaterialTheme.typography.bodyMedium,
            modifier = Modifier.padding(bottom = 8.dp)
        )

        Column(modifier = Modifier.selectableGroup()) {
            ImmersiveModeType.values().forEach { type ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clickable(
                            interactionSource = remember { MutableInteractionSource() },
                            indication = null // 移除涟漪效果
                        ) { onTypeSelected(type) }
                        .padding(vertical = 4.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = selectedType == type,
                        onClick = { onTypeSelected(type) }
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = type.displayName,
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            }
        }
    }
}

/**
 * 环境显示模式选择器组件
 */
@Composable
private fun AmbientDisplayModeSelector(
    selectedMode: AmbientDisplayMode,
    onModeSelected: (AmbientDisplayMode) -> Unit
) {
    Column {
        Text(
            text = "环境显示模式",
            style = MaterialTheme.typography.bodyMedium,
            modifier = Modifier.padding(bottom = 8.dp)
        )

        Column(modifier = Modifier.selectableGroup()) {
            AmbientDisplayMode.values().forEach { mode ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clickable(
                            interactionSource = remember { MutableInteractionSource() },
                            indication = null // 移除涟漪效果
                        ) { onModeSelected(mode) }
                        .padding(vertical = 4.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = selectedMode == mode,
                        onClick = { onModeSelected(mode) }
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = mode.displayName,
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            }
        }
    }
}

/**
 * 系统设置表选择器组件
 */
@Composable
private fun SystemSettingsTableSelector(
    selectedTable: SystemSettingsTable,
    onTableSelected: (SystemSettingsTable) -> Unit
) {
    Column {
        Text(
            text = "系统设置表",
            style = MaterialTheme.typography.bodyMedium,
            modifier = Modifier.padding(bottom = 8.dp)
        )

        Column(modifier = Modifier.selectableGroup()) {
            SystemSettingsTable.values().forEach { table ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clickable(
                            interactionSource = remember { MutableInteractionSource() },
                            indication = null // 移除涟漪效果
                        ) { onTableSelected(table) }
                        .padding(vertical = 4.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = selectedTable == table,
                        onClick = { onTableSelected(table) }
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = table.displayName,
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            }
        }
    }
}

/**
 * 系统设置值类型选择器组件
 */
@Composable
private fun SystemSettingsValueTypeSelector(
    selectedType: SystemSettingsValueType,
    onTypeSelected: (SystemSettingsValueType) -> Unit
) {
    Column {
        Text(
            text = "值类型",
            style = MaterialTheme.typography.bodyMedium,
            modifier = Modifier.padding(bottom = 8.dp)
        )

        Column(modifier = Modifier.selectableGroup()) {
            SystemSettingsValueType.values().forEach { type ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clickable(
                            interactionSource = remember { MutableInteractionSource() },
                            indication = null // 移除涟漪效果
                        ) { onTypeSelected(type) }
                        .padding(vertical = 4.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = selectedType == type,
                        onClick = { onTypeSelected(type) }
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = type.displayName,
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            }
        }
    }
}

/**
 * 壁纸类型选择器组件
 */
@Composable
private fun WallpaperTypeSelector(
    selectedType: WallpaperType,
    onTypeSelected: (WallpaperType) -> Unit
) {
    Column {
        Text(
            text = "壁纸类型",
            style = MaterialTheme.typography.bodyMedium,
            modifier = Modifier.padding(bottom = 8.dp)
        )

        Column(modifier = Modifier.selectableGroup()) {
            WallpaperType.values().forEach { type ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clickable(
                            interactionSource = remember { MutableInteractionSource() },
                            indication = null // 移除涟漪效果
                        ) { onTypeSelected(type) }
                        .padding(vertical = 4.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = selectedType == type,
                        onClick = { onTypeSelected(type) }
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = type.displayName,
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            }
        }
    }
}

/**
 * 壁纸位置选择器组件
 */
@Composable
private fun WallpaperLocationSelector(
    selectedLocation: WallpaperLocation,
    onLocationSelected: (WallpaperLocation) -> Unit
) {
    Column {
        Text(
            text = "壁纸位置",
            style = MaterialTheme.typography.bodyMedium,
            modifier = Modifier.padding(bottom = 8.dp)
        )

        Column(modifier = Modifier.selectableGroup()) {
            WallpaperLocation.values().forEach { location ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clickable(
                            interactionSource = remember { MutableInteractionSource() },
                            indication = null // 移除涟漪效果
                        ) { onLocationSelected(location) }
                        .padding(vertical = 4.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = selectedLocation == location,
                        onClick = { onLocationSelected(location) }
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = location.displayName,
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            }
        }
    }
}
