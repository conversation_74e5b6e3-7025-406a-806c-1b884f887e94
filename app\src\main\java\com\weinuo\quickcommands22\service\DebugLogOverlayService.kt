package com.weinuo.quickcommands22.service

import android.app.Service
import android.content.Context
import android.content.Intent
import android.graphics.PixelFormat
import android.os.Build
import android.os.IBinder
import android.provider.Settings
import android.util.Log
import android.view.Gravity
import android.view.MotionEvent
import android.view.View
import android.view.WindowManager
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.content.ContextCompat
import com.weinuo.quickcommands22.utils.DebugLogManager

/**
 * 调试日志悬浮窗服务
 * 
 * 在屏幕上显示实时的调试日志信息
 */
class DebugLogOverlayService : Service() {
    
    companion object {
        private const val TAG = "DebugLogOverlayService"
        
        fun start(context: Context) {
            if (canDrawOverlays(context)) {
                val intent = Intent(context, DebugLogOverlayService::class.java)
                context.startService(intent)
            } else {
                Log.w(TAG, "Cannot start overlay service: no overlay permission")
            }
        }
        
        fun stop(context: Context) {
            val intent = Intent(context, DebugLogOverlayService::class.java)
            context.stopService(intent)
        }
        
        fun canDrawOverlays(context: Context): Boolean {
            return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                Settings.canDrawOverlays(context)
            } else {
                true
            }
        }
    }
    
    private var windowManager: WindowManager? = null
    private var overlayView: View? = null
    private var debugLogManager: DebugLogManager? = null
    
    private var logTextView1: TextView? = null
    private var logTextView2: TextView? = null
    private var logTextView3: TextView? = null
    
    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "DebugLogOverlayService created")
        
        debugLogManager = DebugLogManager.getInstance(this)
        createOverlayView()
        setupLogListener()
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Log.d(TAG, "DebugLogOverlayService started")
        return START_STICKY
    }
    
    override fun onBind(intent: Intent?): IBinder? = null
    
    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "DebugLogOverlayService destroyed")
        
        debugLogManager?.removeLogUpdateListener()
        removeOverlayView()
    }
    
    /**
     * 创建悬浮窗视图
     */
    private fun createOverlayView() {
        try {
            windowManager = getSystemService(Context.WINDOW_SERVICE) as WindowManager
            
            // 创建悬浮窗布局
            overlayView = createOverlayLayout()
            
            // 设置悬浮窗参数
            val params = WindowManager.LayoutParams().apply {
                width = WindowManager.LayoutParams.WRAP_CONTENT
                height = WindowManager.LayoutParams.WRAP_CONTENT
                type = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
                } else {
                    @Suppress("DEPRECATION")
                    WindowManager.LayoutParams.TYPE_PHONE
                }
                flags = WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                        WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL or
                        WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE
                format = PixelFormat.RGBA_8888
                gravity = Gravity.TOP or Gravity.END
                x = 20
                y = 100
            }
            
            // 添加到窗口管理器
            windowManager?.addView(overlayView, params)
            
            // 设置拖拽功能
            setupDragFunctionality(params)
            
            Log.d(TAG, "Overlay view created and added")
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to create overlay view", e)
        }
    }
    
    /**
     * 创建悬浮窗布局
     */
    private fun createOverlayLayout(): View {
        // 直接创建布局，不使用XML
        val layout = LinearLayout(this).apply {
            orientation = LinearLayout.VERTICAL
            setBackgroundColor(0xE0000000.toInt()) // 更不透明的背景
            setPadding(12, 8, 12, 8)
        }

        // 创建TextView
        logTextView1 = TextView(this).apply {
            text = "调试日志启动..."
            setTextColor(0xFFFFFFFF.toInt())
            textSize = 10f
            typeface = android.graphics.Typeface.MONOSPACE
            maxLines = 1
            setSingleLine(true)
        }

        logTextView2 = TextView(this).apply {
            text = ""
            setTextColor(0xCCFFFFFF.toInt())
            textSize = 10f
            typeface = android.graphics.Typeface.MONOSPACE
            maxLines = 1
            setSingleLine(true)
        }

        logTextView3 = TextView(this).apply {
            text = ""
            setTextColor(0x99FFFFFF.toInt())
            textSize = 10f
            typeface = android.graphics.Typeface.MONOSPACE
            maxLines = 1
            setSingleLine(true)
        }

        // 添加到布局
        layout.addView(logTextView1)
        layout.addView(logTextView2)
        layout.addView(logTextView3)

        return layout
    }
    
    /**
     * 设置拖拽功能
     */
    private fun setupDragFunctionality(params: WindowManager.LayoutParams) {
        var initialX = 0
        var initialY = 0
        var initialTouchX = 0f
        var initialTouchY = 0f
        
        overlayView?.setOnTouchListener { _, event ->
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    initialX = params.x
                    initialY = params.y
                    initialTouchX = event.rawX
                    initialTouchY = event.rawY
                    true
                }
                MotionEvent.ACTION_MOVE -> {
                    params.x = initialX + (event.rawX - initialTouchX).toInt()
                    params.y = initialY + (event.rawY - initialTouchY).toInt()
                    windowManager?.updateViewLayout(overlayView, params)
                    true
                }
                else -> false
            }
        }
    }
    
    /**
     * 设置日志监听器
     */
    private fun setupLogListener() {
        debugLogManager?.setLogUpdateListener { logs ->
            runOnUiThread {
                updateLogDisplay(logs)
            }
        }
    }
    
    /**
     * 更新日志显示
     */
    private fun updateLogDisplay(logs: List<DebugLogManager.LogEntry>) {
        try {
            when (logs.size) {
                0 -> {
                    logTextView1?.text = "等待日志..."
                    logTextView2?.text = ""
                    logTextView3?.text = ""
                }
                1 -> {
                    logTextView1?.text = logs[0].toDisplayString()
                    logTextView2?.text = ""
                    logTextView3?.text = ""
                }
                2 -> {
                    logTextView1?.text = logs[1].toDisplayString()
                    logTextView2?.text = logs[0].toDisplayString()
                    logTextView3?.text = ""
                }
                else -> {
                    logTextView1?.text = logs[logs.size - 1].toDisplayString()
                    logTextView2?.text = logs[logs.size - 2].toDisplayString()
                    logTextView3?.text = logs[logs.size - 3].toDisplayString()
                }
            }
            
            // 根据日志级别设置颜色
            if (logs.isNotEmpty()) {
                val latestLog = logs.last()
                val color = when (latestLog.level) {
                    DebugLogManager.LogLevel.ERROR -> ContextCompat.getColor(this, android.R.color.holo_red_light)
                    DebugLogManager.LogLevel.WARN -> ContextCompat.getColor(this, android.R.color.holo_orange_light)
                    DebugLogManager.LogLevel.INFO -> ContextCompat.getColor(this, android.R.color.holo_blue_light)
                    else -> ContextCompat.getColor(this, android.R.color.white)
                }
                logTextView1?.setTextColor(color)
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to update log display", e)
        }
    }
    
    /**
     * 在UI线程运行
     */
    private fun runOnUiThread(action: () -> Unit) {
        overlayView?.post(action)
    }
    
    /**
     * 移除悬浮窗视图
     */
    private fun removeOverlayView() {
        try {
            overlayView?.let { view ->
                windowManager?.removeView(view)
                overlayView = null
            }
            Log.d(TAG, "Overlay view removed")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to remove overlay view", e)
        }
    }
}
