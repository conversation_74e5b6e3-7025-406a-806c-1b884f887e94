package com.weinuo.quickcommands22.service

import android.service.notification.NotificationListenerService
import android.service.notification.StatusBarNotification
import android.util.Log

/**
 * 快捷指令通知监听服务
 *
 * 这个服务继承自NotificationListenerService，用于：
 * 1. 监听系统通知事件
 * 2. 为通知相关的自动化功能提供支持
 * 3. 使应用能够出现在"通知使用权"权限列表中
 *
 * 设计原则：
 * - **最小化实现**：只实现必要的方法，避免复杂的逻辑
 * - **权限支持**：主要目的是让应用出现在通知使用权限列表中
 * - **日志记录**：记录关键事件用于调试
 * - **不是前台服务**：避免高版本Android系统的崩溃风险
 */
class QuickCommandsNotificationService : NotificationListenerService() {

    companion object {
        private const val TAG = "NotificationListener"
    }

    /**
     * 服务连接时调用
     * 当用户在系统设置中启用此通知监听服务时会调用此方法
     */
    override fun onListenerConnected() {
        super.onListenerConnected()
        Log.d(TAG, "Quick commands notification listener service connected")
    }

    /**
     * 服务断开连接时调用
     * 当用户在系统设置中禁用此通知监听服务时会调用此方法
     */
    override fun onListenerDisconnected() {
        super.onListenerDisconnected()
        Log.d(TAG, "Quick commands notification listener service disconnected")
    }

    /**
     * 通知发布时调用
     * 当系统中有新通知发布时会调用此方法
     *
     * @param sbn 状态栏通知对象
     */
    override fun onNotificationPosted(sbn: StatusBarNotification?) {
        super.onNotificationPosted(sbn)
        // 目前只记录日志，不进行复杂处理
        // 实际的通知事件处理逻辑在其他地方实现
        sbn?.let {
            Log.d(TAG, "Notification posted: ${it.packageName}")
        }
    }

    /**
     * 通知移除时调用
     * 当系统中的通知被移除时会调用此方法
     *
     * @param sbn 状态栏通知对象
     */
    override fun onNotificationRemoved(sbn: StatusBarNotification?) {
        super.onNotificationRemoved(sbn)
        // 目前只记录日志，不进行复杂处理
        sbn?.let {
            Log.d(TAG, "Notification removed: ${it.packageName}")
        }
    }

    /**
     * 获取当前活跃的通知列表
     * 这个方法可以被其他组件调用来获取当前的通知
     *
     * @return 当前活跃的通知列表，如果服务未连接则返回空数组
     */
    fun getCurrentNotifications(): Array<StatusBarNotification> {
        return try {
            activeNotifications ?: emptyArray()
        } catch (e: Exception) {
            Log.e(TAG, "Error getting current notifications", e)
            emptyArray()
        }
    }

    /**
     * 取消指定的通知
     *
     * @param packageName 应用包名
     * @param tag 通知标签（可选）
     * @param id 通知ID
     */
    fun cancelNotificationByPackage(packageName: String, tag: String? = null, id: Int = -1) {
        try {
            if (id != -1) {
                // 取消指定ID的通知
                cancelNotification(packageName, tag, id)
                Log.d(TAG, "Cancelled notification: $packageName, tag=$tag, id=$id")
            } else {
                // 取消该应用的所有通知
                activeNotifications?.filter { it.packageName == packageName }?.forEach {
                    cancelNotification(it.key)
                    Log.d(TAG, "Cancelled notification: ${it.key}")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error cancelling notification", e)
        }
    }

    /**
     * 取消所有通知
     */
    fun cancelAllActiveNotifications() {
        try {
            activeNotifications?.forEach {
                cancelNotification(it.key)
            }
            Log.d(TAG, "Cancelled all active notifications")
        } catch (e: Exception) {
            Log.e(TAG, "Error cancelling all notifications", e)
        }
    }
}
