package com.weinuo.quickcommands22.permission

import android.content.Context
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.platform.LocalContext
import com.weinuo.quickcommands22.utils.*
import com.weinuo.quickcommands22.ui.components.ScrollableAlertDialog

/**
 * 权限感知操作选择器 - 统一的权限检查组件
 *
 * 这个Composable组件负责：
 * 1. 监听操作选择变化
 * 2. 自动检查并申请相应权限
 * 3. 提供统一的权限检查时机控制
 * 4. 支持泛型类型以确保类型安全
 *
 * 使用方法：
 * ```kotlin
 * @Composable
 * fun MyConfigDialog() {
 *     val context = LocalContext.current
 *     val globalPermissionManager = GlobalPermissionManager.getInstance(context)
 *     var selectedOperation by remember { mutableStateOf(MyOperation.DEFAULT) }
 *
 *     // 权限检查组件 - 只在选择需要权限的操作时进行权限检查
 *     PermissionAwareOperationSelector(
 *         selectedOperation = selectedOperation,
 *         globalPermissionManager = globalPermissionManager,
 *         context = context
 *     )
 *
 *     // 其他UI组件...
 * }
 * ```
 *
 * 设计原则：
 * - **统一权限检查时机**：只在用户选择需要权限的具体操作类型时进行权限检查
 * - **最佳用户体验**：避免在对话框初始化时立即申请权限，减少用户打断
 * - **类型安全**：支持泛型类型，确保编译时类型检查
 * - **可扩展性**：新的操作类型自动支持权限检查，无需修改此组件
 *
 * @param T 操作类型的泛型参数
 * @param selectedOperation 当前选择的操作
 * @param globalPermissionManager 全局权限管理器实例
 * @param context Android上下文（可选，默认使用LocalContext.current）
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Composable
fun <T : Any> PermissionAwareOperationSelector(
    selectedOperation: T,
    globalPermissionManager: GlobalPermissionManager,
    onPermissionDialogDismissed: (() -> Unit)? = null,
    context: Context = LocalContext.current
) {
    // 观察权限状态变化
    val permissionStates by globalPermissionManager.permissionStates.collectAsState()

    // 观察权限确认对话框状态
    val permissionConfirmationStates by globalPermissionManager.permissionConfirmationStates.collectAsState()

    // 权限检查逻辑 - 在选择的操作发生变化或权限状态变化时进行权限检查
    LaunchedEffect(selectedOperation, permissionStates) {
        // 调用权限注册表进行权限检查（显示确认对话框）
        PermissionRegistry.checkPermissionForOperation(
            operation = selectedOperation,
            globalPermissionManager = globalPermissionManager,
            context = context
        )
    }

    // 显示权限确认对话框
    permissionConfirmationStates.forEach { (permissionType, shouldShow) ->
        if (shouldShow) {
            PermissionConfirmationDialog(
                permissionType = permissionType,
                onConfirm = {
                    globalPermissionManager.requestPermissionAfterConfirmation(permissionType)
                    // 权限申请后通知调用方重置selectedOperation
                    onPermissionDialogDismissed?.invoke()
                },
                onDismiss = {
                    globalPermissionManager.hidePermissionConfirmationDialog(permissionType)
                    // 权限对话框关闭后通知调用方重置selectedOperation
                    onPermissionDialogDismissed?.invoke()
                }
            )
        }
    }
}

/**
 * 权限感知操作选择器的简化版本 - 自动获取GlobalPermissionManager
 *
 * 这个版本会自动从Context获取GlobalPermissionManager实例，
 * 简化了调用方的代码。
 *
 * 使用方法：
 * ```kotlin
 * @Composable
 * fun MyConfigDialog() {
 *     var selectedOperation by remember { mutableStateOf<MyOperation?>(null) }
 *
 *     // 简化的权限检查组件
 *     selectedOperation?.let { operation ->
 *         PermissionAwareOperationSelector(
 *             selectedOperation = operation,
 *             onPermissionDialogDismissed = { selectedOperation = null }
 *         )
 *     }
 *
 *     // 其他UI组件...
 * }
 * ```
 *
 * @param T 操作类型的泛型参数
 * @param selectedOperation 当前选择的操作
 * @param onPermissionDialogDismissed 权限对话框关闭时的回调，用于重置selectedOperation
 * @param context Android上下文（可选，默认使用LocalContext.current）
 */
@Composable
fun <T : Any> PermissionAwareOperationSelector(
    selectedOperation: T,
    onPermissionDialogDismissed: (() -> Unit)? = null,
    context: Context = LocalContext.current
) {
    val globalPermissionManager = GlobalPermissionManager.getInstance(context)

    PermissionAwareOperationSelector(
        selectedOperation = selectedOperation,
        globalPermissionManager = globalPermissionManager,
        onPermissionDialogDismissed = onPermissionDialogDismissed,
        context = context
    )
}

/**
 * 权限感知操作选择器的扩展版本 - 支持多个操作参数
 *
 * 当配置对话框需要检查多个相关操作的权限时使用此版本。
 * 例如，连接状态条件可能需要同时检查连接类型和子类型的权限。
 *
 * 使用方法：
 * ```kotlin
 * @Composable
 * fun ConnectionConfigDialog() {
 *     var selectedConnectionType by remember { mutableStateOf(ConnectionType.WIFI) }
 *     var selectedSubType by remember { mutableStateOf(ConnectionSubType.WIFI_CONNECTED) }
 *
 *     // 多操作权限检查组件
 *     PermissionAwareOperationSelector(
 *         operations = listOf(selectedConnectionType, selectedSubType)
 *     )
 *
 *     // 其他UI组件...
 * }
 * ```
 *
 * @param operations 需要检查权限的操作列表
 * @param context Android上下文（可选，默认使用LocalContext.current）
 */
@Composable
fun PermissionAwareOperationSelector(
    operations: List<Any>,
    context: Context = LocalContext.current
) {
    val globalPermissionManager = GlobalPermissionManager.getInstance(context)
    val permissionStates by globalPermissionManager.permissionStates.collectAsState()

    // 观察权限确认对话框状态
    val permissionConfirmationStates by globalPermissionManager.permissionConfirmationStates.collectAsState()

    // 权限检查逻辑 - 当任何操作发生变化或权限状态变化时进行权限检查
    LaunchedEffect(operations, permissionStates) {
        operations.forEach { operation ->
            PermissionRegistry.checkPermissionForOperation(
                operation = operation,
                globalPermissionManager = globalPermissionManager,
                context = context
            )
        }
    }

    // 显示权限确认对话框
    permissionConfirmationStates.forEach { (permissionType, shouldShow) ->
        if (shouldShow) {
            PermissionConfirmationDialog(
                permissionType = permissionType,
                onConfirm = {
                    globalPermissionManager.requestPermissionAfterConfirmation(permissionType)
                },
                onDismiss = {
                    globalPermissionManager.hidePermissionConfirmationDialog(permissionType)
                }
            )
        }
    }
}

/**
 * 权限感知操作选择器的条件版本 - 支持条件性权限检查
 *
 * 当只有在特定条件下才需要检查权限时使用此版本。
 * 例如，只有在选择特定子类型时才需要检查权限。
 *
 * 使用方法：
 * ```kotlin
 * @Composable
 * fun MyConfigDialog() {
 *     var selectedOperation by remember { mutableStateOf(MyOperation.DEFAULT) }
 *     var selectedSubType by remember { mutableStateOf(MySubType.DEFAULT) }
 *
 *     // 条件性权限检查组件
 *     ConditionalPermissionAwareOperationSelector(
 *         selectedOperation = selectedOperation,
 *         condition = { selectedSubType == MySubType.REQUIRES_PERMISSION }
 *     )
 *
 *     // 其他UI组件...
 * }
 * ```
 *
 * @param T 操作类型的泛型参数
 * @param selectedOperation 当前选择的操作
 * @param condition 权限检查的条件函数
 * @param context Android上下文（可选，默认使用LocalContext.current）
 */
@Composable
fun <T : Any> ConditionalPermissionAwareOperationSelector(
    selectedOperation: T,
    condition: () -> Boolean,
    context: Context = LocalContext.current
) {
    val globalPermissionManager = GlobalPermissionManager.getInstance(context)
    val permissionStates by globalPermissionManager.permissionStates.collectAsState()

    // 观察权限确认对话框状态
    val permissionConfirmationStates by globalPermissionManager.permissionConfirmationStates.collectAsState()

    // 权限检查逻辑 - 在满足条件且操作发生变化或权限状态变化时进行权限检查
    LaunchedEffect(selectedOperation, condition(), permissionStates) {
        if (condition()) {
            PermissionRegistry.checkPermissionForOperation(
                operation = selectedOperation,
                globalPermissionManager = globalPermissionManager,
                context = context
            )
        }
    }

    // 显示权限确认对话框
    permissionConfirmationStates.forEach { (permissionType, shouldShow) ->
        if (shouldShow) {
            PermissionConfirmationDialog(
                permissionType = permissionType,
                onConfirm = {
                    globalPermissionManager.requestPermissionAfterConfirmation(permissionType)
                },
                onDismiss = {
                    globalPermissionManager.hidePermissionConfirmationDialog(permissionType)
                }
            )
        }
    }
}

/**
 * 权限确认对话框
 *
 * 在实际申请权限之前，向用户显示权限说明并请求确认
 *
 * @param permissionType 权限类型
 * @param onConfirm 用户确认申请权限的回调
 * @param onDismiss 用户取消申请权限的回调
 */
@Composable
private fun PermissionConfirmationDialog(
    permissionType: GlobalPermissionManager.PermissionType,
    onConfirm: () -> Unit,
    onDismiss: () -> Unit
) {
    when (permissionType) {
        GlobalPermissionManager.PermissionType.COMMUNICATION -> {
            CommunicationPermissionUtil.PermissionRationaleDialog(
                onConfirm = onConfirm,
                onDismiss = onDismiss
            )
        }
        GlobalPermissionManager.PermissionType.LOCATION -> {
            LocationPermissionUtil.LocationPermissionRationaleDialog(
                onConfirm = onConfirm,
                onDismiss = onDismiss
            )
        }
        GlobalPermissionManager.PermissionType.BLUETOOTH -> {
            BluetoothPermissionUtil.BluetoothPermissionRationaleDialog(
                onConfirm = onConfirm,
                onDismiss = onDismiss
            )
        }
        GlobalPermissionManager.PermissionType.SENSOR -> {
            SensorPermissionUtil.ActivityRecognitionPermissionRationaleDialog(
                onConfirm = onConfirm,
                onDismiss = onDismiss
            )
        }
        GlobalPermissionManager.PermissionType.DEVICE_ADMIN -> {
            DeviceEventPermissionUtil.DeviceAdminPermissionRationaleDialog(
                onConfirm = onConfirm,
                onDismiss = onDismiss
            )
        }
        GlobalPermissionManager.PermissionType.NOTIFICATION_LISTENER -> {
            DeviceEventPermissionUtil.NotificationListenerPermissionRationaleDialog(
                onConfirm = onConfirm,
                onDismiss = onDismiss
            )
        }
        GlobalPermissionManager.PermissionType.NOTIFICATION -> {
            NotificationPermissionUtil.NotificationPermissionRationaleDialog(
                onDismiss = onDismiss,
                onConfirm = onConfirm
            )
        }
        GlobalPermissionManager.PermissionType.BUBBLE -> {
            NotificationPermissionUtil.BubblePermissionRationaleDialog(
                onDismiss = onDismiss,
                onConfirm = onConfirm
            )
        }
        GlobalPermissionManager.PermissionType.OVERLAY -> {
            ScrollableAlertDialog(
                onDismissRequest = onDismiss,
                title = "悬浮窗权限说明",
                message = """
                    为了使用悬浮窗功能，应用需要悬浮窗权限：

                    • 悬浮按钮：显示悬浮触发按钮
                    • 系统对话框：显示系统级对话框
                    • 闹钟提醒：显示闹钟提醒窗口

                    这些权限仅用于您主动配置的功能，不会在后台自动显示内容。
                    您可以随时在系统设置中撤销这些权限。
                """.trimIndent(),
                confirmText = "前往设置",
                dismissText = "取消",
                onConfirm = onConfirm,
                onDismiss = onDismiss
            )
        }
        GlobalPermissionManager.PermissionType.MEDIA -> {
            MediaPermissionUtil.MediaPermissionExplanationDialog(
                onRequestPermission = onConfirm,
                onDismiss = onDismiss
            )
        }
        GlobalPermissionManager.PermissionType.STORAGE -> {
            StoragePermissionUtil.ShowStoragePermissionDialog(
                onDismiss = onDismiss,
                onConfirm = onConfirm
            )
        }
        GlobalPermissionManager.PermissionType.CAMERA -> {
            ScrollableAlertDialog(
                onDismissRequest = onDismiss,
                title = "相机权限说明",
                message = """
                    为了使用相机功能，应用需要相机权限：

                    • 相机权限：拍照和录像

                    此权限仅用于您主动触发的拍照录像功能，不会在后台自动使用。
                    您可以随时在系统设置中撤销此权限。
                """.trimIndent(),
                confirmText = "申请权限",
                dismissText = "取消",
                onConfirm = onConfirm,
                onDismiss = onDismiss
            )
        }
        GlobalPermissionManager.PermissionType.NETWORK -> {
            ScrollableAlertDialog(
                onDismissRequest = onDismiss,
                title = "网络权限说明",
                message = """
                    为了使用网络相关功能，应用需要网络权限：

                    • 网络状态检测：检测网络连接状态
                    • WiFi控制：管理WiFi连接
                    • 网络连接检查：测试网络连通性

                    这些权限仅用于网络状态检测和管理，不会收集您的网络使用数据。
                    您可以随时在系统设置中撤销这些权限。
                """.trimIndent(),
                confirmText = "申请权限",
                dismissText = "取消",
                onConfirm = onConfirm,
                onDismiss = onDismiss
            )
        }
        GlobalPermissionManager.PermissionType.ACCESSIBILITY -> {
            ScrollableAlertDialog(
                onDismissRequest = onDismiss,
                title = "无障碍服务权限说明",
                message = """
                    为了使用状态栏控制等功能，应用需要无障碍服务权限：

                    • 状态栏控制：展开或折叠状态栏
                    • 系统界面交互：与系统界面进行交互
                    • 自动化操作：执行系统级自动化操作

                    这些权限仅用于您主动配置的自动化功能，不会监控您的其他应用使用。
                    您可以随时在系统设置中撤销这些权限。
                """.trimIndent(),
                confirmText = "前往设置",
                dismissText = "取消",
                onConfirm = onConfirm,
                onDismiss = onDismiss
            )
        }
        GlobalPermissionManager.PermissionType.MICROPHONE -> {
            ScrollableAlertDialog(
                onDismissRequest = onDismiss,
                title = "麦克风权限说明",
                message = """
                    为了使用语音相关功能，应用需要麦克风权限：

                    • 语音搜索：启动语音搜索功能
                    • 录音功能：录制音频文件
                    • 语音识别：处理语音输入

                    这些权限仅用于您主动触发的语音功能，不会在后台自动录音。
                    您可以随时在系统设置中撤销这些权限。
                """.trimIndent(),
                confirmText = "申请权限",
                dismissText = "取消",
                onConfirm = onConfirm,
                onDismiss = onDismiss
            )
        }
        GlobalPermissionManager.PermissionType.SHIZUKU -> {
            ScrollableAlertDialog(
                onDismissRequest = onDismiss,
                title = "Shizuku权限说明",
                message = """
                    为了使用高级系统功能，应用需要Shizuku权限：

                    • Shell命令执行：执行系统级Shell命令
                    • 系统设置修改：修改系统设置参数
                    • 高级自动化：执行需要系统权限的操作

                    这些权限仅用于您主动配置的高级功能，需要先安装并启动Shizuku服务。
                    您可以随时撤销Shizuku权限或停止Shizuku服务。
                """.trimIndent(),
                confirmText = "申请权限",
                dismissText = "取消",
                onConfirm = onConfirm,
                onDismiss = onDismiss
            )
        }
        GlobalPermissionManager.PermissionType.USAGE_STATS -> {
            ScrollableAlertDialog(
                onDismissRequest = onDismiss,
                title = "使用情况访问权限说明",
                message = """
                    为了使用应用状态监控功能，应用需要使用情况访问权限：

                    • 应用状态检测：检测应用进入前台、后台状态
                    • 应用启动关闭：监控应用的启动和关闭事件
                    • 应用使用统计：获取应用使用时间和频率信息

                    这些权限仅用于您主动配置的应用状态条件，不会收集或上传您的应用使用数据。
                    您可以随时在系统设置中撤销这些权限。
                """.trimIndent(),
                confirmText = "前往设置",
                dismissText = "取消",
                onConfirm = onConfirm,
                onDismiss = onDismiss
            )
        }
        GlobalPermissionManager.PermissionType.DO_NOT_DISTURB -> {
            ScrollableAlertDialog(
                onDismissRequest = onDismiss,
                title = "勿扰模式权限说明",
                message = """
                    为了使用勿扰模式控制功能，应用需要通知策略访问权限：

                    • 勿扰模式控制：设置勿扰模式状态（正常、仅通话、仅闹钟、完全静音）
                    • 通知过滤：控制通知的显示和过滤规则
                    • 音频模式管理：管理系统音频和振动模式

                    这些权限仅用于您主动配置的勿扰模式任务，不会自动修改您的通知设置。
                    您可以随时在系统设置中撤销这些权限。
                """.trimIndent(),
                confirmText = "前往设置",
                dismissText = "取消",
                onConfirm = onConfirm,
                onDismiss = onDismiss
            )
        }

    }
}
