package com.weinuo.quickcommands22.storage.adapters

import android.util.Log
import com.weinuo.quickcommands22.model.*
import com.weinuo.quickcommands22.storage.NativeTypeStorageManager
import com.weinuo.quickcommands22.storage.StorageDomain
import com.weinuo.quickcommands22.storage.StorageOperation

/**
 * 日期时间任务存储适配器
 *
 * 负责DateTimeTask的原生数据类型存储和重建。
 * 将复杂的日期时间任务对象拆分为独立的原生字段进行存储，
 * 集合类型（如DayOfWeek集合）通过索引方式存储。
 *
 * 支持的字段类型：
 * - 基础字段：id、taskType
 * - 秒表相关：stopwatchId、stopwatchName、stopwatchOperation
 * - 闹钟相关：alarmOperation、alarmTimeType、alarmHour、alarmMinute、alarmRelativeHours、alarmRelativeMinutes
 * - 闹钟配置：alarmMessage、alarmVibrate、alarmSkipUI、requireTask、clicksCount
 * - 声音配置：enableSound、selectedRingtoneUri、selectedRingtoneName
 * - 振动配置：enableVibration、vibrationMode、vibrationIntensity
 * - 集合字段：alarmDays（DayOfWeek集合）
 *
 * 存储格式示例：
 * task_{id}_type = "date_time"
 * task_{id}_task_type = "ALARM"
 * task_{id}_alarm_operation = "SET_ALARM"
 * task_{id}_alarm_time_type = "ABSOLUTE"
 * task_{id}_alarm_hour = 8
 * task_{id}_alarm_minute = 30
 * task_{id}_alarm_days_count = 2
 * task_{id}_alarm_days_0 = "MONDAY"
 * task_{id}_alarm_days_1 = "FRIDAY"
 *
 * @param storageManager 原生类型存储管理器
 */
class DateTimeTaskAdapter(
    storageManager: NativeTypeStorageManager
) : BaseTaskAdapter<DateTimeTask>(storageManager) {

    companion object {
        private const val TAG = "DateTimeTaskAdapter"

        // 字段名常量
        private const val FIELD_TASK_TYPE = "task_type"
        private const val FIELD_STOPWATCH_ID = "stopwatch_id"
        private const val FIELD_STOPWATCH_NAME = "stopwatch_name"
        private const val FIELD_STOPWATCH_OPERATION = "stopwatch_operation"
        private const val FIELD_ALARM_OPERATION = "alarm_operation"
        private const val FIELD_ALARM_TIME_TYPE = "alarm_time_type"
        private const val FIELD_ALARM_HOUR = "alarm_hour"
        private const val FIELD_ALARM_MINUTE = "alarm_minute"
        private const val FIELD_ALARM_RELATIVE_HOURS = "alarm_relative_hours"
        private const val FIELD_ALARM_RELATIVE_MINUTES = "alarm_relative_minutes"
        private const val FIELD_ALARM_MESSAGE = "alarm_message"
        private const val FIELD_ALARM_VIBRATE = "alarm_vibrate"
        private const val FIELD_ALARM_SKIP_UI = "alarm_skip_ui"
        private const val FIELD_REQUIRE_TASK = "require_task"
        private const val FIELD_CLICKS_COUNT = "clicks_count"
        private const val FIELD_ENABLE_SOUND = "enable_sound"
        private const val FIELD_REMINDER_MESSAGE = "reminder_message"
        private const val FIELD_ENABLE_VIBRATION = "enable_vibration"
        private const val FIELD_VIBRATION_MODE = "vibration_mode"
        private const val FIELD_VIBRATION_INTENSITY = "vibration_intensity"
        private const val FIELD_SELECTED_RINGTONE_URI = "selected_ringtone_uri"
        private const val FIELD_SELECTED_RINGTONE_NAME = "selected_ringtone_name"
        private const val FIELD_ALARM_DAYS = "alarm_days"

        // 语音报时相关字段
        private const val FIELD_VOICE_TIME_FORMAT = "voice_time_format"
        private const val FIELD_VOICE_LANGUAGE = "voice_language"
        private const val FIELD_VOICE_PITCH = "voice_pitch"
        private const val FIELD_VOICE_SPEED = "voice_speed"
        private const val FIELD_VOICE_AUDIO_STREAM = "voice_audio_stream"
    }

    override fun getTaskType() = "date_time"

    /**
     * 保存日期时间任务
     * 将DateTimeTask的所有字段拆分为原生数据类型存储
     *
     * @param task 要保存的日期时间任务
     * @return 操作是否成功
     */
    override fun save(task: DateTimeTask): Boolean {
        if (!isValidTaskId(task.id)) {
            logSaveError(task.id, "Invalid task ID")
            return false
        }

        Log.d(TAG, "开始保存日期时间任务: ${task.id}")

        return try {
            val operations = mutableListOf<StorageOperation>()

            // 保存基础字段
            operations.addAll(saveBaseFields(task.id, task))

            // 保存日期时间任务特有字段
            operations.addAll(listOf(
                saveEnum(generateKey(task.id, FIELD_TASK_TYPE), task.taskType),

                // 秒表相关字段
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_STOPWATCH_ID),
                    task.stopwatchId
                ),
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_STOPWATCH_NAME),
                    task.stopwatchName
                ),
                saveEnum(generateKey(task.id, FIELD_STOPWATCH_OPERATION), task.stopwatchOperation),

                // 闹钟相关字段
                saveEnum(generateKey(task.id, FIELD_ALARM_OPERATION), task.alarmOperation),
                saveEnum(generateKey(task.id, FIELD_ALARM_TIME_TYPE), task.alarmTimeType),
                StorageOperation.createIntOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_ALARM_HOUR),
                    task.alarmHour
                ),
                StorageOperation.createIntOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_ALARM_MINUTE),
                    task.alarmMinute
                ),
                StorageOperation.createIntOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_ALARM_RELATIVE_HOURS),
                    task.alarmRelativeHours
                ),
                StorageOperation.createIntOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_ALARM_RELATIVE_MINUTES),
                    task.alarmRelativeMinutes
                ),
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_ALARM_MESSAGE),
                    task.alarmMessage
                ),
                StorageOperation.createBooleanOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_ALARM_VIBRATE),
                    task.alarmVibrate
                ),
                StorageOperation.createBooleanOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_ALARM_SKIP_UI),
                    task.alarmSkipUI
                ),
                StorageOperation.createBooleanOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_REQUIRE_TASK),
                    task.requireTask
                ),
                StorageOperation.createIntOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_CLICKS_COUNT),
                    task.clicksCount
                ),
                StorageOperation.createBooleanOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_ENABLE_SOUND),
                    task.enableSound
                ),
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_REMINDER_MESSAGE),
                    task.reminderMessage
                ),
                StorageOperation.createBooleanOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_ENABLE_VIBRATION),
                    task.enableVibration
                ),
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_VIBRATION_MODE),
                    task.vibrationMode
                ),
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_VIBRATION_INTENSITY),
                    task.vibrationIntensity
                ),
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_SELECTED_RINGTONE_URI),
                    task.selectedRingtoneUri
                ),
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_SELECTED_RINGTONE_NAME),
                    task.selectedRingtoneName
                ),

                // 语音报时相关字段
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_VOICE_TIME_FORMAT),
                    task.voiceTimeFormat
                ),
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_VOICE_LANGUAGE),
                    task.voiceLanguage
                ),
                StorageOperation.createFloatOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_VOICE_PITCH),
                    task.voicePitch
                ),
                StorageOperation.createFloatOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_VOICE_SPEED),
                    task.voiceSpeed
                ),
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_VOICE_AUDIO_STREAM),
                    task.voiceAudioStream
                )
            ))

            // 保存闹钟天数集合
            operations.addAll(saveIntSet(generateKey(task.id, FIELD_ALARM_DAYS), task.alarmDays))

            val success = storageManager.executeBatch(operations)
            if (success) {
                Log.d(TAG, "日期时间任务保存成功: ${task.id}")
            } else {
                logSaveError(task.id, "Batch operation failed")
            }
            success

        } catch (e: Exception) {
            logSaveError(task.id, "Exception during save: ${e.message}")
            false
        }
    }

    /**
     * 加载日期时间任务
     * 从原生数据类型重建DateTimeTask对象
     *
     * @param taskId 任务ID
     * @return 加载的日期时间任务，失败时返回null
     */
    override fun load(taskId: String): DateTimeTask? {
        if (!isValidTaskId(taskId)) {
            logLoadError(taskId, "Invalid task ID")
            return null
        }

        Log.d(TAG, "开始加载日期时间任务: $taskId")

        return try {
            // 检查任务是否存在
            if (!exists(taskId)) {
                Log.d(TAG, "日期时间任务不存在: $taskId")
                return null
            }

            DateTimeTask(
                id = taskId,
                taskType = loadEnum(generateKey(taskId, FIELD_TASK_TYPE)) { DateTimeTaskType.valueOf(it) }
                    ?: DateTimeTaskType.STOPWATCH,

                // 秒表相关字段
                stopwatchId = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_STOPWATCH_ID),
                    ""
                ),
                stopwatchName = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_STOPWATCH_NAME),
                    ""
                ),
                stopwatchOperation = loadEnum(generateKey(taskId, FIELD_STOPWATCH_OPERATION)) { StopwatchOperation.valueOf(it) }
                    ?: StopwatchOperation.START,

                // 闹钟相关字段
                alarmOperation = loadEnum(generateKey(taskId, FIELD_ALARM_OPERATION)) { AlarmOperation.valueOf(it) }
                    ?: AlarmOperation.SET_ALARM,
                alarmTimeType = loadEnum(generateKey(taskId, FIELD_ALARM_TIME_TYPE)) { AlarmTimeType.valueOf(it) }
                    ?: AlarmTimeType.ABSOLUTE,
                alarmHour = storageManager.loadInt(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_ALARM_HOUR),
                    8
                ),
                alarmMinute = storageManager.loadInt(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_ALARM_MINUTE),
                    0
                ),
                alarmRelativeHours = storageManager.loadInt(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_ALARM_RELATIVE_HOURS),
                    0
                ),
                alarmRelativeMinutes = storageManager.loadInt(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_ALARM_RELATIVE_MINUTES),
                    5
                ),
                alarmMessage = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_ALARM_MESSAGE),
                    ""
                ),
                alarmDays = loadIntSet(generateKey(taskId, FIELD_ALARM_DAYS)),
                alarmVibrate = storageManager.loadBoolean(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_ALARM_VIBRATE),
                    false
                ),
                alarmSkipUI = storageManager.loadBoolean(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_ALARM_SKIP_UI),
                    false
                ),
                requireTask = storageManager.loadBoolean(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_REQUIRE_TASK),
                    false
                ),
                clicksCount = storageManager.loadInt(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_CLICKS_COUNT),
                    5
                ),
                enableSound = storageManager.loadBoolean(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_ENABLE_SOUND),
                    true
                ),
                reminderMessage = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_REMINDER_MESSAGE),
                    ""
                ),
                enableVibration = storageManager.loadBoolean(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_ENABLE_VIBRATION),
                    false
                ),
                vibrationMode = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_VIBRATION_MODE),
                    "CONTINUOUS"
                ),
                vibrationIntensity = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_VIBRATION_INTENSITY),
                    "MEDIUM"
                ),
                selectedRingtoneUri = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_SELECTED_RINGTONE_URI),
                    ""
                ),
                selectedRingtoneName = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_SELECTED_RINGTONE_NAME),
                    ""
                ),

                // 语音报时相关字段
                voiceTimeFormat = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_VOICE_TIME_FORMAT),
                    "24"
                ),
                voiceLanguage = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_VOICE_LANGUAGE),
                    "zh-CN"
                ),
                voicePitch = storageManager.loadFloat(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_VOICE_PITCH),
                    1.0f
                ),
                voiceSpeed = storageManager.loadFloat(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_VOICE_SPEED),
                    1.0f
                ),
                voiceAudioStream = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_VOICE_AUDIO_STREAM),
                    "MUSIC"
                )
            ).also {
                Log.d(TAG, "日期时间任务加载成功: $taskId")
            }

        } catch (e: Exception) {
            logLoadError(taskId, "Exception during load: ${e.message}")
            null
        }
    }

    /**
     * 保存Int集合
     */
    private fun saveIntSet(key: String, intSet: Set<Int>): List<StorageOperation> {
        val operations = mutableListOf<StorageOperation>()

        operations.add(StorageOperation.createIntOperation(
            StorageDomain.TASKS,
            "${key}_count",
            intSet.size
        ))

        intSet.forEachIndexed { index, value ->
            operations.add(StorageOperation.createIntOperation(
                StorageDomain.TASKS,
                "${key}_${index}",
                value
            ))
        }

        return operations
    }

    /**
     * 加载Int集合
     */
    private fun loadIntSet(key: String): Set<Int> {
        val count = storageManager.loadInt(StorageDomain.TASKS, "${key}_count", 0)
        val intSet = mutableSetOf<Int>()

        for (index in 0 until count) {
            val value = storageManager.loadInt(StorageDomain.TASKS, "${key}_${index}", -1)
            if (value != -1) {
                intSet.add(value)
            }
        }

        return intSet
    }
}
