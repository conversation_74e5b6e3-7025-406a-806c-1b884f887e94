<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res"><file name="circular_progress_drawable" path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res\drawable\circular_progress_drawable.xml" qualifiers="" type="drawable"/><file name="circular_reminder_background" path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res\drawable\circular_reminder_background.xml" qualifiers="" type="drawable"/><file name="floating_button_background" path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res\drawable\floating_button_background.xml" qualifiers="" type="drawable"/><file name="ic_add_skyblue" path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res\drawable\ic_add_skyblue.xml" qualifiers="" type="drawable"/><file name="ic_apps_24" path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res\drawable\ic_apps_24.xml" qualifiers="" type="drawable"/><file name="ic_circle_white" path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res\drawable\ic_circle_white.xml" qualifiers="" type="drawable"/><file name="ic_clear" path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res\drawable\ic_clear.xml" qualifiers="" type="drawable"/><file name="ic_close" path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res\drawable\ic_close.xml" qualifiers="" type="drawable"/><file name="ic_close_24" path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res\drawable\ic_close_24.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="ic_lightbulb_24" path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res\drawable\ic_lightbulb_24.xml" qualifiers="" type="drawable"/><file name="ic_long_press" path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res\drawable\ic_long_press.xml" qualifiers="" type="drawable"/><file name="ic_map_24" path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res\drawable\ic_map_24.xml" qualifiers="" type="drawable"/><file name="ic_music_note_24" path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res\drawable\ic_music_note_24.xml" qualifiers="" type="drawable"/><file name="ic_notification" path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res\drawable\ic_notification.xml" qualifiers="" type="drawable"/><file name="ic_pause" path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res\drawable\ic_pause.xml" qualifiers="" type="drawable"/><file name="ic_play_arrow" path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res\drawable\ic_play_arrow.xml" qualifiers="" type="drawable"/><file name="ic_radio_button_checked_sky_blue" path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res\drawable\ic_radio_button_checked_sky_blue.xml" qualifiers="" type="drawable"/><file name="ic_radio_button_unchecked_sky_blue" path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res\drawable\ic_radio_button_unchecked_sky_blue.xml" qualifiers="" type="drawable"/><file name="ic_record_voice_over" path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res\drawable\ic_record_voice_over.xml" qualifiers="" type="drawable"/><file name="ic_save" path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res\drawable\ic_save.xml" qualifiers="" type="drawable"/><file name="ic_screen_rotation_24" path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res\drawable\ic_screen_rotation_24.xml" qualifiers="" type="drawable"/><file name="ic_search_sky_blue_bold" path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res\drawable\ic_search_sky_blue_bold.xml" qualifiers="" type="drawable"/><file name="ic_search_sky_blue_medium" path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res\drawable\ic_search_sky_blue_medium.xml" qualifiers="" type="drawable"/><file name="ic_search_sky_blue_regular" path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res\drawable\ic_search_sky_blue_regular.xml" qualifiers="" type="drawable"/><file name="ic_settings" path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res\drawable\ic_settings.xml" qualifiers="" type="drawable"/><file name="ic_share_24" path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res\drawable\ic_share_24.xml" qualifiers="" type="drawable"/><file name="ic_shopping_cart_24" path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res\drawable\ic_shopping_cart_24.xml" qualifiers="" type="drawable"/><file name="ic_shortcut_command" path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res\drawable\ic_shortcut_command.xml" qualifiers="" type="drawable"/><file name="ic_sky_blue_back_arrow" path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res\drawable\ic_sky_blue_back_arrow.xml" qualifiers="" type="drawable"/><file name="ic_sky_blue_check_disabled" path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res\drawable\ic_sky_blue_check_disabled.xml" qualifiers="" type="drawable"/><file name="ic_sky_blue_check_enabled" path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res\drawable\ic_sky_blue_check_enabled.xml" qualifiers="" type="drawable"/><file name="ic_stop" path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res\drawable\ic_stop.xml" qualifiers="" type="drawable"/><file name="ic_swipe" path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res\drawable\ic_swipe.xml" qualifiers="" type="drawable"/><file name="ic_touch_app" path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res\drawable\ic_touch_app.xml" qualifiers="" type="drawable"/><file name="menu_background" path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res\drawable\menu_background.xml" qualifiers="" type="drawable"/><file name="menu_item_background" path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res\drawable\menu_item_background.xml" qualifiers="" type="drawable"/><file name="overlay_background" path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res\drawable\overlay_background.xml" qualifiers="" type="drawable"/><file name="overlay_background_md3" path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res\drawable\overlay_background_md3.xml" qualifiers="" type="drawable"/><file name="template_anti_embarrassment_mode" path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res\drawable\template_anti_embarrassment_mode.png" qualifiers="" type="drawable"/><file name="template_screen_off_network" path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res\drawable\template_screen_off_network.png" qualifiers="" type="drawable"/><file name="template_screen_on_network" path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res\drawable\template_screen_on_network.png" qualifiers="" type="drawable"/><file name="widget_background" path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res\drawable\widget_background.xml" qualifiers="" type="drawable"/><file name="overlay_smart_reminder" path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res\layout\overlay_smart_reminder.xml" qualifiers="" type="layout"/><file name="widget_one_click_command" path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res\layout\widget_one_click_command.xml" qualifiers="" type="layout"/><file name="advanced_recording_menu" path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res\menu\advanced_recording_menu.xml" qualifiers="" type="menu"/><file name="ic_launcher" path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res\mipmap-anydpi\ic_launcher.xml" qualifiers="anydpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res\mipmap-anydpi\ic_launcher_round.xml" qualifiers="anydpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res\values\colors.xml" qualifiers=""><color name="purple_200">#FFBB86FC</color><color name="purple_500">#FF6200EE</color><color name="purple_700">#FF3700B3</color><color name="teal_200">#FF03DAC5</color><color name="teal_700">#FF018786</color><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color></file><file path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">快捷指令</string><string name="nav_quick_commands">指令</string><string name="nav_command_templates">探索</string><string name="nav_phone_checkup">体检</string><string name="nav_smart_reminders">提醒</string><string name="nav_global_settings">设置</string><string name="save">保存</string><string name="cancel">取消</string><string name="usage_stats_permission_required">需要使用情况访问权限</string><string name="usage_stats_permission_description">检测应用运行状态需要此权限，请在设置中授予权限</string><string name="go_to_settings">前往设置</string><string name="shizuku_not_installed">未安装 Shizuku</string><string name="shizuku_not_running">Shizuku 未运行</string><string name="shizuku_permission_required">需要 Shizuku 权限</string><string name="shizuku_install_guide">请先安装并启动 Shizuku 应用</string><string name="storage_permission_required">需要存储权限才能导出日志</string><string name="storage_permission_description">导出日志需要存储权限，请在设置中授予权限</string><string name="accessibility_service_title">系统优先级提升</string><string name="accessibility_service_manual_title">手动提升系统优先级</string><string name="accessibility_service_description">在系统无障碍设置中启用"快捷指令-系统优先级提升"服务，提高应用后台存活率</string><string name="accessibility_service_system_description">提高应用系统优先级，优化后台存活率</string><string name="accessibility_service_go_to_settings">前往设置</string><string name="accessibility_service_name">快捷指令-系统优先级提升</string><string name="system_operation_accessibility_service_name">快捷指令-系统操作服务</string><string name="system_operation_accessibility_service_description">用于执行系统级操作，包括：快速设置面板、电源菜单、最近任务、应用抽屉、无障碍功能切换、按返回键、屏幕开关控制、主屏幕按钮长按检测、媒体键按下检测等。如需使用系统操作或手动触发相关功能，请启用此服务。</string><string name="interface_interaction_accessibility_service_name">快捷指令-界面交互服务</string><string name="interface_interaction_accessibility_service_description">用于界面自动化操作，包括：界面点击条件（检测到对特定文本内容的点击时触发条件）、屏幕内容条件（当屏幕出现或移出某些文本内容时触发条件）、检查屏幕文字任务（检查特定文本字符串当前是否显示在屏幕上，支持精确匹配/包含匹配、叠加层检测、视图ID提取等高级选项）、读取屏幕内容任务（将当前屏幕的内容捕获到文本文件中）、检查界面元素颜色任务（分析指定位置的界面元素颜色特征，基于元素类型、状态、文本内容等推断颜色信息）。如需使用界面相关的触发条件或任务，请启用此服务。</string><string name="gesture_recognition_accessibility_service_name">快捷指令-手势识别服务</string><string name="gesture_recognition_accessibility_service_description">用于手势识别，包括：指纹手势触发条件（当您做出指定指纹手势时触发条件）。如需使用手势相关的触发条件，请启用此服务。</string><string name="auto_clicker_accessibility_service_name">快捷指令-自动点击器服务</string><string name="auto_clicker_accessibility_service_description">用于自动点击器功能，包括：录制用户的触摸操作（点击、滑动、长按等）、回放录制的操作序列、管理录制文件（保存、加载、编辑）。如需使用自动点击器相关功能，请启用此服务。</string><string name="notification_listener_service_name">快捷指令 - 通知监听</string><string name="notification_listener_service_description">用于监听系统通知事件，实现通知相关的自动化功能。</string><string name="quick_commands_title">指令</string><string name="quick_command_new">新建快捷指令</string><string name="quick_command_edit">编辑快捷指令</string><string name="quick_command_form">快捷指令表单</string><string name="unified_configuration">统一配置选择</string><string name="detailed_configuration">详细配置</string><string name="contact_selection">选择联系人</string><string name="app_selection">选择应用</string><string name="ringtone_selection">选择铃声</string><string name="account_selection">选择账号</string><string name="stopwatch_selection">选择秒表</string><string name="share_target_selection">选择分享方式</string><string name="shortcut_1_short_label">快捷指令1</string><string name="shortcut_1_long_label">快捷指令1</string><string name="shortcut_2_short_label">快捷指令2</string><string name="shortcut_2_long_label">快捷指令2</string><string name="shortcut_3_short_label">快捷指令3</string><string name="shortcut_3_long_label">快捷指令3</string><string name="shortcut_4_short_label">快捷指令4</string><string name="shortcut_4_long_label">快捷指令4</string><string name="widget_1_label">快捷指令1</string><string name="widget_2_label">快捷指令2</string><string name="widget_3_label">快捷指令3</string><string name="widget_4_label">快捷指令4</string><string name="app_importance_management">应用重要性管理</string><string name="app_importance_management_description">管理应用重要性设置</string><string name="advanced_cleanup_strategy">高级清理策略配置</string><string name="advanced_cleanup_strategy_description">自定义清理规则和顺序</string><string name="add_cleanup_rule">添加清理规则</string><string name="add_cleanup_rule_description">添加新的清理规则</string><string name="gesture_recording_edit">编辑手势录制</string><string name="gesture_recording_edit_description">编辑已录制的手势操作</string><string name="language_settings">语言设置</string><string name="language_settings_description">选择应用显示语言</string><string name="language_system_default">系统默认</string><string name="language_chinese">中文</string><string name="language_english">English</string><string name="language_selection_title">选择语言</string><string name="search_field_settings">搜索框设置</string><string name="search_field_placeholder_font_weight">提示文字字重</string><string name="search_field_placeholder_font_weight_description">调整搜索框提示文字的粗细程度</string><string name="font_weight_medium">中等</string><string name="font_weight_regular">常规</string><string name="font_weight_bold">粗体</string><string name="font_weight_selection_title">选择字重</string><string name="search_field_icon_weight">搜索图标粗细</string><string name="search_field_icon_weight_description">调整搜索框图标的粗细程度</string><string name="icon_weight_regular">常规体</string><string name="icon_weight_medium">中黑体</string><string name="icon_weight_bold">粗体</string><string name="icon_weight_selection_title">选择图标粗细</string><string name="widget_update_settings">小组件更新设置</string><string name="widget_update_enabled">启用小组件更新</string><string name="widget_update_enabled_description">定期更新桌面小组件内容，有助于提高应用存活率，但会消耗更多电量</string><string name="widget_update_interval">更新间隔</string><string name="widget_update_interval_hint">建议设置为6-24小时，过于频繁会影响电池续航</string><string name="widget_update_interval_error">请输入有效的数字（最小值为1）</string><string name="manual_widget_update">手动更新小组件</string><string name="manual_widget_update_description">立即更新所有桌面小组件内容，无论是否启用自动更新</string><string name="update_now">立即更新</string><string name="widget_update_completed">小组件更新完成</string><string name="experimental_features">实验性功能</string><string name="experimental_features_enabled">启用实验性功能</string><string name="experimental_features_description">启用实验性功能可能会影响应用稳定性，请谨慎使用</string><string name="edit_condition">编辑条件</string><string name="edit_condition_description">修改触发条件参数</string><string name="edit_abort_condition">编辑中止条件</string><string name="edit_abort_condition_description">修改中止条件参数</string><string name="edit_task">编辑任务</string><string name="edit_task_description">修改任务参数</string><string name="search_apps">搜索应用名称或包名</string><string name="advanced_memory_config">高级内存配置</string><string name="memory_learning_data">内存学习数据</string><string name="shell_script_settings">Shell脚本设置</string><string name="shell_script">Shell脚本</string><string name="shell_script_placeholder">输入要执行的Shell脚本</string><string name="shell_script_placeholder_command">输入要执行的Shell命令或脚本</string><string name="execution_mode">执行模式</string><string name="dialog_settings">显示对话框设置</string><string name="dialog_title">对话框标题</string><string name="dialog_title_placeholder">输入对话框标题</string><string name="dialog_content">对话框内容</string><string name="dialog_content_placeholder">输入对话框内容</string><string name="screen_content">屏幕内容</string><string name="screen_content_description">当屏幕出现或移出某些文本内容时触发条件</string><string name="screen_content_text">屏幕内容文本</string><string name="screen_content_text_label">要检测的屏幕文本内容</string><string name="screen_content_text_placeholder">例如：加载中、网络错误、登录成功等</string><string name="trigger_mode">触发模式</string><string name="tasker_locale_plugin">Tasker/Locale插件</string><string name="tasker_locale_plugin_description">监听Tasker/Locale插件状态</string><string name="screen_text_check_failed">检查屏幕文字失败</string><string name="interface_interaction_service_not_enabled">界面交互无障碍服务未启用，无法执行检查屏幕文字任务</string><string name="device_settings">设备设置</string><string name="device_settings_description">系统设置、主题、壁纸、反色等设备级配置</string><string name="invert_colors">反色</string><string name="invert_colors_description">切换屏幕反色显示模式</string><string name="invert_colors_operation">反色操作</string><string name="font_size">字体大小</string><string name="font_size_description">调整系统字体大小百分比</string><string name="font_size_settings">字体大小设置</string><string name="font_size_percentage">字体大小百分比</string><string name="demo_mode">演示模式</string><string name="demo_mode_description">控制系统演示模式</string><string name="ambient_display">环境显示</string><string name="ambient_display_description">配置环境显示模式</string><string name="power_save_mode">省电模式</string><string name="power_save_mode_description">控制系统省电模式</string><string name="system_settings">系统设置</string><string name="system_settings_description">修改系统设置表中的键值对</string><string name="digital_assistant">设置数字助理</string><string name="digital_assistant_description">设置默认数字助理应用</string><string name="default_keyboard">键盘-设置默认值</string><string name="default_keyboard_description">设置默认输入法键盘</string><string name="keyboard_hint">键盘提示</string><string name="keyboard_hint_description">控制键盘提示功能</string><string name="driving_mode">驾驶模式</string><string name="driving_mode_description">控制驾驶模式功能</string><string name="app_management">应用任务</string><string name="app_management_description">启动、停止、安装、卸载应用</string><string name="application_task">应用任务</string><string name="application_task_description">启动应用、强制停止</string><string name="share_text">分享文本</string><string name="share_text_description">通过系统分享功能分享文本内容</string><string name="enable">开启</string><string name="disable">关闭</string><string name="toggle">切换</string><string name="confirm_configuration">确认配置</string><string name="package_name">包名</string><string name="search_quick_commands">搜索快捷指令名称</string><string name="no_quick_commands">没有快捷指令</string><string name="no_search_results">未找到匹配的快捷指令</string><string name="delete_quick_command">删除快捷指令</string><string name="delete_quick_command_confirm">确定要删除快捷指令"%1$s"吗？此操作将：</string><string name="delete_quick_command_warning">• 从应用中删除此快捷指令</string><string name="delete_operation_irreversible">此操作不可撤销。</string><string name="delete">删除</string><string name="batch_delete_quick_commands">批量删除快捷指令</string><string name="batch_delete_confirm">确定要删除选中的 %1$d 个快捷指令吗？此操作将：</string><string name="batch_delete_warning">• 从应用中删除这些快捷指令</string><string name="shortcut_not_configured">此快捷方式尚未配置，请在应用中创建快捷指令并选择使用此静态快捷方式</string><string name="executing_quick_command">正在执行快捷指令：%1$s（%2$d个任务）</string><string name="quick_command_completed">快捷指令执行完成：%1$s</string><string name="quick_command_aborted">快捷指令执行被中止：%1$s，中止条件：%2$s</string><string name="quick_command_not_found">找不到关联的快捷指令</string><string name="invalid_command">无效的指令</string><string name="quick_command_not_found_simple">找不到快捷指令</string><string name="template_screen_off_network_title">息屏关闭网络</string><string name="template_screen_on_network_title">亮屏恢复网络</string><string name="template_auto_brightness_title">自动亮度调节</string><string name="template_battery_saver_title">省电模式</string><string name="template_do_not_disturb_title">免打扰模式</string><string name="template_anti_embarrassment_mode_title">防社死模式</string><string name="template_screen_off_network_desc">屏幕关闭后延时关闭WiFi和移动数据，节省电量</string><string name="template_screen_on_network_desc">屏幕打开后延时恢复之前保存的网络状态</string><string name="template_auto_brightness_desc">根据环境光线自动调节屏幕亮度</string><string name="template_battery_saver_desc">电量低于阈值时自动开启省电模式</string><string name="template_do_not_disturb_desc">在指定时间段自动开启免打扰模式</string><string name="template_anti_embarrassment_mode_desc">避免打开短视频软件时音量过大带来的尴尬</string><string name="template_category_network">网络管理</string><string name="template_category_power">电源管理</string><string name="template_category_display">显示控制</string><string name="template_category_system">系统操作</string><string name="template_category_automation">自动化</string><string name="search_templates">搜索模板名称或描述</string><string name="no_templates">暂无可用模板</string><string name="no_template_search_results">未找到匹配的模板</string><string name="tasks_count">%1$d个任务</string><string name="shizuku_category_app_management">应用管理命令</string><string name="shizuku_category_network">网络相关命令</string><string name="shizuku_category_system">系统控制命令</string><string name="shizuku_category_key_simulation">按键模拟命令</string><string name="shizuku_category_screen_operation">屏幕操作命令</string><string name="switch_operation_on">开启</string><string name="switch_operation_off">关闭</string><string name="switch_operation_toggle">切换</string><string name="invert_colors_on">开启</string><string name="invert_colors_off">关闭</string><string name="auto_rotate_enable">启用</string><string name="auto_rotate_disable">禁用</string><string name="accessibility_service_enable">启用</string><string name="accessibility_service_disable">禁用</string><string name="check_text_content">要检查的文本内容</string><string name="text_content_label">文本内容</string><string name="text_content_placeholder">例如：登录成功、加载完成等</string><string name="match_options">匹配选项</string><string name="time_condition_stopwatch">秒表</string><string name="time_condition_sun_event">日出日落</string><string name="time_condition_scheduled_time">日程时间</string><string name="time_condition_periodic_time">周期时间</string><string name="time_condition_delayed_trigger">延迟触发</string><string name="time_condition_periodic_trigger">周期触发</string><string name="sun_event_sunrise">日出</string><string name="sun_event_sunset">日落</string><string name="screen_event_unlocked">屏幕解锁</string><string name="screen_event_on">屏幕开启</string><string name="screen_event_off">屏幕关闭</string><string name="screen_event_auto_rotate_enabled">自动旋转启用</string><string name="screen_event_auto_rotate_disabled">自动旋转禁用</string><string name="app_detection_any_app">任何应用</string><string name="app_detection_selected_apps">指定应用</string><string name="app_trigger_any_app">任意应用</string><string name="app_trigger_all_apps">所有应用</string><string name="communication_state">通信状态</string><string name="communication_state_description">通话、短信状态变化</string><string name="connection_state">连接状态</string><string name="connection_state_description">网络、蓝牙、USB连接状态</string><string name="sensor_state">传感器状态</string><string name="sensor_state_description">光线、方向、震动传感器</string><string name="app_state">应用状态</string><string name="app_state_description">应用前台、后台、安装</string><string name="device_event">设备事件</string><string name="device_event_description">电池、屏幕、系统事件</string><string name="time_based">时间条件</string><string name="time_based_description">定时、周期、延迟触发</string><string name="manual_trigger">手动触发</string><string name="manual_trigger_description">快捷方式、小组件触发</string><string name="battery_state">电池状态</string><string name="battery_state_description">电量、充电状态变化</string><string name="state_change">状态变化</string><string name="state_change_description">应用前台、后台状态变化、后台时间超过阈值</string><string name="lifecycle">生命周期</string><string name="lifecycle_description">应用启动、关闭</string><string name="package_management">应用管理</string><string name="package_management_description">应用安装、删除、更新</string><string name="interface_click">界面点击</string><string name="interface_click_description">检测到对特定文本内容的点击时触发条件</string><string name="gps_state">GPS状态</string><string name="gps_state_description">当GPS状态改变时触发条件</string><string name="logcat_message">Logcat消息</string><string name="logcat_message_description">当Logcat出现指定消息时触发条件</string><string name="clipboard_changed">剪贴板变化</string><string name="clipboard_changed_description">当剪贴板内容变化时触发条件</string><string name="screen_state">屏幕状态</string><string name="screen_state_description">屏幕开启、关闭、解锁状态变化</string><string name="dock_state">底座连接</string><string name="dock_state_description">设备底座连接状态变化</string><string name="intent_received">Intent接收</string><string name="intent_received_description">接收到指定Intent时触发条件</string><string name="sim_card_state">SIM卡状态</string><string name="sim_card_state_description">SIM卡状态变化时触发条件</string><string name="dark_theme_changed">深色主题</string><string name="dark_theme_changed_description">系统深色主题状态变化</string><string name="login_attempt_failed">登录失败</string><string name="login_attempt_failed_description">设备登录尝试失败时触发条件</string><string name="system_setting_changed">系统设置</string><string name="system_setting_changed_description">系统设置项变化时触发条件</string><string name="auto_sync_state">自动同步</string><string name="auto_sync_state_description">自动同步状态变化时触发条件</string><string name="device_boot_completed">设备启动</string><string name="device_boot_completed_description">设备启动完成时触发条件</string><string name="notification_event">通知事件</string><string name="notification_event_description">通知发布、取消时触发条件</string><string name="ringer_mode_changed">铃声模式</string><string name="ringer_mode_changed_description">铃声模式变化时触发条件</string><string name="music_playback_state">音乐播放</string><string name="music_playback_state_description">音乐播放状态变化时触发条件</string><string name="airplane_mode_changed">飞行模式</string><string name="airplane_mode_changed_description">飞行模式状态变化时触发条件</string><string name="volume_changed">音量变化</string><string name="volume_changed_description">系统音量变化时触发条件</string><string name="phone_task">电话任务</string><string name="phone_task_description">拨打电话、接听电话等</string><string name="media_task">媒体任务</string><string name="media_task_description">媒体控制、声音播放</string><string name="location_task">位置任务</string><string name="location_task_description">GPS、位置服务控制</string><string name="file_operation_task">文件读写</string><string name="file_operation_task_description">文件读写、打开文件</string><string name="camera_task">照片查看</string><string name="camera_task_description">查看照片、截屏</string><string name="information_task">信息任务</string><string name="information_task_description">短信、邮件、通知</string><string name="connectivity_task">连接任务</string><string name="connectivity_task_description">WiFi、蓝牙、网络控制</string><string name="screen_control_task">屏幕控制</string><string name="screen_control_task_description">屏幕亮度、方向、锁屏</string><string name="volume_task">音量任务</string><string name="volume_task_description">音量调节、静音控制</string><string name="device_action_task">设备动作</string><string name="device_action_task_description">分享、剪贴板、系统操作</string><string name="notification_task">通知任务</string><string name="notification_task_description">发送、取消通知</string><string name="datetime_task">日期时间</string><string name="datetime_task_description">时间设置、闹钟、日历</string><string name="phone_open_call_log">打开通话记录</string><string name="phone_open_call_log_description">打开系统通话记录页面</string><string name="phone_reject_call">拒接电话</string><string name="phone_reject_call_description">拒接当前来电</string><string name="phone_make_call">拨打电话</string><string name="phone_make_call_description">拨打指定电话号码</string><string name="phone_answer_call">接听电话</string><string name="phone_answer_call_description">接听当前来电</string><string name="phone_clear_call_log">清除通话记录</string><string name="phone_clear_call_log_description">清除指定类型的通话记录</string><string name="phone_ringtone_settings">电话铃声设置</string><string name="phone_ringtone_settings_description">设置电话来电铃声，选择自定义铃声并应用到系统</string><string name="info_send_sms">发送短信</string><string name="info_send_sms_description">发送短信到指定号码，支持SIM卡选择和预填写模式</string><string name="info_send_email">发送邮件</string><string name="info_send_email_description">发送邮件到指定邮箱，支持完整的SMTP配置和通知选项</string><string name="info_message_ringtone">信息铃声设置</string><string name="info_message_ringtone_description">设置短信和通知的铃声</string><string name="location_share_location">分享位置</string><string name="location_share_location_description">通过短信、联系人或手机号码分享当前位置</string><string name="location_toggle_location_service">定位服务控制</string><string name="location_force_location_update">强制位置更新</string><string name="location_force_location_update_description">强制更新当前位置信息</string><string name="location_set_location_update_frequency">设置位置更新频率</string><string name="location_set_location_update_frequency_description">设置位置信息的更新频率</string><string name="location_toggle_location_service_description">开启、关闭或切换定位服务状态</string><string name="camera_open_last_photo">打开最后一张照片</string><string name="camera_open_last_photo_description">快速访问相册中最后一张照片</string><string name="camera_take_photo">拍照</string><string name="camera_take_photo_description">使用前置或后置摄像头拍照，支持自定义保存位置</string><string name="connectivity_wifi_control">WiFi控制</string><string name="connectivity_wifi_control_description">控制WiFi的开启、关闭或切换</string><string name="connectivity_mobile_data_control">移动数据控制</string><string name="connectivity_mobile_data_control_description">控制移动数据的开启、关闭或切换</string><string name="media_multimedia_control">多媒体控制</string><string name="media_multimedia_control_description">模拟媒体按钮、默认播放器控制、音频按钮</string><string name="media_play_stop_sound">播放/停止声音</string><string name="media_play_stop_sound_description">播放音频文件、铃声或停止现有声音</string><string name="device_settings_invert_colors">反色</string><string name="device_settings_invert_colors_description">切换屏幕反色显示模式</string><string name="device_settings_font_size">字体大小</string><string name="device_settings_font_size_description">调整系统字体大小百分比</string><string name="time_condition_stopwatch_description">设定倒计时时间，时间到达时触发</string><string name="time_condition_sun_event_description">在日出或日落时间触发</string><string name="datetime_stopwatch">秒表操作</string><string name="datetime_stopwatch_description">启动、停止、重置秒表</string><string name="datetime_alarm">闹钟操作</string><string name="datetime_alarm_description">设置、取消闹钟，支持自定义铃声</string><string name="datetime_voice_time_announcement">语音报时</string><string name="datetime_voice_time_announcement_description">使用TTS引擎播报当前时间，支持24/12小时制</string><string name="notification_show_notification">显示通知</string><string name="notification_show_notification_description">在通知栏显示自定义通知</string><string name="notification_cancel_notification">取消通知</string><string name="notification_cancel_notification_description">取消指定的通知</string><string name="device_action_share_text">分享文本</string><string name="device_action_share_text_description">通过系统分享功能分享文本内容</string><string name="task_app_management">应用任务</string><string name="task_app_management_description">启动、停止、安装、卸载应用</string><string name="task_device_settings">设备设置</string><string name="task_device_settings_description">系统设置、主题、壁纸、反色等设备级配置</string><string name="task_log">日志任务</string><string name="task_log_description">记录日志信息</string><string name="task_phone">电话任务</string><string name="task_phone_description">拨打电话、接听电话、通话记录管理</string><string name="task_screen_control">屏幕控制</string><string name="task_screen_control_description">亮度控制、屏幕开关、旋转控制等</string><string name="task_log_task">日志任务</string><string name="task_log_task_description">记录日志信息</string><string name="task_phone_task">电话任务</string><string name="task_phone_task_description">拨打电话、接听电话、通话记录管理</string><string name="task_screen_control_task">屏幕控制</string><string name="task_screen_control_task_description">亮度控制、屏幕开关、旋转控制等</string><string name="task_media_task">媒体任务</string><string name="task_media_task_description">播放音频、录制音频、拍照、录像</string><string name="task_alarm_reminder">闹钟提醒</string><string name="task_alarm_reminder_description">设置闹钟和提醒</string><string name="task_file_operation">文件操作</string><string name="task_file_operation_description">创建、删除、移动、复制文件</string><string name="task_network">网络任务</string><string name="task_network_description">HTTP请求、网络检测</string><string name="task_location">位置任务</string><string name="task_location_description">获取位置信息、地理围栏</string><string name="configure_item">配置 %1$s</string><string name="back">返回</string><string name="media_microphone_recording">麦克风录音</string><string name="media_microphone_recording_description">录制音频，支持自定义时长和格式</string><string name="device_settings_enter_screensaver">进入屏保模式</string><string name="device_settings_enter_screensaver_description">激活系统屏保模式</string><string name="device_settings_auto_rotate">屏幕自动旋转</string><string name="device_settings_auto_rotate_description">控制屏幕自动旋转功能</string><string name="device_settings_accessibility_service">无障碍服务</string><string name="device_settings_accessibility_service_description">控制指定无障碍服务的启用状态</string><string name="device_settings_display_density">显示密度</string><string name="device_settings_display_density_description">调整屏幕显示密度百分比</string><string name="device_settings_immersive_mode">沉浸模式</string><string name="device_settings_immersive_mode_description">设置系统沉浸模式类型</string><string name="device_settings_keyboard_hint">键盘提示</string><string name="device_settings_keyboard_hint_description">控制键盘提示功能</string><string name="device_settings_driving_mode">驾驶模式</string><string name="device_settings_driving_mode_description">控制驾驶模式功能</string><string name="camera_record_video">录像</string><string name="camera_record_video_description">使用前置或后置摄像头录像，支持定时录制和自定义保存位置</string><string name="camera_flashlight_control">手电筒控制</string><string name="camera_flashlight_control_description">控制手电筒的开启、关闭或切换</string><string name="camera_screenshot">截屏</string><string name="camera_screenshot_description">截取当前屏幕内容并保存</string><string name="connectivity_bluetooth_control">蓝牙控制</string><string name="connectivity_bluetooth_control_description">控制蓝牙的开启、关闭或切换</string><string name="connectivity_hotspot_control">热点控制</string><string name="connectivity_hotspot_control_description">控制WiFi热点的开启、关闭或切换</string><string name="connectivity_airplane_mode_control">飞行模式控制</string><string name="connectivity_airplane_mode_control_description">控制飞行模式的开启、关闭或切换</string><string name="connectivity_nfc_control">NFC控制</string><string name="connectivity_nfc_control_description">控制NFC的开启、关闭或切换</string><string name="connectivity_send_intent">发送Intent</string><string name="connectivity_send_intent_description">发送自定义Intent到指定应用或系统</string><string name="connectivity_network_check">网络连接检查</string><string name="connectivity_network_check_description">检查网络连接状态并返回结果</string><string name="location_get_location">获取位置</string><string name="location_get_location_description">获取当前位置信息并保存到变量</string><string name="info_show_toast">显示Toast</string><string name="info_show_toast_description">在屏幕上显示短暂的提示消息</string><string name="info_show_dialog">显示对话框</string><string name="info_show_dialog_description">显示自定义对话框，支持标题、内容和按钮配置</string><string name="app_launch_app">启动应用</string><string name="app_launch_app_description">启动指定的应用程序</string><string name="app_force_stop_app">强制停止应用</string><string name="app_force_stop_app_description">强制停止指定的应用程序</string><string name="app_tasker_locale_plugin">Tasker/Locale插件</string><string name="app_tasker_locale_plugin_description">执行Tasker或Locale任务插件</string><string name="app_freeze_app">冻结应用</string><string name="app_freeze_app_description">冻结指定的应用程序</string><string name="app_unfreeze_app">解冻应用</string><string name="app_unfreeze_app_description">解冻指定的应用程序</string><string name="app_open_website">打开网站</string><string name="app_open_website_description">在默认浏览器中打开指定网站</string><string name="app_execute_javascript">执行JavaScript代码</string><string name="app_execute_javascript_description">执行自定义的JavaScript代码</string><string name="app_execute_shell_script">执行Shell脚本</string><string name="app_execute_shell_script_description">执行自定义的Shell脚本</string><string name="volume_volume_change">音量变化</string><string name="volume_volume_change_description">设置指定音频流的音量大小</string><string name="volume_volume_adjust">音量调节</string><string name="volume_volume_adjust_description">增加或减少指定音频流的音量</string><string name="volume_speakerphone_control">免提通话控制</string><string name="volume_speakerphone_control_description">控制免提通话的开启、关闭或切换</string><string name="volume_vibration_mode">振动模式</string><string name="volume_vibration_mode_description">设置设备的振动模式</string><string name="volume_volume_popup">音量弹出窗口</string><string name="volume_volume_popup_description">显示系统音量调节弹出窗口</string><string name="volume_do_not_disturb">勿扰模式</string><string name="volume_do_not_disturb_description">设置勿扰模式状态</string><string name="screen_brightness_control">亮度控制</string><string name="screen_brightness_control_description">调节屏幕亮度，支持百分比和绝对值设置</string><string name="screen_keep_device_awake">保持设备唤醒</string><string name="screen_keep_device_awake_description">防止设备进入休眠状态</string><string name="file_write_file">写入文件</string><string name="file_write_file_description">向指定文件写入内容，支持添加、覆盖、准备提交模式</string><string name="file_open_file">打开文件</string><string name="file_open_file_description">打开指定文件，支持手动输入或选择文件，可指定应用</string><string name="file_file_operation">文件管理</string><string name="file_file_operation_description">文件管理操作：复制、移动、删除、压缩、新建文件夹、重命名</string><string name="comm_sms_received">收到短信</string><string name="comm_sms_received_description">当收到短信时触发条件</string><string name="comm_sms_sent">发送短信</string><string name="comm_sms_sent_description">当发送短信时触发条件</string><string name="comm_incoming_call">接到呼叫</string><string name="comm_incoming_call_description">当有来电时触发条件</string><string name="comm_outgoing_call">拨出电话</string><string name="comm_outgoing_call_description">当拨打电话时触发条件</string><string name="comm_call_active">通话中</string><string name="comm_call_active_description">当通话进行中时触发条件</string><string name="comm_call_ended">通话结束</string><string name="comm_call_ended_description">当通话结束时触发条件</string><string name="conn_wifi_state">WiFi状态</string><string name="conn_wifi_state_description">当WiFi开启/关闭或连接/断开时触发条件</string><string name="conn_wifi_network">WiFi网络</string><string name="conn_wifi_network_description">当连接到特定WiFi网络时触发条件</string><string name="sensor_light_sensor">光线传感器</string><string name="sensor_light_sensor_description">当环境光线变化时触发条件</string><string name="sensor_orientation_sensor">屏幕方向传感器</string><string name="sensor_orientation_sensor_description">当屏幕方向变化时触发条件</string><string name="app_state_change">状态变化</string><string name="app_state_change_description">应用前台、后台状态变化、后台时间超过阈值</string><string name="app_lifecycle">生命周期</string><string name="app_lifecycle_description">应用启动、关闭</string><string name="app_package_management">应用管理</string><string name="app_package_management_description">应用安装、删除、更新</string><string name="app_interface_click">界面点击</string><string name="app_interface_click_description">检测到对特定文本内容的点击时触发条件</string><string name="app_screen_content">屏幕内容</string><string name="app_screen_content_description">当屏幕出现或移出某些文本内容时触发条件</string><string name="device_gps_state">GPS状态</string><string name="device_gps_state_description">当GPS状态改变时触发条件</string><string name="device_logcat_message">Logcat消息</string><string name="device_logcat_message_description">当Logcat出现指定消息时触发条件</string><string name="device_clipboard_changed">剪贴板变化</string><string name="device_clipboard_changed_description">当剪贴板内容变化时触发条件</string><string name="battery_battery_level">电池电量</string><string name="battery_battery_level_description">监控电池电量变化和阈值</string><string name="battery_charging_state">充电状态</string><string name="battery_charging_state_description">监控设备充电状态变化</string><string name="manual_dynamic_shortcut">动态快捷方式</string><string name="manual_dynamic_shortcut_description">通过动态快捷方式触发</string><string name="manual_static_shortcut">静态快捷方式</string><string name="manual_static_shortcut_description">通过静态快捷方式触发</string><string name="manual_fingerprint_gesture">指纹手势</string><string name="manual_fingerprint_gesture_description">通过指纹手势触发</string><string name="manual_home_button_long_press">主屏幕按钮长按</string><string name="manual_home_button_long_press_description">当你长时间按住主页键时,将触发条件。这个条件可能需要将本应用配置为默认的辅助和语音输入应用程序，可能需要申请录音权限，以便应用可持续正常运行。</string><string name="manual_media_key_press">媒体键按下</string><string name="manual_media_key_press_description">当媒体键(在耳机上)被按下1到3次时，将触发条件</string><string name="manual_volume_key_press">音量键按下</string><string name="manual_volume_key_press_description">通过音量键按下触发，可选择是否保留原音量</string><string name="time_condition_scheduled_time_description">在指定的日期和时间触发</string><string name="time_condition_time_period">时间段</string><string name="time_condition_time_period_description">在指定的时间段内触发</string><string name="time_condition_periodic_time_description">在每周指定的时间重复触发</string><string name="time_condition_delayed_trigger_description">延迟指定时间后触发一次</string><string name="conn_mobile_data">移动数据</string><string name="conn_mobile_data_description">当移动数据连接状态改变时触发条件</string><string name="conn_bluetooth_state">蓝牙状态</string><string name="conn_bluetooth_state_description">当蓝牙开启/关闭或连接/断开时触发条件</string><string name="sensor_shake_sensor">摇晃检测</string><string name="sensor_shake_sensor_description">当设备摇晃时触发条件</string><string name="sensor_sleep_sensor">睡眠检测</string><string name="sensor_sleep_sensor_description">当检测到睡眠状态变化时触发条件</string><string name="sensor_flip_sensor">设备翻转检测</string><string name="sensor_flip_sensor_description">当设备翻转时触发条件</string><string name="sensor_proximity_sensor">接近传感器</string><string name="sensor_proximity_sensor_description">当物体接近或远离时触发条件</string><string name="sensor_activity_recognition">运动识别</string><string name="sensor_activity_recognition_description">当检测到特定运动状态时触发条件</string><string name="smart_reminders_title">智慧提醒</string><string name="smart_reminders_description">智能检测并提供有用的提醒</string><string name="search_smart_reminders">搜索智慧提醒功能</string><string name="screen_rotation_reminder_title">屏幕旋转提醒</string><string name="screen_rotation_reminder_description">翻转手机后，建议屏幕旋转</string><string name="screen_rotation_reminder_status_unconfigured">功能可直接使用</string><string name="screen_rotation_reminder_status_enabled">正在监控屏幕旋转</string><string name="screen_rotation_reminder_status_disabled">已关闭</string><string name="flashlight_reminder_title">手电筒提醒</string><string name="flashlight_reminder_description">手电筒开启后，建议关闭</string><string name="flashlight_reminder_status_unconfigured">功能可直接使用</string><string name="flashlight_reminder_status_enabled">正在监控手电筒状态</string><string name="flashlight_reminder_status_disabled">已关闭</string><string name="new_app_reminder_title">打开新应用</string><string name="new_app_reminder_description">安装新应用后，建议打开</string><string name="new_app_reminder_status_unconfigured">功能可直接使用</string><string name="new_app_reminder_status_enabled">正在监控应用安装</string><string name="new_app_reminder_status_disabled">已关闭</string><string name="music_app_reminder_title">音乐应用提醒</string><string name="music_app_reminder_description">连接耳机时，建议打开音乐应用</string><string name="music_app_reminder_status_unconfigured">需要选择音乐应用</string><string name="music_app_reminder_status_configured">已选择：%s</string><string name="music_app_reminder_status_enabled">耳机连接时提醒打开：%s</string><string name="music_app_reminder_status_disabled">已关闭</string><string name="shopping_app_reminder_title">购物应用提醒</string><string name="shopping_app_reminder_description">复制商品链接时，建议打开对应购物应用</string><string name="shopping_app_reminder_status_unconfigured">功能可直接使用</string><string name="shopping_app_reminder_status_enabled">正在监控剪贴板</string><string name="shopping_app_reminder_status_disabled">已关闭</string><string name="app_link_reminder_title">打开应用链接</string><string name="app_link_reminder_description">检测到应用链接时，智能识别平台并建议打开对应应用</string><string name="app_link_reminder_status_unconfigured">功能可直接使用</string><string name="app_link_reminder_status_enabled">正在监控剪贴板</string><string name="app_link_reminder_status_disabled">已关闭</string><string name="custom_app_platform_config">自定义应用平台配置</string><string name="intelligent_link_recognition_title">智能链接识别</string><string name="intelligent_link_recognition_description">自动优化链接格式，提升识别准确性和兼容性</string><string name="intelligent_link_recognition_enabled">启用智能识别</string><string name="intelligent_link_recognition_help">自动标准化链接格式，清理非标准字符和干扰内容</string><string name="share_url_reminder_title">分享网址</string><string name="share_url_reminder_description">复制网址链接时，建议分享网址</string><string name="share_url_reminder_status_unconfigured">功能可直接使用</string><string name="share_url_reminder_status_enabled">正在监控剪贴板</string><string name="share_url_reminder_status_disabled">已关闭</string><string name="smart_reminder_configure">配置</string><string name="smart_reminder_detail_settings">详细设置</string><string name="smart_reminder_select_app">选择应用</string><string name="smart_reminder_change_app">更换应用</string><string name="smart_reminder_status_label">状态：</string><string name="smart_reminder_needs_configuration">需要完成配置</string><string name="smart_reminder_detail_config">智慧提醒详细配置</string><string name="smart_reminder_config_error">配置错误</string><string name="smart_reminder_type_not_found">找不到指定的智慧提醒类型</string><string name="smart_reminder_config_not_found">找不到对应的配置项</string><string name="smart_reminder_tap_to_setup">轻触设置</string><string name="smart_reminder_configured">已配置</string><string name="smart_reminder_ready_to_use">可直接使用</string><string name="smart_reminder_selected_apps_count">已选择 %d 个应用</string><string name="phone_checkup_title">体检</string><string name="running_apps_count">后台运行应用: %d个</string><string name="optimize_button">一键优化</string><string name="checkup_button">立即体检</string><string name="phone_status_excellent">手机运行良好，性能优秀</string><string name="phone_status_good">手机运行正常，建议优化</string><string name="phone_status_poor">手机运行缓慢，需要优化</string><string name="optimizing">正在优化...</string><string name="optimization_complete">优化完成</string><string name="optimization_failed">优化失败</string><string name="select_music_apps">选择音乐应用</string><string name="change_music_apps">更换音乐应用</string><string name="tap_to_select_music_apps">轻触选择要监控的音乐应用</string><string name="select_map_apps">选择地图应用</string><string name="change_map_apps">更换地图应用</string><string name="tap_to_select_map_apps">轻触选择要监控的地图应用</string><string name="custom_shopping_platform_config">自定义购物平台配置</string><string name="address_reminder_title">地址提醒</string><string name="address_reminder_description">复制地址时，建议打开地图应用</string><string name="address_reminder_status_enabled">正在监控地址复制 - %1$s</string><string name="address_reminder_status_disabled">已关闭</string><string name="address_reminder_message">检测到地址内容，建议打开「%1$s」</string></file><file path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.QuickCommands" parent="android:Theme.Material.Light.NoActionBar"/><style name="Theme.QuickCommands.NoActionBar" parent="Theme.QuickCommands">
        <item name="android:windowActionBar">false</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsTranslucent">true</item>
    </style></file><file path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res\values-en\strings.xml" qualifiers="en"><string name="app_name">Quick Commands</string><string name="nav_quick_commands">Commands</string><string name="nav_command_templates">Explore</string><string name="nav_phone_checkup">Checkup</string><string name="nav_smart_reminders">Reminders</string><string name="nav_global_settings">Settings</string><string name="save">Save</string><string name="cancel">Cancel</string><string name="usage_stats_permission_required">Usage Access Permission Required</string><string name="usage_stats_permission_description">This permission is required to detect app running status, please grant it in settings</string><string name="go_to_settings">Go to Settings</string><string name="shizuku_not_installed">Shizuku Not Installed</string><string name="shizuku_not_running">Shizuku Not Running</string><string name="shizuku_permission_required">Shizuku Permission Required</string><string name="shizuku_install_guide">Please install and start Shizuku app first</string><string name="storage_permission_required">Storage permission required to export logs</string><string name="storage_permission_description">Storage permission is required to export logs, please grant it in settings</string><string name="accessibility_service_title">System Priority Enhancement</string><string name="accessibility_service_manual_title">Manual System Priority Enhancement</string><string name="accessibility_service_description">Enable \"Quick Commands - System Priority Enhancement\" service in system accessibility settings to improve app background survival rate</string><string name="accessibility_service_system_description">Improve app system priority and optimize background survival rate</string><string name="accessibility_service_go_to_settings">Go to Settings</string><string name="accessibility_service_name">Quick Commands - System Priority Enhancement</string><string name="system_operation_accessibility_service_name">Quick Commands - System Operation Service</string><string name="system_operation_accessibility_service_description">Used for system-level operations including: Quick Settings panel, Power menu, Recent tasks, App drawer, Accessibility feature toggle, Back button, Screen on/off control, Home button long press detection, Media key press detection, etc. Please enable this service if you need to use system operation or manual trigger related features.</string><string name="interface_interaction_accessibility_service_name">Quick Commands - Interface Interaction Service</string><string name="interface_interaction_accessibility_service_description">Used for interface automation operations including: Interface click conditions (triggered when specific text content is clicked), Screen content conditions (triggered when certain text content appears or disappears on screen), Check screen text task (check if specific text string is currently displayed on screen, supports exact match/contains match, overlay detection, view ID extraction and other advanced options), Read screen content task (capture current screen content to text file), Check interface element color task (analyze color characteristics of interface elements at specified positions, infer color information based on element type, state, text content, etc.). Please enable this service if you need to use interface-related trigger conditions or tasks.</string><string name="gesture_recognition_accessibility_service_name">Quick Commands - Gesture Recognition Service</string><string name="gesture_recognition_accessibility_service_description">Used for gesture recognition including: Fingerprint gesture trigger conditions (triggered when you perform specified fingerprint gestures). Please enable this service if you need to use gesture-related trigger conditions.</string><string name="auto_clicker_accessibility_service_name">Quick Commands - Auto Clicker Service</string><string name="auto_clicker_accessibility_service_description">Used for auto clicker functionality including: Recording user touch operations (tap, swipe, long press, etc.), Playing back recorded operation sequences, Managing recording files (save, load, edit). Please enable this service if you need to use auto clicker related features.</string><string name="notification_listener_service_name">Quick Commands - Notification Listener</string><string name="notification_listener_service_description">Used to listen to system notification events and implement notification-related automation features.</string><string name="quick_commands_title">Commands</string><string name="quick_command_new">New Quick Command</string><string name="quick_command_edit">Edit Quick Command</string><string name="quick_command_form">Quick Command Form</string><string name="unified_configuration">Unified Configuration Selection</string><string name="detailed_configuration">Detailed Configuration</string><string name="contact_selection">Select Contact</string><string name="app_selection">Select App</string><string name="ringtone_selection">Select Ringtone</string><string name="account_selection">Select Account</string><string name="stopwatch_selection">Select Stopwatch</string><string name="share_target_selection">Select Share Target</string><string name="shortcut_1_short_label">Quick Command 1</string><string name="shortcut_1_long_label">Quick Command 1</string><string name="shortcut_2_short_label">Quick Command 2</string><string name="shortcut_2_long_label">Quick Command 2</string><string name="shortcut_3_short_label">Quick Command 3</string><string name="shortcut_3_long_label">Quick Command 3</string><string name="shortcut_4_short_label">Quick Command 4</string><string name="shortcut_4_long_label">Quick Command 4</string><string name="widget_1_label">Quick Command 1</string><string name="widget_2_label">Quick Command 2</string><string name="widget_3_label">Quick Command 3</string><string name="widget_4_label">Quick Command 4</string><string name="app_importance_management">App Importance Management</string><string name="app_importance_management_description">Manage app importance settings</string><string name="advanced_cleanup_strategy">Advanced Cleanup Strategy Configuration</string><string name="advanced_cleanup_strategy_description">Customize cleanup rules and order</string><string name="add_cleanup_rule">Add Cleanup Rule</string><string name="add_cleanup_rule_description">Add new cleanup rule</string><string name="language_settings">Language Settings</string><string name="language_settings_description">Select app display language</string><string name="language_system_default">System Default</string><string name="language_chinese">中文</string><string name="language_english">English</string><string name="language_selection_title">Select Language</string><string name="search_field_settings">Search Field Settings</string><string name="search_field_placeholder_font_weight">Placeholder Font Weight</string><string name="search_field_placeholder_font_weight_description">Adjust the thickness of search field placeholder text</string><string name="font_weight_medium">Medium</string><string name="font_weight_regular">Regular</string><string name="font_weight_bold">Bold</string><string name="font_weight_selection_title">Select Font Weight</string><string name="search_field_icon_weight">Search Icon Weight</string><string name="search_field_icon_weight_description">Adjust the thickness of search field icon</string><string name="icon_weight_regular">Regular</string><string name="icon_weight_medium">Medium</string><string name="icon_weight_bold">Bold</string><string name="icon_weight_selection_title">Select Icon Weight</string><string name="widget_update_settings">Widget Update Settings</string><string name="widget_update_enabled">Enable Widget Updates</string><string name="widget_update_enabled_description">Periodically update desktop widget content, helps improve app survival rate but consumes more battery</string><string name="widget_update_interval">Update Interval</string><string name="widget_update_interval_hint">Recommended to set 6-24 hours, too frequent updates will affect battery life</string><string name="widget_update_interval_error">Please enter a valid number (minimum value is 1)</string><string name="manual_widget_update">Manual Widget Update</string><string name="manual_widget_update_description">Immediately update all desktop widget content, regardless of whether auto-update is enabled</string><string name="update_now">Update Now</string><string name="widget_update_completed">Widget update completed</string><string name="experimental_features">Experimental Features</string><string name="experimental_features_enabled">Enable Experimental Features</string><string name="experimental_features_description">Enabling experimental features may affect app stability, please use with caution</string><string name="shell_script_settings">Shell Script Settings</string><string name="shell_script">Shell Script</string><string name="shell_script_placeholder">Enter shell script to execute</string><string name="shell_script_placeholder_command">Enter shell command or script to execute</string><string name="execution_mode">Execution Mode</string><string name="dialog_settings">Show Dialog Settings</string><string name="dialog_title">Dialog Title</string><string name="dialog_title_placeholder">Enter dialog title</string><string name="dialog_content">Dialog Content</string><string name="dialog_content_placeholder">Enter dialog content</string><string name="edit_condition">Edit Condition</string><string name="edit_condition_description">Modify trigger condition parameters</string><string name="edit_abort_condition">Edit Abort Condition</string><string name="edit_abort_condition_description">Modify abort condition parameters</string><string name="edit_task">Edit Task</string><string name="edit_task_description">Modify task parameters</string><string name="search_apps">Search app name or package name</string><string name="screen_content">Screen Content</string><string name="screen_content_description">Triggered when certain text content appears or disappears on screen</string><string name="screen_content_text">Screen Content Text</string><string name="screen_content_text_label">Screen text content to detect</string><string name="screen_content_text_placeholder">e.g.: Loading, Network Error, Login Successful, etc.</string><string name="trigger_mode">Trigger Mode</string><string name="tasker_locale_plugin">Tasker/Locale Plugin</string><string name="tasker_locale_plugin_description">Monitor Tasker/Locale plugin status</string><string name="screen_text_check_failed">Screen text check failed</string><string name="interface_interaction_service_not_enabled">Interface interaction accessibility service not enabled, cannot execute screen text check task</string><string name="device_settings">Device Settings</string><string name="device_settings_description">System settings, themes, wallpapers, invert colors and other device-level configurations</string><string name="invert_colors">Invert Colors</string><string name="invert_colors_description">Toggle screen color inversion mode</string><string name="invert_colors_operation">Invert Colors Operation</string><string name="font_size">Font Size</string><string name="font_size_description">Adjust system font size percentage</string><string name="font_size_settings">Font Size Settings</string><string name="font_size_percentage">Font Size Percentage</string><string name="demo_mode">Demo Mode</string><string name="demo_mode_description">Control system demo mode</string><string name="ambient_display">Ambient Display</string><string name="ambient_display_description">Configure ambient display mode</string><string name="power_save_mode">Power Save Mode</string><string name="power_save_mode_description">Control system power save mode</string><string name="system_settings">System Settings</string><string name="system_settings_description">Modify key-value pairs in system settings table</string><string name="digital_assistant">Set Digital Assistant</string><string name="digital_assistant_description">Set default digital assistant app</string><string name="default_keyboard">Keyboard - Set Default</string><string name="default_keyboard_description">Set default input method keyboard</string><string name="keyboard_hint">Keyboard Hint</string><string name="keyboard_hint_description">Control keyboard hint functionality</string><string name="driving_mode">Driving Mode</string><string name="driving_mode_description">Control driving mode functionality</string><string name="app_management">App Management</string><string name="app_management_description">Launch, stop, install, uninstall apps</string><string name="application_task">Application Task</string><string name="application_task_description">Launch apps, force stop</string><string name="share_text">Share Text</string><string name="share_text_description">Share text content through system share function</string><string name="enable">Enable</string><string name="disable">Disable</string><string name="toggle">Toggle</string><string name="confirm_configuration">Confirm Configuration</string><string name="package_name">Package Name</string><string name="search_quick_commands">Search quick command names</string><string name="no_quick_commands">No quick commands</string><string name="no_search_results">No matching quick commands found</string><string name="delete_quick_command">Delete Quick Command</string><string name="delete_quick_command_confirm">Are you sure you want to delete quick command \"%1$s\"? This action will:</string><string name="delete_quick_command_warning">• Remove this quick command from the app</string><string name="delete_operation_irreversible">This action cannot be undone.</string><string name="delete">Delete</string><string name="batch_delete_quick_commands">Batch Delete Quick Commands</string><string name="batch_delete_confirm">Are you sure you want to delete the selected %1$d quick commands? This action will:</string><string name="batch_delete_warning">• Remove these quick commands from the app</string><string name="shortcut_not_configured">This shortcut is not configured yet, please create a quick command in the app and select to use this static shortcut</string><string name="executing_quick_command">Executing quick command: %1$s (%2$d tasks)</string><string name="quick_command_completed">Quick command execution completed: %1$s</string><string name="quick_command_aborted">Quick command execution aborted: %1$s, abort conditions: %2$s</string><string name="quick_command_not_found">Cannot find associated quick command</string><string name="invalid_command">Invalid command</string><string name="quick_command_not_found_simple">Quick command not found</string><string name="template_screen_off_network_title">Screen Off Network</string><string name="template_screen_on_network_title">Screen On Network</string><string name="template_auto_brightness_title">Auto Brightness</string><string name="template_battery_saver_title">Battery Saver</string><string name="template_do_not_disturb_title">Do Not Disturb</string><string name="template_anti_embarrassment_mode_title">Anti-Embarrassment Mode</string><string name="template_screen_off_network_desc">Turn off WiFi and mobile data after screen off with delay to save battery</string><string name="template_screen_on_network_desc">Restore previously saved network state after screen on with delay</string><string name="template_auto_brightness_desc">Automatically adjust screen brightness based on ambient light</string><string name="template_battery_saver_desc">Automatically enable battery saver when battery level is below threshold</string><string name="template_do_not_disturb_desc">Automatically enable do not disturb mode during specified time periods</string><string name="template_anti_embarrassment_mode_desc">Avoid embarrassment from loud volume when opening short video apps</string><string name="template_category_network">Network Management</string><string name="template_category_power">Power Management</string><string name="template_category_display">Display Control</string><string name="template_category_system">System Operations</string><string name="template_category_automation">Automation</string><string name="search_templates">Search template names or descriptions</string><string name="no_templates">No templates available</string><string name="no_template_search_results">No matching templates found</string><string name="tasks_count">%1$d tasks</string><string name="shizuku_category_app_management">App Management Commands</string><string name="shizuku_category_network">Network Related Commands</string><string name="shizuku_category_system">System Control Commands</string><string name="shizuku_category_key_simulation">Key Simulation Commands</string><string name="shizuku_category_screen_operation">Screen Operation Commands</string><string name="switch_operation_on">On</string><string name="switch_operation_off">Off</string><string name="switch_operation_toggle">Toggle</string><string name="invert_colors_on">On</string><string name="invert_colors_off">Off</string><string name="auto_rotate_enable">Enable</string><string name="auto_rotate_disable">Disable</string><string name="accessibility_service_enable">Enable</string><string name="accessibility_service_disable">Disable</string><string name="check_text_content">Text Content to Check</string><string name="text_content_label">Text Content</string><string name="text_content_placeholder">e.g.: Login Successful, Loading Complete, etc.</string><string name="match_options">Match Options</string><string name="sun_event_sunrise">Sunrise</string><string name="sun_event_sunset">Sunset</string><string name="screen_event_unlocked">Screen Unlocked</string><string name="screen_event_on">Screen On</string><string name="screen_event_off">Screen Off</string><string name="screen_event_auto_rotate_enabled">Auto Rotate Enabled</string><string name="screen_event_auto_rotate_disabled">Auto Rotate Disabled</string><string name="app_detection_any_app">Any App</string><string name="app_detection_selected_apps">Selected Apps</string><string name="app_trigger_any_app">Any App</string><string name="app_trigger_all_apps">All Apps</string><string name="communication_state">Communication State</string><string name="communication_state_description">Call and SMS state changes</string><string name="connection_state">Connection State</string><string name="connection_state_description">Network, Bluetooth, USB connection states</string><string name="sensor_state">Sensor State</string><string name="sensor_state_description">Light, orientation, vibration sensors</string><string name="app_state">App State</string><string name="app_state_description">App foreground, background, installation</string><string name="device_event">Device Event</string><string name="device_event_description">Battery, screen, system events</string><string name="time_based">Time Condition</string><string name="time_based_description">Scheduled, periodic, delayed triggers</string><string name="manual_trigger">Manual Trigger</string><string name="manual_trigger_description">Shortcuts, widget triggers</string><string name="battery_state">Battery State</string><string name="battery_state_description">Battery level, charging state changes</string><string name="state_change">State Change</string><string name="state_change_description">App foreground, background state changes, background time exceeds threshold</string><string name="lifecycle">Lifecycle</string><string name="lifecycle_description">App launch, close</string><string name="package_management">Package Management</string><string name="package_management_description">App installation, removal, update</string><string name="interface_click">Interface Click</string><string name="interface_click_description">Triggered when specific text content is clicked</string><string name="gps_state">GPS State</string><string name="gps_state_description">Triggered when GPS state changes</string><string name="logcat_message">Logcat Message</string><string name="logcat_message_description">Triggered when specified message appears in Logcat</string><string name="clipboard_changed">Clipboard Changed</string><string name="clipboard_changed_description">Triggered when clipboard content changes</string><string name="screen_state">Screen State</string><string name="screen_state_description">Screen on, off, unlock state changes</string><string name="dock_state">Dock State</string><string name="dock_state_description">Device dock connection state changes</string><string name="intent_received">Intent Received</string><string name="intent_received_description">Triggered when specified Intent is received</string><string name="sim_card_state">SIM Card State</string><string name="sim_card_state_description">Triggered when SIM card state changes</string><string name="dark_theme_changed">Dark Theme</string><string name="dark_theme_changed_description">System dark theme state changes</string><string name="login_attempt_failed">Login Failed</string><string name="login_attempt_failed_description">Triggered when device login attempt fails</string><string name="system_setting_changed">System Setting</string><string name="system_setting_changed_description">Triggered when system setting changes</string><string name="auto_sync_state">Auto Sync</string><string name="auto_sync_state_description">Triggered when auto sync state changes</string><string name="device_boot_completed">Device Boot</string><string name="device_boot_completed_description">Triggered when device boot completes</string><string name="notification_event">Notification Event</string><string name="notification_event_description">Triggered when notification is posted or cancelled</string><string name="ringer_mode_changed">Ringer Mode</string><string name="ringer_mode_changed_description">Triggered when ringer mode changes</string><string name="music_playback_state">Music Playback</string><string name="music_playback_state_description">Triggered when music playback state changes</string><string name="airplane_mode_changed">Airplane Mode</string><string name="airplane_mode_changed_description">Triggered when airplane mode state changes</string><string name="volume_changed">Volume Changed</string><string name="volume_changed_description">Triggered when system volume changes</string><string name="phone_task">Phone Task</string><string name="phone_task_description">Make calls, answer calls, etc.</string><string name="media_task">Media Task</string><string name="media_task_description">Media control, sound playback</string><string name="location_task">Location Task</string><string name="location_task_description">GPS, location service control</string><string name="file_operation_task">File Read/Write</string><string name="file_operation_task_description">File read/write, open files</string><string name="camera_task">Photo Viewer</string><string name="camera_task_description">View photos, screenshot</string><string name="information_task">Information Task</string><string name="information_task_description">SMS, email, notifications</string><string name="connectivity_task">Connectivity Task</string><string name="connectivity_task_description">WiFi, Bluetooth, network control</string><string name="screen_control_task">Screen Control</string><string name="screen_control_task_description">Screen brightness, orientation, lock</string><string name="volume_task">Volume Task</string><string name="volume_task_description">Volume adjustment, mute control</string><string name="device_action_task">Device Action</string><string name="device_action_task_description">Share, clipboard, system operations</string><string name="notification_task">Notification Task</string><string name="notification_task_description">Send, cancel notifications</string><string name="datetime_task">Date Time</string><string name="datetime_task_description">Time settings, alarms, calendar</string><string name="phone_open_call_log">Open Call Log</string><string name="phone_open_call_log_description">Open system call log page</string><string name="phone_reject_call">Reject Call</string><string name="phone_reject_call_description">Reject incoming call</string><string name="phone_make_call">Make Call</string><string name="phone_make_call_description">Dial specified phone number</string><string name="phone_answer_call">Answer Call</string><string name="phone_answer_call_description">Answer incoming call</string><string name="phone_clear_call_log">Clear Call Log</string><string name="phone_clear_call_log_description">Clear specified type of call log</string><string name="phone_ringtone_settings">Phone Ringtone Settings</string><string name="phone_ringtone_settings_description">Set phone ringtone, select custom ringtone and apply to system</string><string name="info_send_sms">Send SMS</string><string name="info_send_sms_description">Send SMS to specified number, supports SIM card selection and pre-fill mode</string><string name="info_send_email">Send Email</string><string name="info_send_email_description">Send email to specified address, supports complete SMTP configuration and notification options</string><string name="info_message_ringtone">Message Ringtone Settings</string><string name="info_message_ringtone_description">Set ringtone for SMS and notifications</string><string name="location_share_location">Share Location</string><string name="location_share_location_description">Share current location via SMS, contact or phone number</string><string name="location_toggle_location_service">Location Service Control</string><string name="location_toggle_location_service_description">Enable, disable or toggle location service status</string><string name="location_force_location_update">Force Location Update</string><string name="location_force_location_update_description">Force update current location information</string><string name="location_set_location_update_frequency">Set Location Update Frequency</string><string name="location_set_location_update_frequency_description">Set the frequency of location information updates</string><string name="camera_open_last_photo">Open Last Photo</string><string name="camera_open_last_photo_description">Quick access to the last photo in gallery</string><string name="camera_take_photo">Take Photo</string><string name="camera_take_photo_description">Take photo using front or rear camera, supports custom save location</string><string name="connectivity_wifi_control">WiFi Control</string><string name="connectivity_wifi_control_description">Control WiFi enable, disable or toggle</string><string name="connectivity_mobile_data_control">Mobile Data Control</string><string name="connectivity_mobile_data_control_description">Control mobile data enable, disable or toggle</string><string name="media_multimedia_control">Multimedia Control</string><string name="media_multimedia_control_description">Simulate media buttons, default player control, audio buttons</string><string name="media_play_stop_sound">Play/Stop Sound</string><string name="media_play_stop_sound_description">Play audio file, ringtone or stop existing sound</string><string name="device_settings_invert_colors">Invert Colors</string><string name="device_settings_invert_colors_description">Toggle screen color inversion mode</string><string name="device_settings_font_size">Font Size</string><string name="device_settings_font_size_description">Adjust system font size percentage</string><string name="time_condition_stopwatch">Stopwatch</string><string name="time_condition_stopwatch_description">Set countdown time, trigger when time reaches</string><string name="time_condition_sun_event">Sun Event</string><string name="time_condition_sun_event_description">Trigger at sunrise or sunset time</string><string name="datetime_stopwatch">Stopwatch Operation</string><string name="datetime_stopwatch_description">Start, stop, reset stopwatch</string><string name="datetime_alarm">Alarm Operation</string><string name="datetime_alarm_description">Set, cancel alarm, supports custom ringtone</string><string name="notification_show_notification">Show Notification</string><string name="notification_show_notification_description">Display custom notification in notification bar</string><string name="notification_cancel_notification">Cancel Notification</string><string name="notification_cancel_notification_description">Cancel specified notification</string><string name="device_action_share_text">Share Text</string><string name="device_action_share_text_description">Share text content through system share function</string><string name="task_app_management">App Management</string><string name="task_app_management_description">Launch, stop, install, uninstall apps</string><string name="task_device_settings">Device Settings</string><string name="task_device_settings_description">System settings, theme, wallpaper, color inversion and other device-level configurations</string><string name="task_log">Log Task</string><string name="task_log_description">Record log information</string><string name="task_phone">Phone Task</string><string name="task_phone_description">Make calls, answer calls, call log management</string><string name="task_screen_control">Screen Control</string><string name="task_screen_control_description">Brightness control, screen on/off, rotation control, etc.</string><string name="task_log_task">Log Task</string><string name="task_log_task_description">Record log information</string><string name="task_phone_task">Phone Task</string><string name="task_phone_task_description">Make calls, answer calls, call log management</string><string name="task_screen_control_task">Screen Control</string><string name="task_screen_control_task_description">Brightness control, screen on/off, rotation control, etc.</string><string name="task_media_task">Media Task</string><string name="task_media_task_description">Play audio, record audio, take photos, record videos</string><string name="task_alarm_reminder">Alarm Reminder</string><string name="task_alarm_reminder_description">Set alarms and reminders</string><string name="task_file_operation">File Operation</string><string name="task_file_operation_description">Create, delete, move, copy files</string><string name="task_network">Network Task</string><string name="task_network_description">HTTP requests, network detection</string><string name="task_location">Location Task</string><string name="task_location_description">Get location information, geofencing</string><string name="configure_item">Configure %1$s</string><string name="back">Back</string><string name="media_microphone_recording">Microphone Recording</string><string name="media_microphone_recording_description">Record audio with custom duration and format support</string><string name="device_settings_enter_screensaver">Enter Screensaver</string><string name="device_settings_enter_screensaver_description">Activate system screensaver mode</string><string name="device_settings_auto_rotate">Auto Rotate</string><string name="device_settings_auto_rotate_description">Control screen auto rotation function</string><string name="device_settings_accessibility_service">Accessibility Service</string><string name="device_settings_accessibility_service_description">Control the enable status of specified accessibility service</string><string name="device_settings_display_density">Display Density</string><string name="device_settings_display_density_description">Adjust screen display density percentage</string><string name="device_settings_immersive_mode">Immersive Mode</string><string name="device_settings_immersive_mode_description">Set system immersive mode type</string><string name="device_settings_keyboard_hint">Keyboard Hint</string><string name="device_settings_keyboard_hint_description">Control keyboard hint function</string><string name="device_settings_driving_mode">Driving Mode</string><string name="device_settings_driving_mode_description">Control driving mode function</string><string name="camera_record_video">Record Video</string><string name="camera_record_video_description">Record video using front or rear camera, supports timed recording and custom save location</string><string name="camera_flashlight_control">Flashlight Control</string><string name="camera_flashlight_control_description">Control flashlight enable, disable or toggle</string><string name="camera_screenshot">Screenshot</string><string name="camera_screenshot_description">Capture current screen content and save</string><string name="connectivity_bluetooth_control">Bluetooth Control</string><string name="connectivity_bluetooth_control_description">Control Bluetooth enable, disable or toggle</string><string name="connectivity_hotspot_control">Hotspot Control</string><string name="connectivity_hotspot_control_description">Control WiFi hotspot enable, disable or toggle</string><string name="connectivity_airplane_mode_control">Airplane Mode Control</string><string name="connectivity_airplane_mode_control_description">Control airplane mode enable, disable or toggle</string><string name="connectivity_nfc_control">NFC Control</string><string name="connectivity_nfc_control_description">Control NFC enable, disable or toggle</string><string name="connectivity_send_intent">Send Intent</string><string name="connectivity_send_intent_description">Send custom Intent to specified app or system</string><string name="connectivity_network_check">Network Connection Check</string><string name="connectivity_network_check_description">Check network connection status and return result</string><string name="location_get_location">Get Location</string><string name="location_get_location_description">Get current location information and save to variable</string><string name="info_show_toast">Show Toast</string><string name="info_show_toast_description">Display brief message on screen</string><string name="info_show_dialog">Show Dialog</string><string name="info_show_dialog_description">Display custom dialog with title, content and button configuration support</string><string name="app_launch_app">Launch App</string><string name="app_launch_app_description">Launch specified application</string><string name="app_force_stop_app">Force Stop App</string><string name="app_force_stop_app_description">Force stop specified application</string><string name="app_tasker_locale_plugin">Tasker/Locale Plugin</string><string name="app_tasker_locale_plugin_description">Execute Tasker or Locale task plugin</string><string name="app_freeze_app">Freeze App</string><string name="app_freeze_app_description">Freeze specified application</string><string name="app_unfreeze_app">Unfreeze App</string><string name="app_unfreeze_app_description">Unfreeze specified application</string><string name="app_open_website">Open Website</string><string name="app_open_website_description">Open specified website in default browser</string><string name="app_execute_javascript">Execute JavaScript Code</string><string name="app_execute_javascript_description">Execute custom JavaScript code</string><string name="app_execute_shell_script">Execute Shell Script</string><string name="app_execute_shell_script_description">Execute custom Shell script</string><string name="volume_volume_change">Volume Change</string><string name="volume_volume_change_description">Set volume level for specified audio stream</string><string name="volume_volume_adjust">Volume Adjust</string><string name="volume_volume_adjust_description">Increase or decrease volume for specified audio stream</string><string name="volume_speakerphone_control">Speakerphone Control</string><string name="volume_speakerphone_control_description">Control speakerphone enable, disable or toggle</string><string name="volume_vibration_mode">Vibration Mode</string><string name="volume_vibration_mode_description">Set device vibration mode</string><string name="volume_volume_popup">Volume Popup</string><string name="volume_volume_popup_description">Show system volume adjustment popup</string><string name="volume_do_not_disturb">Do Not Disturb</string><string name="volume_do_not_disturb_description">Set do not disturb mode status</string><string name="screen_brightness_control">Brightness Control</string><string name="screen_brightness_control_description">Adjust screen brightness, supports percentage and absolute value settings</string><string name="screen_keep_device_awake">Keep Device Awake</string><string name="screen_keep_device_awake_description">Prevent device from entering sleep state</string><string name="file_write_file">Write File</string><string name="file_write_file_description">Write content to specified file, supports append, overwrite, prepare commit modes</string><string name="file_open_file">Open File</string><string name="file_open_file_description">Open specified file, supports manual input or file selection, can specify app</string><string name="file_file_operation">File Management</string><string name="file_file_operation_description">File management operations: copy, move, delete, compress, create folder, rename</string><string name="comm_sms_received">SMS Received</string><string name="comm_sms_received_description">Trigger condition when SMS is received</string><string name="comm_sms_sent">SMS Sent</string><string name="comm_sms_sent_description">Trigger condition when SMS is sent</string><string name="comm_incoming_call">Incoming Call</string><string name="comm_incoming_call_description">Trigger condition when incoming call</string><string name="comm_outgoing_call">Outgoing Call</string><string name="comm_outgoing_call_description">Trigger condition when making call</string><string name="comm_call_active">Call Active</string><string name="comm_call_active_description">Trigger condition when call is active</string><string name="comm_call_ended">Call Ended</string><string name="comm_call_ended_description">Trigger condition when call ends</string><string name="conn_wifi_state">WiFi State</string><string name="conn_wifi_state_description">Trigger condition when WiFi is enabled/disabled or connected/disconnected</string><string name="conn_wifi_network">WiFi Network</string><string name="conn_wifi_network_description">Trigger condition when connected to specific WiFi network</string><string name="sensor_light_sensor">Light Sensor</string><string name="sensor_light_sensor_description">Trigger condition when ambient light changes</string><string name="sensor_orientation_sensor">Orientation Sensor</string><string name="sensor_orientation_sensor_description">Trigger condition when screen orientation changes</string><string name="app_state_change">State Change</string><string name="app_state_change_description">App foreground/background state changes, background time exceeds threshold</string><string name="app_lifecycle">Lifecycle</string><string name="app_lifecycle_description">App launch, close</string><string name="app_package_management">Package Management</string><string name="app_package_management_description">App install, delete, update</string><string name="app_interface_click">Interface Click</string><string name="app_interface_click_description">Trigger condition when specific text content is clicked</string><string name="app_screen_content">Screen Content</string><string name="app_screen_content_description">Trigger condition when certain text content appears or disappears on screen</string><string name="device_gps_state">GPS State</string><string name="device_gps_state_description">Trigger condition when GPS state changes</string><string name="device_logcat_message">Logcat Message</string><string name="device_logcat_message_description">Trigger condition when specified message appears in Logcat</string><string name="device_clipboard_changed">Clipboard Changed</string><string name="device_clipboard_changed_description">Trigger condition when clipboard content changes</string><string name="battery_battery_level">Battery Level</string><string name="battery_battery_level_description">Monitor battery level changes and thresholds</string><string name="battery_charging_state">Charging State</string><string name="battery_charging_state_description">Monitor device charging state changes</string><string name="manual_dynamic_shortcut">Dynamic Shortcut</string><string name="manual_dynamic_shortcut_description">Trigger via dynamic shortcut</string><string name="manual_static_shortcut">Static Shortcut</string><string name="manual_static_shortcut_description">Trigger via static shortcut</string><string name="manual_fingerprint_gesture">Fingerprint Gesture</string><string name="manual_fingerprint_gesture_description">Trigger via fingerprint gesture</string><string name="manual_media_key_press">Media Key Press</string><string name="manual_media_key_press_description">Triggered when media keys (on headphones) are pressed 1 to 3 times</string><string name="manual_volume_key_press">Volume Key Press</string><string name="manual_volume_key_press_description">Trigger via volume key press, with option to preserve original volume</string><string name="time_condition_scheduled_time">Scheduled Time</string><string name="time_condition_scheduled_time_description">Trigger at specified date and time</string><string name="time_condition_time_period">Time Period</string><string name="time_condition_time_period_description">Trigger within specified time period</string><string name="time_condition_periodic_time">Periodic Time</string><string name="time_condition_periodic_time_description">Repeat trigger at specified time each week</string><string name="time_condition_delayed_trigger">Delayed Trigger</string><string name="time_condition_delayed_trigger_description">Trigger once after specified delay</string><string name="conn_mobile_data">Mobile Data</string><string name="conn_mobile_data_description">Trigger condition when mobile data connection state changes</string><string name="conn_bluetooth_state">Bluetooth State</string><string name="conn_bluetooth_state_description">Trigger condition when Bluetooth is enabled/disabled or connected/disconnected</string><string name="sensor_shake_sensor">Shake Detection</string><string name="sensor_shake_sensor_description">Trigger condition when device is shaken</string><string name="sensor_sleep_sensor">Sleep Detection</string><string name="sensor_sleep_sensor_description">Trigger condition when sleep state change is detected</string><string name="sensor_flip_sensor">Device Flip Detection</string><string name="sensor_flip_sensor_description">Trigger condition when device is flipped</string><string name="sensor_proximity_sensor">Proximity Sensor</string><string name="sensor_proximity_sensor_description">Trigger condition when object approaches or moves away</string><string name="sensor_activity_recognition">Activity Recognition</string><string name="sensor_activity_recognition_description">Trigger condition when specific activity state is detected</string><string name="smart_reminders_title">Smart Reminders</string><string name="smart_reminders_description">Intelligent detection and useful reminders</string><string name="search_smart_reminders">Search smart reminder features</string><string name="screen_rotation_reminder_title">Screen Rotation Reminder</string><string name="screen_rotation_reminder_description">Suggest screen rotation after flipping phone</string><string name="screen_rotation_reminder_status_unconfigured">Feature ready to use</string><string name="screen_rotation_reminder_status_enabled">Monitoring screen rotation</string><string name="screen_rotation_reminder_status_disabled">Disabled</string><string name="music_app_reminder_title">Music App Reminder</string><string name="music_app_reminder_description">Suggest opening music app when headphones connected</string><string name="music_app_reminder_status_unconfigured">Need to select music app</string><string name="music_app_reminder_status_configured">Selected: %s</string><string name="music_app_reminder_status_enabled">Suggest opening when headphones connected: %s</string><string name="music_app_reminder_status_disabled">Disabled</string><string name="shopping_app_reminder_title">Shopping App Reminder</string><string name="shopping_app_reminder_description">Suggest opening shopping app when copying product links</string><string name="shopping_app_reminder_status_unconfigured">Feature ready to use</string><string name="shopping_app_reminder_status_enabled">Monitoring clipboard</string><string name="shopping_app_reminder_status_disabled">Disabled</string><string name="app_link_reminder_title">Open App Links</string><string name="app_link_reminder_description">Intelligently detect app links and suggest opening the corresponding application</string><string name="app_link_reminder_status_unconfigured">Feature ready to use</string><string name="app_link_reminder_status_enabled">Monitoring clipboard</string><string name="app_link_reminder_status_disabled">Disabled</string><string name="custom_app_platform_config">Custom App Platform Config</string><string name="intelligent_link_recognition_title">Intelligent Link Recognition</string><string name="intelligent_link_recognition_description">Automatically optimize link format to improve recognition accuracy and compatibility</string><string name="intelligent_link_recognition_enabled">Enable Intelligent Recognition</string><string name="intelligent_link_recognition_help">Automatically standardize link format and clean non-standard characters</string><string name="share_url_reminder_title">Share URL</string><string name="share_url_reminder_description">Suggest sharing URL when copying web links</string><string name="share_url_reminder_status_unconfigured">Feature ready to use</string><string name="share_url_reminder_status_enabled">Monitoring clipboard</string><string name="share_url_reminder_status_disabled">Disabled</string><string name="smart_reminder_configure">Configure</string><string name="smart_reminder_detail_settings">Detail Settings</string><string name="smart_reminder_select_app">Select App</string><string name="smart_reminder_change_app">Change App</string><string name="smart_reminder_status_label">Status: </string><string name="smart_reminder_needs_configuration">Configuration required</string><string name="smart_reminder_detail_config">Smart Reminder Detail Config</string><string name="smart_reminder_config_error">Configuration Error</string><string name="smart_reminder_type_not_found">Smart reminder type not found</string><string name="smart_reminder_config_not_found">Configuration item not found</string><string name="smart_reminder_tap_to_setup">Tap to setup</string><string name="smart_reminder_configured">Configured</string><string name="smart_reminder_ready_to_use">Ready to use</string><string name="smart_reminder_selected_apps_count">%d apps selected</string><string name="select_music_apps">Select Music Apps</string><string name="change_music_apps">Change Music Apps</string><string name="tap_to_select_music_apps">Tap to select music apps to monitor</string><string name="select_map_apps">Select Map Apps</string><string name="change_map_apps">Change Map Apps</string><string name="tap_to_select_map_apps">Tap to select map apps to monitor</string><string name="address_reminder_title">Address Reminder</string><string name="address_reminder_description">Suggest opening map app when copying address</string><string name="address_reminder_status_enabled">Monitoring address copying - %1$s</string><string name="address_reminder_status_disabled">Disabled</string><string name="address_reminder_message">Address detected, suggest opening \"%1$s\"</string><string name="phone_checkup_title">Checkup</string></file><file name="accessibility_service_config" path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res\xml\accessibility_service_config.xml" qualifiers="" type="xml"/><file name="auto_clicker_accessibility_service_config" path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res\xml\auto_clicker_accessibility_service_config.xml" qualifiers="" type="xml"/><file name="backup_rules" path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="device_admin" path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res\xml\device_admin.xml" qualifiers="" type="xml"/><file name="file_provider_paths" path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res\xml\file_provider_paths.xml" qualifiers="" type="xml"/><file name="gesture_recognition_accessibility_service_config" path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res\xml\gesture_recognition_accessibility_service_config.xml" qualifiers="" type="xml"/><file name="interface_interaction_accessibility_service_config" path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res\xml\interface_interaction_accessibility_service_config.xml" qualifiers="" type="xml"/><file name="one_click_command_widget_1_info" path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res\xml\one_click_command_widget_1_info.xml" qualifiers="" type="xml"/><file name="one_click_command_widget_2_info" path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res\xml\one_click_command_widget_2_info.xml" qualifiers="" type="xml"/><file name="one_click_command_widget_3_info" path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res\xml\one_click_command_widget_3_info.xml" qualifiers="" type="xml"/><file name="one_click_command_widget_4_info" path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res\xml\one_click_command_widget_4_info.xml" qualifiers="" type="xml"/><file name="shortcuts" path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res\xml\shortcuts.xml" qualifiers="" type="xml"/><file name="system_operation_accessibility_service_config" path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res\xml\system_operation_accessibility_service_config.xml" qualifiers="" type="xml"/><file name="ic_sky_blue_arrow_right" path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res\drawable\ic_sky_blue_arrow_right.xml" qualifiers="" type="drawable"/><file name="ic_sky_blue_more_vert" path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res\drawable\ic_sky_blue_more_vert.xml" qualifiers="" type="drawable"/><file name="overlay_debug_log" path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\res\layout\overlay_debug_log.xml" qualifiers="" type="layout"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>