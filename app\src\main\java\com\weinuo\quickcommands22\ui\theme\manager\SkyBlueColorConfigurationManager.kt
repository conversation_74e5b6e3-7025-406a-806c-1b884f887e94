package com.weinuo.quickcommands22.ui.theme.manager

import android.content.Context
import android.util.Log
import androidx.compose.runtime.*
import androidx.compose.ui.graphics.Color
import com.weinuo.quickcommands22.data.SettingsRepository

/**
 * 天空蓝主题颜色配置管理器
 *
 * 负责管理天空蓝主题的所有颜色配置，包括：
 * - 从全局设置中获取用户自定义颜色配置
 * - 颜色配置的实时更新
 * - 颜色配置的状态管理
 * - 颜色配置的验证和同步
 * - 颜色字符串与Color对象的转换
 *
 * 设计原则：
 * - 所有颜色配置都通过GlobalSettings进行管理，确保与其他设置项保持一致的存储和同步机制
 * - 支持颜色代码的直接输入，用户可以输入十六进制颜色代码
 * - 配置变更立即生效，无需重启应用
 */
class SkyBlueColorConfigurationManager private constructor(
    private val context: Context,
    private val settingsRepository: SettingsRepository
) {

    companion object {
        @Volatile
        private var INSTANCE: SkyBlueColorConfigurationManager? = null

        /**
         * 获取天空蓝主题颜色配置管理器实例
         */
        fun getInstance(context: Context, settingsRepository: SettingsRepository): SkyBlueColorConfigurationManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: SkyBlueColorConfigurationManager(context.applicationContext, settingsRepository).also { INSTANCE = it }
            }
        }

        /**
         * 获取天空蓝主题颜色配置管理器实例（兼容旧版本）
         */
        fun getInstance(context: Context): SkyBlueColorConfigurationManager {
            return getInstance(context, SettingsRepository(context))
        }

        /**
         * 将颜色字符串转换为Color对象
         * 支持格式：0xFF123456、#123456、123456
         */
        fun parseColorString(colorString: String): Color {
            return try {
                val cleanString = colorString.trim()
                when {
                    cleanString.startsWith("0x") || cleanString.startsWith("0X") -> {
                        val hex = cleanString.substring(2)
                        Color(hex.toLong(16))
                    }
                    cleanString.startsWith("#") -> {
                        val hex = cleanString.substring(1)
                        Color(hex.toLong(16) or 0xFF000000)
                    }
                    else -> {
                        Color(cleanString.toLong(16) or 0xFF000000)
                    }
                }
            } catch (e: Exception) {
                // 解析失败时返回默认颜色（品牌色）
                Color(0xFF0A59F7)
            }
        }

        /**
         * 将Color对象转换为颜色字符串
         * 返回格式：0xFF123456
         */
        fun colorToString(color: Color): String {
            val argb = (color.alpha * 255).toInt() shl 24 or
                    ((color.red * 255).toInt() shl 16) or
                    ((color.green * 255).toInt() shl 8) or
                    (color.blue * 255).toInt()
            return "0x${argb.toUInt().toString(16).uppercase().padStart(8, '0')}"
        }

        /**
         * 验证颜色字符串格式是否正确
         */
        fun isValidColorString(colorString: String): Boolean {
            return try {
                parseColorString(colorString)
                true
            } catch (e: Exception) {
                false
            }
        }
    }

    /**
     * 获取当前天空蓝主题颜色配置
     *
     * 从GlobalSettings中读取用户自定义的颜色配置
     */
    @Composable
    fun getSkyBlueColorConfiguration(): SkyBlueColorConfiguration {
        val globalSettings by settingsRepository.globalSettings.collectAsState()

        // 添加调试日志
        LaunchedEffect(globalSettings.skyBluePrimary) {
            Log.d("SkyBlueColorConfig", "=== 颜色配置读取 ===")
            Log.d("SkyBlueColorConfig", "Primary: ${globalSettings.skyBluePrimary}")
            Log.d("SkyBlueColorConfig", "Background: ${globalSettings.skyBlueBackground}")
            Log.d("SkyBlueColorConfig", "Surface: ${globalSettings.skyBlueSurface}")
            Log.d("SkyBlueColorConfig", "==================")
        }

        return SkyBlueColorConfiguration(
            // 主要颜色系统
            primary = parseColorString(globalSettings.skyBluePrimary),
            onPrimary = parseColorString(globalSettings.skyBlueOnPrimary),
            primaryContainer = parseColorString(globalSettings.skyBluePrimaryContainer),
            onPrimaryContainer = parseColorString(globalSettings.skyBlueOnPrimaryContainer),

            // 次要颜色系统
            secondary = parseColorString(globalSettings.skyBlueSecondary),
            onSecondary = parseColorString(globalSettings.skyBlueOnSecondary),
            secondaryContainer = parseColorString(globalSettings.skyBlueSecondaryContainer),
            onSecondaryContainer = parseColorString(globalSettings.skyBlueOnSecondaryContainer),

            // 第三颜色系统
            tertiary = parseColorString(globalSettings.skyBlueTertiary),
            onTertiary = parseColorString(globalSettings.skyBlueOnTertiary),
            tertiaryContainer = parseColorString(globalSettings.skyBlueTertiaryContainer),
            onTertiaryContainer = parseColorString(globalSettings.skyBlueOnTertiaryContainer),

            // 错误颜色系统
            error = parseColorString(globalSettings.skyBlueError),
            onError = parseColorString(globalSettings.skyBlueOnError),
            errorContainer = parseColorString(globalSettings.skyBlueErrorContainer),
            onErrorContainer = parseColorString(globalSettings.skyBlueOnErrorContainer),

            // 表面颜色系统
            background = parseColorString(globalSettings.skyBlueBackground),
            onBackground = parseColorString(globalSettings.skyBlueOnBackground),
            surface = parseColorString(globalSettings.skyBlueSurface),
            onSurface = parseColorString(globalSettings.skyBlueOnSurface),
            surfaceVariant = parseColorString(globalSettings.skyBlueSurfaceVariant),
            onSurfaceVariant = parseColorString(globalSettings.skyBlueOnSurfaceVariant),

            // 扩展颜色
            confirm = parseColorString(globalSettings.skyBlueConfirm),
            fontEmphasize = parseColorString(globalSettings.skyBlueFontEmphasize),
            iconEmphasize = parseColorString(globalSettings.skyBlueIconEmphasize),
            iconSubEmphasize = parseColorString(globalSettings.skyBlueIconSubEmphasize),
            backgroundEmphasize = parseColorString(globalSettings.skyBlueBackgroundEmphasize),
            backgroundFocus = parseColorString(globalSettings.skyBlueBackgroundFocus),

            // 底部导航栏颜色
            bottomNavBackground = parseColorString(globalSettings.skyBlueBottomNavBackground),
            bottomNavSelectedIcon = parseColorString(globalSettings.skyBlueBottomNavSelectedIcon),
            bottomNavUnselectedIcon = parseColorString(globalSettings.skyBlueBottomNavUnselectedIcon),

            // 标题栏颜色
            topBarBackground = parseColorString(globalSettings.skyBlueTopBarBackground)
        )
    }

    // 主要颜色系统更新方法
    fun updatePrimary(colorString: String) {
        if (isValidColorString(colorString)) {
            Log.d("SkyBlueColorConfig", "更新主色: $colorString")
            val currentSettings = settingsRepository.globalSettings.value
            val newSettings = currentSettings.copy(skyBluePrimary = colorString)
            settingsRepository.saveGlobalSettings(newSettings)
            Log.d("SkyBlueColorConfig", "主色更新完成")
        } else {
            Log.w("SkyBlueColorConfig", "无效的颜色字符串: $colorString")
        }
    }

    fun updateOnPrimary(colorString: String) {
        if (isValidColorString(colorString)) {
            val currentSettings = settingsRepository.globalSettings.value
            val newSettings = currentSettings.copy(skyBlueOnPrimary = colorString)
            settingsRepository.saveGlobalSettings(newSettings)
        }
    }

    fun updatePrimaryContainer(colorString: String) {
        if (isValidColorString(colorString)) {
            val currentSettings = settingsRepository.globalSettings.value
            val newSettings = currentSettings.copy(skyBluePrimaryContainer = colorString)
            settingsRepository.saveGlobalSettings(newSettings)
        }
    }

    fun updateOnPrimaryContainer(colorString: String) {
        if (isValidColorString(colorString)) {
            val currentSettings = settingsRepository.globalSettings.value
            val newSettings = currentSettings.copy(skyBlueOnPrimaryContainer = colorString)
            settingsRepository.saveGlobalSettings(newSettings)
        }
    }

    // 次要颜色系统更新方法
    fun updateSecondary(colorString: String) {
        if (isValidColorString(colorString)) {
            val currentSettings = settingsRepository.globalSettings.value
            val newSettings = currentSettings.copy(skyBlueSecondary = colorString)
            settingsRepository.saveGlobalSettings(newSettings)
        }
    }

    fun updateOnSecondary(colorString: String) {
        if (isValidColorString(colorString)) {
            val currentSettings = settingsRepository.globalSettings.value
            val newSettings = currentSettings.copy(skyBlueOnSecondary = colorString)
            settingsRepository.saveGlobalSettings(newSettings)
        }
    }

    fun updateSecondaryContainer(colorString: String) {
        if (isValidColorString(colorString)) {
            val currentSettings = settingsRepository.globalSettings.value
            val newSettings = currentSettings.copy(skyBlueSecondaryContainer = colorString)
            settingsRepository.saveGlobalSettings(newSettings)
        }
    }

    fun updateOnSecondaryContainer(colorString: String) {
        if (isValidColorString(colorString)) {
            val currentSettings = settingsRepository.globalSettings.value
            val newSettings = currentSettings.copy(skyBlueOnSecondaryContainer = colorString)
            settingsRepository.saveGlobalSettings(newSettings)
        }
    }

    // 第三颜色系统更新方法
    fun updateTertiary(colorString: String) {
        if (isValidColorString(colorString)) {
            val currentSettings = settingsRepository.globalSettings.value
            val newSettings = currentSettings.copy(skyBlueTertiary = colorString)
            settingsRepository.saveGlobalSettings(newSettings)
        }
    }

    fun updateOnTertiary(colorString: String) {
        if (isValidColorString(colorString)) {
            val currentSettings = settingsRepository.globalSettings.value
            val newSettings = currentSettings.copy(skyBlueOnTertiary = colorString)
            settingsRepository.saveGlobalSettings(newSettings)
        }
    }

    fun updateTertiaryContainer(colorString: String) {
        if (isValidColorString(colorString)) {
            val currentSettings = settingsRepository.globalSettings.value
            val newSettings = currentSettings.copy(skyBlueTertiaryContainer = colorString)
            settingsRepository.saveGlobalSettings(newSettings)
        }
    }

    fun updateOnTertiaryContainer(colorString: String) {
        if (isValidColorString(colorString)) {
            val currentSettings = settingsRepository.globalSettings.value
            val newSettings = currentSettings.copy(skyBlueOnTertiaryContainer = colorString)
            settingsRepository.saveGlobalSettings(newSettings)
        }
    }

    // 错误颜色系统更新方法
    fun updateError(colorString: String) {
        if (isValidColorString(colorString)) {
            val currentSettings = settingsRepository.globalSettings.value
            val newSettings = currentSettings.copy(skyBlueError = colorString)
            settingsRepository.saveGlobalSettings(newSettings)
        }
    }

    fun updateOnError(colorString: String) {
        if (isValidColorString(colorString)) {
            val currentSettings = settingsRepository.globalSettings.value
            val newSettings = currentSettings.copy(skyBlueOnError = colorString)
            settingsRepository.saveGlobalSettings(newSettings)
        }
    }

    fun updateErrorContainer(colorString: String) {
        if (isValidColorString(colorString)) {
            val currentSettings = settingsRepository.globalSettings.value
            val newSettings = currentSettings.copy(skyBlueErrorContainer = colorString)
            settingsRepository.saveGlobalSettings(newSettings)
        }
    }

    fun updateOnErrorContainer(colorString: String) {
        if (isValidColorString(colorString)) {
            val currentSettings = settingsRepository.globalSettings.value
            val newSettings = currentSettings.copy(skyBlueOnErrorContainer = colorString)
            settingsRepository.saveGlobalSettings(newSettings)
        }
    }

    // 表面颜色系统更新方法
    fun updateBackground(colorString: String) {
        if (isValidColorString(colorString)) {
            val currentSettings = settingsRepository.globalSettings.value
            val newSettings = currentSettings.copy(skyBlueBackground = colorString)
            settingsRepository.saveGlobalSettings(newSettings)
        }
    }

    fun updateOnBackground(colorString: String) {
        if (isValidColorString(colorString)) {
            val currentSettings = settingsRepository.globalSettings.value
            val newSettings = currentSettings.copy(skyBlueOnBackground = colorString)
            settingsRepository.saveGlobalSettings(newSettings)
        }
    }

    fun updateSurface(colorString: String) {
        if (isValidColorString(colorString)) {
            val currentSettings = settingsRepository.globalSettings.value
            val newSettings = currentSettings.copy(skyBlueSurface = colorString)
            settingsRepository.saveGlobalSettings(newSettings)
        }
    }

    fun updateOnSurface(colorString: String) {
        if (isValidColorString(colorString)) {
            val currentSettings = settingsRepository.globalSettings.value
            val newSettings = currentSettings.copy(skyBlueOnSurface = colorString)
            settingsRepository.saveGlobalSettings(newSettings)
        }
    }

    fun updateSurfaceVariant(colorString: String) {
        if (isValidColorString(colorString)) {
            val currentSettings = settingsRepository.globalSettings.value
            val newSettings = currentSettings.copy(skyBlueSurfaceVariant = colorString)
            settingsRepository.saveGlobalSettings(newSettings)
        }
    }

    fun updateOnSurfaceVariant(colorString: String) {
        if (isValidColorString(colorString)) {
            val currentSettings = settingsRepository.globalSettings.value
            val newSettings = currentSettings.copy(skyBlueOnSurfaceVariant = colorString)
            settingsRepository.saveGlobalSettings(newSettings)
        }
    }

    // 扩展颜色更新方法
    fun updateConfirm(colorString: String) {
        if (isValidColorString(colorString)) {
            val currentSettings = settingsRepository.globalSettings.value
            val newSettings = currentSettings.copy(skyBlueConfirm = colorString)
            settingsRepository.saveGlobalSettings(newSettings)
        }
    }

    fun updateFontEmphasize(colorString: String) {
        if (isValidColorString(colorString)) {
            val currentSettings = settingsRepository.globalSettings.value
            val newSettings = currentSettings.copy(skyBlueFontEmphasize = colorString)
            settingsRepository.saveGlobalSettings(newSettings)
        }
    }

    fun updateIconEmphasize(colorString: String) {
        if (isValidColorString(colorString)) {
            val currentSettings = settingsRepository.globalSettings.value
            val newSettings = currentSettings.copy(skyBlueIconEmphasize = colorString)
            settingsRepository.saveGlobalSettings(newSettings)
        }
    }

    fun updateIconSubEmphasize(colorString: String) {
        if (isValidColorString(colorString)) {
            val currentSettings = settingsRepository.globalSettings.value
            val newSettings = currentSettings.copy(skyBlueIconSubEmphasize = colorString)
            settingsRepository.saveGlobalSettings(newSettings)
        }
    }

    fun updateBackgroundEmphasize(colorString: String) {
        if (isValidColorString(colorString)) {
            val currentSettings = settingsRepository.globalSettings.value
            val newSettings = currentSettings.copy(skyBlueBackgroundEmphasize = colorString)
            settingsRepository.saveGlobalSettings(newSettings)
        }
    }

    fun updateBackgroundFocus(colorString: String) {
        if (isValidColorString(colorString)) {
            val currentSettings = settingsRepository.globalSettings.value
            val newSettings = currentSettings.copy(skyBlueBackgroundFocus = colorString)
            settingsRepository.saveGlobalSettings(newSettings)
        }
    }

    // 底部导航栏颜色更新方法
    fun updateBottomNavBackground(colorString: String) {
        if (isValidColorString(colorString)) {
            Log.d("SkyBlueColorConfig", "更新底部导航栏背景色: $colorString")
            val currentSettings = settingsRepository.globalSettings.value
            val newSettings = currentSettings.copy(skyBlueBottomNavBackground = colorString)
            settingsRepository.saveGlobalSettings(newSettings)
            Log.d("SkyBlueColorConfig", "底部导航栏背景色更新完成")
        } else {
            Log.w("SkyBlueColorConfig", "无效的底部导航栏背景色字符串: $colorString")
        }
    }

    fun updateBottomNavSelectedIcon(colorString: String) {
        if (isValidColorString(colorString)) {
            Log.d("SkyBlueColorConfig", "更新底部导航栏选中图标色: $colorString")
            val currentSettings = settingsRepository.globalSettings.value
            val newSettings = currentSettings.copy(skyBlueBottomNavSelectedIcon = colorString)
            settingsRepository.saveGlobalSettings(newSettings)
            Log.d("SkyBlueColorConfig", "底部导航栏选中图标色更新完成")
        } else {
            Log.w("SkyBlueColorConfig", "无效的底部导航栏选中图标色字符串: $colorString")
        }
    }

    fun updateBottomNavUnselectedIcon(colorString: String) {
        if (isValidColorString(colorString)) {
            Log.d("SkyBlueColorConfig", "更新底部导航栏未选中图标色: $colorString")
            val currentSettings = settingsRepository.globalSettings.value
            val newSettings = currentSettings.copy(skyBlueBottomNavUnselectedIcon = colorString)
            settingsRepository.saveGlobalSettings(newSettings)
            Log.d("SkyBlueColorConfig", "底部导航栏未选中图标色更新完成")
        } else {
            Log.w("SkyBlueColorConfig", "无效的底部导航栏未选中图标色字符串: $colorString")
        }
    }

    // 标题栏颜色更新方法
    fun updateTopBarBackground(colorString: String) {
        if (isValidColorString(colorString)) {
            Log.d("SkyBlueColorConfig", "更新标题栏背景色: $colorString")
            val currentSettings = settingsRepository.globalSettings.value
            val newSettings = currentSettings.copy(skyBlueTopBarBackground = colorString)
            settingsRepository.saveGlobalSettings(newSettings)
            Log.d("SkyBlueColorConfig", "标题栏背景色更新完成")
        } else {
            Log.w("SkyBlueColorConfig", "无效的标题栏背景色字符串: $colorString")
        }
    }

    /**
     * 批量更新颜色配置
     *
     * 用于一次性更新多个颜色配置项，避免多次触发设置保存
     */
    fun updateColorConfiguration(
        primary: String? = null,
        onPrimary: String? = null,
        primaryContainer: String? = null,
        onPrimaryContainer: String? = null,
        secondary: String? = null,
        onSecondary: String? = null,
        secondaryContainer: String? = null,
        onSecondaryContainer: String? = null,
        tertiary: String? = null,
        onTertiary: String? = null,
        tertiaryContainer: String? = null,
        onTertiaryContainer: String? = null,
        error: String? = null,
        onError: String? = null,
        errorContainer: String? = null,
        onErrorContainer: String? = null,
        background: String? = null,
        onBackground: String? = null,
        surface: String? = null,
        onSurface: String? = null,
        surfaceVariant: String? = null,
        onSurfaceVariant: String? = null,
        confirm: String? = null,
        fontEmphasize: String? = null,
        iconEmphasize: String? = null,
        iconSubEmphasize: String? = null,
        backgroundEmphasize: String? = null,
        backgroundFocus: String? = null,
        bottomNavBackground: String? = null,
        bottomNavSelectedIcon: String? = null,
        bottomNavUnselectedIcon: String? = null,
        topBarBackground: String? = null
    ) {
        val currentSettings = settingsRepository.globalSettings.value
        val newSettings = currentSettings.copy(
            skyBluePrimary = if (primary != null && isValidColorString(primary)) primary else currentSettings.skyBluePrimary,
            skyBlueOnPrimary = if (onPrimary != null && isValidColorString(onPrimary)) onPrimary else currentSettings.skyBlueOnPrimary,
            skyBluePrimaryContainer = if (primaryContainer != null && isValidColorString(primaryContainer)) primaryContainer else currentSettings.skyBluePrimaryContainer,
            skyBlueOnPrimaryContainer = if (onPrimaryContainer != null && isValidColorString(onPrimaryContainer)) onPrimaryContainer else currentSettings.skyBlueOnPrimaryContainer,
            skyBlueSecondary = if (secondary != null && isValidColorString(secondary)) secondary else currentSettings.skyBlueSecondary,
            skyBlueOnSecondary = if (onSecondary != null && isValidColorString(onSecondary)) onSecondary else currentSettings.skyBlueOnSecondary,
            skyBlueSecondaryContainer = if (secondaryContainer != null && isValidColorString(secondaryContainer)) secondaryContainer else currentSettings.skyBlueSecondaryContainer,
            skyBlueOnSecondaryContainer = if (onSecondaryContainer != null && isValidColorString(onSecondaryContainer)) onSecondaryContainer else currentSettings.skyBlueOnSecondaryContainer,
            skyBlueTertiary = if (tertiary != null && isValidColorString(tertiary)) tertiary else currentSettings.skyBlueTertiary,
            skyBlueOnTertiary = if (onTertiary != null && isValidColorString(onTertiary)) onTertiary else currentSettings.skyBlueOnTertiary,
            skyBlueTertiaryContainer = if (tertiaryContainer != null && isValidColorString(tertiaryContainer)) tertiaryContainer else currentSettings.skyBlueTertiaryContainer,
            skyBlueOnTertiaryContainer = if (onTertiaryContainer != null && isValidColorString(onTertiaryContainer)) onTertiaryContainer else currentSettings.skyBlueOnTertiaryContainer,
            skyBlueError = if (error != null && isValidColorString(error)) error else currentSettings.skyBlueError,
            skyBlueOnError = if (onError != null && isValidColorString(onError)) onError else currentSettings.skyBlueOnError,
            skyBlueErrorContainer = if (errorContainer != null && isValidColorString(errorContainer)) errorContainer else currentSettings.skyBlueErrorContainer,
            skyBlueOnErrorContainer = if (onErrorContainer != null && isValidColorString(onErrorContainer)) onErrorContainer else currentSettings.skyBlueOnErrorContainer,
            skyBlueBackground = if (background != null && isValidColorString(background)) background else currentSettings.skyBlueBackground,
            skyBlueOnBackground = if (onBackground != null && isValidColorString(onBackground)) onBackground else currentSettings.skyBlueOnBackground,
            skyBlueSurface = if (surface != null && isValidColorString(surface)) surface else currentSettings.skyBlueSurface,
            skyBlueOnSurface = if (onSurface != null && isValidColorString(onSurface)) onSurface else currentSettings.skyBlueOnSurface,
            skyBlueSurfaceVariant = if (surfaceVariant != null && isValidColorString(surfaceVariant)) surfaceVariant else currentSettings.skyBlueSurfaceVariant,
            skyBlueOnSurfaceVariant = if (onSurfaceVariant != null && isValidColorString(onSurfaceVariant)) onSurfaceVariant else currentSettings.skyBlueOnSurfaceVariant,
            skyBlueConfirm = if (confirm != null && isValidColorString(confirm)) confirm else currentSettings.skyBlueConfirm,
            skyBlueFontEmphasize = if (fontEmphasize != null && isValidColorString(fontEmphasize)) fontEmphasize else currentSettings.skyBlueFontEmphasize,
            skyBlueIconEmphasize = if (iconEmphasize != null && isValidColorString(iconEmphasize)) iconEmphasize else currentSettings.skyBlueIconEmphasize,
            skyBlueIconSubEmphasize = if (iconSubEmphasize != null && isValidColorString(iconSubEmphasize)) iconSubEmphasize else currentSettings.skyBlueIconSubEmphasize,
            skyBlueBackgroundEmphasize = if (backgroundEmphasize != null && isValidColorString(backgroundEmphasize)) backgroundEmphasize else currentSettings.skyBlueBackgroundEmphasize,
            skyBlueBackgroundFocus = if (backgroundFocus != null && isValidColorString(backgroundFocus)) backgroundFocus else currentSettings.skyBlueBackgroundFocus,
            skyBlueBottomNavBackground = if (bottomNavBackground != null && isValidColorString(bottomNavBackground)) bottomNavBackground else currentSettings.skyBlueBottomNavBackground,
            skyBlueBottomNavSelectedIcon = if (bottomNavSelectedIcon != null && isValidColorString(bottomNavSelectedIcon)) bottomNavSelectedIcon else currentSettings.skyBlueBottomNavSelectedIcon,
            skyBlueBottomNavUnselectedIcon = if (bottomNavUnselectedIcon != null && isValidColorString(bottomNavUnselectedIcon)) bottomNavUnselectedIcon else currentSettings.skyBlueBottomNavUnselectedIcon,
            skyBlueTopBarBackground = if (topBarBackground != null && isValidColorString(topBarBackground)) topBarBackground else currentSettings.skyBlueTopBarBackground
        )
        settingsRepository.saveGlobalSettings(newSettings)
    }

    /**
     * 重置颜色配置为默认值
     */
    fun resetToDefaults() {
        updateColorConfiguration(
            primary = "0xFF0A59F7",
            onPrimary = "0xFFFFFFFF",
            primaryContainer = "0x330A59F7",
            onPrimaryContainer = "0xE5000000",
            secondary = "0x99000000",
            onSecondary = "0x99FFFFFF",
            secondaryContainer = "0xFFF1F3F5",
            onSecondaryContainer = "0x99000000",
            tertiary = "0x66000000",
            onTertiary = "0x66FFFFFF",
            tertiaryContainer = "0xFFE5E5EA",
            onTertiaryContainer = "0x66000000",
            error = "0xFFE84026",
            onError = "0xFFFFFFFF",
            errorContainer = "0xFFED6F21",
            onErrorContainer = "0xFFFFFFFF",
            background = "0xFFF1F3F5",
            onBackground = "0xE5000000",
            surface = "0xFFF1F3F5",
            onSurface = "0xE5000000",
            surfaceVariant = "0xFFF1F3F5",
            onSurfaceVariant = "0x99000000",
            confirm = "0xFF64BB5C",
            fontEmphasize = "0xFF0A59F7",
            iconEmphasize = "0xFF0A59F7",
            iconSubEmphasize = "0x660A59F7",
            backgroundEmphasize = "0xFF0A59F7",
            backgroundFocus = "0xFFF1F3F5",
            bottomNavBackground = "0xFFF1F3F5",
            bottomNavSelectedIcon = "0xFF0A59F7",
            bottomNavUnselectedIcon = "0x99000000",
            topBarBackground = "0xFFF1F3F5"
        )
    }
}

/**
 * 天空蓝主题颜色配置数据类
 *
 * 包含天空蓝主题的所有颜色配置
 */
data class SkyBlueColorConfiguration(
    // 主要颜色系统
    val primary: Color,
    val onPrimary: Color,
    val primaryContainer: Color,
    val onPrimaryContainer: Color,

    // 次要颜色系统
    val secondary: Color,
    val onSecondary: Color,
    val secondaryContainer: Color,
    val onSecondaryContainer: Color,

    // 第三颜色系统
    val tertiary: Color,
    val onTertiary: Color,
    val tertiaryContainer: Color,
    val onTertiaryContainer: Color,

    // 错误颜色系统
    val error: Color,
    val onError: Color,
    val errorContainer: Color,
    val onErrorContainer: Color,

    // 表面颜色系统
    val background: Color,
    val onBackground: Color,
    val surface: Color,
    val onSurface: Color,
    val surfaceVariant: Color,
    val onSurfaceVariant: Color,

    // 扩展颜色
    val confirm: Color,
    val fontEmphasize: Color,
    val iconEmphasize: Color,
    val iconSubEmphasize: Color,
    val backgroundEmphasize: Color,
    val backgroundFocus: Color,

    // 底部导航栏颜色
    val bottomNavBackground: Color,
    val bottomNavSelectedIcon: Color,
    val bottomNavUnselectedIcon: Color,

    // 标题栏颜色
    val topBarBackground: Color
)
