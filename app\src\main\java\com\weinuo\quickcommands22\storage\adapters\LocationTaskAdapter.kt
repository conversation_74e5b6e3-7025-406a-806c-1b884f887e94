package com.weinuo.quickcommands22.storage.adapters

import android.util.Log
import com.weinuo.quickcommands22.model.*
import com.weinuo.quickcommands22.storage.NativeTypeStorageManager
import com.weinuo.quickcommands22.storage.StorageDomain
import com.weinuo.quickcommands22.storage.StorageOperation

/**
 * 位置任务存储适配器
 *
 * 负责LocationTask的原生数据类型存储和重建。
 * 将复杂的位置任务对象拆分为独立的原生字段进行存储，
 * 完全避免JSON序列化问题。
 *
 * 支持的字段类型：
 * - 基础字段：id、operation
 * - 分享方式相关：shareMethod
 * - 联系人相关：contactId、contactName、phoneNumber
 * - 分享消息相关：shareMessage
 * - 位置服务相关：locationServiceOperation、locationServiceControlMethod
 * - 更新频率相关：updateFrequencyValue、updateFrequencyUnit
 *
 * 存储格式示例：
 * task_{id}_type = "location"
 * task_{id}_operation = "SHARE_LOCATION"
 * task_{id}_share_method = "SMS"
 * task_{id}_contact_id = "123"
 * task_{id}_contact_name = "<PERSON> Doe"
 * task_{id}_phone_number = "13800138000"
 * task_{id}_share_message = "我的位置："
 * task_{id}_location_service_operation = "TOGGLE"
 * task_{id}_location_service_control_method = "SYSTEM_SETTINGS"
 * task_{id}_update_frequency_value = 30
 * task_{id}_update_frequency_unit = "SECONDS"
 *
 * @param storageManager 原生类型存储管理器
 */
class LocationTaskAdapter(
    storageManager: NativeTypeStorageManager
) : BaseTaskAdapter<LocationTask>(storageManager) {

    companion object {
        private const val TAG = "LocationTaskAdapter"

        // 字段名常量
        private const val FIELD_OPERATION = "operation"
        private const val FIELD_SHARE_METHOD = "share_method"
        private const val FIELD_CONTACT_ID = "contact_id"
        private const val FIELD_CONTACT_NAME = "contact_name"
        private const val FIELD_PHONE_NUMBER = "phone_number"
        private const val FIELD_SHARE_MESSAGE = "share_message"
        private const val FIELD_LOCATION_SERVICE_OPERATION = "location_service_operation"
        private const val FIELD_LOCATION_SERVICE_CONTROL_METHOD = "location_service_control_method"
        private const val FIELD_UPDATE_FREQUENCY_VALUE = "update_frequency_value"
        private const val FIELD_UPDATE_FREQUENCY_UNIT = "update_frequency_unit"
    }

    override fun getTaskType() = "location"

    /**
     * 保存位置任务
     * 将LocationTask的所有字段拆分为原生数据类型存储
     *
     * @param task 要保存的位置任务
     * @return 操作是否成功
     */
    override fun save(task: LocationTask): Boolean {
        if (!isValidTaskId(task.id)) {
            logSaveError(task.id, "Invalid task ID")
            return false
        }

        Log.d(TAG, "开始保存位置任务: ${task.id}")

        return try {
            val operations = mutableListOf<StorageOperation>()

            // 保存基础字段
            operations.addAll(saveBaseFields(task.id, task))

            // 保存位置任务特有字段
            operations.addAll(listOf(
                saveEnum(generateKey(task.id, FIELD_OPERATION), task.operation),
                saveEnum(generateKey(task.id, FIELD_SHARE_METHOD), task.shareMethod),
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_CONTACT_ID),
                    task.contactId
                ),
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_CONTACT_NAME),
                    task.contactName
                ),
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_PHONE_NUMBER),
                    task.phoneNumber
                ),
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_SHARE_MESSAGE),
                    task.shareMessage
                ),
                saveEnum(generateKey(task.id, FIELD_LOCATION_SERVICE_OPERATION), task.locationServiceOperation),
                saveEnum(generateKey(task.id, FIELD_LOCATION_SERVICE_CONTROL_METHOD), task.locationServiceControlMethod),
                StorageOperation.createIntOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_UPDATE_FREQUENCY_VALUE),
                    task.updateFrequencyValue
                ),
                saveEnum(generateKey(task.id, FIELD_UPDATE_FREQUENCY_UNIT), task.updateFrequencyUnit)
            ))

            val success = storageManager.executeBatch(operations)
            if (success) {
                Log.d(TAG, "位置任务保存成功: ${task.id}")
            } else {
                logSaveError(task.id, "Batch operation failed")
            }
            success

        } catch (e: Exception) {
            logSaveError(task.id, "Exception during save: ${e.message}")
            false
        }
    }

    /**
     * 加载位置任务
     * 从原生数据类型重建LocationTask对象
     *
     * @param taskId 任务ID
     * @return 加载的位置任务，失败时返回null
     */
    override fun load(taskId: String): LocationTask? {
        if (!isValidTaskId(taskId)) {
            logLoadError(taskId, "Invalid task ID")
            return null
        }

        Log.d(TAG, "开始加载位置任务: $taskId")

        return try {
            // 检查任务是否存在
            if (!exists(taskId)) {
                Log.d(TAG, "位置任务不存在: $taskId")
                return null
            }

            LocationTask(
                id = taskId,
                operation = loadEnum(generateKey(taskId, FIELD_OPERATION)) { LocationOperation.valueOf(it) }
                    ?: LocationOperation.SHARE_LOCATION,
                shareMethod = loadEnum(generateKey(taskId, FIELD_SHARE_METHOD)) { LocationShareMethod.valueOf(it) }
                    ?: LocationShareMethod.SMS,
                contactId = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_CONTACT_ID),
                    ""
                ),
                contactName = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_CONTACT_NAME),
                    ""
                ),
                phoneNumber = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_PHONE_NUMBER),
                    ""
                ),
                shareMessage = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_SHARE_MESSAGE),
                    "我的位置："
                ),
                locationServiceOperation = loadEnum(generateKey(taskId, FIELD_LOCATION_SERVICE_OPERATION)) { SwitchOperation.valueOf(it) }
                    ?: SwitchOperation.TOGGLE,
                locationServiceControlMethod = loadEnum(generateKey(taskId, FIELD_LOCATION_SERVICE_CONTROL_METHOD)) { LocationServiceControlMethod.valueOf(it) }
                    ?: LocationServiceControlMethod.SYSTEM_SETTINGS,
                updateFrequencyValue = storageManager.loadInt(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_UPDATE_FREQUENCY_VALUE),
                    30
                ),
                updateFrequencyUnit = loadEnum(generateKey(taskId, FIELD_UPDATE_FREQUENCY_UNIT)) { LocationUpdateFrequencyUnit.valueOf(it) }
                    ?: LocationUpdateFrequencyUnit.SECONDS
            ).also {
                Log.d(TAG, "位置任务加载成功: $taskId")
            }

        } catch (e: Exception) {
            logLoadError(taskId, "Exception during load: ${e.message}")
            null
        }
    }
}
