package com.weinuo.quickcommands22.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.selection.selectable
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.icons.filled.Person
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import com.weinuo.quickcommands22.ui.components.themed.ThemedSearchTextField
import com.weinuo.quickcommands22.ui.components.themed.ThemedRadioButton
import com.weinuo.quickcommands22.ui.theme.config.RadioButtonConfig
import com.weinuo.quickcommands22.utils.AccountsCacheManager
import kotlinx.coroutines.launch

/**
 * 账号选择模式
 */
enum class AccountSelectionMode {
    SINGLE,  // 单选模式
    MULTI    // 多选模式
}

/**
 * 账号信息数据类
 */
data class AccountInfo(
    val name: String,
    val type: String
) {
    val displayName: String
        get() = if (name.isNotEmpty()) name else type
}

/**
 * 可复用的账号选择界面
 *
 * 这个组件提供了一个全屏的账号选择界面，支持单选和多选模式。
 * 它会自动获取系统中的所有账号，并提供搜索功能。
 * 使用按需加载策略和智能缓存，避免不必要的电量消耗。
 *
 * @param selectionMode 选择模式（单选或多选）
 * @param initialSelectedAccountNames 初始选中的账号名称列表
 * @param onAccountsSelected 账号选择完成回调
 * @param onDismiss 取消选择回调
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AccountSelectionScreen(
    selectionMode: AccountSelectionMode,
    initialSelectedAccountNames: List<String> = emptyList(),
    onAccountsSelected: (List<AccountInfo>) -> Unit,
    onDismiss: () -> Unit
) {
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()
    val focusManager = LocalFocusManager.current

    // 状态管理
    var accounts by remember { mutableStateOf<List<AccountInfo>>(emptyList()) }
    var isLoading by remember { mutableStateOf(true) }
    var searchQuery by rememberSaveable { mutableStateOf("") }
    var selectedAccountNames by rememberSaveable { mutableStateOf(initialSelectedAccountNames.toSet()) }

    // 加载账号列表 - 使用按需加载和智能缓存
    LaunchedEffect(Unit) {
        coroutineScope.launch {
            try {
                accounts = AccountsCacheManager.getAccounts(context)
            } catch (e: Exception) {
                // 处理错误，显示空列表
                accounts = emptyList()
            } finally {
                isLoading = false
            }
        }
    }

    // 过滤账号列表
    val filteredAccounts = remember(accounts, searchQuery) {
        if (searchQuery.isBlank()) {
            accounts
        } else {
            accounts.filter { account ->
                account.name.contains(searchQuery, ignoreCase = true) ||
                account.type.contains(searchQuery, ignoreCase = true)
            }
        }
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = when (selectionMode) {
                            AccountSelectionMode.SINGLE -> "选择账号"
                            AccountSelectionMode.MULTI -> "选择账号 (${selectedAccountNames.size})"
                        }
                    )
                },
                navigationIcon = {
                    IconButton(onClick = onDismiss) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "返回"
                        )
                    }
                },
                actions = {
                    // 多选模式下显示确认按钮
                    if (selectionMode == AccountSelectionMode.MULTI && selectedAccountNames.isNotEmpty()) {
                        IconButton(
                            onClick = {
                                val selectedAccounts = accounts.filter { it.name in selectedAccountNames }
                                onAccountsSelected(selectedAccounts)
                            }
                        ) {
                            Icon(
                                imageVector = Icons.Default.Check,
                                contentDescription = "确认选择"
                            )
                        }
                    }
                }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            // 搜索框
            ThemedSearchTextField(
                searchQuery = searchQuery,
                onSearchQueryChange = { searchQuery = it },
                onClearSearch = {
                    searchQuery = ""
                    focusManager.clearFocus()
                },
                placeholder = "搜索账号名称或类型",
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp)
            )

            // 内容区域
            when {
                isLoading -> {
                    // 加载状态
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally,
                            verticalArrangement = Arrangement.spacedBy(16.dp)
                        ) {
                            CircularProgressIndicator()
                            Text("正在加载账号...")
                        }
                    }
                }

                filteredAccounts.isEmpty() -> {
                    // 空状态
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally,
                            verticalArrangement = Arrangement.spacedBy(16.dp)
                        ) {
                            Icon(
                                imageVector = Icons.Default.Person,
                                contentDescription = null,
                                modifier = Modifier.size(64.dp),
                                tint = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                            Text(
                                text = if (searchQuery.isBlank()) "没有找到账号" else "没有匹配的账号",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }
                }

                else -> {
                    // 账号列表
                    LazyColumn(
                        modifier = Modifier.fillMaxSize(),
                        contentPadding = PaddingValues(horizontal = 16.dp, vertical = 8.dp),
                        verticalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        items(filteredAccounts) { account ->
                            AccountItem(
                                account = account,
                                isSelected = account.name in selectedAccountNames,
                                selectionMode = selectionMode,
                                onAccountClick = { clickedAccount ->
                                    when (selectionMode) {
                                        AccountSelectionMode.SINGLE -> {
                                            // 单选模式：直接返回选中的账号
                                            onAccountsSelected(listOf(clickedAccount))
                                        }
                                        AccountSelectionMode.MULTI -> {
                                            // 多选模式：切换选中状态
                                            selectedAccountNames = if (clickedAccount.name in selectedAccountNames) {
                                                selectedAccountNames - clickedAccount.name
                                            } else {
                                                selectedAccountNames + clickedAccount.name
                                            }
                                        }
                                    }
                                }
                            )
                        }
                    }
                }
            }
        }
    }
}

/**
 * 账号列表项组件
 */
@Composable
private fun AccountItem(
    account: AccountInfo,
    isSelected: Boolean,
    selectionMode: AccountSelectionMode,
    onAccountClick: (AccountInfo) -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .selectable(
                selected = isSelected,
                onClick = { onAccountClick(account) }
            ),
        colors = CardDefaults.cardColors(
            containerColor = if (isSelected) {
                MaterialTheme.colorScheme.primaryContainer
            } else {
                MaterialTheme.colorScheme.surface
            }
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 选择指示器
            when (selectionMode) {
                AccountSelectionMode.SINGLE -> {
                    ThemedRadioButton(
                        config = RadioButtonConfig(
                            selected = isSelected,
                            onClick = { onAccountClick(account) },
                            enabled = true
                        )
                    )
                }
                AccountSelectionMode.MULTI -> {
                    Checkbox(
                        checked = isSelected,
                        onCheckedChange = { onAccountClick(account) }
                    )
                }
            }

            Spacer(modifier = Modifier.width(12.dp))

            // 账号信息
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = account.displayName,
                    style = MaterialTheme.typography.bodyLarge,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
                if (account.name != account.type) {
                    Text(
                        text = account.type,
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                }
            }
        }
    }
}

