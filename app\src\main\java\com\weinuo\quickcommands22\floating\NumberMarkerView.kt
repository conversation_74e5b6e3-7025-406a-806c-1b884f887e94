package com.weinuo.quickcommands22.floating

import android.content.Context
import android.graphics.*
import android.view.View
import android.view.WindowManager
import com.weinuo.quickcommands22.model.AdvancedGestureType
import com.weinuo.quickcommands22.utils.OverlayPermissionUtil

/**
 * 数字标记视图
 *
 * 在屏幕上显示带数字的坐标标记，用于标记手势操作的位置和顺序。
 * 支持直接拖拽移动来调整手势位置。
 */
class NumberMarkerView(
    private val context: Context,
    private val number: Int,
    private val gestureType: AdvancedGestureType,
    private val onPositionChanged: ((Float, Float) -> Unit)? = null
) {
    companion object {
        private const val TAG = "NumberMarkerView"
        private const val MARKER_SIZE_DP = 32
    }

    private val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
    private var markerView: MarkerView? = null
    private var markerParams: WindowManager.LayoutParams? = null
    private var isShowing = false

    /**
     * 显示数字标记
     */
    fun show(x: Float, y: Float): Bo<PERSON>an {
        if (!OverlayPermissionUtil.hasOverlayPermission(context)) {
            return false
        }

        if (isShowing) {
            hide()
        }

        try {
            markerView = MarkerView(context, number, gestureType)

            val density = context.resources.displayMetrics.density
            val sizeInPx = (MARKER_SIZE_DP * density).toInt()

            markerParams = WindowManager.LayoutParams(
                sizeInPx,
                sizeInPx,
                WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY,
                WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
                PixelFormat.TRANSLUCENT
            ).apply {
                gravity = android.view.Gravity.TOP or android.view.Gravity.START
                this.x = (x - sizeInPx / 2).toInt()
                this.y = (y - sizeInPx / 2).toInt()
            }

            // 添加拖拽功能
            addDragFunctionality(markerView!!, markerParams!!)

            windowManager.addView(markerView, markerParams)
            isShowing = true
            return true
        } catch (e: Exception) {
            return false
        }
    }

    /**
     * 隐藏数字标记
     */
    fun hide() {
        if (!isShowing) return

        try {
            markerView?.let { view ->
                windowManager.removeView(view)
            }
            markerView = null
            isShowing = false
        } catch (e: Exception) {
            // 忽略异常
        }
    }

    /**
     * 添加拖拽功能
     */
    private fun addDragFunctionality(view: View, params: WindowManager.LayoutParams) {
        var initialX = 0
        var initialY = 0
        var initialTouchX = 0f
        var initialTouchY = 0f

        view.setOnTouchListener { _, event ->
            when (event.action) {
                android.view.MotionEvent.ACTION_DOWN -> {
                    initialX = params.x
                    initialY = params.y
                    initialTouchX = event.rawX
                    initialTouchY = event.rawY
                    true
                }
                android.view.MotionEvent.ACTION_MOVE -> {
                    val newX = initialX + (event.rawX - initialTouchX).toInt()
                    val newY = initialY + (event.rawY - initialTouchY).toInt()

                    params.x = newX
                    params.y = newY
                    windowManager.updateViewLayout(view, params)

                    // 通知位置变化
                    val density = context.resources.displayMetrics.density
                    val sizeInPx = (MARKER_SIZE_DP * density).toInt()
                    val centerX = newX + sizeInPx / 2f
                    val centerY = newY + sizeInPx / 2f
                    onPositionChanged?.invoke(centerX, centerY)

                    true
                }
                else -> false
            }
        }
    }

    /**
     * 标记视图
     */
    private class MarkerView(
        context: Context,
        private val number: Int,
        private val gestureType: AdvancedGestureType
    ) : View(context) {

        private val backgroundPaint = Paint(Paint.ANTI_ALIAS_FLAG)
        private val coordinateLinesPaint = Paint(Paint.ANTI_ALIAS_FLAG)
        private val textPaint = Paint(Paint.ANTI_ALIAS_FLAG)

        init {
            setupPaints()
        }

        /**
         * 设置画笔
         */
        private fun setupPaints() {
            val density = context.resources.displayMetrics.density

            // 根据手势类型设置背景颜色
            val backgroundColor = when (gestureType) {
                AdvancedGestureType.TAP -> Color.argb(220, 76, 175, 80) // 绿色
                AdvancedGestureType.LONG_PRESS -> Color.argb(220, 255, 152, 0) // 橙色
                AdvancedGestureType.SWIPE -> Color.argb(220, 156, 39, 176) // 紫色
                AdvancedGestureType.MULTI_TOUCH -> Color.argb(220, 244, 67, 54) // 红色
            }

            // 背景画笔（圆形背景）
            backgroundPaint.apply {
                style = Paint.Style.FILL
                color = backgroundColor
            }

            // 坐标线画笔
            coordinateLinesPaint.apply {
                style = Paint.Style.STROKE
                strokeWidth = 3 * density
                color = Color.WHITE
                strokeCap = Paint.Cap.ROUND
            }

            // 文字画笔 - 使用对比色，确保在坐标线上清晰可见
            textPaint.apply {
                color = Color.BLACK
                textSize = 14 * density
                textAlign = Paint.Align.CENTER
                typeface = Typeface.DEFAULT_BOLD
                // 添加白色描边效果，确保在任何背景下都清晰可见
                setShadowLayer(2f, 0f, 0f, Color.WHITE)
            }
        }

        override fun onDraw(canvas: Canvas) {
            super.onDraw(canvas)

            val centerX = width / 2f
            val centerY = height / 2f

            // 先绘制坐标线（底层）
            val lineLength = Math.min(width, height) / 2f - 4

            // 水平坐标线
            canvas.drawLine(
                centerX - lineLength, centerY,
                centerX + lineLength, centerY,
                coordinateLinesPaint
            )

            // 垂直坐标线
            canvas.drawLine(
                centerX, centerY - lineLength,
                centerX, centerY + lineLength,
                coordinateLinesPaint
            )

            // 后绘制数字（顶层），确保数字在坐标线上方显示
            val textY = centerY - lineLength / 2f + textPaint.textSize / 3
            canvas.drawText(number.toString(), centerX, textY, textPaint)
        }
    }
}
