package com.weinuo.quickcommands22.ui.recording

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.weinuo.quickcommands22.model.TouchEvent
import com.weinuo.quickcommands22.model.TouchEventType
import com.weinuo.quickcommands22.model.TouchPosition

/**
 * 动作编辑全屏界面
 *
 * 提供完整的动作属性编辑功能，包括：
 * - 动作类型选择
 * - 触摸位置编辑
 * - 持续时间设置
 * - 延迟时间设置
 * - 高级选项配置
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ActionEditScreen(
    action: TouchEvent,
    onActionUpdated: (TouchEvent) -> Unit,
    onNavigateBack: () -> Unit,
    onPositionPicker: () -> Unit
) {
    val configuration = LocalConfiguration.current
    val screenWidth = configuration.screenWidthDp * LocalDensity.current.density
    val screenHeight = configuration.screenHeightDp * LocalDensity.current.density

    var editedAction by remember(action) { mutableStateOf(action) }
    var durationText by remember(action) { mutableStateOf(action.duration.toString()) }
    var delayText by remember(action) { mutableStateOf((action.delayAfter / 1000.0).toString()) }
    var startXText by remember(action, screenWidth) { mutableStateOf((action.position.startX * screenWidth).toInt().toString()) }
    var startYText by remember(action, screenHeight) { mutableStateOf((action.position.startY * screenHeight).toInt().toString()) }
    var endXText by remember(action, screenWidth) { mutableStateOf((action.position.endX * screenWidth).toInt().toString()) }
    var endYText by remember(action, screenHeight) { mutableStateOf((action.position.endY * screenHeight).toInt().toString()) }
    var touchPointsText by remember(action) { mutableStateOf(action.position.touchPoints.toString()) }
    var descriptionText by remember(action) { mutableStateOf(action.description) }

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = "编辑动作: ${action.getDisplayName()}",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Medium
                    )
                },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                    }
                },
                actions = {
                    TextButton(
                        onClick = {
                            onActionUpdated(editedAction)
                            // 不需要再调用onNavigateBack()，因为onActionUpdated内部已经处理了导航
                        }
                    ) {
                        Text("完成", fontWeight = FontWeight.Medium)
                    }
                }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .verticalScroll(rememberScrollState())
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(24.dp)
        ) {
            // 动作类型选择
            ActionTypeSection(
                selectedType = editedAction.type,
                onTypeSelected = { newType ->
                    editedAction = editedAction.copy(type = newType)
                }
            )

            // 触摸位置编辑
            PositionEditSection(
                position = editedAction.position,
                startXText = startXText,
                startYText = startYText,
                endXText = endXText,
                endYText = endYText,
                onStartXChanged = {
                    startXText = it
                    // 只有当输入有效时才更新editedAction
                    val xPixels = it.toFloatOrNull()
                    if (xPixels != null) {
                        val x = xPixels / screenWidth
                        editedAction = editedAction.copy(
                            position = editedAction.position.copy(startX = x.coerceIn(0f, 1f))
                        )
                    }
                },
                onStartYChanged = {
                    startYText = it
                    // 只有当输入有效时才更新editedAction
                    val yPixels = it.toFloatOrNull()
                    if (yPixels != null) {
                        val y = yPixels / screenHeight
                        editedAction = editedAction.copy(
                            position = editedAction.position.copy(startY = y.coerceIn(0f, 1f))
                        )
                    }
                },
                onEndXChanged = {
                    endXText = it
                    // 只有当输入有效时才更新editedAction
                    val xPixels = it.toFloatOrNull()
                    if (xPixels != null) {
                        val x = xPixels / screenWidth
                        editedAction = editedAction.copy(
                            position = editedAction.position.copy(endX = x.coerceIn(0f, 1f))
                        )
                    }
                },
                onEndYChanged = {
                    endYText = it
                    // 只有当输入有效时才更新editedAction
                    val yPixels = it.toFloatOrNull()
                    if (yPixels != null) {
                        val y = yPixels / screenHeight
                        editedAction = editedAction.copy(
                            position = editedAction.position.copy(endY = y.coerceIn(0f, 1f))
                        )
                    }
                },
                onPositionPicker = onPositionPicker,
                isSwipe = editedAction.type == TouchEventType.SWIPE
            )

            // 持续时间设置
            DurationSection(
                durationText = durationText,
                onDurationChanged = {
                    durationText = it
                    // 只有当输入有效时才更新editedAction
                    val duration = it.toLongOrNull()
                    if (duration != null) {
                        editedAction = editedAction.copy(duration = duration.coerceAtLeast(0))
                    }
                },
                actionType = editedAction.type
            )

            // 延迟时间设置
            DelaySection(
                delayText = delayText,
                onDelayChanged = {
                    delayText = it
                    // 只有当输入有效时才更新editedAction
                    val delay = it.toDoubleOrNull()
                    if (delay != null) {
                        editedAction = editedAction.copy(delayAfter = (delay * 1000).toLong().coerceAtLeast(0))
                    }
                }
            )

            // 高级选项
            AdvancedOptionsSection(
                touchPointsText = touchPointsText,
                descriptionText = descriptionText,
                onTouchPointsChanged = {
                    touchPointsText = it
                    // 只有当输入有效时才更新editedAction
                    val points = it.toIntOrNull()
                    if (points != null) {
                        editedAction = editedAction.copy(
                            position = editedAction.position.copy(touchPoints = points.coerceIn(1, 10))
                        )
                    }
                },
                onDescriptionChanged = {
                    descriptionText = it
                    editedAction = editedAction.copy(description = it)
                },
                showTouchPoints = editedAction.type == TouchEventType.MULTI_TAP
            )
        }
    }
}

/**
 * 动作类型选择区域
 */
@Composable
private fun ActionTypeSection(
    selectedType: TouchEventType,
    onTypeSelected: (TouchEventType) -> Unit
) {
    Card {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = "动作类型",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                TouchEventType.values().forEach { type ->
                    FilterChip(
                        onClick = { onTypeSelected(type) },
                        label = { Text(type.displayName) },
                        selected = selectedType == type,
                        modifier = Modifier.weight(1f)
                    )
                }
            }
        }
    }
}

/**
 * 位置编辑区域
 */
@Composable
private fun PositionEditSection(
    position: TouchPosition,
    startXText: String,
    startYText: String,
    endXText: String,
    endYText: String,
    onStartXChanged: (String) -> Unit,
    onStartYChanged: (String) -> Unit,
    onEndXChanged: (String) -> Unit,
    onEndYChanged: (String) -> Unit,
    onPositionPicker: () -> Unit,
    isSwipe: Boolean
) {
    Card {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "触摸位置",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )

                OutlinedButton(
                    onClick = onPositionPicker,
                    modifier = Modifier.height(36.dp)
                ) {
                    Icon(
                        Icons.Default.TouchApp,
                        contentDescription = null,
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("在屏幕上选择", fontSize = 12.sp)
                }
            }

            // 起始位置
            Text(
                text = if (isSwipe) "起始位置" else "触摸位置",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                OutlinedTextField(
                    value = startXText,
                    onValueChange = onStartXChanged,
                    label = { Text("X坐标 (像素)") },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                    modifier = Modifier.weight(1f)
                )

                OutlinedTextField(
                    value = startYText,
                    onValueChange = onStartYChanged,
                    label = { Text("Y坐标 (像素)") },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                    modifier = Modifier.weight(1f)
                )
            }

            // 结束位置（仅滑动操作显示）
            if (isSwipe) {
                Text(
                    text = "结束位置",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    OutlinedTextField(
                        value = endXText,
                        onValueChange = onEndXChanged,
                        label = { Text("X坐标 (像素)") },
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                        modifier = Modifier.weight(1f)
                    )

                    OutlinedTextField(
                        value = endYText,
                        onValueChange = onEndYChanged,
                        label = { Text("Y坐标 (像素)") },
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                        modifier = Modifier.weight(1f)
                    )
                }
            }
        }
    }
}

/**
 * 持续时间设置区域
 */
@Composable
private fun DurationSection(
    durationText: String,
    onDurationChanged: (String) -> Unit,
    actionType: TouchEventType
) {
    Card {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = "动作持续时间",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )

            OutlinedTextField(
                value = durationText,
                onValueChange = onDurationChanged,
                label = { Text("持续时间 (毫秒)") },
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                supportingText = {
                    Text(
                        text = when (actionType) {
                            TouchEventType.LONG_PRESS -> "长按操作的持续时间"
                            TouchEventType.SWIPE -> "滑动操作的执行时间"
                            else -> "动作执行的持续时间"
                        }
                    )
                },
                modifier = Modifier.fillMaxWidth()
            )

            // 快捷设置按钮
            Text(
                text = "快捷设置",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                val presetDurations = when (actionType) {
                    TouchEventType.LONG_PRESS -> listOf(500L, 1000L, 1500L, 2000L, 3000L)
                    TouchEventType.SWIPE -> listOf(100L, 300L, 500L, 800L, 1000L)
                    else -> listOf(0L, 100L, 300L, 500L, 1000L)
                }

                presetDurations.forEach { duration ->
                    FilterChip(
                        onClick = { onDurationChanged(duration.toString()) },
                        label = {
                            Text(
                                text = if (duration == 0L) "瞬间" else "${duration}ms",
                                fontSize = 12.sp
                            )
                        },
                        selected = durationText == duration.toString()
                    )
                }
            }
        }
    }
}

/**
 * 延迟时间设置区域
 */
@Composable
private fun DelaySection(
    delayText: String,
    onDelayChanged: (String) -> Unit
) {
    Card {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = "完成后延迟时间",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )

            OutlinedTextField(
                value = delayText,
                onValueChange = onDelayChanged,
                label = { Text("延迟时间 (秒)") },
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                supportingText = {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(4.dp)
                    ) {
                        Icon(
                            Icons.Default.Info,
                            contentDescription = null,
                            modifier = Modifier.size(16.dp),
                            tint = MaterialTheme.colorScheme.primary
                        )
                        Text("这个动作完成后等待多久再执行下一个动作")
                    }
                },
                modifier = Modifier.fillMaxWidth()
            )

            // 快捷设置按钮
            Text(
                text = "常用延迟",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                val presetDelays = listOf(0.0, 0.5, 1.0, 2.0, 3.0, 5.0)

                presetDelays.forEach { delay ->
                    FilterChip(
                        onClick = { onDelayChanged(delay.toString()) },
                        label = {
                            Text(
                                text = if (delay == 0.0) "无延迟" else "${delay}秒",
                                fontSize = 12.sp
                            )
                        },
                        selected = delayText == delay.toString()
                    )
                }
            }
        }
    }
}

/**
 * 高级选项区域
 */
@Composable
private fun AdvancedOptionsSection(
    touchPointsText: String,
    descriptionText: String,
    onTouchPointsChanged: (String) -> Unit,
    onDescriptionChanged: (String) -> Unit,
    showTouchPoints: Boolean
) {
    var expanded by remember { mutableStateOf(false) }

    Card {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "高级选项",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )

                IconButton(onClick = { expanded = !expanded }) {
                    Icon(
                        if (expanded) Icons.Default.ExpandLess else Icons.Default.ExpandMore,
                        contentDescription = if (expanded) "收起" else "展开"
                    )
                }
            }

            if (expanded) {
                // 触摸点数量（仅多点触控显示）
                if (showTouchPoints) {
                    OutlinedTextField(
                        value = touchPointsText,
                        onValueChange = onTouchPointsChanged,
                        label = { Text("触摸点数量") },
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                        supportingText = { Text("多点触控的触摸点数量 (1-10)") },
                        modifier = Modifier.fillMaxWidth()
                    )
                }

                // 动作描述
                OutlinedTextField(
                    value = descriptionText,
                    onValueChange = onDescriptionChanged,
                    label = { Text("动作描述") },
                    supportingText = { Text("为这个动作添加描述，便于识别") },
                    modifier = Modifier.fillMaxWidth(),
                    maxLines = 3
                )
            }
        }
    }
}