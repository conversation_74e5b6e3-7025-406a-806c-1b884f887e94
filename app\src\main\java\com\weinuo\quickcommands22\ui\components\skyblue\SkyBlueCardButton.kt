package com.weinuo.quickcommands22.ui.components.skyblue

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.weinuo.quickcommands22.data.SettingsRepository
import com.weinuo.quickcommands22.ui.theme.skyblue.SkyBlueStyleConfiguration

/**
 * 天空蓝主题专用卡片型按钮组件
 *
 * 特点：
 * - 卡片背景样式（浅色背景）
 * - 蓝色字体和图标
 * - 圆角大小由全局设置控制
 * - 无阴影设计，符合天空蓝主题的整合设计风格
 * - 支持图标和文本的组合显示
 */
@Composable
fun SkyBlueCardButton(
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true
) {
    val context = LocalContext.current
    val settingsRepository = remember { SettingsRepository(context) }
    val globalSettings by settingsRepository.globalSettings.collectAsState()
    
    // 获取动态卡片样式配置
    val cardStyle = SkyBlueStyleConfiguration.getDynamicCardStyleConfig(settingsRepository)
    
    // 颜色配置 - 使用与快捷指令卡片相同的背景颜色
    val backgroundColor = if (enabled) {
        MaterialTheme.colorScheme.surfaceContainerLow // 与快捷指令卡片相同的背景颜色（白色）
    } else {
        MaterialTheme.colorScheme.surfaceContainerLow.copy(alpha = 0.6f)
    }
    
    val contentColor = if (enabled) {
        Color(0xFF0A59F7) // 天空蓝色文字和图标
    } else {
        Color(0xFF0A59F7).copy(alpha = 0.38f)
    }
    
    // 使用全局设置的圆角大小
    val cornerRadius = cardStyle.defaultCornerRadius
    
    Box(
        modifier = modifier
            .fillMaxWidth()
            .height(70.dp) // 增加高度，使其更接近卡片的高度
            .clip(RoundedCornerShape(cornerRadius))
            .background(backgroundColor)
            .clickable(enabled = enabled) { onClick() }
            .padding(horizontal = 16.dp),
        contentAlignment = Alignment.Center
    ) {
        // 天空蓝主题不显示图标，只显示文本
        Text(
            text = text,
            color = contentColor,
            style = MaterialTheme.typography.bodyLarge.copy(
                fontWeight = FontWeight.Medium
            )
        )
    }
}

/**
 * 天空蓝主题专用普通按钮组件
 *
 * 特点：
 * - 灰色背景样式（类似图中NORMAL样式）
 * - 黑色文字
 * - 圆角大小由全局设置控制
 * - 无阴影设计，符合天空蓝主题的整合设计风格
 */
@Composable
fun SkyBlueNormalButton(
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true
) {
    val context = LocalContext.current
    val settingsRepository = remember { SettingsRepository(context) }

    // 获取动态卡片样式配置
    val cardStyle = SkyBlueStyleConfiguration.getDynamicCardStyleConfig(settingsRepository)

    // 颜色配置 - 灰色背景，蓝色文字
    val backgroundColor = if (enabled) {
        Color(0xFFE5E5EA) // 灰色背景，类似图中NORMAL样式
    } else {
        Color(0xFFE5E5EA).copy(alpha = 0.6f)
    }

    val contentColor = if (enabled) {
        Color(0xFF0A59F7) // 天空蓝色文字
    } else {
        Color(0xFF0A59F7).copy(alpha = 0.38f)
    }

    // 使用全局设置的圆角大小
    val cornerRadius = cardStyle.defaultCornerRadius

    Box(
        modifier = modifier
            .fillMaxWidth()
            .height(40.dp) // 更低的高度，比之前的48dp更小
            .clip(RoundedCornerShape(cornerRadius))
            .background(backgroundColor)
            .clickable(enabled = enabled) { onClick() }
            .padding(horizontal = 16.dp), // 恢复标准内边距，占满全宽
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = text,
            color = contentColor,
            style = MaterialTheme.typography.bodyLarge.copy(
                fontWeight = FontWeight.Medium
            )
        )
    }
}

/**
 * 天空蓝主题专用文字按钮组件
 *
 * 特点：
 * - 无背景
 * - 蓝色文字
 * - 适用于次要操作
 */
@Composable
fun SkyBlueTextButton(
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true
) {
    val contentColor = if (enabled) {
        Color(0xFF0A59F7) // 天空蓝色文字
    } else {
        Color(0xFF0A59F7).copy(alpha = 0.38f)
    }

    Box(
        modifier = modifier
            .fillMaxWidth()
            .height(48.dp) // 文字按钮使用较小的高度
            .clickable(enabled = enabled) { onClick() }
            .padding(horizontal = 16.dp),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = text,
            color = contentColor,
            style = MaterialTheme.typography.bodyLarge.copy(
                fontWeight = FontWeight.Medium
            )
        )
    }
}
