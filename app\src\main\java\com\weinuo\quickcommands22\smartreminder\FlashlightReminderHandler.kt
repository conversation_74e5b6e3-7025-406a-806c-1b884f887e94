package com.weinuo.quickcommands22.smartreminder

import android.content.Context
import android.hardware.camera2.CameraManager
import android.os.Handler
import android.os.Looper
import android.util.Log
import com.weinuo.quickcommands22.R
import com.weinuo.quickcommands22.model.SmartReminderType
import com.weinuo.quickcommands22.service.SmartReminderOverlayService
import com.weinuo.quickcommands22.storage.SmartReminderConfigAdapter
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * 手电筒提醒处理器
 *
 * 检测手电筒状态变化，当检测到手电筒开启时，
 * 发送智能提醒建议用户关闭手电筒以节省电量。
 *
 * 检测逻辑：
 * - 监听手电筒状态变化
 * - 检测手电筒从关闭到开启的变化
 * - 在合适的时机发送提醒通知
 *
 * 设计特点：
 * - 智能防重复：避免频繁提醒
 * - 延迟检测：给用户使用时间
 * - 状态感知：只在需要时提醒
 * - 用户友好：提供直接的操作建议
 */
class FlashlightReminderHandler(
    private val context: Context,
    private val onReminderTriggered: (Map<String, Any>) -> Unit
) {

    companion object {
        private const val TAG = "FlashlightReminder"

        // 默认配置值
        private const val DEFAULT_REMINDER_DELAY = 10000L // 10秒延迟提醒
        private const val DEFAULT_REMINDER_COOLDOWN = 60000L // 60秒冷却时间
    }

    private val configAdapter = SmartReminderConfigAdapter(context)
    private val handlerScope = CoroutineScope(SupervisorJob() + Dispatchers.Default)
    private val mainHandler = Handler(Looper.getMainLooper())

    private var cameraManager: CameraManager? = null
    private var torchCallback: CameraManager.TorchCallback? = null
    private var isMonitoring = false
    private var lastReminderTime = 0L
    private var isFlashlightOn = false

    /**
     * 启动手电筒状态监控
     */
    fun startMonitoring() {
        if (isMonitoring) {
            Log.d(TAG, "Flashlight monitoring already started")
            return
        }

        try {
            cameraManager = context.getSystemService(Context.CAMERA_SERVICE) as CameraManager

            // 检查是否有可用的摄像头
            val cameraIdList = cameraManager?.cameraIdList
            if (cameraIdList.isNullOrEmpty()) {
                Log.e(TAG, "No camera available for flashlight monitoring")
                return
            }

            // 创建手电筒状态回调
            torchCallback = object : CameraManager.TorchCallback() {
                override fun onTorchModeChanged(cameraId: String, enabled: Boolean) {
                    super.onTorchModeChanged(cameraId, enabled)
                    handleTorchModeChanged(enabled)
                }

                override fun onTorchModeUnavailable(cameraId: String) {
                    super.onTorchModeUnavailable(cameraId)
                    Log.d(TAG, "Torch mode unavailable for camera: $cameraId")
                }
            }

            // 注册回调，使用主线程Handler
            cameraManager?.registerTorchCallback(torchCallback!!, mainHandler)
            isMonitoring = true

            Log.d(TAG, "Flashlight monitoring started successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Error starting flashlight monitoring", e)
        }
    }

    /**
     * 停止手电筒状态监控
     */
    fun stopMonitoring() {
        if (!isMonitoring) {
            Log.d(TAG, "Flashlight monitoring not started")
            return
        }

        try {
            torchCallback?.let { callback ->
                cameraManager?.unregisterTorchCallback(callback)
            }

            torchCallback = null
            cameraManager = null
            isMonitoring = false
            isFlashlightOn = false

            Log.d(TAG, "Flashlight monitoring stopped")
        } catch (e: Exception) {
            Log.e(TAG, "Error stopping flashlight monitoring", e)
        }
    }

    /**
     * 处理手电筒状态变化
     */
    private fun handleTorchModeChanged(enabled: Boolean) {
        Log.d(TAG, "Torch mode changed: enabled=$enabled, previous=$isFlashlightOn")

        // 检测从关闭到开启的变化
        if (enabled && !isFlashlightOn) {
            Log.d(TAG, "Flashlight turned on, scheduling reminder")
            scheduleFlashlightReminder()
        }

        isFlashlightOn = enabled
    }

    /**
     * 安排手电筒提醒
     */
    private fun scheduleFlashlightReminder() {
        handlerScope.launch {
            try {
                // 加载配置
                val config = loadFlashlightReminderConfig()

                // 检查冷却时间
                val currentTime = System.currentTimeMillis()
                if (currentTime - lastReminderTime < config.cooldownTime * 1000L) {
                    Log.d(TAG, "Reminder in cooldown period, skipping")
                    return@launch
                }

                // 延迟提醒
                delay(config.delayTime * 1000L)

                // 再次检查手电筒是否仍然开启
                if (isFlashlightOn) {
                    Log.d(TAG, "Flashlight still on after delay, showing reminder")
                    showFlashlightReminder()
                    lastReminderTime = currentTime
                } else {
                    Log.d(TAG, "Flashlight turned off during delay, skipping reminder")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error in flashlight reminder scheduling", e)
            }
        }
    }

    /**
     * 显示手电筒提醒悬浮窗
     */
    private fun showFlashlightReminder() {
        try {
            Log.d(TAG, "Attempting to show flashlight reminder")

            val title = context.getString(R.string.flashlight_reminder_title)
            val message = "检测到手电筒已开启，建议关闭以节省电量"

            Log.d(TAG, "Calling SmartReminderOverlayService.showReminder with title: $title")

            SmartReminderOverlayService.showReminder(
                context = context,
                title = title,
                message = message,
                reminderType = "flashlight"
            )

            Log.d(TAG, "SmartReminderOverlayService.showReminder called successfully")

            // 触发回调通知
            onReminderTriggered(mapOf(
                "type" to "flashlight_reminder",
                "timestamp" to System.currentTimeMillis(),
                "message" to message
            ))
        } catch (e: Exception) {
            Log.e(TAG, "Error showing flashlight reminder", e)
        }
    }

    /**
     * 加载手电筒提醒配置
     */
    private fun loadFlashlightReminderConfig(): SmartReminderConfigAdapter.FlashlightReminderConfig {
        return try {
            val reminderTypeId = SmartReminderType.FLASHLIGHT_REMINDER.id
            if (configAdapter.hasConfig(reminderTypeId)) {
                configAdapter.loadFlashlightReminderConfig(reminderTypeId)
            } else {
                // 返回默认配置
                SmartReminderConfigAdapter.FlashlightReminderConfig()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error loading flashlight reminder config, using defaults", e)
            SmartReminderConfigAdapter.FlashlightReminderConfig()
        }
    }
}
