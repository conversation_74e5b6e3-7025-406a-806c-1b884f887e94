package com.weinuo.quickcommands22.ui.recording

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.runtime.*
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.weinuo.quickcommands22.model.TouchEvent
import com.weinuo.quickcommands22.ui.theme.QuickCommandsTheme
import kotlinx.coroutines.launch
import androidx.compose.runtime.rememberCoroutineScope

/**
 * 手势录制编辑Activity
 *
 * 提供手势录制编辑功能的独立Activity，支持：
 * - 编辑已录制的手势操作
 * - 调整延迟时间
 * - 删除和插入事件
 * - 撤销/重做功能
 * - 保存编辑结果
 */
class GestureRecordingEditActivity : ComponentActivity() {

    companion object {
        const val EXTRA_RECORDING_ID = "recording_id"
        const val EXTRA_RETURN_RESULT = "return_result"
        const val REQUEST_CODE_EDIT = 1002

        /**
         * 启动编辑界面
         */
        fun startForResult(activity: Activity, recordingId: String, requestCode: Int = REQUEST_CODE_EDIT) {
            val intent = Intent(activity, GestureRecordingEditActivity::class.java).apply {
                putExtra(EXTRA_RECORDING_ID, recordingId)
                putExtra(EXTRA_RETURN_RESULT, true)
            }
            activity.startActivityForResult(intent, requestCode)
        }

        /**
         * 启动编辑界面（不返回结果）
         */
        fun start(activity: Activity, recordingId: String) {
            val intent = Intent(activity, GestureRecordingEditActivity::class.java).apply {
                putExtra(EXTRA_RECORDING_ID, recordingId)
                putExtra(EXTRA_RETURN_RESULT, false)
            }
            activity.startActivity(intent)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        val recordingId = intent.getStringExtra(EXTRA_RECORDING_ID)
        val shouldReturnResult = intent.getBooleanExtra(EXTRA_RETURN_RESULT, false)

        if (recordingId.isNullOrEmpty()) {
            // 如果没有录制ID，直接返回错误
            setResult(Activity.RESULT_CANCELED)
            finish()
            return
        }

        setContent {
            QuickCommandsTheme {
                val navController = rememberNavController()
                var currentEditingEventIndex by remember { mutableStateOf(-1) }
                var currentEditingEvent by remember { mutableStateOf<TouchEvent?>(null) }

                // 用于管理录制数据的状态
                val scope = rememberCoroutineScope()

                // 创建ViewModel来获取录制数据
                val viewModel = remember {
                    com.weinuo.quickcommands22.ui.recording.GestureRecordingEditViewModel(this@GestureRecordingEditActivity, recordingId)
                }

                NavHost(
                    navController = navController,
                    startDestination = "edit_main"
                ) {
                    // 主编辑界面
                    composable("edit_main") {
                        GestureRecordingEditScreen(
                            recordingId = recordingId,
                            onNavigateBack = {
                                if (shouldReturnResult) {
                                    setResult(Activity.RESULT_CANCELED)
                                }
                                finish()
                            },
                            onSaveCompleted = {
                                if (shouldReturnResult) {
                                    // 返回编辑后的录制ID
                                    val resultIntent = Intent().apply {
                                        putExtra(EXTRA_RECORDING_ID, recordingId)
                                    }
                                    setResult(Activity.RESULT_OK, resultIntent)
                                } else {
                                    // 非返回结果模式，更新SharedPreferences供配置界面获取
                                    val sharedPrefs = getSharedPreferences("floating_recording_result", MODE_PRIVATE)
                                    sharedPrefs.edit()
                                        .putString("latest_recording_id", recordingId)
                                        .putLong("recording_timestamp", System.currentTimeMillis())
                                        .putBoolean("edit_completed", true)
                                        .apply()
                                }
                                finish()
                            },
                            onNavigateToActionEdit = { eventIndex ->
                                // 设置当前编辑的事件
                                val recording = viewModel.currentRecording.value
                                if (recording != null && eventIndex < recording.events.size) {
                                    currentEditingEventIndex = eventIndex
                                    currentEditingEvent = recording.events[eventIndex]
                                    navController.navigate("action_edit")
                                }
                            },
                            onNavigateToPositionPicker = { eventIndex ->
                                // 设置当前编辑的事件
                                val recording = viewModel.currentRecording.value
                                if (recording != null && eventIndex < recording.events.size) {
                                    currentEditingEventIndex = eventIndex
                                    currentEditingEvent = recording.events[eventIndex]
                                    navController.navigate("position_picker")
                                }
                            },
                            onContinueRecording = { existingEventCount ->
                                // 继续录制：启动高级悬浮窗录制，数字标记从现有数量往后顺延
                                scope.launch {
                                    try {
                                        // 启动高级悬浮窗录制服务，传递当前录制ID和事件数量
                                        val continueIntent = Intent(this@GestureRecordingEditActivity,
                                            com.weinuo.quickcommands22.floating.AdvancedFloatingRecordingService::class.java).apply {
                                            putExtra("continue_recording", true)
                                            putExtra("recording_id", recordingId)
                                            putExtra("existing_event_count", existingEventCount)
                                        }
                                        startService(continueIntent)

                                        // 最小化当前Activity，让用户可以看到底层应用
                                        moveTaskToBack(true)
                                    } catch (e: Exception) {
                                        // 如果启动服务失败，可以显示错误提示
                                    }
                                }
                            },
                            viewModel = viewModel
                        )
                    }

                    // 动作编辑界面
                    composable("action_edit") {
                        currentEditingEvent?.let { event ->
                            ActionEditScreen(
                                action = event,
                                onActionUpdated = { updatedEvent ->
                                    // 更新事件
                                    if (currentEditingEventIndex >= 0) {
                                        android.util.Log.d("GestureEditActivity", "准备更新事件 $currentEditingEventIndex")
                                        android.util.Log.d("GestureEditActivity", "新延迟时间: ${updatedEvent.delayAfter}ms")
                                        viewModel.updateEvent(currentEditingEventIndex, updatedEvent)
                                        // 同步更新currentEditingEvent以保持数据一致性
                                        currentEditingEvent = updatedEvent
                                        android.util.Log.d("GestureEditActivity", "事件更新完成")
                                    }
                                    navController.popBackStack()
                                },
                                onNavigateBack = {
                                    navController.popBackStack()
                                },
                                onPositionPicker = {
                                    navController.navigate("position_picker")
                                }
                            )
                        }
                    }

                    // 位置选择界面
                    composable("position_picker") {
                        currentEditingEvent?.let { event ->
                            PositionPickerScreen(
                                currentPosition = event.position,
                                actionType = event.type,
                                onPositionSelected = { newPosition ->
                                    // 更新事件位置
                                    if (currentEditingEventIndex >= 0 && currentEditingEvent != null) {
                                        val updatedEvent = currentEditingEvent!!.copy(position = newPosition)
                                        viewModel.updateEvent(currentEditingEventIndex, updatedEvent)
                                        // 同步更新currentEditingEvent以保持数据一致性
                                        currentEditingEvent = updatedEvent
                                    }
                                    navController.popBackStack()
                                },
                                onNavigateBack = {
                                    navController.popBackStack()
                                }
                            )
                        }
                    }
                }
            }
        }
    }
}
