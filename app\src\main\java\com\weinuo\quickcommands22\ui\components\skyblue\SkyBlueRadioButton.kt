package com.weinuo.quickcommands22.ui.components.skyblue

import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.size
import androidx.compose.material3.ripple
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.unit.dp
import com.weinuo.quickcommands22.R
import com.weinuo.quickcommands22.ui.theme.config.RadioButtonConfig

/**
 * 天空蓝主题专用RadioButton组件 - 圆形实心蓝色勾选样式
 *
 * 特点：
 * - 选中状态：使用SVG Vector Drawable显示实心天空蓝色圆形和白色勾选标记
 * - 未选中状态：使用SVG Vector Drawable显示空心圆形边框（天空蓝色边框）
 * - 禁用状态：灰色样式
 * - 平滑的动画过渡效果
 * - 支持点击涟漪效果
 * - 符合天空蓝主题的整合设计风格
 * - 使用专业设计的SVG图标，视觉效果更佳
 */
@Composable
fun SkyBlueRadioButton(
    config: RadioButtonConfig,
    modifier: Modifier = Modifier
) {
    // 天空蓝主题颜色定义
    val skyBlueColor = Color(0xFF0A59F7)        // 天空蓝色

    // RadioButton尺寸
    val radioButtonSize = 20.dp

    // 动画状态
    val animationDuration = 200

    // 选中状态图标透明度动画
    val selectedIconAlpha by animateFloatAsState(
        targetValue = if (config.selected && config.enabled) 1f else 0f,
        animationSpec = tween(animationDuration),
        label = "selected_icon_alpha"
    )

    // 未选中状态图标透明度动画
    val unselectedIconAlpha by animateFloatAsState(
        targetValue = if (!config.selected && config.enabled) 1f else 0f,
        animationSpec = tween(animationDuration),
        label = "unselected_icon_alpha"
    )

    val interactionSource = remember { MutableInteractionSource() }

    Box(
        modifier = modifier
            .size(radioButtonSize)
            .clickable(
                interactionSource = interactionSource,
                indication = ripple(
                    bounded = true,
                    radius = radioButtonSize / 2,
                    color = skyBlueColor
                ),
                enabled = config.enabled,
                onClick = { config.onClick?.invoke() }
            ),
        contentAlignment = Alignment.Center
    ) {
        // 未选中状态：显示空心圆形边框SVG图标
        Icon(
            imageVector = ImageVector.vectorResource(R.drawable.ic_radio_button_unchecked_sky_blue),
            contentDescription = null,
            modifier = Modifier
                .size(radioButtonSize)
                .alpha(unselectedIconAlpha),
            tint = Color.Unspecified // 使用SVG原始颜色
        )

        // 选中状态：显示实心圆形和勾选标记SVG图标
        Icon(
            imageVector = ImageVector.vectorResource(R.drawable.ic_radio_button_checked_sky_blue),
            contentDescription = null,
            modifier = Modifier
                .size(radioButtonSize)
                .alpha(selectedIconAlpha),
            tint = Color.Unspecified // 使用SVG原始颜色
        )
    }
}
