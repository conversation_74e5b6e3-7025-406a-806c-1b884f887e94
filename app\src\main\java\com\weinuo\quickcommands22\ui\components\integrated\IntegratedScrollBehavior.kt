package com.weinuo.quickcommands22.ui.components.integrated

import androidx.compose.animation.core.DecayAnimationSpec
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.TopAppBarScrollBehavior
import androidx.compose.material3.TopAppBarState
import androidx.compose.runtime.*
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.input.nestedscroll.NestedScrollConnection
import androidx.compose.ui.input.nestedscroll.NestedScrollSource
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.Velocity
import androidx.compose.ui.unit.dp
import kotlin.math.abs

/**
 * 整合设计专用的自定义TopAppBar滚动行为
 * 
 * 特点：
 * - 完全自定义的滚动逻辑，不依赖MD3 TopAppBar
 * - 支持iOS风格的模糊效果
 * - 流畅的折叠动画
 * - 与NestedScrollConnection完美集成
 */
@OptIn(ExperimentalMaterial3Api::class)
class IntegratedTopAppBarScrollBehavior(
    override val state: TopAppBarState,
    val canScroll: () -> Boolean = { true }
) : TopAppBarScrollBehavior {

    override val flingAnimationSpec: DecayAnimationSpec<Float>? = null
    override val snapAnimationSpec: androidx.compose.animation.core.AnimationSpec<Float>? = null
    override val isPinned: Boolean = false

    override val nestedScrollConnection = object : NestedScrollConnection {
        override fun onPreScroll(available: Offset, source: NestedScrollSource): Offset {
            // 实现同时滚动：标题栏和内容按比例分配滚动距离
            if (!canScroll()) return Offset.Zero

            val delta = available.y

            // 向上滚动时：标题栏折叠和内容滚动同时进行
            if (delta < 0 && state.heightOffset > state.heightOffsetLimit) {
                // 计算标题栏可以消费的最大距离
                val maxConsumable = state.heightOffset - state.heightOffsetLimit

                // 使用50%的分配比例：标题栏消费50%，内容消费50%
                val topBarConsumption = (delta * 0.5f).coerceAtLeast(-maxConsumable)

                val newOffset = (state.heightOffset + topBarConsumption).coerceIn(
                    minimumValue = state.heightOffsetLimit,
                    maximumValue = 0f
                )

                val actualConsumed = newOffset - state.heightOffset
                state.heightOffset = newOffset

                return if (abs(actualConsumed) > 0.5f) {
                    Offset(0f, actualConsumed)
                } else {
                    Offset.Zero
                }
            }

            // 向下滚动时：标题栏展开和内容滚动同时进行
            if (delta > 0 && state.heightOffset < 0f) {
                // 计算标题栏可以展开的最大距离
                val maxExpandable = -state.heightOffset

                // 使用50%的分配比例：标题栏消费50%，内容消费50%
                val topBarConsumption = (delta * 0.5f).coerceAtMost(maxExpandable)

                val newOffset = (state.heightOffset + topBarConsumption).coerceIn(
                    minimumValue = state.heightOffsetLimit,
                    maximumValue = 0f
                )

                val actualConsumed = newOffset - state.heightOffset
                state.heightOffset = newOffset

                return if (abs(actualConsumed) > 0.5f) {
                    Offset(0f, actualConsumed)
                } else {
                    Offset.Zero
                }
            }

            return Offset.Zero
        }

        override fun onPostScroll(
            consumed: Offset,
            available: Offset,
            source: NestedScrollSource
        ): Offset {
            // onPreScroll已经处理了大部分情况，这里只处理边界情况
            if (!canScroll()) return Offset.Zero

            val delta = available.y

            // 处理剩余的滚动距离（如果有的话）
            if (abs(delta) > 0.5f) {
                val newOffset = (state.heightOffset + delta).coerceIn(
                    minimumValue = state.heightOffsetLimit,
                    maximumValue = 0f
                )

                val consumedDelta = newOffset - state.heightOffset
                state.heightOffset = newOffset

                return if (abs(consumedDelta) > 0.5f) {
                    Offset(0f, consumedDelta)
                } else {
                    Offset.Zero
                }
            }

            return Offset.Zero
        }

        override suspend fun onPreFling(available: Velocity): Velocity {
            return if (state.heightOffset < 0 && state.heightOffset > state.heightOffsetLimit) {
                // 在中间状态时，根据速度决定是展开还是折叠
                if (available.y < 0) {
                    // 向上滑动，折叠
                    state.heightOffset = state.heightOffsetLimit
                } else {
                    // 向下滑动，展开
                    state.heightOffset = 0f
                }
                available
            } else {
                Velocity.Zero
            }
        }

        override suspend fun onPostFling(consumed: Velocity, available: Velocity): Velocity {
            return Velocity.Zero
        }
    }
}

/**
 * 创建整合设计专用的TopAppBar滚动行为
 *
 * @param canScroll 是否允许滚动的回调
 * @return 自定义的滚动行为实例
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun rememberIntegratedTopAppBarScrollBehavior(
    canScroll: () -> Boolean = { true }
): IntegratedTopAppBarScrollBehavior {
    val density = LocalDensity.current
    
    // 计算高度偏移限制：展开高度152dp - 折叠高度64dp = 88dp
    val heightOffsetLimit = with(density) { -88.dp.toPx() }
    
    val state = remember {
        TopAppBarState(
            initialHeightOffsetLimit = heightOffsetLimit,
            initialHeightOffset = 0f,
            initialContentOffset = 0f
        )
    }
    
    return remember(state, canScroll) {
        IntegratedTopAppBarScrollBehavior(state, canScroll)
    }
}

/**
 * 获取折叠进度
 *
 * @return 折叠进度，0f表示完全展开，1f表示完全折叠
 */
@OptIn(ExperimentalMaterial3Api::class)
val TopAppBarState.collapsedFraction: Float
    get() {
        return if (heightOffsetLimit != 0f) {
            (heightOffset / heightOffsetLimit).coerceIn(0f, 1f)
        } else {
            0f
        }
    }

/**
 * 获取当前高度偏移的dp值
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TopAppBarState.getHeightOffsetDp(): androidx.compose.ui.unit.Dp {
    val density = LocalDensity.current
    return with(density) { heightOffset.toDp() }
}


