package com.weinuo.quickcommands22.ui.screens.themed

/**
 * 主题感知界面组件统一入口
 *
 * 这个文件提供了所有主题感知界面组件的统一访问点，
 * 便于管理和使用主题感知的界面系统。
 *
 * 主题感知界面系统的核心特性：
 * 1. **完全解耦**：每个主题都有独立的界面实现
 * 2. **功能独立**：可以为不同主题添加专有功能
 * 3. **布局自由**：每个主题可以有完全不同的布局
 * 4. **维护简单**：修改一个主题的界面不影响其他主题
 * 5. **扩展性强**：添加新主题时只需创建对应的界面文件
 *
 * 使用方式：
 * ```kotlin
 * // 在导航配置中使用主题感知组件
 * composable("quick_commands") {
 *     ThemedQuickCommandsScreen(navController, repository, shortcutManager)
 * }
 * ```
 *
 * 架构说明：
 * - ThemedXxxScreen：主题感知的界面路由组件
 * - XxxScreen：海洋蓝主题专用界面（现有文件）
 * - SkyBlueXxxScreen：天空蓝主题专用界面（复制的文件）
 * - ScreenFactory：主题界面工厂，负责创建对应主题的界面
 */

// 重新导出所有主题感知界面组件，提供统一的访问点

/**
 * 主题感知的快捷指令界面
 *
 * 根据当前主题选择：
 * - 海洋蓝：OceanBlueQuickCommandsScreen（分层设计）
 * - 天空蓝：SkyBlueQuickCommandsScreen（整合设计）
 */
// ThemedQuickCommandsScreen 已在单独文件中定义

/**
 * 主题感知的全局设置界面
 *
 * 根据当前主题选择：
 * - 海洋蓝：OceanBlueGlobalSettingsScreen（标准设置）
 * - 天空蓝：SkyBlueGlobalSettingsScreen（包含模糊效果设置）
 */
// ThemedGlobalSettingsScreen 已在单独文件中定义

/**
 * 主题感知的命令模板界面
 *
 * 根据当前主题选择：
 * - 海洋蓝：OceanBlueCommandTemplatesScreen（分层设计）
 * - 天空蓝：SkyBlueCommandTemplatesScreen（整合设计）
 */
// ThemedCommandTemplatesScreen 已在单独文件中定义

/**
 * 主题感知的智能提醒界面
 *
 * 根据当前主题选择：
 * - 海洋蓝：OceanBlueSmartRemindersScreen（分层设计）
 * - 天空蓝：SkyBlueSmartRemindersScreen（整合设计）
 */
// ThemedSmartRemindersScreen 已在单独文件中定义

/**
 * 主题感知界面系统版本信息
 */
object ThemedScreensInfo {
    const val VERSION = "1.0.0"
    const val DESCRIPTION = "主题感知界面系统 - 支持完全解耦的主题界面实现"
    
    /**
     * 获取支持的主题列表
     */
    val supportedThemes = listOf(
        "ocean_blue" to "海洋蓝主题（分层设计）",
        "sky_blue" to "天空蓝主题（整合设计）"
    )
    
    /**
     * 获取支持的界面列表
     */
    val supportedScreens = listOf(
        "quick_commands" to "快捷指令界面",
        "global_settings" to "全局设置界面",
        "command_templates" to "命令模板界面",
        "smart_reminders" to "智能提醒界面"
    )
}
