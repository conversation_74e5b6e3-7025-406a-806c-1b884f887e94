package com.weinuo.quickcommands22.ui.components.themed

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.sp
import com.weinuo.quickcommands22.model.CommandTemplate
import com.weinuo.quickcommands22.ui.theme.manager.LocalThemeContext
import com.weinuo.quickcommands22.ui.theme.manager.ThemeManager
import com.weinuo.quickcommands22.ui.theme.skyblue.SkyBlueStyleConfiguration
import com.weinuo.quickcommands22.data.SettingsRepository

/**
 * 主题感知的指令模板卡片组件
 *
 * 特点：
 * - 使用主题配置的样式，而非硬编码值
 * - 支持主题切换时的实时样式更新
 * - 保持与原CommandTemplateCard完全相同的功能
 * - 根据主题自动调整圆角、间距、阴影等样式
 *
 * 简洁的设计：
 * - 第一行显示模板标题
 * - 第二行显示模板描述
 * - 点击后导航到预填充的快捷指令创建界面
 *
 * @param template 模板数据
 * @param onClick 点击回调
 * @param modifier 修饰符
 */
@Composable
fun ThemedCommandTemplateCard(
    template: CommandTemplate,
    onClick: (CommandTemplate) -> Unit,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    val themeContext = LocalThemeContext.current
    val themeManager = remember { ThemeManager.getInstance(context) }

    // 获取动态卡片样式配置（仅在天空蓝主题下使用）
    val cardStyle = if (themeManager.getCurrentThemeId() == "sky_blue") {
        val settingsRepository = remember { SettingsRepository(context) }
        SkyBlueStyleConfiguration.getDynamicCardStyleConfig(settingsRepository)
    } else {
        themeContext.styleConfiguration.cardStyle
    }

    // 获取全局设置以使用动态字体大小（仅在天空蓝主题下使用）
    val settingsRepository = remember { SettingsRepository(context) }
    val globalSettings by settingsRepository.globalSettings.collectAsState()

    // 主题感知的字体样式配置
    val titleStyle = if (themeManager.getCurrentThemeId() == "sky_blue") {
        // 天空蓝主题：使用全局设置的字体大小和字重
        MaterialTheme.typography.titleMedium.copy(
            fontSize = globalSettings.cardTitleFontSize.sp,
            fontWeight = when (globalSettings.cardTitleFontWeight) {
                "normal" -> FontWeight.Normal
                "medium" -> FontWeight.Medium
                "bold" -> FontWeight.Bold
                else -> FontWeight.Medium
            }
        )
    } else {
        // 海洋蓝主题：使用原始固定样式
        MaterialTheme.typography.titleMedium
    }

    val contentStyle = if (themeManager.getCurrentThemeId() == "sky_blue") {
        // 天空蓝主题：使用全局设置的字体大小和字重
        MaterialTheme.typography.bodyMedium.copy(
            fontSize = globalSettings.cardContentFontSize.sp,
            fontWeight = when (globalSettings.cardContentFontWeight) {
                "normal" -> FontWeight.Normal
                "medium" -> FontWeight.Medium
                "bold" -> FontWeight.Bold
                else -> FontWeight.Medium
            }
        )
    } else {
        // 海洋蓝主题：使用原始固定样式
        MaterialTheme.typography.bodyMedium
    }

    // 根据主题获取样式配置
    val cornerRadius = cardStyle.defaultCornerRadius
    val paddingValues = cardStyle.getPaddingValues()
    val elevation = cardStyle.defaultElevation

    Card(
        modifier = modifier
            .fillMaxWidth()
            .clickable { onClick(template) },
        shape = RoundedCornerShape(cornerRadius),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceContainerLow
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = elevation
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(paddingValues),
            verticalArrangement = Arrangement.spacedBy(cardStyle.contentVerticalSpacing)
        ) {
            // 第一行：模板标题
            Text(
                text = template.getLocalizedTitle(context),
                style = titleStyle,
                color = MaterialTheme.colorScheme.onSurface,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )

            // 第二行：模板描述
            Text(
                text = template.getLocalizedDescription(context),
                style = contentStyle,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                maxLines = 2,
                overflow = TextOverflow.Ellipsis
            )
        }
    }
}



/**
 * 主题感知的指令模板卡片组件 - 简化版本
 *
 * 只显示标题和描述，不显示标签，适用于空间受限的场景
 */
@Composable
fun ThemedCommandTemplateCardSimple(
    template: CommandTemplate,
    onClick: (CommandTemplate) -> Unit,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    val themeContext = LocalThemeContext.current
    val themeManager = remember { ThemeManager.getInstance(context) }

    // 获取动态卡片样式配置（仅在天空蓝主题下使用）
    val cardStyle = if (themeManager.getCurrentThemeId() == "sky_blue") {
        val settingsRepository = remember { SettingsRepository(context) }
        SkyBlueStyleConfiguration.getDynamicCardStyleConfig(settingsRepository)
    } else {
        themeContext.styleConfiguration.cardStyle
    }

    // 获取全局设置以使用动态字体大小（仅在天空蓝主题下使用）
    val settingsRepository = remember { SettingsRepository(context) }
    val globalSettings by settingsRepository.globalSettings.collectAsState()

    // 主题感知的字体样式配置
    val titleStyle = if (themeManager.getCurrentThemeId() == "sky_blue") {
        // 天空蓝主题：使用全局设置的字体大小和字重
        MaterialTheme.typography.titleMedium.copy(
            fontSize = globalSettings.cardTitleFontSize.sp,
            fontWeight = when (globalSettings.cardTitleFontWeight) {
                "normal" -> FontWeight.Normal
                "medium" -> FontWeight.Medium
                "bold" -> FontWeight.Bold
                else -> FontWeight.Medium
            }
        )
    } else {
        // 海洋蓝主题：使用原始固定样式
        MaterialTheme.typography.titleMedium
    }

    val contentStyle = if (themeManager.getCurrentThemeId() == "sky_blue") {
        // 天空蓝主题：使用全局设置的字体大小和字重
        MaterialTheme.typography.bodyMedium.copy(
            fontSize = globalSettings.cardContentFontSize.sp,
            fontWeight = when (globalSettings.cardContentFontWeight) {
                "normal" -> FontWeight.Normal
                "medium" -> FontWeight.Medium
                "bold" -> FontWeight.Bold
                else -> FontWeight.Medium
            }
        )
    } else {
        // 海洋蓝主题：使用原始固定样式
        MaterialTheme.typography.bodyMedium
    }

    // 根据主题获取样式配置
    val cornerRadius = cardStyle.defaultCornerRadius
    val paddingValues = cardStyle.getPaddingValues()
    val elevation = cardStyle.defaultElevation

    Card(
        modifier = modifier
            .fillMaxWidth()
            .clickable { onClick(template) },
        shape = RoundedCornerShape(cornerRadius),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceContainerLow
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = elevation
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(paddingValues),
            verticalArrangement = Arrangement.spacedBy(cardStyle.contentVerticalSpacing)
        ) {
            // 模板标题
            Text(
                text = template.getLocalizedTitle(context),
                style = titleStyle,
                color = MaterialTheme.colorScheme.onSurface,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )

            // 模板描述
            Text(
                text = template.getLocalizedDescription(context),
                style = contentStyle,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                maxLines = 2,
                overflow = TextOverflow.Ellipsis
            )
        }
    }
}
