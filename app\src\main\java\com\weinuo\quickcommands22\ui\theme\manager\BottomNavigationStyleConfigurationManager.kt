package com.weinuo.quickcommands22.ui.theme.manager

import android.content.Context
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.runtime.*
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.weinuo.quickcommands22.ui.theme.config.BottomNavigationStyleConfig
import com.weinuo.quickcommands22.data.SettingsRepository

/**
 * 底部导航栏样式配置管理器
 *
 * 负责管理应用中底部导航栏样式的配置，包括：
 * - 从全局设置中获取用户自定义配置
 * - 底部导航栏样式配置的实时更新
 * - 配置的状态管理
 * - 配置的验证和同步
 *
 * 参照CardStyleConfigurationManager的实现方式，
 * 确保与其他设置项保持一致的存储和同步机制。
 */
class BottomNavigationStyleConfigurationManager private constructor(
    private val context: Context,
    private val settingsRepository: SettingsRepository
) {

    companion object {
        @Volatile
        private var INSTANCE: BottomNavigationStyleConfigurationManager? = null

        /**
         * 获取底部导航栏样式配置管理器实例
         */
        fun getInstance(context: Context, settingsRepository: SettingsRepository): BottomNavigationStyleConfigurationManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: BottomNavigationStyleConfigurationManager(context.applicationContext, settingsRepository).also { INSTANCE = it }
            }
        }

        /**
         * 获取底部导航栏样式配置管理器实例（兼容旧版本）
         */
        fun getInstance(context: Context): BottomNavigationStyleConfigurationManager {
            return getInstance(context, SettingsRepository(context))
        }
    }

    /**
     * 获取当前底部导航栏样式配置
     *
     * 从GlobalSettings中读取用户自定义的底部导航栏样式配置
     */
    @Composable
    fun getBottomNavigationStyleConfiguration(): BottomNavigationStyleConfig {
        val globalSettings by settingsRepository.globalSettings.collectAsState()
        
        return BottomNavigationStyleConfig(
            // 基础尺寸参数
            height = globalSettings.bottomNavHeight.dp,
            horizontalPadding = globalSettings.bottomNavHorizontalPadding.dp,
            verticalPadding = globalSettings.bottomNavVerticalPadding.dp,
            
            // 导航项样式参数
            itemCornerRadius = globalSettings.bottomNavItemCornerRadius.dp,
            itemOuterPadding = globalSettings.bottomNavItemOuterPadding.dp,
            itemVerticalPadding = globalSettings.bottomNavItemVerticalPadding.dp,
            itemHorizontalPadding = globalSettings.bottomNavItemHorizontalPadding.dp,
            
            // 图标文字参数
            iconSize = globalSettings.bottomNavIconSize.dp,
            iconTextSpacing = globalSettings.bottomNavIconTextSpacing.dp,
            textFontSize = globalSettings.bottomNavTextFontSize.sp,
            
            // 字重参数
            selectedFontWeight = when (globalSettings.bottomNavSelectedFontWeight) {
                "normal" -> FontWeight.Normal
                "medium" -> FontWeight.Medium
                "bold" -> FontWeight.Bold
                else -> FontWeight.Medium
            },
            unselectedFontWeight = when (globalSettings.bottomNavUnselectedFontWeight) {
                "normal" -> FontWeight.Normal
                "medium" -> FontWeight.Medium
                "bold" -> FontWeight.Bold
                else -> FontWeight.Normal
            },
            
            // 动画参数
            colorAnimationDuration = globalSettings.bottomNavColorAnimationDuration,
            backgroundAnimationDuration = globalSettings.bottomNavBackgroundAnimationDuration,
            
            // 布局参数
            itemArrangement = when (globalSettings.bottomNavItemArrangement) {
                "spaceEvenly" -> Arrangement.SpaceEvenly
                "spaceBetween" -> Arrangement.SpaceBetween
                "spaceAround" -> Arrangement.SpaceAround
                else -> Arrangement.SpaceEvenly
            }
        )
    }

    // 基础尺寸参数更新方法
    
    /**
     * 更新底部导航栏高度
     */
    fun updateHeight(height: Int) {
        val currentSettings = settingsRepository.globalSettings.value
        val newSettings = currentSettings.copy(bottomNavHeight = height)
        settingsRepository.saveGlobalSettings(newSettings)
    }

    /**
     * 更新底部导航栏水平内边距
     */
    fun updateHorizontalPadding(padding: Int) {
        val currentSettings = settingsRepository.globalSettings.value
        val newSettings = currentSettings.copy(bottomNavHorizontalPadding = padding)
        settingsRepository.saveGlobalSettings(newSettings)
    }

    /**
     * 更新底部导航栏垂直内边距
     */
    fun updateVerticalPadding(padding: Int) {
        val currentSettings = settingsRepository.globalSettings.value
        val newSettings = currentSettings.copy(bottomNavVerticalPadding = padding)
        settingsRepository.saveGlobalSettings(newSettings)
    }

    // 导航项样式参数更新方法
    
    /**
     * 更新导航项圆角大小
     */
    fun updateItemCornerRadius(radius: Int) {
        val currentSettings = settingsRepository.globalSettings.value
        val newSettings = currentSettings.copy(bottomNavItemCornerRadius = radius)
        settingsRepository.saveGlobalSettings(newSettings)
    }

    /**
     * 更新导航项外边距
     */
    fun updateItemOuterPadding(padding: Int) {
        val currentSettings = settingsRepository.globalSettings.value
        val newSettings = currentSettings.copy(bottomNavItemOuterPadding = padding)
        settingsRepository.saveGlobalSettings(newSettings)
    }

    /**
     * 更新导航项垂直内边距
     */
    fun updateItemVerticalPadding(padding: Int) {
        val currentSettings = settingsRepository.globalSettings.value
        val newSettings = currentSettings.copy(bottomNavItemVerticalPadding = padding)
        settingsRepository.saveGlobalSettings(newSettings)
    }

    /**
     * 更新导航项水平内边距
     */
    fun updateItemHorizontalPadding(padding: Int) {
        val currentSettings = settingsRepository.globalSettings.value
        val newSettings = currentSettings.copy(bottomNavItemHorizontalPadding = padding)
        settingsRepository.saveGlobalSettings(newSettings)
    }

    // 图标文字参数更新方法
    
    /**
     * 更新图标大小
     */
    fun updateIconSize(size: Int) {
        val currentSettings = settingsRepository.globalSettings.value
        val newSettings = currentSettings.copy(bottomNavIconSize = size)
        settingsRepository.saveGlobalSettings(newSettings)
    }

    /**
     * 更新图标与文字间距
     */
    fun updateIconTextSpacing(spacing: Int) {
        val currentSettings = settingsRepository.globalSettings.value
        val newSettings = currentSettings.copy(bottomNavIconTextSpacing = spacing)
        settingsRepository.saveGlobalSettings(newSettings)
    }

    /**
     * 更新文字字体大小
     */
    fun updateTextFontSize(size: Int) {
        val currentSettings = settingsRepository.globalSettings.value
        val newSettings = currentSettings.copy(bottomNavTextFontSize = size)
        settingsRepository.saveGlobalSettings(newSettings)
    }

    // 字重参数更新方法
    
    /**
     * 更新选中状态字重
     */
    fun updateSelectedFontWeight(fontWeight: String) {
        val currentSettings = settingsRepository.globalSettings.value
        val newSettings = currentSettings.copy(bottomNavSelectedFontWeight = fontWeight)
        settingsRepository.saveGlobalSettings(newSettings)
    }

    /**
     * 更新未选中状态字重
     */
    fun updateUnselectedFontWeight(fontWeight: String) {
        val currentSettings = settingsRepository.globalSettings.value
        val newSettings = currentSettings.copy(bottomNavUnselectedFontWeight = fontWeight)
        settingsRepository.saveGlobalSettings(newSettings)
    }

    // 动画参数更新方法
    
    /**
     * 更新颜色动画时长
     */
    fun updateColorAnimationDuration(duration: Int) {
        val currentSettings = settingsRepository.globalSettings.value
        val newSettings = currentSettings.copy(bottomNavColorAnimationDuration = duration)
        settingsRepository.saveGlobalSettings(newSettings)
    }

    /**
     * 更新背景动画时长
     */
    fun updateBackgroundAnimationDuration(duration: Int) {
        val currentSettings = settingsRepository.globalSettings.value
        val newSettings = currentSettings.copy(bottomNavBackgroundAnimationDuration = duration)
        settingsRepository.saveGlobalSettings(newSettings)
    }

    // 布局参数更新方法
    
    /**
     * 更新导航项排列方式
     */
    fun updateItemArrangement(arrangement: String) {
        val currentSettings = settingsRepository.globalSettings.value
        val newSettings = currentSettings.copy(bottomNavItemArrangement = arrangement)
        settingsRepository.saveGlobalSettings(newSettings)
    }

    /**
     * 批量更新底部导航栏样式配置
     *
     * 用于一次性更新多个配置项，避免多次触发设置保存
     */
    fun updateBottomNavigationStyleConfiguration(
        height: Int? = null,
        horizontalPadding: Int? = null,
        verticalPadding: Int? = null,
        itemCornerRadius: Int? = null,
        itemOuterPadding: Int? = null,
        itemVerticalPadding: Int? = null,
        itemHorizontalPadding: Int? = null,
        iconSize: Int? = null,
        iconTextSpacing: Int? = null,
        textFontSize: Int? = null,
        selectedFontWeight: String? = null,
        unselectedFontWeight: String? = null,
        colorAnimationDuration: Int? = null,
        backgroundAnimationDuration: Int? = null,
        itemArrangement: String? = null
    ) {
        val currentSettings = settingsRepository.globalSettings.value
        val newSettings = currentSettings.copy(
            bottomNavHeight = height ?: currentSettings.bottomNavHeight,
            bottomNavHorizontalPadding = horizontalPadding ?: currentSettings.bottomNavHorizontalPadding,
            bottomNavVerticalPadding = verticalPadding ?: currentSettings.bottomNavVerticalPadding,
            bottomNavItemCornerRadius = itemCornerRadius ?: currentSettings.bottomNavItemCornerRadius,
            bottomNavItemOuterPadding = itemOuterPadding ?: currentSettings.bottomNavItemOuterPadding,
            bottomNavItemVerticalPadding = itemVerticalPadding ?: currentSettings.bottomNavItemVerticalPadding,
            bottomNavItemHorizontalPadding = itemHorizontalPadding ?: currentSettings.bottomNavItemHorizontalPadding,
            bottomNavIconSize = iconSize ?: currentSettings.bottomNavIconSize,
            bottomNavIconTextSpacing = iconTextSpacing ?: currentSettings.bottomNavIconTextSpacing,
            bottomNavTextFontSize = textFontSize ?: currentSettings.bottomNavTextFontSize,
            bottomNavSelectedFontWeight = selectedFontWeight ?: currentSettings.bottomNavSelectedFontWeight,
            bottomNavUnselectedFontWeight = unselectedFontWeight ?: currentSettings.bottomNavUnselectedFontWeight,
            bottomNavColorAnimationDuration = colorAnimationDuration ?: currentSettings.bottomNavColorAnimationDuration,
            bottomNavBackgroundAnimationDuration = backgroundAnimationDuration ?: currentSettings.bottomNavBackgroundAnimationDuration,
            bottomNavItemArrangement = itemArrangement ?: currentSettings.bottomNavItemArrangement
        )
        settingsRepository.saveGlobalSettings(newSettings)
    }

    /**
     * 重置底部导航栏样式配置为默认值
     */
    fun resetToDefaults() {
        updateBottomNavigationStyleConfiguration(
            height = 80,
            horizontalPadding = 16,
            verticalPadding = 7,
            itemCornerRadius = 16,
            itemOuterPadding = 4,
            itemVerticalPadding = 8,
            itemHorizontalPadding = 12,
            iconSize = 24,
            iconTextSpacing = 4,
            textFontSize = 11,
            selectedFontWeight = "medium",
            unselectedFontWeight = "normal",
            colorAnimationDuration = 150,
            backgroundAnimationDuration = 150,
            itemArrangement = "spaceEvenly"
        )
    }
}
