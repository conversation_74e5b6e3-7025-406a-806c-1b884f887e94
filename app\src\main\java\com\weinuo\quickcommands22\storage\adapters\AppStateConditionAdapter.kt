package com.weinuo.quickcommands22.storage.adapters

import android.util.Log
import com.weinuo.quickcommands22.model.SimpleAppInfo
import com.weinuo.quickcommands22.model.*
import com.weinuo.quickcommands22.storage.AppListStorageEngine
import com.weinuo.quickcommands22.storage.NativeTypeStorageManager
import com.weinuo.quickcommands22.storage.StorageDomain
import com.weinuo.quickcommands22.storage.StorageOperation

/**
 * 应用状态条件存储适配器
 *
 * 负责AppStateCondition的原生数据类型存储和重建。
 * 将复杂的应用状态条件对象拆分为独立的原生字段进行存储，
 * 完全避免JSON序列化问题。
 *
 * 支持的字段类型：
 * - 基础字段：id、categoryType、stateType、detectionMode等
 * - 应用信息：targetPackageName、targetAppName
 * - 后台时间相关：selectedApps、backgroundTimeThresholdMinutes等
 * - Tasker/Locale插件相关：pluginAction、pluginExtras等
 *
 * 存储格式示例：
 * condition_{id}_type = "app_state"
 * condition_{id}_category_type = "STATE_CHANGE"
 * condition_{id}_state_type = "FOREGROUND"
 * condition_{id}_detection_mode = "SPECIFIC_APP"
 * condition_{id}_target_package_name = "com.example.app"
 * condition_{id}_selected_apps = 通过AppListStorageEngine存储
 *
 * @param storageManager 原生类型存储管理器
 * @param appListEngine 应用列表存储引擎
 */
class AppStateConditionAdapter(
    storageManager: NativeTypeStorageManager,
    appListEngine: AppListStorageEngine
) : BaseConditionAdapter<AppStateCondition>(storageManager, appListEngine) {

    companion object {
        private const val TAG = "AppStateConditionAdapter"

        // 字段名常量
        private const val FIELD_CATEGORY_TYPE = "category_type"
        private const val FIELD_STATE_TYPE = "state_type"
        private const val FIELD_DETECTION_MODE = "detection_mode"
        private const val FIELD_TARGET_PACKAGE_NAME = "target_package_name"
        private const val FIELD_TARGET_APP_NAME = "target_app_name"

        // 后台时间相关字段
        private const val FIELD_SELECTED_APPS = "selected_apps"
        private const val FIELD_BACKGROUND_TIME_THRESHOLD_MINUTES = "background_time_threshold_minutes"
        private const val FIELD_TRIGGER_MODE = "trigger_mode"
        private const val FIELD_SKIP_FOREGROUND_APP = "skip_foreground_app"
        private const val FIELD_SKIP_MUSIC_PLAYING_APP = "skip_music_playing_app"
        private const val FIELD_SKIP_VPN_APP = "skip_vpn_app"
        private const val FIELD_SELECTED_VPN_APPS = "selected_vpn_apps"
        private const val FIELD_AUTO_INCLUDE_NEW_APPS = "auto_include_new_apps"

        // Tasker/Locale插件相关字段
        private const val FIELD_PLUGIN_ACTION = "plugin_action"
        private const val FIELD_PLUGIN_EXTRAS = "plugin_extras"
        private const val FIELD_EXPECTED_STATE = "expected_state"
        private const val FIELD_CHECK_INTERVAL = "check_interval"
        private const val FIELD_TIMEOUT_SECONDS = "timeout_seconds"

        // 检测频率字段
        private const val FIELD_CHECK_FREQUENCY = "check_frequency"
        private const val FIELD_ENABLE_CUSTOM_CHECK_FREQUENCY = "enable_custom_check_frequency"
        private const val FIELD_CUSTOM_CHECK_FREQUENCY_SECONDS = "custom_check_frequency_seconds"
        private const val FIELD_BACKGROUND_TIME_CHECK_FREQUENCY = "background_time_check_frequency"
        private const val FIELD_ENABLE_CUSTOM_BACKGROUND_TIME_CHECK_FREQUENCY = "enable_custom_background_time_check_frequency"
        private const val FIELD_CUSTOM_BACKGROUND_TIME_CHECK_FREQUENCY_SECONDS = "custom_background_time_check_frequency_seconds"

        // 界面交互相关字段
        private const val FIELD_CLICK_TARGET_TEXT = "click_target_text"
        private const val FIELD_SCREEN_CONTENT_TEXT = "screen_content_text"
        private const val FIELD_SCREEN_CONTENT_TRIGGER_MODE = "screen_content_trigger_mode"
        private const val FIELD_CASE_SENSITIVE = "case_sensitive"
        private const val FIELD_USE_REGEX = "use_regex"
        private const val FIELD_INTERFACE_INTERACTION_TYPE = "interface_interaction_type"
        private const val FIELD_SCREEN_CONTENT_MATCH_TYPE = "screen_content_match_type"
        private const val FIELD_INCLUDE_OVERLAY_LAYERS = "include_overlay_layers"
    }

    override fun getConditionType(): String = "app_state"

    /**
     * 保存应用状态条件
     * 将AppStateCondition的所有字段拆分为原生数据类型存储
     *
     * @param condition 要保存的应用状态条件
     * @return 操作是否成功
     */
    override fun save(condition: AppStateCondition): Boolean {
        if (!isValidConditionId(condition.id)) {
            logSaveError(condition.id, "Invalid condition ID")
            return false
        }

        return try {
            val operations = mutableListOf<StorageOperation>()

            // 保存基础字段
            operations.addAll(saveBaseFields(condition.id, condition))

            // 保存应用状态条件特有字段
            operations.addAll(listOf(
                saveEnum(generateKey(condition.id, FIELD_CATEGORY_TYPE), condition.categoryType),
                saveEnum(generateKey(condition.id, FIELD_STATE_TYPE), condition.stateType),
                saveEnum(generateKey(condition.id, FIELD_DETECTION_MODE), condition.detectionMode),

                StorageOperation.createStringOperation(
                    StorageDomain.CONDITIONS,
                    generateKey(condition.id, FIELD_TARGET_PACKAGE_NAME),
                    condition.targetPackageName
                ),
                StorageOperation.createStringOperation(
                    StorageDomain.CONDITIONS,
                    generateKey(condition.id, FIELD_TARGET_APP_NAME),
                    condition.targetAppName
                ),

                // 后台时间相关字段
                StorageOperation.createIntOperation(
                    StorageDomain.CONDITIONS,
                    generateKey(condition.id, FIELD_BACKGROUND_TIME_THRESHOLD_MINUTES),
                    condition.backgroundTimeThresholdMinutes
                ),
                saveEnum(generateKey(condition.id, FIELD_TRIGGER_MODE), condition.appStateTriggerMode),
                StorageOperation.createBooleanOperation(
                    StorageDomain.CONDITIONS,
                    generateKey(condition.id, FIELD_SKIP_FOREGROUND_APP),
                    condition.skipForegroundApp
                ),
                StorageOperation.createBooleanOperation(
                    StorageDomain.CONDITIONS,
                    generateKey(condition.id, FIELD_SKIP_MUSIC_PLAYING_APP),
                    condition.skipMusicPlayingApp
                ),
                StorageOperation.createBooleanOperation(
                    StorageDomain.CONDITIONS,
                    generateKey(condition.id, FIELD_SKIP_VPN_APP),
                    condition.skipVpnApp
                ),
                StorageOperation.createBooleanOperation(
                    StorageDomain.CONDITIONS,
                    generateKey(condition.id, FIELD_AUTO_INCLUDE_NEW_APPS),
                    condition.autoIncludeNewApps
                ),

                // Tasker/Locale插件相关字段
                StorageOperation.createStringOperation(
                    StorageDomain.CONDITIONS,
                    generateKey(condition.id, FIELD_PLUGIN_ACTION),
                    condition.pluginAction
                ),
                StorageOperation.createStringOperation(
                    StorageDomain.CONDITIONS,
                    generateKey(condition.id, FIELD_PLUGIN_EXTRAS),
                    condition.pluginExtras
                ),
                saveEnum(generateKey(condition.id, FIELD_EXPECTED_STATE), condition.expectedState),
                StorageOperation.createIntOperation(
                    StorageDomain.CONDITIONS,
                    generateKey(condition.id, FIELD_CHECK_INTERVAL),
                    condition.checkInterval
                ),
                StorageOperation.createIntOperation(
                    StorageDomain.CONDITIONS,
                    generateKey(condition.id, FIELD_TIMEOUT_SECONDS),
                    condition.timeoutSeconds
                ),

                // 检测频率字段
                saveEnum(generateKey(condition.id, FIELD_CHECK_FREQUENCY), condition.checkFrequency),
                StorageOperation.createBooleanOperation(
                    StorageDomain.CONDITIONS,
                    generateKey(condition.id, FIELD_ENABLE_CUSTOM_CHECK_FREQUENCY),
                    condition.enableCustomCheckFrequency
                ),
                StorageOperation.createIntOperation(
                    StorageDomain.CONDITIONS,
                    generateKey(condition.id, FIELD_CUSTOM_CHECK_FREQUENCY_SECONDS),
                    condition.customCheckFrequencySeconds
                ),
                saveEnum(generateKey(condition.id, FIELD_BACKGROUND_TIME_CHECK_FREQUENCY), condition.backgroundTimeCheckFrequency),
                StorageOperation.createBooleanOperation(
                    StorageDomain.CONDITIONS,
                    generateKey(condition.id, FIELD_ENABLE_CUSTOM_BACKGROUND_TIME_CHECK_FREQUENCY),
                    condition.enableCustomBackgroundTimeCheckFrequency
                ),
                StorageOperation.createIntOperation(
                    StorageDomain.CONDITIONS,
                    generateKey(condition.id, FIELD_CUSTOM_BACKGROUND_TIME_CHECK_FREQUENCY_SECONDS),
                    condition.customBackgroundTimeCheckFrequencySeconds
                ),

                // 界面交互相关字段
                StorageOperation.createStringOperation(
                    StorageDomain.CONDITIONS,
                    generateKey(condition.id, FIELD_CLICK_TARGET_TEXT),
                    condition.clickTargetText
                ),
                StorageOperation.createStringOperation(
                    StorageDomain.CONDITIONS,
                    generateKey(condition.id, FIELD_SCREEN_CONTENT_TEXT),
                    condition.screenContentText
                ),
                saveEnum(generateKey(condition.id, FIELD_SCREEN_CONTENT_TRIGGER_MODE), condition.screenContentTriggerMode),
                StorageOperation.createBooleanOperation(
                    StorageDomain.CONDITIONS,
                    generateKey(condition.id, FIELD_CASE_SENSITIVE),
                    condition.caseSensitive
                ),
                StorageOperation.createBooleanOperation(
                    StorageDomain.CONDITIONS,
                    generateKey(condition.id, FIELD_USE_REGEX),
                    condition.useRegex
                ),
                saveEnum(generateKey(condition.id, FIELD_INTERFACE_INTERACTION_TYPE), condition.interfaceInteractionType),
                saveEnum(generateKey(condition.id, FIELD_SCREEN_CONTENT_MATCH_TYPE), condition.screenContentMatchType),
                StorageOperation.createBooleanOperation(
                    StorageDomain.CONDITIONS,
                    generateKey(condition.id, FIELD_INCLUDE_OVERLAY_LAYERS),
                    condition.includeOverlayLayers
                )
            ))

            // 执行批量存储操作
            val success = storageManager.executeBatch(operations)

            if (success) {
                // 保存应用列表（使用独立的应用列表存储引擎）
                val selectedAppsSuccess = saveAppLists(condition)
                if (selectedAppsSuccess) {
                    logSaveSuccess(condition.id)
                    return true
                } else {
                    logSaveError(condition.id, "Failed to save app lists")
                    // 清理已保存的基础字段
                    delete(condition.id)
                    return false
                }
            } else {
                logSaveError(condition.id, "Failed to save basic fields")
                return false
            }

        } catch (e: Exception) {
            logSaveError(condition.id, "Exception: ${e.message}")
            Log.e(TAG, "Exception while saving AppStateCondition: ${condition.id}", e)
            false
        }
    }

    /**
     * 保存应用列表
     * 使用AppListStorageEngine保存selectedApps和selectedVpnApps
     *
     * @param condition 应用状态条件
     * @return 操作是否成功
     */
    private fun saveAppLists(condition: AppStateCondition): Boolean {
        return try {
            // 保存选择的应用列表
            val selectedAppsKey = generateKey(condition.id, FIELD_SELECTED_APPS)
            val selectedAppsSuccess = appListEngine.saveAppList(selectedAppsKey, condition.selectedApps)

            // 保存选择的VPN应用列表
            val selectedVpnAppsKey = generateKey(condition.id, FIELD_SELECTED_VPN_APPS)
            val selectedVpnAppsSuccess = appListEngine.saveAppList(selectedVpnAppsKey, condition.selectedVpnApps)

            selectedAppsSuccess && selectedVpnAppsSuccess
        } catch (e: Exception) {
            Log.e(TAG, "Exception while saving app lists for condition: ${condition.id}", e)
            false
        }
    }

    /**
     * 加载应用状态条件
     * 从拆分的原生数据类型字段重建AppStateCondition对象
     *
     * @param conditionId 条件ID
     * @return 重建的应用状态条件，失败时返回null
     */
    override fun load(conditionId: String): AppStateCondition? {
        if (!isValidConditionId(conditionId)) {
            logLoadError(conditionId, "Invalid condition ID")
            return null
        }

        if (!exists(conditionId)) {
            logLoadError(conditionId, "Condition does not exist")
            return null
        }

        return try {
            // 加载基础字段
            val categoryType = loadEnum(
                generateKey(conditionId, FIELD_CATEGORY_TYPE),
                AppStateCategoryType::class.java,
                AppStateCategoryType.STATE_CHANGE
            )

            val stateType = loadEnum(
                generateKey(conditionId, FIELD_STATE_TYPE),
                AppStateType::class.java,
                AppStateType.STATE_CHANGED
            )

            val detectionMode = loadEnum(
                generateKey(conditionId, FIELD_DETECTION_MODE),
                AppDetectionMode::class.java,
                AppDetectionMode.ANY_APP
            )

            val targetPackageName = storageManager.loadString(
                StorageDomain.CONDITIONS,
                generateKey(conditionId, FIELD_TARGET_PACKAGE_NAME),
                ""
            )

            val targetAppName = storageManager.loadString(
                StorageDomain.CONDITIONS,
                generateKey(conditionId, FIELD_TARGET_APP_NAME),
                ""
            )

            // 加载后台时间相关字段
            val backgroundTimeThresholdMinutes = storageManager.loadInt(
                StorageDomain.CONDITIONS,
                generateKey(conditionId, FIELD_BACKGROUND_TIME_THRESHOLD_MINUTES),
                5
            )

            // 加载应用状态触发模式
            val appStateTriggerMode = loadEnum(
                generateKey(conditionId, FIELD_TRIGGER_MODE),
                AppStateTriggerMode::class.java,
                AppStateTriggerMode.ANY_APP
            )

            val skipForegroundApp = storageManager.loadBoolean(
                StorageDomain.CONDITIONS,
                generateKey(conditionId, FIELD_SKIP_FOREGROUND_APP),
                true
            )

            val skipMusicPlayingApp = storageManager.loadBoolean(
                StorageDomain.CONDITIONS,
                generateKey(conditionId, FIELD_SKIP_MUSIC_PLAYING_APP),
                true
            )

            val skipVpnApp = storageManager.loadBoolean(
                StorageDomain.CONDITIONS,
                generateKey(conditionId, FIELD_SKIP_VPN_APP),
                false
            )

            val autoIncludeNewApps = storageManager.loadBoolean(
                StorageDomain.CONDITIONS,
                generateKey(conditionId, FIELD_AUTO_INCLUDE_NEW_APPS),
                false
            )

            // 加载Tasker/Locale插件相关字段
            val pluginAction = storageManager.loadString(
                StorageDomain.CONDITIONS,
                generateKey(conditionId, FIELD_PLUGIN_ACTION),
                "com.twofortyfouram.locale.intent.action.QUERY_CONDITION"
            )

            val pluginExtras = storageManager.loadString(
                StorageDomain.CONDITIONS,
                generateKey(conditionId, FIELD_PLUGIN_EXTRAS),
                ""
            )

            val expectedState = loadEnum(
                generateKey(conditionId, FIELD_EXPECTED_STATE),
                TaskerLocaleConditionState::class.java,
                TaskerLocaleConditionState.SATISFIED
            )

            val checkInterval = storageManager.loadInt(
                StorageDomain.CONDITIONS,
                generateKey(conditionId, FIELD_CHECK_INTERVAL),
                30
            )

            val timeoutSeconds = storageManager.loadInt(
                StorageDomain.CONDITIONS,
                generateKey(conditionId, FIELD_TIMEOUT_SECONDS),
                10
            )

            // 加载检测频率字段
            val checkFrequency = loadEnum(
                generateKey(conditionId, FIELD_CHECK_FREQUENCY),
                ConditionCheckFrequency::class.java,
                ConditionCheckFrequency.BALANCED
            )

            val enableCustomCheckFrequency = storageManager.loadBoolean(
                StorageDomain.CONDITIONS,
                generateKey(conditionId, FIELD_ENABLE_CUSTOM_CHECK_FREQUENCY),
                false
            )

            val customCheckFrequencySeconds = storageManager.loadInt(
                StorageDomain.CONDITIONS,
                generateKey(conditionId, FIELD_CUSTOM_CHECK_FREQUENCY_SECONDS),
                5
            )

            val backgroundTimeCheckFrequency = loadEnum(
                generateKey(conditionId, FIELD_BACKGROUND_TIME_CHECK_FREQUENCY),
                ConditionCheckFrequency::class.java,
                ConditionCheckFrequency.SLOW
            )

            val enableCustomBackgroundTimeCheckFrequency = storageManager.loadBoolean(
                StorageDomain.CONDITIONS,
                generateKey(conditionId, FIELD_ENABLE_CUSTOM_BACKGROUND_TIME_CHECK_FREQUENCY),
                false
            )

            val customBackgroundTimeCheckFrequencySeconds = storageManager.loadInt(
                StorageDomain.CONDITIONS,
                generateKey(conditionId, FIELD_CUSTOM_BACKGROUND_TIME_CHECK_FREQUENCY_SECONDS),
                30
            )

            // 加载界面交互相关字段
            val clickTargetText = storageManager.loadString(
                StorageDomain.CONDITIONS,
                generateKey(conditionId, FIELD_CLICK_TARGET_TEXT),
                ""
            )

            val screenContentText = storageManager.loadString(
                StorageDomain.CONDITIONS,
                generateKey(conditionId, FIELD_SCREEN_CONTENT_TEXT),
                ""
            )

            val screenContentTriggerMode = loadEnum(
                generateKey(conditionId, FIELD_SCREEN_CONTENT_TRIGGER_MODE),
                com.weinuo.quickcommands22.model.ScreenContentTriggerMode::class.java,
                com.weinuo.quickcommands22.model.ScreenContentTriggerMode.APPEAR
            )

            val caseSensitive = storageManager.loadBoolean(
                StorageDomain.CONDITIONS,
                generateKey(conditionId, FIELD_CASE_SENSITIVE),
                false
            )

            val useRegex = storageManager.loadBoolean(
                StorageDomain.CONDITIONS,
                generateKey(conditionId, FIELD_USE_REGEX),
                false
            )

            val interfaceInteractionType = loadEnum(
                generateKey(conditionId, FIELD_INTERFACE_INTERACTION_TYPE),
                com.weinuo.quickcommands22.model.InterfaceInteractionType::class.java,
                com.weinuo.quickcommands22.model.InterfaceInteractionType.CLICK
            )

            val screenContentMatchType = loadEnum(
                generateKey(conditionId, FIELD_SCREEN_CONTENT_MATCH_TYPE),
                com.weinuo.quickcommands22.model.ScreenContentMatchType::class.java,
                com.weinuo.quickcommands22.model.ScreenContentMatchType.TEXT_CONTENT
            )

            val includeOverlayLayers = storageManager.loadBoolean(
                StorageDomain.CONDITIONS,
                generateKey(conditionId, FIELD_INCLUDE_OVERLAY_LAYERS),
                false
            )

            // 加载应用列表
            val selectedApps = loadAppList(conditionId, FIELD_SELECTED_APPS)
            val selectedVpnApps = loadAppList(conditionId, FIELD_SELECTED_VPN_APPS)

            // 重建AppStateCondition对象
            val condition = AppStateCondition(
                id = conditionId,
                categoryType = categoryType,
                stateType = stateType,
                detectionMode = detectionMode,
                targetPackageName = targetPackageName,
                targetAppName = targetAppName,
                selectedApps = selectedApps,
                backgroundTimeThresholdMinutes = backgroundTimeThresholdMinutes,
                appStateTriggerMode = appStateTriggerMode,
                skipForegroundApp = skipForegroundApp,
                skipMusicPlayingApp = skipMusicPlayingApp,
                skipVpnApp = skipVpnApp,
                selectedVpnApps = selectedVpnApps,
                autoIncludeNewApps = autoIncludeNewApps,
                pluginAction = pluginAction,
                pluginExtras = pluginExtras,
                expectedState = expectedState,
                checkInterval = checkInterval,
                timeoutSeconds = timeoutSeconds,
                checkFrequency = checkFrequency,
                enableCustomCheckFrequency = enableCustomCheckFrequency,
                customCheckFrequencySeconds = customCheckFrequencySeconds,
                backgroundTimeCheckFrequency = backgroundTimeCheckFrequency,
                enableCustomBackgroundTimeCheckFrequency = enableCustomBackgroundTimeCheckFrequency,
                customBackgroundTimeCheckFrequencySeconds = customBackgroundTimeCheckFrequencySeconds,
                clickTargetText = clickTargetText,
                screenContentText = screenContentText,
                screenContentTriggerMode = screenContentTriggerMode,
                caseSensitive = caseSensitive,
                useRegex = useRegex,
                interfaceInteractionType = interfaceInteractionType,
                screenContentMatchType = screenContentMatchType,
                includeOverlayLayers = includeOverlayLayers
            )

            Log.d(TAG, "Successfully loaded AppStateCondition: $conditionId")
            condition

        } catch (e: Exception) {
            logLoadError(conditionId, "Exception: ${e.message}")
            Log.e(TAG, "Exception while loading AppStateCondition: $conditionId", e)
            null
        }
    }

    /**
     * 加载应用列表
     * 使用AppListStorageEngine加载应用列表
     *
     * @param conditionId 条件ID
     * @param fieldName 字段名
     * @return 应用列表
     */
    private fun loadAppList(conditionId: String, fieldName: String): List<SimpleAppInfo> {
        return try {
            val appListKey = generateKey(conditionId, fieldName)
            appListEngine.loadAppList(appListKey)
        } catch (e: Exception) {
            Log.e(TAG, "Exception while loading app list for condition: $conditionId, field: $fieldName", e)
            emptyList()
        }
    }

    /**
     * 删除应用状态条件
     * 删除条件的所有相关数据，包括应用列表
     *
     * @param conditionId 条件ID
     * @return 操作是否成功
     */
    override fun delete(conditionId: String): Boolean {
        return try {
            // 删除应用列表
            val selectedAppsKey = generateKey(conditionId, FIELD_SELECTED_APPS)
            val selectedVpnAppsKey = generateKey(conditionId, FIELD_SELECTED_VPN_APPS)

            appListEngine.deleteAppList(selectedAppsKey)
            appListEngine.deleteAppList(selectedVpnAppsKey)

            // 删除基础字段
            super.delete(conditionId)
        } catch (e: Exception) {
            Log.e(TAG, "Exception while deleting AppStateCondition: $conditionId", e)
            false
        }
    }
}
