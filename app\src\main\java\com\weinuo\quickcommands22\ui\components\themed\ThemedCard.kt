package com.weinuo.quickcommands22.ui.components.themed

import androidx.compose.foundation.BorderStroke
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shape
import com.weinuo.quickcommands22.ui.theme.config.CardConfig
import com.weinuo.quickcommands22.ui.theme.manager.LocalThemeContext

/**
 * 主题感知的卡片组件
 *
 * 根据当前主题自动选择合适的实现：
 * - 海洋蓝主题：使用分层设计风格（LayeredCard）- 带阴影效果
 * - 天空蓝主题：使用整合设计风格（IntegratedCard）- 无阴影，大圆角
 * - 未来主题：可以使用各自独有的实现
 *
 * 卡片是UI中最常用的容器组件之一，
 * 不同主题的卡片风格差异最能体现设计理念的不同。
 */
@Composable
fun ThemedCard(
    config: CardConfig,
    modifier: Modifier = Modifier
) {
    val themeContext = LocalThemeContext.current
    
    // 使用当前主题的组件工厂创建卡片
    themeContext.componentFactory.createCard()(
        config.copy(modifier = modifier)
    )
}

/**
 * 主题感知的卡片组件 - 便捷版本
 *
 * 提供更简洁的API，只需要内容和点击事件
 */
@Composable
fun ThemedCard(
    onClick: (() -> Unit)? = null,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    backgroundColor: Color? = null,
    contentColor: Color? = null,
    border: BorderStroke? = null,
    shape: Shape? = null,
    content: @Composable () -> Unit
) {
    val config = CardConfig(
        onClick = onClick,
        modifier = modifier,
        enabled = enabled,
        backgroundColor = backgroundColor,
        contentColor = contentColor,
        border = border,
        shape = shape,
        content = content
    )
    
    // 使用主题感知组件
    ThemedCard(config = config)
}

/**
 * 主题感知的简单卡片 - 最简版本
 *
 * 只需要内容的最简单版本
 */
@Composable
fun ThemedCard(
    modifier: Modifier = Modifier,
    content: @Composable () -> Unit
) {
    ThemedCard(
        onClick = null,
        modifier = modifier,
        content = content
    )
}
