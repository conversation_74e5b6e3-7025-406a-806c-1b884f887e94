package com.weinuo.quickcommands22.ui.components.themed

import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.navigation.NavController
import com.weinuo.quickcommands22.ui.components.integrated.IntegratedMainLayout
import com.weinuo.quickcommands22.ui.components.integrated.IntegratedTopAppBarProvider
import com.weinuo.quickcommands22.ui.components.layered.LayeredMainLayout
import com.weinuo.quickcommands22.ui.theme.config.BottomNavigationConfig
import com.weinuo.quickcommands22.ui.theme.manager.LocalThemeContext
import com.weinuo.quickcommands22.ui.theme.system.DesignApproach
import dev.chrisbanes.haze.HazeState

/**
 * 主题感知的主布局组件
 *
 * 根据当前主题的设计方法自动选择合适的布局策略：
 * - 分层设计（海洋蓝主题）：使用LayeredMainLayout，传统分离式布局
 * - 整合设计（天空蓝主题）：使用IntegratedMainLayout，覆盖式模糊布局
 * - 未来设计方法：可以添加新的布局实现
 *
 * 这个组件将布局策略的选择从MainActivity中分离出来，
 * 让MainActivity保持简洁，同时确保不同主题有完全不同的布局体验。
 * 同时统一处理TopAppBar和BottomNavigation的布局策略。
 */
@Composable
fun ThemedMainLayout(
    navController: NavController,
    bottomNavConfig: BottomNavigationConfig,
    hazeState: HazeState? = null,
    modifier: Modifier = Modifier,
    content: @Composable (PaddingValues) -> Unit
) {
    val themeContext = LocalThemeContext.current
    
    // 根据设计方法选择不同的布局策略
    when (themeContext.designApproach) {
        DesignApproach.LAYERED_DESIGN -> {
            // 海洋蓝主题：传统分离式布局
            LayeredMainLayout(
                navController = navController,
                bottomNavConfig = bottomNavConfig,
                content = content
            )
        }

        DesignApproach.INTEGRATED_DESIGN -> {
            // 天空蓝主题：整合式模糊布局
            if (hazeState != null) {
                // 为天空蓝主题提供TopAppBar配置机制
                IntegratedTopAppBarProvider {
                    IntegratedMainLayout(
                        navController = navController,
                        bottomNavConfig = bottomNavConfig,
                        hazeState = hazeState,
                        content = content
                    )
                }
            } else {
                // 降级到分层设计（如果没有提供hazeState）
                LayeredMainLayout(
                    navController = navController,
                    bottomNavConfig = bottomNavConfig,
                    content = content
                )
            }
        }

        DesignApproach.MINIMAL_DESIGN -> {
            // 未来的极简设计：可以添加MinimalMainLayout
            LayeredMainLayout(
                navController = navController,
                bottomNavConfig = bottomNavConfig,
                content = content
            )
        }

        DesignApproach.DYNAMIC_DESIGN -> {
            // 未来的动态设计：可以添加DynamicMainLayout
            LayeredMainLayout(
                navController = navController,
                bottomNavConfig = bottomNavConfig,
                content = content
            )
        }
    }
}
