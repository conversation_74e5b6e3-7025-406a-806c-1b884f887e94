package com.weinuo.quickcommands22.utils

import android.content.Context
import android.content.pm.PackageManager
import android.os.Build
import android.os.Environment
import android.util.Log
import androidx.core.content.ContextCompat
import kotlinx.coroutines.*
import java.io.File
import java.io.FileWriter
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.ConcurrentLinkedQueue

/**
 * 调试日志管理器
 * 
 * 用于记录应用后台检测和强制停止的详细日志
 * 支持文件日志和悬浮窗实时显示
 */
class DebugLogManager private constructor(private val context: Context) {
    
    companion object {
        private const val TAG = "DebugLogManager"
        private const val LOG_FILE_NAME = "log.txt"
        private const val MAX_LOG_ENTRIES = 1000
        private const val MAX_DISPLAY_ENTRIES = 3 // 悬浮窗显示最近3条日志
        
        @Volatile
        private var INSTANCE: DebugLogManager? = null
        
        fun getInstance(context: Context): DebugLogManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: DebugLogManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    private val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS", Locale.getDefault())
    private val logQueue = ConcurrentLinkedQueue<LogEntry>()
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    // 悬浮窗显示的日志条目
    private val displayLogs = mutableListOf<LogEntry>()
    private var onLogUpdateListener: ((List<LogEntry>) -> Unit)? = null
    
    data class LogEntry(
        val timestamp: String,
        val level: LogLevel,
        val tag: String,
        val message: String
    ) {
        fun toDisplayString(): String {
            val levelStr = when (level) {
                LogLevel.ERROR -> "E"
                LogLevel.WARN -> "W"
                LogLevel.INFO -> "I"
                LogLevel.DEBUG -> "D"
            }
            return "[$timestamp] $levelStr/$tag: $message"
        }
        
        fun toFileString(): String {
            return "[$timestamp] [$level] [$tag] $message"
        }
    }
    
    enum class LogLevel {
        DEBUG, INFO, WARN, ERROR
    }
    
    /**
     * 设置日志更新监听器（用于悬浮窗显示）
     */
    fun setLogUpdateListener(listener: (List<LogEntry>) -> Unit) {
        onLogUpdateListener = listener
        // 立即发送当前日志
        listener(displayLogs.toList())
    }
    
    /**
     * 移除日志更新监听器
     */
    fun removeLogUpdateListener() {
        onLogUpdateListener = null
    }
    
    /**
     * 记录调试日志
     */
    fun logDebug(tag: String, message: String) {
        addLog(LogLevel.DEBUG, tag, message)
    }
    
    /**
     * 记录信息日志
     */
    fun logInfo(tag: String, message: String) {
        addLog(LogLevel.INFO, tag, message)
    }
    
    /**
     * 记录警告日志
     */
    fun logWarn(tag: String, message: String) {
        addLog(LogLevel.WARN, tag, message)
    }
    
    /**
     * 记录错误日志
     */
    fun logError(tag: String, message: String) {
        addLog(LogLevel.ERROR, tag, message)
    }
    
    /**
     * 添加日志条目
     */
    private fun addLog(level: LogLevel, tag: String, message: String) {
        val timestamp = dateFormat.format(Date())
        val logEntry = LogEntry(timestamp, level, tag, message)
        
        // 添加到队列
        logQueue.offer(logEntry)
        
        // 限制队列大小
        while (logQueue.size > MAX_LOG_ENTRIES) {
            logQueue.poll()
        }
        
        // 更新显示日志
        synchronized(displayLogs) {
            displayLogs.add(logEntry)
            while (displayLogs.size > MAX_DISPLAY_ENTRIES) {
                displayLogs.removeAt(0)
            }
        }
        
        // 通知监听器
        onLogUpdateListener?.invoke(displayLogs.toList())
        
        // 异步写入文件
        scope.launch {
            writeToFile(logEntry)
        }
        
        // 同时输出到系统日志
        when (level) {
            LogLevel.DEBUG -> Log.d(tag, message)
            LogLevel.INFO -> Log.i(tag, message)
            LogLevel.WARN -> Log.w(tag, message)
            LogLevel.ERROR -> Log.e(tag, message)
        }
    }
    
    /**
     * 写入日志到文件
     */
    private suspend fun writeToFile(logEntry: LogEntry) = withContext(Dispatchers.IO) {
        try {
            val logFile = getLogFile()
            if (logFile != null) {
                FileWriter(logFile, true).use { writer ->
                    writer.appendLine(logEntry.toFileString())
                    writer.flush()
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to write log to file", e)
        }
    }
    
    /**
     * 获取日志文件
     */
    private fun getLogFile(): File? {
        return try {
            // 检查存储权限
            if (!hasStoragePermission()) {
                Log.w(TAG, "No storage permission for log file")
                return null
            }

            val externalStorageDir = Environment.getExternalStorageDirectory()
            val logFile = File(externalStorageDir, LOG_FILE_NAME)

            // 确保文件存在
            if (!logFile.exists()) {
                logFile.createNewFile()
            }

            logFile
        } catch (e: Exception) {
            Log.e(TAG, "Failed to get log file", e)
            null
        }
    }

    /**
     * 检查是否有存储权限
     */
    private fun hasStoragePermission(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            // Android 11+ 需要 MANAGE_EXTERNAL_STORAGE 权限
            Environment.isExternalStorageManager()
        } else {
            // Android 10 及以下需要 WRITE_EXTERNAL_STORAGE 权限
            ContextCompat.checkSelfPermission(
                context,
                android.Manifest.permission.WRITE_EXTERNAL_STORAGE
            ) == PackageManager.PERMISSION_GRANTED
        }
    }
    
    /**
     * 清空日志
     */
    fun clearLogs() {
        logQueue.clear()
        synchronized(displayLogs) {
            displayLogs.clear()
        }
        onLogUpdateListener?.invoke(emptyList())
        
        scope.launch {
            try {
                val logFile = getLogFile()
                logFile?.writeText("")
            } catch (e: Exception) {
                Log.e(TAG, "Failed to clear log file", e)
            }
        }
    }
    
    /**
     * 获取当前显示的日志
     */
    fun getCurrentDisplayLogs(): List<LogEntry> {
        return synchronized(displayLogs) {
            displayLogs.toList()
        }
    }
    
    /**
     * 清理资源
     */
    fun shutdown() {
        scope.cancel()
        onLogUpdateListener = null
    }
}
