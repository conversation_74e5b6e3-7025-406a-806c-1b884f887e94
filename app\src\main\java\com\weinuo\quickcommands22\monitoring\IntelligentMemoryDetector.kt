package com.weinuo.quickcommands22.monitoring

import android.app.ActivityManager
import android.content.Context
import android.util.Log
import com.weinuo.quickcommands22.model.*
import kotlinx.coroutines.*
import kotlin.math.abs
import kotlin.math.sqrt

/**
 * 智能内存检测器
 *
 * 实现基于机器学习的内存使用模式识别和预测功能
 */
class IntelligentMemoryDetector(
    private val context: Context
) {
    companion object {
        private const val TAG = "IntelligentMemoryDetector"
        private const val LEARNING_DATA_PREFIX = "memory_learning_"
        private const val STABILITY_THRESHOLD_DEFAULT = 50 // MB
        private const val MIN_SAMPLES_DEFAULT = 5
        private const val CONFIDENCE_THRESHOLD_DEFAULT = 0.7f
    }

    private val prefs = context.getSharedPreferences("intelligent_memory_detector", Context.MODE_PRIVATE)
    private val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
    private val detectorScope = CoroutineScope(Dispatchers.Default + SupervisorJob())

    // 内存使用历史记录
    private val memoryHistory = mutableMapOf<String, MutableList<MemorySnapshot>>()

    // 学习统计数据
    private val learningStatistics = mutableMapOf<String, MemoryStatistics>()

    /**
     * 内存快照数据类
     */
    data class MemorySnapshot(
        val timestamp: Long,
        val memoryUsageMB: Int,
        val isStable: Boolean = false
    )

    /**
     * 内存使用统计数据类
     */
    data class MemoryStatistics(
        val packageName: String,
        val sampleCount: Int,
        val averageMemory: Double,
        val peakMemory: Int,
        val standardDeviation: Double,
        val confidence: Float,
        val lastUpdated: Long
    )

    /**
     * 开始智能学习
     */
    fun startLearning(packageName: String, config: IntelligentConfig) {
        Log.d(TAG, "Starting intelligent learning for $packageName")

        detectorScope.launch {
            try {
                // 开始连续监控和学习
                monitorAndLearn(packageName, config)
            } catch (e: Exception) {
                Log.e(TAG, "Error in intelligent learning for $packageName", e)
            }
        }
    }

    /**
     * 停止智能学习
     */
    fun stopLearning(packageName: String) {
        Log.d(TAG, "Stopping intelligent learning for $packageName")
        // 清理内存历史记录
        memoryHistory.remove(packageName)
    }

    /**
     * 预测应用内存使用是否稳定
     */
    suspend fun predictMemoryStability(
        packageName: String,
        config: IntelligentConfig
    ): Boolean {
        val statistics = getMemoryStatistics(packageName)

        if (statistics == null || statistics.sampleCount < config.minSamplesForPrediction) {
            Log.d(TAG, "Insufficient data for prediction: $packageName")
            return false
        }

        if (statistics.confidence < config.confidenceThreshold) {
            Log.d(TAG, "Low confidence for prediction: $packageName (${statistics.confidence})")
            return false
        }

        // 获取当前内存使用
        val currentMemory = getCurrentMemoryUsage(packageName)
        if (currentMemory == -1) {
            return false
        }

        // 判断当前内存使用是否在预期范围内
        val expectedRange = statistics.averageMemory + statistics.standardDeviation
        val isWithinExpectedRange = abs(currentMemory - statistics.averageMemory) <= expectedRange

        Log.d(TAG, "Memory prediction for $packageName: current=$currentMemory, expected=${statistics.averageMemory}±${statistics.standardDeviation}, stable=$isWithinExpectedRange")

        return isWithinExpectedRange
    }

    /**
     * 监控和学习内存使用模式
     */
    private suspend fun monitorAndLearn(packageName: String, config: IntelligentConfig) {
        val history = memoryHistory.getOrPut(packageName) { mutableListOf() }
        var consecutiveStableChecks = 0

        while (true) {
            try {
                val currentMemory = getCurrentMemoryUsage(packageName)
                if (currentMemory == -1) {
                    delay(config.stabilityCheckInterval * 1000L)
                    continue
                }

                val snapshot = MemorySnapshot(
                    timestamp = System.currentTimeMillis(),
                    memoryUsageMB = currentMemory
                )

                // 检查内存是否稳定
                val isStable = if (history.isNotEmpty()) {
                    val lastSnapshot = history.last()
                    abs(currentMemory - lastSnapshot.memoryUsageMB) <= config.stabilityThresholdMB
                } else {
                    false
                }

                if (isStable) {
                    consecutiveStableChecks++
                } else {
                    consecutiveStableChecks = 0
                }

                // 如果连续稳定检查达到要求，记录稳定的内存快照
                if (consecutiveStableChecks >= config.requiredStableChecks) {
                    val stableSnapshot = snapshot.copy(isStable = true)
                    history.add(stableSnapshot)

                    // 限制历史记录数量
                    if (history.size > config.maxHistoryRecords) {
                        history.removeAt(0)
                    }

                    // 更新学习数据
                    updateLearningData(packageName, history, config)

                    Log.d(TAG, "Recorded stable memory snapshot for $packageName: ${currentMemory}MB")
                    consecutiveStableChecks = 0
                }

                delay(config.stabilityCheckInterval * 1000L)

            } catch (e: Exception) {
                Log.e(TAG, "Error in memory monitoring for $packageName", e)
                delay(5000) // 错误时等待5秒再重试
            }
        }
    }

    /**
     * 获取当前内存使用量
     */
    private fun getCurrentMemoryUsage(packageName: String): Int {
        try {
            val runningProcesses = activityManager.runningAppProcesses ?: return -1

            for (process in runningProcesses) {
                if (process.processName == packageName) {
                    val memoryInfo = activityManager.getProcessMemoryInfo(intArrayOf(process.pid))
                    if (memoryInfo.isNotEmpty()) {
                        return memoryInfo[0].totalPss / 1024 // 转换为MB
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting memory usage for $packageName", e)
        }
        return -1
    }

    /**
     * 更新学习数据
     */
    private fun updateLearningData(
        packageName: String,
        history: List<MemorySnapshot>,
        config: IntelligentConfig
    ) {
        val stableSnapshots = history.filter { it.isStable }
        if (stableSnapshots.isEmpty()) return

        val memoryValues = stableSnapshots.map { it.memoryUsageMB.toDouble() }
        val average = memoryValues.average()
        val variance = memoryValues.map { (it - average) * (it - average) }.average()
        val standardDeviation = sqrt(variance)
        val peakMemory = memoryValues.maxOrNull()?.toInt() ?: 0

        // 计算置信度（基于样本数量和标准差）
        val confidence = calculateConfidence(stableSnapshots.size, standardDeviation)

        val statistics = MemoryStatistics(
            packageName = packageName,
            sampleCount = stableSnapshots.size,
            averageMemory = average,
            peakMemory = peakMemory,
            standardDeviation = standardDeviation,
            confidence = confidence,
            lastUpdated = System.currentTimeMillis()
        )

        // 保存到SharedPreferences
        saveLearningData(packageName, statistics)

        Log.d(TAG, "Updated learning data for $packageName: avg=${average}MB, peak=${peakMemory}MB, confidence=$confidence")
    }

    /**
     * 计算置信度
     */
    private fun calculateConfidence(sampleCount: Int, standardDeviation: Double): Float {
        // 基于样本数量的置信度
        val sampleConfidence = minOf(sampleCount.toFloat() / 20f, 1f)

        // 基于标准差的置信度（标准差越小，置信度越高）
        val stabilityConfidence = maxOf(0f, 1f - (standardDeviation / 200f).toFloat())

        // 综合置信度
        return (sampleConfidence * 0.6f + stabilityConfidence * 0.4f).coerceIn(0f, 1f)
    }

    /**
     * 保存学习数据
     */
    private fun saveLearningData(packageName: String, statistics: MemoryStatistics) {
        val prefix = "$LEARNING_DATA_PREFIX$packageName"
        val editor = prefs.edit()
        editor.putInt("${prefix}_sample_count", statistics.sampleCount)
        editor.putFloat("${prefix}_average_memory", statistics.averageMemory.toFloat())
        editor.putInt("${prefix}_peak_memory", statistics.peakMemory)
        editor.putFloat("${prefix}_standard_deviation", statistics.standardDeviation.toFloat())
        editor.putFloat("${prefix}_confidence", statistics.confidence)
        editor.putLong("${prefix}_last_updated", statistics.lastUpdated)
        editor.apply()
    }

    /**
     * 获取内存统计数据
     */
    private fun getMemoryStatistics(packageName: String): MemoryStatistics? {
        val prefix = "$LEARNING_DATA_PREFIX$packageName"
        val sampleCount = prefs.getInt("${prefix}_sample_count", 0)

        if (sampleCount == 0) return null

        return MemoryStatistics(
            packageName = packageName,
            sampleCount = sampleCount,
            averageMemory = prefs.getFloat("${prefix}_average_memory", 0f).toDouble(),
            peakMemory = prefs.getInt("${prefix}_peak_memory", 0),
            standardDeviation = prefs.getFloat("${prefix}_standard_deviation", 0f).toDouble(),
            confidence = prefs.getFloat("${prefix}_confidence", 0f),
            lastUpdated = prefs.getLong("${prefix}_last_updated", 0)
        )
    }

    /**
     * 清理过期的学习数据
     */
    fun cleanupExpiredData(retentionDays: Int) {
        detectorScope.launch {
            val cutoffTime = System.currentTimeMillis() - (retentionDays * 24 * 60 * 60 * 1000L)
            var totalCleaned = 0

            try {
                // 遍历所有应用的内存历史记录
                val packagesToRemove = mutableListOf<String>()

                memoryHistory.forEach { (packageName, history) ->
                    val originalSize = history.size

                    // 移除过期的记录
                    history.removeAll { snapshot ->
                        snapshot.timestamp < cutoffTime
                    }

                    val cleanedCount = originalSize - history.size
                    totalCleaned += cleanedCount

                    if (cleanedCount > 0) {
                        Log.d(TAG, "Cleaned $cleanedCount expired records for $packageName")
                    }

                    // 如果某个应用的所有记录都被清理了，标记为待移除
                    if (history.isEmpty()) {
                        packagesToRemove.add(packageName)
                    }
                }

                // 移除没有任何记录的应用
                packagesToRemove.forEach { packageName ->
                    memoryHistory.remove(packageName)
                    Log.d(TAG, "Removed empty history for $packageName")
                }

                // 清理学习统计数据中的过期记录
                learningStatistics.entries.removeAll { (packageName, _) ->
                    !memoryHistory.containsKey(packageName)
                }

                Log.d(TAG, "Cleanup completed: removed $totalCleaned expired records from ${packagesToRemove.size} packages")

            } catch (e: Exception) {
                Log.e(TAG, "Error during data cleanup", e)
            }
        }
    }

    /**
     * 停止所有学习任务
     */
    fun shutdown() {
        Log.d(TAG, "Shutting down intelligent memory detector")
        detectorScope.cancel()
        memoryHistory.clear()
    }
}
