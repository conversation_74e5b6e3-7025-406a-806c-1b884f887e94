package com.weinuo.quickcommands22.ui.recording

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Save
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.platform.LocalContext
import com.weinuo.quickcommands22.ui.theme.QuickCommandsTheme
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * 手势录制界面
 * 提供全屏录制区域，用户可以在此进行手势操作演示
 */
class GestureRecordingActivity : ComponentActivity() {

    companion object {
        const val EXTRA_RECORDED_GESTURE = "recorded_gesture"
        const val REQUEST_CODE_RECORDING = 1001

        /**
         * 启动录制界面
         */
        fun startForResult(activity: Activity, requestCode: Int = REQUEST_CODE_RECORDING) {
            val intent = Intent(activity, GestureRecordingActivity::class.java)
            activity.startActivityForResult(intent, requestCode)
        }
    }

    // ActivityResult launcher for editing
    private val editLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        when (result.resultCode) {
            Activity.RESULT_OK -> {
                // 编辑完成，返回录制结果
                val editedGestureData = result.data?.getStringExtra(GestureRecordingEditActivity.EXTRA_RECORDING_ID) ?: ""
                val resultIntent = Intent().apply {
                    putExtra(EXTRA_RECORDED_GESTURE, editedGestureData)
                }
                setResult(Activity.RESULT_OK, resultIntent)
                finish()
            }
            Activity.RESULT_FIRST_USER -> {
                // 重新录制请求
                result.data?.getStringExtra("action")?.let { action ->
                    if (action == "re_record") {
                        // 重新开始录制，不关闭当前Activity
                        // 这里可以重置录制状态或重新启动录制流程
                    }
                }
            }
            else -> {
                // 编辑取消，返回取消结果
                setResult(Activity.RESULT_CANCELED)
                finish()
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        setContent {
            QuickCommandsTheme {
                val context = LocalContext.current
                GestureRecordingScreen(
                    onSave = { gestureData ->
                        // 录制完成后导航到编辑界面
                        if (gestureData.isNotEmpty()) {
                            val editIntent = Intent(this@GestureRecordingActivity, GestureRecordingEditActivity::class.java).apply {
                                putExtra(GestureRecordingEditActivity.EXTRA_RECORDING_ID, gestureData)
                                putExtra(GestureRecordingEditActivity.EXTRA_RETURN_RESULT, true)
                            }
                            editLauncher.launch(editIntent)
                        } else {
                            // 如果没有录制数据，直接返回取消
                            setResult(Activity.RESULT_CANCELED)
                            finish()
                        }
                    },
                    onCancel = {
                        setResult(Activity.RESULT_CANCELED)
                        finish()
                    },
                    viewModel = GestureRecordingViewModel(context)
                )
            }
        }
    }


}

/**
 * 手势录制界面组件
 * 全屏录制区域，最小化UI干扰
 */
@Composable
fun GestureRecordingScreen(
    onSave: (String) -> Unit,
    onCancel: () -> Unit,
    viewModel: GestureRecordingViewModel
) {
    val scope = rememberCoroutineScope()

    val isRecording by viewModel.isRecording.collectAsState()
    val recordedEvents by viewModel.recordedEvents.collectAsState()
    val recordingDuration by viewModel.recordingDuration.collectAsState()
    val recordingMode by viewModel.recordingMode.collectAsState()

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.Black.copy(alpha = 0.9f))
    ) {
        // 全屏录制区域
        GestureRecordingArea(
            isRecording = isRecording,
            onStartRecording = { viewModel.startRecording() },
            onStopRecording = { viewModel.stopRecording() },
            onTouchEvent = { event -> viewModel.addTouchEvent(event) },
            modifier = Modifier.fillMaxSize()
        )

        // 顶部录制状态指示器
        if (isRecording) {
            Box(
                modifier = Modifier
                    .align(Alignment.TopCenter)
                    .padding(top = 48.dp)
            ) {
                RecordingStatusIndicator(recordingDuration)
            }
        }

        // 右上角按钮组
        Row(
            modifier = Modifier
                .align(Alignment.TopEnd)
                .padding(top = 48.dp, end = 16.dp),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {


            // 保存按钮（仅在有录制内容时显示）
            if (recordedEvents.isNotEmpty()) {
                FloatingActionButton(
                    onClick = {
                        scope.launch {
                            val gestureData = viewModel.saveRecording()
                            onSave(gestureData)
                        }
                    },
                    modifier = Modifier.size(56.dp),
                    containerColor = Color.Green,
                    contentColor = Color.White
                ) {
                    Icon(
                        Icons.Default.Save,
                        contentDescription = "保存录制",
                        modifier = Modifier.size(24.dp)
                    )
                }
            }
        }
    }


}

/**
 * 录制状态指示器
 */
@Composable
private fun RecordingStatusIndicator(recordingDuration: Long) {
    var isVisible by remember { mutableStateOf(true) }

    LaunchedEffect(Unit) {
        while (true) {
            delay(500)
            isVisible = !isVisible
        }
    }

    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier
            .background(
                Color.Black.copy(alpha = 0.8f),
                shape = RoundedCornerShape(24.dp)
            )
            .padding(horizontal = 16.dp, vertical = 8.dp)
    ) {
        Box(
            modifier = Modifier
                .size(12.dp)
                .background(
                    if (isVisible) Color.Red else Color.Transparent,
                    shape = RoundedCornerShape(6.dp)
                )
        )

        Spacer(modifier = Modifier.width(8.dp))

        Text(
            text = "录制中 ${recordingDuration / 1000}s",
            color = Color.White,
            fontSize = 16.sp,
            fontWeight = FontWeight.Medium
        )
    }
}
