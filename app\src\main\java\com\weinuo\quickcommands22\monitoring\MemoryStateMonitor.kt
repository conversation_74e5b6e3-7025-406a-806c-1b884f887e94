package com.weinuo.quickcommands22.monitoring

import android.app.ActivityManager
import android.app.usage.UsageStatsManager
import android.content.Context
import android.util.Log
import com.weinuo.quickcommands22.model.*
import kotlinx.coroutines.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * 内存状态监听器
 * 监听内存状态变化，触发相应的条件
 *
 * 支持的监听功能：
 * - 内存低于阈值检测（百分比模式和绝对值模式）
 * - 内存高于阈值检测
 * - 内存变化检测
 */
class MemoryStateMonitor(
    private val context: Context,
    private val onConditionTriggered: (DeviceEventCondition, Map<String, Any>) -> Unit
) {

    companion object {
        private const val TAG = "MemoryStateMonitor"
        private const val DEFAULT_MEMORY_CHECK_INTERVAL = 5000L // 默认5秒检查一次
    }

    // 协程作用域
    private val monitorScope = CoroutineScope(Dispatchers.Default + SupervisorJob())

    // 监听器状态
    private var isMonitoring = false

    // 注册的条件列表
    private val registeredConditions = mutableListOf<DeviceEventCondition>()

    // 内存检查任务
    private var memoryCheckJob: Job? = null

    // 系统服务
    private val activityManager: ActivityManager by lazy {
        context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
    }

    // 上次内存状态
    private var lastAvailableMemory: Long = 0L
    private var lastMemoryPercentage: Int = 0

    // 事件驱动检测相关
    private val eventDrivenJobs = mutableMapOf<String, Job>()
    private val appStateCallbacks = mutableMapOf<String, (String, Boolean) -> Unit>()

    // 智能检测器
    private val intelligentDetector = IntelligentMemoryDetector(context)

    // 应用状态监听器
    private val appStateMonitor = AppStateMonitor(context)

    /**
     * 注册内存状态条件
     */
    fun registerCondition(condition: DeviceEventCondition) {
        synchronized(registeredConditions) {
            if (condition.eventType == DeviceEventType.MEMORY_STATE &&
                !registeredConditions.any { it.id == condition.id }) {
                registeredConditions.add(condition)
                Log.d(TAG, "Registered memory condition: ${condition.getDescription()}")

                // 如果是智能模式，启动学习
                if (condition.memoryCheckMode == MemoryCheckMode.INTELLIGENT && condition.enableLearning) {
                    val config = IntelligentConfig(
                        enableLearning = condition.enableLearning,
                        minSamplesForPrediction = condition.minSamplesForPrediction,
                        confidenceThreshold = condition.confidenceThreshold,
                        stabilityCheckInterval = condition.stabilityCheckInterval,
                        stabilityThresholdMB = condition.stabilityThresholdMB,
                        requiredStableChecks = condition.requiredStableChecks,
                        maxHistoryRecords = condition.maxHistoryRecords,
                        dataRetentionDays = condition.dataRetentionDays
                    )

                    // 启动系统级学习
                    intelligentDetector.startLearning("system", config)

                    // 为当前运行的应用启动学习
                    startLearningForRunningApps(config)
                }

                // 如果监控已启动，重新评估是否需要定时检查
                if (isMonitoring) {
                    restartMemoryCheckJobIfNeeded()
                }
            }
        }
    }

    /**
     * 取消注册内存状态条件
     */
    fun unregisterCondition(conditionId: String) {
        synchronized(registeredConditions) {
            registeredConditions.removeAll { it.id == conditionId }
            Log.d(TAG, "Unregistered memory condition: $conditionId")

            // 如果监控已启动，重新评估是否需要定时检查
            if (isMonitoring) {
                restartMemoryCheckJobIfNeeded()
            }
        }
    }

    /**
     * 清除所有注册的条件
     */
    fun clearAllConditions() {
        synchronized(registeredConditions) {
            registeredConditions.clear()
            Log.d(TAG, "Cleared all memory conditions")
        }
    }

    /**
     * 开始监听内存状态
     */
    fun startMonitoring() {
        if (isMonitoring) {
            Log.d(TAG, "Memory monitoring already started")
            return
        }

        try {
            startMemoryCheckJob()

            // 启动应用状态监听器
            appStateMonitor.registerCallback("memory_monitor", object : AppStateMonitor.AppStateCallback {
                override fun onAppLaunched(packageName: String) {
                    <EMAIL>(packageName)
                }

                override fun onAppForeground(packageName: String) {
                    <EMAIL>(packageName)
                }

                override fun onAppBackground(packageName: String) {
                    // 可以在这里处理应用进入后台的逻辑
                }
            })
            appStateMonitor.startMonitoring()

            isMonitoring = true
            Log.d(TAG, "Memory monitoring started successfully")

        } catch (e: Exception) {
            Log.e(TAG, "Error starting memory monitoring", e)
            stopMonitoring()
        }
    }

    /**
     * 停止监听内存状态
     */
    fun stopMonitoring() {
        if (!isMonitoring) {
            Log.d(TAG, "Memory monitoring already stopped")
            return
        }

        try {
            memoryCheckJob?.cancel()
            memoryCheckJob = null

            // 停止所有事件驱动的任务
            eventDrivenJobs.values.forEach { it.cancel() }
            eventDrivenJobs.clear()
            appStateCallbacks.clear()

            // 停止智能检测器
            intelligentDetector.shutdown()

            // 停止应用状态监听器
            appStateMonitor.unregisterCallback("memory_monitor")
            appStateMonitor.stopMonitoring()

            isMonitoring = false
            Log.d(TAG, "Memory monitoring stopped successfully")

        } catch (e: Exception) {
            Log.e(TAG, "Error stopping memory monitoring", e)
        }
    }

    /**
     * 启动内存检查任务
     */
    private fun startMemoryCheckJob() {
        // 计算检测间隔，如果是纯事件驱动模式则不启动定时任务
        val checkInterval = calculateCheckInterval()

        if (checkInterval == Long.MAX_VALUE) {
            Log.d(TAG, "All conditions are event-driven, skipping periodic memory check job")
            return
        }

        memoryCheckJob = monitorScope.launch {
            Log.d(TAG, "Starting memory check job with interval: ${checkInterval}ms")

            while (isActive) {
                try {
                    checkMemoryConditions()

                    // 重新计算动态检测间隔（可能有条件变化）
                    val currentInterval = calculateCheckInterval()
                    if (currentInterval == Long.MAX_VALUE) {
                        Log.d(TAG, "All conditions became event-driven, stopping periodic check")
                        break
                    }
                    delay(currentInterval)
                } catch (e: CancellationException) {
                    Log.d(TAG, "Memory check job cancelled")
                    break
                } catch (e: Exception) {
                    Log.e(TAG, "Error in memory check job", e)
                    if (isActive) {
                        delay(10000) // 出错时等待10秒再重试
                    }
                }
            }
            Log.d(TAG, "Memory check job ended")
        }
    }

    /**
     * 计算检测间隔
     * 根据注册的条件中最频繁的检测频率来确定
     */
    private fun calculateCheckInterval(): Long {
        synchronized(registeredConditions) {
            if (registeredConditions.isEmpty()) {
                return DEFAULT_MEMORY_CHECK_INTERVAL
            }

            // 检查是否所有条件都是纯事件驱动模式
            val allEventDriven = registeredConditions.all { condition ->
                when (condition.memoryCheckMode) {
                    MemoryCheckMode.EVENT_DRIVEN -> {
                        // 纯事件驱动模式：必须至少启用一种事件触发
                        condition.triggerOnAppForeground || condition.triggerOnAppLaunch || condition.triggerOnMemoryPressure
                    }
                    MemoryCheckMode.INTELLIGENT -> {
                        // 智能模式：如果没有启用学习，且有事件触发，则认为是纯事件驱动
                        !condition.enableLearning &&
                        (condition.triggerOnAppForeground || condition.triggerOnAppLaunch || condition.triggerOnMemoryPressure)
                    }
                    MemoryCheckMode.HYBRID -> {
                        // 混合模式：如果只启用事件驱动，且有事件触发，则认为是纯事件驱动
                        condition.enableEventDriven && !condition.enableAdaptive && !condition.enableIntelligent &&
                        (condition.triggerOnAppForeground || condition.triggerOnAppLaunch || condition.triggerOnMemoryPressure)
                    }
                    else -> false
                }
            }

            // 如果所有条件都是纯事件驱动，返回一个很长的间隔（实际上不会被使用）
            if (allEventDriven) {
                return Long.MAX_VALUE // 纯事件驱动模式不需要定时检查
            }

            // 找到最短的检测间隔（最频繁的检测）
            var minInterval = DEFAULT_MEMORY_CHECK_INTERVAL

            for (condition in registeredConditions) {
                val interval = when (condition.memoryCheckMode) {
                    MemoryCheckMode.TRADITIONAL -> {
                        if (condition.enableCustomCheckFrequency) {
                            condition.customCheckFrequencySeconds * 1000L
                        } else {
                            condition.checkFrequency.intervalMs
                        }
                    }
                    MemoryCheckMode.ADAPTIVE -> {
                        // 自适应模式使用动态间隔
                        calculateAdaptiveInterval(condition)
                    }
                    MemoryCheckMode.EVENT_DRIVEN -> {
                        // 纯事件驱动模式不参与定时检查间隔计算
                        continue
                    }
                    MemoryCheckMode.INTELLIGENT -> {
                        // 智能模式：如果没有启用学习，且配置为事件驱动，则跳过定时检查
                        if (!condition.enableLearning &&
                            (condition.triggerOnAppForeground || condition.triggerOnAppLaunch || condition.triggerOnMemoryPressure)) {
                            continue
                        }
                        // 否则使用较长的基础间隔用于学习
                        30000L // 30秒基础检查
                    }
                    MemoryCheckMode.HYBRID -> {
                        // 混合模式：如果只启用事件驱动，则跳过定时检查
                        if (condition.enableEventDriven && !condition.enableAdaptive && !condition.enableIntelligent &&
                            (condition.triggerOnAppForeground || condition.triggerOnAppLaunch || condition.triggerOnMemoryPressure)) {
                            continue
                        }
                        // 否则根据启用的策略和权重计算混合间隔
                        calculateHybridInterval(condition)
                    }
                }

                if (interval < minInterval) {
                    minInterval = interval
                }
            }

            return minInterval
        }
    }

    /**
     * 计算自适应检测间隔
     */
    private fun calculateAdaptiveInterval(condition: DeviceEventCondition): Long {
        val memoryInfo = ActivityManager.MemoryInfo()
        activityManager.getMemoryInfo(memoryInfo)

        val availablePercentage = (memoryInfo.availMem * 100 / memoryInfo.totalMem).toInt()

        return when {
            availablePercentage < 20 -> condition.memoryTightFrequency * 1000L // 内存紧张
            availablePercentage > 50 -> condition.memoryAbundantFrequency * 1000L // 内存充足
            else -> 15000L // 中等状态，15秒间隔
        }
    }

    /**
     * 计算混合模式检测间隔
     * 根据启用的策略和权重计算最终间隔
     */
    private fun calculateHybridInterval(condition: DeviceEventCondition): Long {
        var totalWeight = 0f
        var weightedInterval = 0f

        // 事件驱动策略（权重影响基础间隔）
        if (condition.enableEventDriven) {
            val eventDrivenInterval = 60000L // 事件驱动的基础间隔较长
            weightedInterval += eventDrivenInterval * condition.eventDrivenWeight
            totalWeight += condition.eventDrivenWeight
        }

        // 自适应策略
        if (condition.enableAdaptive) {
            val adaptiveInterval = calculateAdaptiveInterval(condition)
            weightedInterval += adaptiveInterval * condition.adaptiveWeight
            totalWeight += condition.adaptiveWeight
        }

        // 智能学习策略
        if (condition.enableIntelligent) {
            val intelligentInterval = if (condition.enableLearning) {
                // 学习模式需要更频繁的检查
                20000L
            } else {
                // 非学习模式可以使用较长间隔
                45000L
            }
            weightedInterval += intelligentInterval * condition.intelligentWeight
            totalWeight += condition.intelligentWeight
        }

        // 如果没有启用任何策略，使用默认间隔
        if (totalWeight == 0f) {
            return 30000L
        }

        // 计算加权平均间隔
        val finalInterval = (weightedInterval / totalWeight).toLong()

        // 确保间隔在合理范围内（最小5秒，最大5分钟）
        return finalInterval.coerceIn(5000L, 300000L)
    }

    /**
     * 检查内存条件
     */
    private suspend fun checkMemoryConditions() {
        try {
            val memoryInfo = ActivityManager.MemoryInfo()
            activityManager.getMemoryInfo(memoryInfo)

            val totalMemory = memoryInfo.totalMem
            val availableMemory = memoryInfo.availMem
            val usedMemory = totalMemory - availableMemory
            val memoryPercentage = (usedMemory.toFloat() / totalMemory * 100).toInt()

            // 检查内存阈值条件
            checkMemoryThresholds(lastAvailableMemory, availableMemory, lastMemoryPercentage, memoryPercentage)

            // 更新上次内存值
            lastAvailableMemory = availableMemory
            lastMemoryPercentage = memoryPercentage

        } catch (e: Exception) {
            Log.e(TAG, "Error checking memory conditions", e)
        }
    }

    /**
     * 检查内存阈值条件
     */
    private suspend fun checkMemoryThresholds(
        oldAvailableBytes: Long,
        newAvailableBytes: Long,
        oldPercentage: Int,
        newPercentage: Int
    ) {
        try {
            // 获取一次内存信息，避免重复调用
            val memoryInfo = ActivityManager.MemoryInfo()
            activityManager.getMemoryInfo(memoryInfo)

            synchronized(registeredConditions) {
                for (condition in registeredConditions) {
                    val threshold = condition.memoryThreshold
                    val isPercentageMode = condition.isPercentageMode

                    when (condition.memoryStateType) {
                        MemoryStateType.BELOW_THRESHOLD -> {
                            checkBelowThreshold(condition, oldAvailableBytes, newAvailableBytes,
                                              oldPercentage, newPercentage, threshold, isPercentageMode, memoryInfo)
                        }
                        MemoryStateType.ABOVE_THRESHOLD -> {
                            checkAboveThreshold(condition, oldAvailableBytes, newAvailableBytes,
                                              oldPercentage, newPercentage, threshold, isPercentageMode, memoryInfo)
                        }
                        MemoryStateType.CHANGED -> {
                            checkMemoryChanged(condition, oldPercentage, newPercentage)
                        }
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error checking memory thresholds", e)
        }
    }

    /**
     * 检查内存低于阈值条件
     */
    private fun checkBelowThreshold(
        condition: DeviceEventCondition,
        oldAvailableBytes: Long,
        newAvailableBytes: Long,
        oldPercentage: Int,
        newPercentage: Int,
        threshold: Int,
        isPercentageMode: Boolean,
        memoryInfo: ActivityManager.MemoryInfo
    ) {
        try {
            val triggered = if (isPercentageMode) {
                // 百分比模式：可用内存百分比低于阈值
                // 注意：oldPercentage 和 newPercentage 是已使用内存百分比
                // 需要转换为可用内存百分比进行比较
                val oldAvailablePercentage = (oldAvailableBytes * 100 / memoryInfo.totalMem).toInt()
                val newAvailablePercentage = (newAvailableBytes * 100 / memoryInfo.totalMem).toInt()
                oldAvailablePercentage >= threshold && newAvailablePercentage < threshold
            } else {
                // 绝对值模式 (GB)
                val oldAvailableGB = oldAvailableBytes / (1024 * 1024 * 1024)
                val newAvailableGB = newAvailableBytes / (1024 * 1024 * 1024)
                oldAvailableGB >= threshold && newAvailableGB < threshold
            }

            if (triggered) {
                val unit = if (isPercentageMode) "%" else "GB"
                val value = if (isPercentageMode) {
                    // 计算当前可用内存百分比
                    (newAvailableBytes * 100 / memoryInfo.totalMem).toInt()
                } else {
                    newAvailableBytes / (1024 * 1024 * 1024)
                }
                Log.d(TAG, "Available memory below $threshold$unit: $value$unit")

                triggerCondition(condition, mapOf(
                    "memoryStateType" to "BELOW_THRESHOLD",
                    "threshold" to threshold,
                    "currentValue" to value,
                    "isPercentageMode" to isPercentageMode,
                    "availableMemory" to newAvailableBytes,
                    "usedPercentage" to newPercentage,
                    "timestamp" to System.currentTimeMillis()
                ))
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error checking below threshold condition", e)
        }
    }

    /**
     * 检查内存高于阈值条件
     */
    private fun checkAboveThreshold(
        condition: DeviceEventCondition,
        oldAvailableBytes: Long,
        newAvailableBytes: Long,
        oldPercentage: Int,
        newPercentage: Int,
        threshold: Int,
        isPercentageMode: Boolean,
        memoryInfo: ActivityManager.MemoryInfo
    ) {
        try {
            val triggered = if (isPercentageMode) {
                // 百分比模式：可用内存百分比高于阈值
                // 注意：oldPercentage 和 newPercentage 是已使用内存百分比
                // 需要转换为可用内存百分比进行比较
                val oldAvailablePercentage = (oldAvailableBytes * 100 / memoryInfo.totalMem).toInt()
                val newAvailablePercentage = (newAvailableBytes * 100 / memoryInfo.totalMem).toInt()
                oldAvailablePercentage <= threshold && newAvailablePercentage > threshold
            } else {
                // 绝对值模式 (GB)
                val oldAvailableGB = oldAvailableBytes / (1024 * 1024 * 1024)
                val newAvailableGB = newAvailableBytes / (1024 * 1024 * 1024)
                oldAvailableGB <= threshold && newAvailableGB > threshold
            }

            if (triggered) {
                val unit = if (isPercentageMode) "%" else "GB"
                val value = if (isPercentageMode) {
                    // 计算当前可用内存百分比
                    (newAvailableBytes * 100 / memoryInfo.totalMem).toInt()
                } else {
                    newAvailableBytes / (1024 * 1024 * 1024)
                }
                Log.d(TAG, "Available memory above $threshold$unit: $value$unit")

                triggerCondition(condition, mapOf(
                    "memoryStateType" to "ABOVE_THRESHOLD",
                    "threshold" to threshold,
                    "currentValue" to value,
                    "isPercentageMode" to isPercentageMode,
                    "availableMemory" to newAvailableBytes,
                    "usedPercentage" to newPercentage,
                    "timestamp" to System.currentTimeMillis()
                ))
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error checking above threshold condition", e)
        }
    }

    /**
     * 检查内存变化条件
     */
    private fun checkMemoryChanged(
        condition: DeviceEventCondition,
        oldPercentage: Int,
        newPercentage: Int
    ) {
        try {
            val memoryChange = kotlin.math.abs(newPercentage - oldPercentage)
            if (memoryChange >= condition.memoryChangeThreshold) {
                Log.d(TAG, "Memory change detected: ${memoryChange}% >= ${condition.memoryChangeThreshold}%")

                triggerCondition(condition, mapOf(
                    "memoryStateType" to "CHANGED",
                    "changeThreshold" to condition.memoryChangeThreshold,
                    "actualChange" to memoryChange,
                    "oldPercentage" to oldPercentage,
                    "newPercentage" to newPercentage,
                    "timestamp" to System.currentTimeMillis()
                ))
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error checking memory changed condition", e)
        }
    }

    /**
     * 触发条件
     */
    private fun triggerCondition(condition: DeviceEventCondition, eventData: Map<String, Any>) {
        try {
            onConditionTriggered(condition, eventData)
        } catch (e: Exception) {
            Log.e(TAG, "Error triggering condition: ${condition.getDescription()}", e)
        }
    }

    /**
     * 获取监听状态
     */
    fun isMonitoring(): Boolean = isMonitoring

    /**
     * 获取注册的条件数量
     */
    fun getRegisteredConditionCount(): Int = registeredConditions.size

    /**
     * 应用进入前台时触发检测
     */
    fun onAppForeground(packageName: String) {
        synchronized(registeredConditions) {
            for (condition in registeredConditions) {
                if (condition.memoryCheckMode in listOf(
                    MemoryCheckMode.EVENT_DRIVEN,
                    MemoryCheckMode.INTELLIGENT,
                    MemoryCheckMode.HYBRID
                ) && condition.triggerOnAppForeground) {
                    scheduleDelayedCheck(
                        condition,
                        packageName,
                        condition.foregroundDelaySeconds,
                        "APP_FOREGROUND"
                    )
                }
            }
        }
    }

    /**
     * 应用启动时触发检测
     */
    fun onAppLaunched(packageName: String) {
        synchronized(registeredConditions) {
            for (condition in registeredConditions) {
                if (condition.memoryCheckMode in listOf(
                    MemoryCheckMode.EVENT_DRIVEN,
                    MemoryCheckMode.INTELLIGENT,
                    MemoryCheckMode.HYBRID
                ) && condition.triggerOnAppLaunch) {
                    scheduleDelayedCheck(
                        condition,
                        packageName,
                        condition.appLaunchDelaySeconds,
                        "APP_LAUNCH"
                    )
                }
            }
        }
    }

    /**
     * 系统内存压力时触发检测
     */
    fun onMemoryPressure() {
        synchronized(registeredConditions) {
            for (condition in registeredConditions) {
                if (condition.memoryCheckMode in listOf(
                    MemoryCheckMode.EVENT_DRIVEN,
                    MemoryCheckMode.INTELLIGENT,
                    MemoryCheckMode.HYBRID
                ) && condition.triggerOnMemoryPressure) {
                    scheduleDelayedCheck(
                        condition,
                        "SYSTEM",
                        0, // 内存压力时立即检测
                        "MEMORY_PRESSURE"
                    )
                }
            }
        }
    }

    /**
     * 安排延迟检测
     */
    private fun scheduleDelayedCheck(
        condition: DeviceEventCondition,
        packageName: String,
        delaySeconds: Int,
        triggerType: String
    ) {
        val jobKey = "${condition.id}_${packageName}_${triggerType}"

        // 取消之前的任务
        eventDrivenJobs[jobKey]?.cancel()

        val job = monitorScope.launch {
            try {
                if (delaySeconds > 0) {
                    Log.d(TAG, "Scheduling delayed memory check for $packageName in ${delaySeconds}s (trigger: $triggerType)")
                    delay(delaySeconds * 1000L)
                }

                // 开始连续监控
                startContinuousMonitoring(condition, packageName, triggerType)

            } catch (e: CancellationException) {
                Log.d(TAG, "Delayed check cancelled for $packageName (trigger: $triggerType)")
            } catch (e: Exception) {
                Log.e(TAG, "Error in delayed check for $packageName", e)
            } finally {
                eventDrivenJobs.remove(jobKey)
            }
        }

        eventDrivenJobs[jobKey] = job
    }

    /**
     * 开始连续监控
     */
    private suspend fun startContinuousMonitoring(
        condition: DeviceEventCondition,
        packageName: String,
        triggerType: String
    ) {
        val monitorDuration = condition.monitorDurationSeconds
        val monitorInterval = condition.monitorIntervalSeconds
        val startTime = System.currentTimeMillis()

        Log.d(TAG, "Starting continuous monitoring for $packageName (${monitorDuration}s, trigger: $triggerType)")

        while (System.currentTimeMillis() - startTime < monitorDuration * 1000L) {
            try {
                // 检查内存条件
                checkMemoryConditions()

                // 等待下次检测
                delay(monitorInterval * 1000L)

            } catch (e: CancellationException) {
                Log.d(TAG, "Continuous monitoring cancelled for $packageName")
                break
            } catch (e: Exception) {
                Log.e(TAG, "Error in continuous monitoring for $packageName", e)
                break
            }
        }

        Log.d(TAG, "Continuous monitoring completed for $packageName (trigger: $triggerType)")

        // 进入冷却期
        delay(condition.cooldownSeconds * 1000L)
    }

    /**
     * 重新启动内存检查任务（如果需要）
     * 当条件发生变化时调用，重新评估是否需要定时检查
     */
    private fun restartMemoryCheckJobIfNeeded() {
        // 停止当前的定时检查任务
        memoryCheckJob?.cancel()
        memoryCheckJob = null

        // 重新启动内存检查任务（会自动判断是否需要定时检查）
        startMemoryCheckJob()
    }

    /**
     * 为当前运行的应用启动智能学习
     */
    private fun startLearningForRunningApps(config: IntelligentConfig) {
        monitorScope.launch {
            try {
                // 获取当前运行的应用列表
                val runningApps = getRunningApplications()

                Log.d(TAG, "Starting intelligent learning for ${runningApps.size} running apps")

                // 为每个运行的应用启动学习
                runningApps.forEach { packageName ->
                    try {
                        intelligentDetector.startLearning(packageName, config)
                        Log.d(TAG, "Started learning for app: $packageName")
                    } catch (e: Exception) {
                        Log.w(TAG, "Failed to start learning for app: $packageName", e)
                    }
                }

                // 定期清理过期数据
                scheduleDataCleanup(config.dataRetentionDays)

            } catch (e: Exception) {
                Log.e(TAG, "Error starting learning for running apps", e)
            }
        }
    }

    /**
     * 获取当前运行的应用列表
     */
    private suspend fun getRunningApplications(): List<String> {
        return try {
            val runningApps = mutableListOf<String>()

            // 获取最近使用的应用（过去1小时内）
            val usageStatsManager = context.getSystemService(Context.USAGE_STATS_SERVICE) as? UsageStatsManager
            if (usageStatsManager != null) {
                val endTime = System.currentTimeMillis()
                val startTime = endTime - (60 * 60 * 1000) // 1小时前

                val usageStats = usageStatsManager.queryUsageStats(
                    UsageStatsManager.INTERVAL_BEST,
                    startTime,
                    endTime
                )

                // 过滤出有实际使用时间的应用
                usageStats?.forEach { stats ->
                    if (stats.totalTimeInForeground > 0 && stats.packageName.isNotEmpty()) {
                        runningApps.add(stats.packageName)
                    }
                }
            }

            // 如果无法获取使用统计，则获取当前前台应用
            if (runningApps.isEmpty()) {
                try {
                    val currentApp = getCurrentForegroundAppDirect()
                    if (!currentApp.isNullOrEmpty()) {
                        runningApps.add(currentApp)
                    }
                } catch (e: Exception) {
                    Log.w(TAG, "Failed to get current foreground app", e)
                }
            }

            Log.d(TAG, "Found ${runningApps.size} running applications")
            runningApps.distinct()

        } catch (e: Exception) {
            Log.e(TAG, "Error getting running applications", e)
            emptyList()
        }
    }

    /**
     * 直接获取当前前台应用
     */
    private suspend fun getCurrentForegroundAppDirect(): String? = withContext(Dispatchers.IO) {
        try {
            val usageStatsManager = context.getSystemService(Context.USAGE_STATS_SERVICE) as? UsageStatsManager
            if (usageStatsManager == null) {
                Log.e(TAG, "UsageStatsManager not available")
                return@withContext null
            }

            val currentTime = System.currentTimeMillis()
            val endTime = currentTime
            val startTime = endTime - 30 * 60 * 1000 // 30分钟前

            val usageStats = usageStatsManager.queryUsageStats(
                UsageStatsManager.INTERVAL_DAILY,
                startTime,
                endTime
            )

            if (usageStats.isNullOrEmpty()) {
                return@withContext null
            }

            // 查找最近活跃的应用
            var mostRecentApp: String? = null
            var mostRecentTime = 0L

            for (stat in usageStats) {
                val lastTimeUsed = stat.lastTimeUsed
                val totalTimeInForeground = stat.totalTimeInForeground

                // 检查是否在活跃阈值内（15分钟）
                if (currentTime - lastTimeUsed <= 15 * 60 * 1000L &&
                    totalTimeInForeground > 0 &&
                    lastTimeUsed > mostRecentTime) {
                    mostRecentTime = lastTimeUsed
                    mostRecentApp = stat.packageName
                }
            }

            return@withContext mostRecentApp

        } catch (e: Exception) {
            Log.e(TAG, "Error getting current foreground app directly", e)
            return@withContext null
        }
    }

    /**
     * 安排数据清理任务
     */
    private fun scheduleDataCleanup(retentionDays: Int) {
        monitorScope.launch {
            try {
                // 每天执行一次数据清理
                while (isMonitoring) {
                    delay(24 * 60 * 60 * 1000L) // 24小时

                    try {
                        intelligentDetector.cleanupExpiredData(retentionDays)
                        Log.d(TAG, "Scheduled data cleanup completed")
                    } catch (e: Exception) {
                        Log.e(TAG, "Error during scheduled data cleanup", e)
                    }
                }
            } catch (e: CancellationException) {
                Log.d(TAG, "Data cleanup task cancelled")
            } catch (e: Exception) {
                Log.e(TAG, "Error in data cleanup scheduling", e)
            }
        }
    }
}
