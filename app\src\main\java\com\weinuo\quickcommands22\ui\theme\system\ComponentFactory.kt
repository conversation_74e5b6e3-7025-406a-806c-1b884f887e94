package com.weinuo.quickcommands22.ui.theme.system

import androidx.compose.runtime.Composable
import com.weinuo.quickcommands22.ui.theme.config.*
import dev.chrisbanes.haze.HazeState

/**
 * 组件工厂接口
 *
 * 定义创建主题特定UI组件的统一接口
 * 每个主题可以提供完全不同的组件实现，实现高度的视觉差异化
 */
interface ComponentFactory {
    /**
     * 创建底部导航栏组件
     *
     * @return 返回一个接受配置参数的Composable函数
     */
    fun createBottomNavigation(): @Composable (BottomNavigationConfig) -> Unit



    /**
     * 创建卡片组件
     *
     * @return 返回一个接受配置参数的Composable函数
     */
    fun createCard(): @Composable (CardConfig) -> Unit

    /**
     * 创建按钮组件
     *
     * @return 返回一个接受配置参数的Composable函数
     */
    fun createButton(): @Composable (ButtonConfig) -> Unit

    /**
     * 创建文本输入框组件
     *
     * @return 返回一个接受配置参数的Composable函数
     */
    fun createTextField(): @Composable (TextFieldConfig) -> Unit

    /**
     * 创建搜索框组件
     *
     * @return 返回一个接受搜索框配置参数的Composable函数
     */
    fun createSearchTextField(): @Composable (SearchTextFieldConfig) -> Unit

    /**
     * 创建浮动操作按钮组件
     *
     * @return 返回一个接受配置参数的Composable函数
     */
    fun createFloatingActionButton(): @Composable (FloatingActionButtonConfig) -> Unit

    /**
     * 创建对话框组件
     *
     * @return 返回一个接受配置参数的Composable函数
     */
    fun createDialog(): @Composable (DialogConfig) -> Unit

    /**
     * 创建底部弹窗组件
     *
     * @return 返回一个接受配置参数的Composable函数
     */
    fun createBottomSheet(): @Composable (BottomSheetConfig) -> Unit

    /**
     * 创建开关组件
     *
     * @return 返回一个接受配置参数的Composable函数
     */
    fun createSwitch(): @Composable (SwitchConfig) -> Unit

    /**
     * 创建复选框组件
     *
     * @return 返回一个接受配置参数的Composable函数
     */
    fun createCheckbox(): @Composable (CheckboxConfig) -> Unit

    /**
     * 创建单选按钮组件
     *
     * @return 返回一个接受配置参数的Composable函数
     */
    fun createRadioButton(): @Composable (RadioButtonConfig) -> Unit

    /**
     * 创建滑块组件
     *
     * @return 返回一个接受配置参数的Composable函数
     */
    fun createSlider(): @Composable (SliderConfig) -> Unit

    /**
     * 创建进度指示器组件
     *
     * @return 返回一个接受配置参数的Composable函数
     */
    fun createProgressIndicator(): @Composable (ProgressIndicatorConfig) -> Unit

    /**
     * 创建分割线组件
     *
     * @return 返回一个接受配置参数的Composable函数
     */
    fun createDivider(): @Composable (DividerConfig) -> Unit

    /**
     * 创建芯片组件
     *
     * @return 返回一个接受配置参数的Composable函数
     */
    fun createChip(): @Composable (ChipConfig) -> Unit

    /**
     * 创建标签页组件
     *
     * @return 返回一个接受配置参数的Composable函数
     */
    fun createTabs(): @Composable (TabsConfig) -> Unit

    /**
     * 创建列表项组件
     *
     * @return 返回一个接受配置参数的Composable函数
     */
    fun createListItem(): @Composable (ListItemConfig) -> Unit

    /**
     * 创建图标按钮组件
     *
     * @return 返回一个接受配置参数的Composable函数
     */
    fun createIconButton(): @Composable (IconButtonConfig) -> Unit

    /**
     * 创建徽章组件
     *
     * @return 返回一个接受配置参数的Composable函数
     */
    fun createBadge(): @Composable (BadgeConfig) -> Unit

    /**
     * 创建工具提示组件
     *
     * @return 返回一个接受配置参数的Composable函数
     */
    fun createTooltip(): @Composable (TooltipConfig) -> Unit

    /**
     * 获取组件工厂的版本
     *
     * @return 工厂版本，用于兼容性检查
     */
    fun getVersion(): String {
        return "1.0.0"
    }

    /**
     * 检查是否支持特定组件类型
     *
     * @param componentType 组件类型
     * @return 是否支持该组件
     */
    fun supportsComponent(componentType: ComponentType): Boolean {
        return true // 默认支持所有组件
    }
}

/**
 * 支持Haze模糊效果的组件工厂扩展接口
 *
 * 为需要模糊效果的组件提供HazeState支持
 */
interface HazeAwareComponentFactory : ComponentFactory {
    /**
     * 创建支持模糊效果的底部导航栏组件
     *
     * @return 返回一个接受配置参数和HazeState的Composable函数
     */
    fun createBottomNavigationWithHaze(): @Composable (BottomNavigationConfig, HazeState) -> Unit



    /**
     * 创建支持模糊效果的对话框组件
     *
     * @return 返回一个接受配置参数和HazeState的Composable函数
     */
    fun createDialogWithHaze(): @Composable (DialogConfig, HazeState) -> Unit {
        // 默认实现：忽略HazeState，使用原有方法
        return { config, _ -> createDialog()(config) }
    }

    /**
     * 创建支持模糊效果的底部弹窗组件
     *
     * @return 返回一个接受配置参数和HazeState的Composable函数
     */
    fun createBottomSheetWithHaze(): @Composable (BottomSheetConfig, HazeState) -> Unit {
        // 默认实现：忽略HazeState，使用原有方法
        return { config, _ -> createBottomSheet()(config) }
    }
}

/**
 * 组件类型枚举
 *
 * 定义所有可用的组件类型
 */
enum class ComponentType {
    BOTTOM_NAVIGATION,
    TOP_APP_BAR,
    CARD,
    BUTTON,
    TEXT_FIELD,
    SEARCH_TEXT_FIELD,
    FLOATING_ACTION_BUTTON,
    DIALOG,
    BOTTOM_SHEET,
    SWITCH,
    CHECKBOX,
    RADIO_BUTTON,
    SLIDER,
    PROGRESS_INDICATOR,
    DIVIDER,
    CHIP,
    TABS,
    LIST_ITEM,
    ICON_BUTTON,
    BADGE,
    TOOLTIP
}
