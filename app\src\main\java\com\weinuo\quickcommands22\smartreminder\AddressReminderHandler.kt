package com.weinuo.quickcommands22.smartreminder

import android.content.ClipboardManager
import android.content.Context
import android.content.pm.PackageManager
import android.util.Log
import com.weinuo.quickcommands22.R
import com.weinuo.quickcommands22.model.SmartReminderType
import com.weinuo.quickcommands22.storage.SmartReminderConfigAdapter
import kotlinx.coroutines.*

/**
 * 地址提醒处理器
 *
 * 监听剪贴板变化，检测地址内容并建议打开地图应用。
 * 当用户复制地址时，自动识别地址格式并显示提醒悬浮窗。
 *
 * 检测逻辑：
 * - 监听剪贴板变化事件
 * - 检测中英文地址格式
 * - 检查是否已配置地图应用
 * - 在合适的时机发送提醒通知
 *
 * 设计特点：
 * - 智能防重复：避免频繁提醒
 * - 延迟检测：给剪贴板变化稳定时间
 * - 状态感知：只在需要时提醒
 * - 用户友好：提供直接的操作建议
 */
class AddressReminderHandler(
    private val context: Context,
    private val onReminderTriggered: (Map<String, Any>) -> Unit
) {

    companion object {
        private const val TAG = "AddressReminderHandler"
        private const val DEFAULT_DETECTION_DELAY = 500L // 检测延迟，避免频繁触发
    }

    private val clipboardManager = context.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
    private val packageManager = context.packageManager
    private val configAdapter = SmartReminderConfigAdapter(context)
    
    private var clipboardListener: ClipboardManager.OnPrimaryClipChangedListener? = null
    private var isMonitoring = false
    private var lastReminderTime = 0L
    
    private val handlerScope = CoroutineScope(SupervisorJob() + Dispatchers.Main)

    /**
     * 开始监听剪贴板变化
     */
    fun startMonitoring() {
        if (isMonitoring) {
            Log.d(TAG, "Already monitoring clipboard")
            return
        }

        try {
            Log.d(TAG, "Starting address reminder monitoring")

            // 创建剪贴板监听器
            clipboardListener = ClipboardManager.OnPrimaryClipChangedListener {
                handlerScope.launch {
                    // 使用默认延迟时间
                    delay(DEFAULT_DETECTION_DELAY)
                    handleClipboardChange()
                }
            }

            // 注册监听器
            clipboardManager.addPrimaryClipChangedListener(clipboardListener)
            isMonitoring = true

            Log.d(TAG, "Address reminder monitoring started")

        } catch (e: Exception) {
            Log.e(TAG, "Error starting address reminder monitoring", e)
        }
    }

    /**
     * 停止监听剪贴板变化
     */
    fun stopMonitoring() {
        if (!isMonitoring) {
            Log.d(TAG, "Not monitoring clipboard")
            return
        }

        try {
            Log.d(TAG, "Stopping address reminder monitoring")

            clipboardListener?.let {
                clipboardManager.removePrimaryClipChangedListener(it)
            }
            clipboardListener = null
            isMonitoring = false

            Log.d(TAG, "Address reminder monitoring stopped")

        } catch (e: Exception) {
            Log.e(TAG, "Error stopping address reminder monitoring", e)
        }
    }

    /**
     * 处理剪贴板变化事件
     */
    private suspend fun handleClipboardChange() {
        try {
            // 加载配置
            val config = loadAddressReminderConfig()

            // 检查是否已配置地图应用
            if (config.selectedMapApps.isEmpty()) {
                Log.d(TAG, "Map app not configured, skipping reminder")
                return
            }

            // 检查冷却时间
            val currentTime = System.currentTimeMillis()
            if (currentTime - lastReminderTime < config.cooldownTime * 1000L) {
                Log.d(TAG, "Reminder in cooldown period, skipping")
                return
            }

            // 获取剪贴板内容
            val clipText = getClipboardText() ?: return

            Log.d(TAG, "Clipboard changed, checking for address: ${clipText.take(50)}...")

            // 检测地址
            val detectionResult = AddressDetector.detectAddress(clipText)
            if (!detectionResult.isAddress) {
                Log.d(TAG, "No address detected")
                return
            }

            Log.d(TAG, "Detected address: ${detectionResult.detectedAddress}")

            // 检查地图应用是否已安装
            val enabledApps = config.selectedMapApps.filter { it.isEnabled }
            val installedApps = enabledApps.filter { isAppInstalled(it.packageName) }
            if (installedApps.isEmpty()) {
                Log.d(TAG, "No enabled map apps installed, skipping reminder")
                return
            }

            // 延迟提醒，给剪贴板变化稳定时间
            delay(config.delayTime * 1000L)

            // 显示地址提醒
            showAddressReminder(config, detectionResult)

            // 更新最后提醒时间
            lastReminderTime = currentTime

        } catch (e: Exception) {
            Log.e(TAG, "Error handling clipboard change", e)
        }
    }

    /**
     * 获取剪贴板文本内容
     */
    private fun getClipboardText(): String? {
        return try {
            val clip = clipboardManager.primaryClip
            if (clip != null && clip.itemCount > 0) {
                val item = clip.getItemAt(0)
                item.text?.toString()
            } else {
                null
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting clipboard text", e)
            null
        }
    }

    /**
     * 检查应用是否已安装
     */
    private fun isAppInstalled(packageName: String): Boolean {
        return try {
            packageManager.getPackageInfo(packageName, 0)
            true
        } catch (e: PackageManager.NameNotFoundException) {
            false
        } catch (e: Exception) {
            Log.e(TAG, "Error checking if app is installed: $packageName", e)
            false
        }
    }

    /**
     * 显示地址提醒悬浮窗
     */
    private fun showAddressReminder(config: SmartReminderConfigAdapter.AddressReminderConfig, detectionResult: AddressDetector.DetectionResult) {
        try {
            // 获取启用的地图应用
            val enabledApps = config.selectedMapApps.filter { it.isEnabled }
            if (enabledApps.isEmpty()) {
                Log.d(TAG, "No enabled map apps found")
                return
            }

            Log.d(TAG, "Attempting to show address reminder for ${enabledApps.size} apps")

            val title = context.getString(R.string.address_reminder_title)

            // 根据应用数量生成不同的消息
            val message = when {
                enabledApps.size == 1 -> context.getString(R.string.address_reminder_message, enabledApps.first().appName)
                enabledApps.size <= 3 -> "检测到地址，建议打开：${enabledApps.joinToString("、") { "「${it.appName}」" }}"
                else -> "检测到地址，建议打开地图应用（${enabledApps.size}个可选）"
            }

            Log.d(TAG, "Calling SmartReminderOverlayService.showMultiAddressReminder")

            // 调用SmartReminderOverlayService显示多应用地址提醒
            com.weinuo.quickcommands22.service.SmartReminderOverlayService.showMultiAddressReminder(
                context = context,
                title = title,
                message = message,
                selectedApps = enabledApps,
                buttonConfigs = config.buttonConfigs.filter { buttonConfig ->
                    enabledApps.any { app -> app.packageName == buttonConfig.appPackageName } && buttonConfig.isEnabled
                }
            )

            Log.d(TAG, "SmartReminderOverlayService.showMultiAddressReminder called successfully")

            // 触发回调通知
            onReminderTriggered(mapOf(
                "type" to "address_reminder",
                "timestamp" to System.currentTimeMillis(),
                "selectedApps" to enabledApps.map { mapOf("packageName" to it.packageName, "appName" to it.appName) },
                "detectedAddress" to detectionResult.detectedAddress,
                "addressType" to detectionResult.addressType.name,
                "message" to message
            ))
        } catch (e: Exception) {
            Log.e(TAG, "Error showing address reminder", e)
        }
    }

    /**
     * 加载地址提醒配置
     */
    private fun loadAddressReminderConfig(): SmartReminderConfigAdapter.AddressReminderConfig {
        return try {
            val reminderTypeId = SmartReminderType.ADDRESS_REMINDER.id
            if (configAdapter.hasConfig(reminderTypeId)) {
                configAdapter.loadAddressReminderConfig(reminderTypeId)
            } else {
                // 返回默认配置
                SmartReminderConfigAdapter.AddressReminderConfig()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error loading address reminder config", e)
            SmartReminderConfigAdapter.AddressReminderConfig()
        }
    }
}
