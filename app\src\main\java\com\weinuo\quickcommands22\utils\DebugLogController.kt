package com.weinuo.quickcommands22.utils

import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Environment
import android.provider.Settings
import android.util.Log
import androidx.core.content.ContextCompat
import com.weinuo.quickcommands22.service.DebugLogOverlayService

/**
 * 调试日志控制器
 * 
 * 用于控制调试日志功能的启动和停止
 */
object DebugLogController {
    
    private const val TAG = "DebugLogController"
    
    /**
     * 启动调试日志功能
     * 包括文件日志和悬浮窗显示
     */
    fun startDebugLogging(context: Context): Boolean {
        return try {
            Log.d(TAG, "Starting debug logging")

            // 检查存储权限
            if (!hasStoragePermission(context)) {
                Log.w(TAG, "No storage permission, requesting permission")
                requestStoragePermission(context)
                return false
            }

            // 检查悬浮窗权限
            if (!DebugLogOverlayService.canDrawOverlays(context)) {
                Log.w(TAG, "No overlay permission, requesting permission")
                requestOverlayPermission(context)
                return false
            }

            // 启动悬浮窗服务
            DebugLogOverlayService.start(context)

            // 记录启动日志
            val debugLogManager = DebugLogManager.getInstance(context)
            debugLogManager.logInfo(TAG, "调试日志功能已启动")
            debugLogManager.logInfo(TAG, "日志文件位置: /storage/emulated/0/log.txt")
            debugLogManager.logInfo(TAG, "悬浮窗显示最近3条日志")

            Log.d(TAG, "Debug logging started successfully")
            true

        } catch (e: Exception) {
            Log.e(TAG, "Failed to start debug logging", e)
            false
        }
    }
    
    /**
     * 停止调试日志功能
     */
    fun stopDebugLogging(context: Context) {
        try {
            Log.d(TAG, "Stopping debug logging")
            
            // 记录停止日志
            val debugLogManager = DebugLogManager.getInstance(context)
            debugLogManager.logInfo(TAG, "调试日志功能即将停止")
            
            // 停止悬浮窗服务
            DebugLogOverlayService.stop(context)
            
            Log.d(TAG, "Debug logging stopped")
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to stop debug logging", e)
        }
    }
    
    /**
     * 清空调试日志
     */
    fun clearDebugLogs(context: Context) {
        try {
            Log.d(TAG, "Clearing debug logs")
            
            val debugLogManager = DebugLogManager.getInstance(context)
            debugLogManager.clearLogs()
            debugLogManager.logInfo(TAG, "调试日志已清空")
            
            Log.d(TAG, "Debug logs cleared")
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to clear debug logs", e)
        }
    }
    
    /**
     * 请求存储权限
     */
    private fun requestStoragePermission(context: Context) {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                // Android 11+ 需要 MANAGE_EXTERNAL_STORAGE 权限
                val intent = Intent(Settings.ACTION_MANAGE_APP_ALL_FILES_ACCESS_PERMISSION).apply {
                    data = Uri.parse("package:${context.packageName}")
                    flags = Intent.FLAG_ACTIVITY_NEW_TASK
                }
                context.startActivity(intent)
            } else {
                // Android 10 及以下，需要在运行时请求 WRITE_EXTERNAL_STORAGE 权限
                // 这里只是记录日志，实际权限请求需要在Activity中进行
                Log.w(TAG, "需要在Activity中请求WRITE_EXTERNAL_STORAGE权限")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to request storage permission", e)
        }
    }

    /**
     * 请求悬浮窗权限
     */
    private fun requestOverlayPermission(context: Context) {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                val intent = Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION).apply {
                    data = Uri.parse("package:${context.packageName}")
                    flags = Intent.FLAG_ACTIVITY_NEW_TASK
                }
                context.startActivity(intent)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to request overlay permission", e)
        }
    }
    
    /**
     * 检查是否有存储权限
     */
    fun hasStoragePermission(context: Context): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            // Android 11+ 需要 MANAGE_EXTERNAL_STORAGE 权限
            Environment.isExternalStorageManager()
        } else {
            // Android 10 及以下需要 WRITE_EXTERNAL_STORAGE 权限
            ContextCompat.checkSelfPermission(
                context,
                android.Manifest.permission.WRITE_EXTERNAL_STORAGE
            ) == PackageManager.PERMISSION_GRANTED
        }
    }

    /**
     * 检查是否有悬浮窗权限
     */
    fun hasOverlayPermission(context: Context): Boolean {
        return DebugLogOverlayService.canDrawOverlays(context)
    }

    /**
     * 检查是否有所有必要权限
     */
    fun hasAllPermissions(context: Context): Boolean {
        return hasStoragePermission(context) && hasOverlayPermission(context)
    }
    
    /**
     * 记录应用状态变化的关键日志
     */
    fun logAppStateChange(context: Context, fromApp: String?, toApp: String?) {
        val debugLogManager = DebugLogManager.getInstance(context)
        
        when {
            fromApp == null && toApp != null -> {
                debugLogManager.logInfo("AppState", "应用启动: $toApp")
            }
            fromApp != null && toApp == null -> {
                debugLogManager.logInfo("AppState", "应用退出: $fromApp")
            }
            fromApp != null && toApp != null && fromApp != toApp -> {
                debugLogManager.logInfo("AppState", "应用切换: $fromApp -> $toApp")
            }
        }
    }
    
    /**
     * 记录强制停止应用的关键日志
     */
    fun logForceStopApp(context: Context, packageName: String, reason: String, success: Boolean) {
        val debugLogManager = DebugLogManager.getInstance(context)
        
        if (success) {
            debugLogManager.logInfo("ForceStop", "成功停止应用: $packageName (原因: $reason)")
        } else {
            debugLogManager.logError("ForceStop", "停止应用失败: $packageName (原因: $reason)")
        }
    }
    
    /**
     * 记录前台应用检测的关键日志
     */
    fun logForegroundDetection(context: Context, packageName: String?, isSkipped: Boolean = false) {
        val debugLogManager = DebugLogManager.getInstance(context)
        
        if (packageName != null) {
            if (isSkipped) {
                debugLogManager.logInfo("ForegroundCheck", "跳过前台应用: $packageName")
            } else {
                debugLogManager.logDebug("ForegroundCheck", "检测到前台应用: $packageName")
            }
        } else {
            debugLogManager.logDebug("ForegroundCheck", "未检测到前台应用")
        }
    }
    
    /**
     * 记录音乐播放检测的关键日志
     */
    fun logMusicPlayingDetection(context: Context, musicApps: Set<String>) {
        val debugLogManager = DebugLogManager.getInstance(context)
        
        if (musicApps.isNotEmpty()) {
            debugLogManager.logInfo("MusicCheck", "检测到正在播放音乐的应用: ${musicApps.joinToString(", ")}")
        } else {
            debugLogManager.logDebug("MusicCheck", "未检测到正在播放音乐的应用")
        }
    }
    
    /**
     * 记录条件触发的关键日志
     */
    fun logConditionTriggered(context: Context, conditionDescription: String, triggerApp: String?) {
        val debugLogManager = DebugLogManager.getInstance(context)
        
        if (triggerApp != null) {
            debugLogManager.logInfo("Condition", "条件触发: $conditionDescription (触发应用: $triggerApp)")
        } else {
            debugLogManager.logInfo("Condition", "条件触发: $conditionDescription")
        }
    }
}
