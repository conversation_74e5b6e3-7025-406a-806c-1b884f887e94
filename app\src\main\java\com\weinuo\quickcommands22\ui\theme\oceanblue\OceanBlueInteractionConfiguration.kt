package com.weinuo.quickcommands22.ui.theme.oceanblue

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import com.weinuo.quickcommands22.ui.theme.system.InteractionConfiguration
import com.weinuo.quickcommands22.ui.theme.config.*

/**
 * 海洋蓝主题交互配置
 *
 * 实现分层设计风格的交互反馈配置
 */
class OceanBlueInteractionConfiguration : InteractionConfiguration {
    
    override val hover = InteractionStateConfig(
        backgroundColor = Color.Black.copy(alpha = 0.05f),
        contentColor = null,
        borderColor = null,
        elevation = 6.dp,
        scale = 1.02f,
        alpha = 1.0f,
        animationDuration = 150
    )
    
    override val pressed = InteractionStateConfig(
        backgroundColor = Color.Black.copy(alpha = 0.1f),
        contentColor = null,
        borderColor = null,
        elevation = 3.dp,
        scale = 0.98f,
        alpha = 1.0f,
        animationDuration = 150
    )
    
    override val focused = InteractionStateConfig(
        backgroundColor = Color.Blue.copy(alpha = 0.1f),
        contentColor = null,
        borderColor = Color.Blue,
        elevation = 6.dp,
        scale = 1.0f,
        alpha = 1.0f,
        animationDuration = 150
    )
    
    override val active = InteractionStateConfig(
        backgroundColor = Color.Blue.copy(alpha = 0.2f),
        contentColor = null,
        borderColor = null,
        elevation = 6.dp,
        scale = 1.0f,
        alpha = 1.0f,
        animationDuration = 150
    )
    
    override val selected = InteractionStateConfig(
        backgroundColor = Color.Blue.copy(alpha = 0.16f),
        contentColor = null,
        borderColor = null,
        elevation = 6.dp,
        scale = 1.0f,
        alpha = 1.0f,
        animationDuration = 150
    )
    
    override val disabled = InteractionStateConfig(
        backgroundColor = null,
        contentColor = Color.Gray,
        borderColor = null,
        elevation = 0.dp,
        scale = 1.0f,
        alpha = 0.38f,
        animationDuration = 150
    )
    
    override val dragging = InteractionStateConfig(
        backgroundColor = Color.Black.copy(alpha = 0.16f),
        contentColor = null,
        borderColor = null,
        elevation = 12.dp,
        scale = 1.05f,
        alpha = 0.9f,
        animationDuration = 150
    )
}
