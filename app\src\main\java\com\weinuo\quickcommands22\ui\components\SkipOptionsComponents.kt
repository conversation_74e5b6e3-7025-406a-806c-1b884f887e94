package com.weinuo.quickcommands22.ui.components

import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Info
import androidx.compose.material.icons.filled.VpnKey
import androidx.compose.material.icons.filled.Warning
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.weinuo.quickcommands22.data.SettingsRepository
import com.weinuo.quickcommands22.model.SimpleAppInfo

/**
 * 可复用的跳过选项配置组件
 *
 * 提供统一的跳过选项UI，包括前台应用、音乐应用、VPN应用等选项。
 * 支持参数化配置，允许不同功能模块使用不同的选项组合。
 *
 * 设计原则：
 * - 高内聚低耦合：组件职责单一，接口清晰
 * - 可扩展性：支持未来添加新的跳过选项
 * - 参数化配置：通过参数控制显示哪些选项
 * - 状态独立：不同功能的状态完全独立
 *
 * @param title 跳过选项区域的标题，默认为"跳过选项"
 * @param skipForegroundApp 是否跳过前台应用
 * @param onSkipForegroundAppChanged 跳过前台应用状态变化回调
 * @param skipMusicPlayingApp 是否跳过音乐播放应用
 * @param onSkipMusicPlayingAppChanged 跳过音乐播放应用状态变化回调
 * @param skipVpnApp 是否跳过VPN应用
 * @param onSkipVpnAppChanged 跳过VPN应用状态变化回调
 * @param selectedVpnApps 选中的VPN应用列表
 * @param onVpnAppsChanged VPN应用列表变化回调
 * @param navController 导航控制器，用于跳转到应用选择界面
 * @param stateKey 状态键，用于区分不同功能模块的状态存储
 * @param showForegroundOption 是否显示前台应用选项，默认true
 * @param showMusicOption 是否显示音乐应用选项，默认true
 * @param showVpnOption 是否显示VPN应用选项，默认true
 * @param enableRealtimeForegroundDetection 是否启用实时前台应用检测
 * @param onEnableRealtimeForegroundDetectionChanged 实时前台应用检测状态变化回调
 * @param showRealtimeForegroundOption 是否显示实时前台应用检测选项，默认false
 */
@Composable
fun SkipOptionsSection(
    title: String = "跳过选项",
    skipForegroundApp: Boolean,
    onSkipForegroundAppChanged: (Boolean) -> Unit,
    skipMusicPlayingApp: Boolean,
    onSkipMusicPlayingAppChanged: (Boolean) -> Unit,
    skipVpnApp: Boolean,
    onSkipVpnAppChanged: (Boolean) -> Unit,
    selectedVpnApps: List<SimpleAppInfo>,
    onVpnAppsChanged: (List<SimpleAppInfo>) -> Unit,
    onNavigateToVpnAppSelection: (List<String>) -> Unit,
    stateKey: String,
    showForegroundOption: Boolean = true,
    showMusicOption: Boolean = true,
    showVpnOption: Boolean = true,
    enableRealtimeForegroundDetection: Boolean = false,
    onEnableRealtimeForegroundDetectionChanged: (Boolean) -> Unit = {},
    showRealtimeForegroundOption: Boolean = false
) {
    // 获取实验性功能状态
    val context = LocalContext.current
    val settingsRepository = remember { SettingsRepository(context) }
    val globalSettings by settingsRepository.globalSettings.collectAsState()

    Column(
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        Text(
            text = title,
            style = MaterialTheme.typography.titleMedium
        )

        // 跳过前台应用选项
        if (showForegroundOption) {
            SkipForegroundAppOption(
                skipForegroundApp = skipForegroundApp,
                onSkipForegroundAppChanged = onSkipForegroundAppChanged
            )
        }

        // 跳过正在播放音乐的应用选项
        if (showMusicOption) {
            SkipMusicPlayingAppOption(
                skipMusicPlayingApp = skipMusicPlayingApp,
                onSkipMusicPlayingAppChanged = onSkipMusicPlayingAppChanged
            )
        }

        // 跳过VPN应用选项 - 实验性功能
        if (showVpnOption && globalSettings.experimentalFeaturesEnabled) {
            SkipVpnAppOption(
                skipVpnApp = skipVpnApp,
                onSkipVpnAppChanged = onSkipVpnAppChanged,
                selectedVpnApps = selectedVpnApps,
                onNavigateToVpnAppSelection = onNavigateToVpnAppSelection
            )
        }

        // 实时前台应用检测选项
        if (showRealtimeForegroundOption) {
            RealtimeForegroundDetectionOption(
                enableRealtimeForegroundDetection = enableRealtimeForegroundDetection,
                onEnableRealtimeForegroundDetectionChanged = onEnableRealtimeForegroundDetectionChanged
            )
        }
    }
}

/**
 * 跳过前台应用选项组件
 */
@Composable
private fun SkipForegroundAppOption(
    skipForegroundApp: Boolean,
    onSkipForegroundAppChanged: (Boolean) -> Unit
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Checkbox(
            checked = skipForegroundApp,
            onCheckedChange = onSkipForegroundAppChanged
        )
        Spacer(modifier = Modifier.width(8.dp))
        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = "跳过前台应用",
                style = MaterialTheme.typography.bodyMedium
            )
            Text(
                text = "自动检测当前正在使用的应用并跳过",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

/**
 * 实时前台应用检测选项组件
 */
@Composable
private fun RealtimeForegroundDetectionOption(
    enableRealtimeForegroundDetection: Boolean,
    onEnableRealtimeForegroundDetectionChanged: (Boolean) -> Unit
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Checkbox(
                checked = enableRealtimeForegroundDetection,
                onCheckedChange = onEnableRealtimeForegroundDetectionChanged
            )
            Spacer(modifier = Modifier.width(8.dp))
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = "实时前台应用检测",
                    style = MaterialTheme.typography.bodyMedium
                )
                Text(
                    text = "每个应用强制停止前检测是否为前台应用",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }

        // 详细说明卡片
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = if (enableRealtimeForegroundDetection) {
                    MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.3f)
                } else {
                    MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.3f)
                }
            )
        ) {
            Column(
                modifier = Modifier.padding(12.dp),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = if (enableRealtimeForegroundDetection) {
                            Icons.Default.Info
                        } else {
                            Icons.Default.Warning
                        },
                        contentDescription = null,
                        tint = if (enableRealtimeForegroundDetection) {
                            MaterialTheme.colorScheme.primary
                        } else {
                            MaterialTheme.colorScheme.onSurfaceVariant
                        },
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = if (enableRealtimeForegroundDetection) "启用后的效果" else "不启用的风险",
                        style = MaterialTheme.typography.labelMedium,
                        fontWeight = FontWeight.Medium
                    )
                }

                if (enableRealtimeForegroundDetection) {
                    Text(
                        text = "✓ 完全避免误停止前台应用\n✓ 检测到前台应用变化时自动跳过\n✓ 适合大量应用的批量操作",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Text(
                        text = "⚠ 会增加少量耗电量（每个应用检测前台状态）\n⚠ 执行时间可能略微延长",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                } else {
                    Text(
                        text = "⚠ 可能误停止前台应用\n⚠ 用户切换应用时存在时序问题\n⚠ 大量应用操作时风险更高",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Text(
                        text = "✓ 耗电量更低\n✓ 执行速度更快",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }
    }
}

/**
 * 跳过音乐播放应用选项组件
 */
@Composable
private fun SkipMusicPlayingAppOption(
    skipMusicPlayingApp: Boolean,
    onSkipMusicPlayingAppChanged: (Boolean) -> Unit
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Checkbox(
            checked = skipMusicPlayingApp,
            onCheckedChange = onSkipMusicPlayingAppChanged
        )
        Spacer(modifier = Modifier.width(8.dp))
        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = "跳过正在播放音乐的应用",
                style = MaterialTheme.typography.bodyMedium
            )
            Text(
                text = "自动检测正在播放音乐的应用并跳过",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

/**
 * 跳过VPN应用选项组件
 */
@Composable
private fun SkipVpnAppOption(
    skipVpnApp: Boolean,
    onSkipVpnAppChanged: (Boolean) -> Unit,
    selectedVpnApps: List<SimpleAppInfo>,
    onNavigateToVpnAppSelection: (List<String>) -> Unit
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Checkbox(
            checked = skipVpnApp,
            onCheckedChange = onSkipVpnAppChanged
        )
        Spacer(modifier = Modifier.width(8.dp))
        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = "跳过VPN应用",
                style = MaterialTheme.typography.bodyMedium
            )
            Text(
                text = "VPN激活时跳过指定的VPN应用",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }

    // VPN应用选择按钮（条件显示）
    if (skipVpnApp) {
        VpnAppSelectionButton(
            selectedVpnApps = selectedVpnApps,
            onNavigateToVpnAppSelection = onNavigateToVpnAppSelection
        )

        // 显示选中的VPN应用（条件显示）
        if (selectedVpnApps.isNotEmpty()) {
            VpnAppList(selectedVpnApps = selectedVpnApps)
        }
    }
}

/**
 * VPN应用选择按钮组件
 */
@Composable
private fun VpnAppSelectionButton(
    selectedVpnApps: List<SimpleAppInfo>,
    onNavigateToVpnAppSelection: (List<String>) -> Unit
) {
    OutlinedButton(
        onClick = {
            onNavigateToVpnAppSelection(selectedVpnApps.map { it.packageName })
        },
        modifier = Modifier.fillMaxWidth()
    ) {
        Icon(
            imageVector = Icons.Default.VpnKey,
            contentDescription = null,
            modifier = Modifier.size(16.dp)
        )
        Spacer(modifier = Modifier.width(8.dp))
        Text(
            text = when {
                selectedVpnApps.isEmpty() -> "选择VPN应用"
                selectedVpnApps.size == 1 -> "已选择: ${selectedVpnApps.first().appName}"
                else -> "已选择 ${selectedVpnApps.size} 个VPN应用"
            }
        )
    }
}

/**
 * VPN应用列表显示组件
 */
@Composable
private fun VpnAppList(selectedVpnApps: List<SimpleAppInfo>) {
    selectedVpnApps.forEach { app ->
        Text(
            text = "• ${app.appName}",
            style = MaterialTheme.typography.bodySmall
        )
    }
}


