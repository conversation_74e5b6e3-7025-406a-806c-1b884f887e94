package com.weinuo.quickcommands22.storage

import android.content.Context
import com.weinuo.quickcommands22.model.*

/**
 * 高级内存配置存储工具
 */
object AdvancedMemoryConfigStorage {

    private const val PREFS_NAME = "advanced_memory_config"

    /**
     * 加载高级配置
     */
    fun loadAdvancedConfig(
        context: Context,
        mode: MemoryCheckMode,
        conditionId: String
    ): Any {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        val prefix = "advanced_memory_${mode.name.lowercase()}_${conditionId}_"

        return when (mode) {
            MemoryCheckMode.EVENT_DRIVEN -> {
                EventDrivenConfig(
                    triggerOnAppForeground = prefs.getBoolean("${prefix}trigger_on_app_foreground", true),
                    triggerOnAppLaunch = prefs.getBoolean("${prefix}trigger_on_app_launch", true),
                    triggerOnMemoryPressure = prefs.getBoolean("${prefix}trigger_on_memory_pressure", true),
                    appLaunchDelaySeconds = prefs.getInt("${prefix}app_launch_delay_seconds", 10),
                    foregroundDelaySeconds = prefs.getInt("${prefix}foreground_delay_seconds", 5),
                    enableSmartDelay = prefs.getBoolean("${prefix}enable_smart_delay", true),
                    monitorDurationSeconds = prefs.getInt("${prefix}monitor_duration_seconds", 30),
                    monitorIntervalSeconds = prefs.getInt("${prefix}monitor_interval_seconds", 3),
                    cooldownSeconds = prefs.getInt("${prefix}cooldown_seconds", 60)
                )
            }
            MemoryCheckMode.ADAPTIVE -> {
                AdaptiveConfig(
                    adaptiveStrategy = try {
                        AdaptiveStrategy.valueOf(prefs.getString("${prefix}adaptive_strategy", AdaptiveStrategy.BALANCED.name) ?: AdaptiveStrategy.BALANCED.name)
                    } catch (e: Exception) {
                        AdaptiveStrategy.BALANCED
                    },
                    enableMemoryPressureAdaptation = prefs.getBoolean("${prefix}enable_memory_pressure_adaptation", true),
                    enableAppActivityAdaptation = prefs.getBoolean("${prefix}enable_app_activity_adaptation", true),
                    memoryAbundantFrequency = prefs.getInt("${prefix}memory_abundant_frequency", 120),
                    memoryTightFrequency = prefs.getInt("${prefix}memory_tight_frequency", 5)
                )
            }
            MemoryCheckMode.INTELLIGENT -> {
                IntelligentConfig(
                    enableLearning = prefs.getBoolean("${prefix}enable_learning", true),
                    minSamplesForPrediction = prefs.getInt("${prefix}min_samples_for_prediction", 5),
                    confidenceThreshold = prefs.getFloat("${prefix}confidence_threshold", 0.7f),
                    stabilityCheckInterval = prefs.getInt("${prefix}stability_check_interval", 2),
                    stabilityThresholdMB = prefs.getInt("${prefix}stability_threshold_mb", 50),
                    requiredStableChecks = prefs.getInt("${prefix}required_stable_checks", 3),
                    maxHistoryRecords = prefs.getInt("${prefix}max_history_records", 30),
                    dataRetentionDays = prefs.getInt("${prefix}data_retention_days", 30)
                )
            }
            MemoryCheckMode.HYBRID -> {
                HybridConfig(
                    enableEventDriven = prefs.getBoolean("${prefix}enable_event_driven", true),
                    enableAdaptive = prefs.getBoolean("${prefix}enable_adaptive", true),
                    enableIntelligent = prefs.getBoolean("${prefix}enable_intelligent", false),
                    eventDrivenWeight = prefs.getFloat("${prefix}event_driven_weight", 0.4f),
                    adaptiveWeight = prefs.getFloat("${prefix}adaptive_weight", 0.4f),
                    intelligentWeight = prefs.getFloat("${prefix}intelligent_weight", 0.2f)
                )
            }
            else -> EventDrivenConfig() // 默认配置
        }
    }

    /**
     * 保存高级配置
     */
    fun saveAdvancedConfig(
        context: Context,
        mode: MemoryCheckMode,
        conditionId: String,
        config: Any
    ) {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        val prefix = "advanced_memory_${mode.name.lowercase()}_${conditionId}_"

        val editor = prefs.edit()

        when (mode) {
            MemoryCheckMode.EVENT_DRIVEN -> {
                val eventConfig = config as EventDrivenConfig
                editor.putBoolean("${prefix}trigger_on_app_foreground", eventConfig.triggerOnAppForeground)
                editor.putBoolean("${prefix}trigger_on_app_launch", eventConfig.triggerOnAppLaunch)
                editor.putBoolean("${prefix}trigger_on_memory_pressure", eventConfig.triggerOnMemoryPressure)
                editor.putInt("${prefix}app_launch_delay_seconds", eventConfig.appLaunchDelaySeconds)
                editor.putInt("${prefix}foreground_delay_seconds", eventConfig.foregroundDelaySeconds)
                editor.putBoolean("${prefix}enable_smart_delay", eventConfig.enableSmartDelay)
                editor.putInt("${prefix}monitor_duration_seconds", eventConfig.monitorDurationSeconds)
                editor.putInt("${prefix}monitor_interval_seconds", eventConfig.monitorIntervalSeconds)
                editor.putInt("${prefix}cooldown_seconds", eventConfig.cooldownSeconds)
            }
            MemoryCheckMode.ADAPTIVE -> {
                val adaptiveConfig = config as AdaptiveConfig
                editor.putString("${prefix}adaptive_strategy", adaptiveConfig.adaptiveStrategy.name)
                editor.putBoolean("${prefix}enable_memory_pressure_adaptation", adaptiveConfig.enableMemoryPressureAdaptation)
                editor.putBoolean("${prefix}enable_app_activity_adaptation", adaptiveConfig.enableAppActivityAdaptation)
                editor.putInt("${prefix}memory_abundant_frequency", adaptiveConfig.memoryAbundantFrequency)
                editor.putInt("${prefix}memory_tight_frequency", adaptiveConfig.memoryTightFrequency)
            }
            MemoryCheckMode.INTELLIGENT -> {
                val intelligentConfig = config as IntelligentConfig
                editor.putBoolean("${prefix}enable_learning", intelligentConfig.enableLearning)
                editor.putInt("${prefix}min_samples_for_prediction", intelligentConfig.minSamplesForPrediction)
                editor.putFloat("${prefix}confidence_threshold", intelligentConfig.confidenceThreshold)
                editor.putInt("${prefix}stability_check_interval", intelligentConfig.stabilityCheckInterval)
                editor.putInt("${prefix}stability_threshold_mb", intelligentConfig.stabilityThresholdMB)
                editor.putInt("${prefix}required_stable_checks", intelligentConfig.requiredStableChecks)
                editor.putInt("${prefix}max_history_records", intelligentConfig.maxHistoryRecords)
                editor.putInt("${prefix}data_retention_days", intelligentConfig.dataRetentionDays)
            }
            MemoryCheckMode.HYBRID -> {
                val hybridConfig = config as HybridConfig
                editor.putBoolean("${prefix}enable_event_driven", hybridConfig.enableEventDriven)
                editor.putBoolean("${prefix}enable_adaptive", hybridConfig.enableAdaptive)
                editor.putBoolean("${prefix}enable_intelligent", hybridConfig.enableIntelligent)
                editor.putFloat("${prefix}event_driven_weight", hybridConfig.eventDrivenWeight)
                editor.putFloat("${prefix}adaptive_weight", hybridConfig.adaptiveWeight)
                editor.putFloat("${prefix}intelligent_weight", hybridConfig.intelligentWeight)
            }
            else -> {
                // 不支持的模式，不保存
            }
        }

        editor.apply()
    }
}

/**
 * 扩展函数，方便在界面中使用
 */
fun loadAdvancedConfig(
    context: Context,
    mode: MemoryCheckMode,
    conditionId: String
): Any = AdvancedMemoryConfigStorage.loadAdvancedConfig(context, mode, conditionId)

fun saveAdvancedConfig(
    context: Context,
    mode: MemoryCheckMode,
    conditionId: String,
    config: Any
) = AdvancedMemoryConfigStorage.saveAdvancedConfig(context, mode, conditionId, config)
