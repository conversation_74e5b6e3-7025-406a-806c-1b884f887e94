package com.weinuo.quickcommands22.ui.theme.oceanblue

import com.weinuo.quickcommands22.ui.theme.system.AnimationConfiguration
import com.weinuo.quickcommands22.ui.theme.config.*

/**
 * 海洋蓝主题动画配置
 *
 * 实现分层设计风格的动画效果配置
 */
class OceanBlueAnimationConfiguration : AnimationConfiguration {
    
    override val fastDuration: Int = 150
    override val mediumDuration: Int = 300
    override val slowDuration: Int = 500
    
    override val enter = AnimationSpec(
        duration = mediumDuration,
        delay = 0,
        easing = "ease_out"
    )
    
    override val exit = AnimationSpec(
        duration = fastDuration,
        delay = 0,
        easing = "ease_in"
    )
    
    override val transition = AnimationSpec(
        duration = mediumDuration,
        delay = 0,
        easing = "ease_in_out"
    )
    
    override val spring = SpringAnimationSpec(
        damping = 0.8f,
        stiffness = 400f,
        visibilityThreshold = 0.01f
    )
    
    override val easing = EasingAnimationSpec(
        duration = mediumDuration,
        easing = "ease_in_out"
    )
    
    override val animationsEnabled: Boolean = true
}
