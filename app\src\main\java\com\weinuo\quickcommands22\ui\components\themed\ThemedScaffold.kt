package com.weinuo.quickcommands22.ui.components.themed

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.ui.zIndex
import androidx.compose.material3.FabPosition
import androidx.compose.material3.Scaffold
import androidx.compose.material3.ScaffoldDefaults
import androidx.compose.material3.contentColorFor
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import com.weinuo.quickcommands22.ui.theme.manager.LocalThemeContext
import com.weinuo.quickcommands22.ui.theme.system.DesignApproach
import com.weinuo.quickcommands22.ui.effects.HazeManager
import com.weinuo.quickcommands22.ui.effects.backgroundBlurSource

/**
 * 主题感知的脚手架组件
 *
 * 根据当前主题的设计方法自动调整布局逻辑：
 * - 分层设计（LAYERED_DESIGN）：传统的分层布局，内容与导航栏分离
 * - 整合设计（INTEGRATED_DESIGN）：整合布局，内容与导航栏融合
 * - 极简设计（MINIMAL_DESIGN）：极简布局，最小化UI元素
 * - 动态设计（DYNAMIC_DESIGN）：动态布局，根据内容自适应
 *
 * 这是应用的主要布局容器，不同设计方法的差异在这里体现最明显。
 */
@Composable
fun ThemedScaffold(
    modifier: Modifier = Modifier,
    topBar: @Composable () -> Unit = {},
    bottomBar: @Composable () -> Unit = {},
    snackbarHost: @Composable () -> Unit = {},
    floatingActionButton: @Composable () -> Unit = {},
    floatingActionButtonPosition: FabPosition = FabPosition.End,
    containerColor: Color = Color.Unspecified,
    contentColor: Color = Color.Unspecified,
    contentWindowInsets: WindowInsets = ScaffoldDefaults.contentWindowInsets,
    content: @Composable (PaddingValues) -> Unit
) {
    val themeContext = LocalThemeContext.current
    
    // 根据设计方法选择不同的布局策略
    when (themeContext.designApproach) {
        DesignApproach.LAYERED_DESIGN -> {
            // 分层设计：使用传统的Scaffold布局，TopAppBar和内容分离
            Scaffold(
                modifier = modifier,
                topBar = topBar,
                bottomBar = bottomBar,
                snackbarHost = snackbarHost,
                floatingActionButton = floatingActionButton,
                floatingActionButtonPosition = floatingActionButtonPosition,
                containerColor = if (containerColor != Color.Unspecified) containerColor else androidx.compose.material3.MaterialTheme.colorScheme.background,
                contentColor = if (contentColor != Color.Unspecified) contentColor else contentColorFor(androidx.compose.material3.MaterialTheme.colorScheme.background),
                contentWindowInsets = contentWindowInsets,
                content = content
            )
        }

        DesignApproach.INTEGRATED_DESIGN -> {
            // 整合设计：TopAppBar覆盖在内容上方，支持模糊效果
            val context = LocalContext.current
            val hazeManager = remember { HazeManager.getInstance(context) }
            val hazeState = hazeManager.globalHazeState

            Box(
                modifier = modifier
                    .fillMaxSize()
                    .background(
                        if (containerColor != Color.Unspecified) containerColor
                        else androidx.compose.material3.MaterialTheme.colorScheme.background
                    )
            ) {
                // 内容区域延伸到全屏，设置为模糊源，这样TopAppBar可以模糊这些内容
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .backgroundBlurSource(
                            hazeState = hazeState,
                            zIndex = 0f // 背景层级，会被上层的hazeEffect模糊
                        )
                ) {
                    content(PaddingValues(0.dp))
                }

                // TopAppBar覆盖在内容上方，设置正确的层级
                Box(
                    modifier = Modifier
                        .align(Alignment.TopCenter)
                        .fillMaxWidth()
                        .zIndex(1f) // 确保TopAppBar在内容上方，与IntegratedMainLayout保持一致
                ) {
                    topBar()
                }

                // FloatingActionButton - 为底部导航栏留出空间，设置高层级确保可见
                Box(
                    modifier = Modifier.align(
                        when (floatingActionButtonPosition) {
                            FabPosition.End -> Alignment.BottomEnd
                            FabPosition.Center -> Alignment.BottomCenter
                            FabPosition.Start -> Alignment.BottomStart
                            else -> Alignment.BottomEnd
                        }
                    ).padding(
                        start = 16.dp,
                        end = 16.dp,
                        bottom = 96.dp // 80dp(导航栏高度) + 16dp(额外间距)
                    ).zIndex(2f) // 设置高层级，确保FAB在所有内容之上，避免被模糊效果覆盖
                ) {
                    floatingActionButton()
                }

                // SnackbarHost
                Box(
                    modifier = Modifier.align(Alignment.BottomCenter)
                ) {
                    snackbarHost()
                }
            }
        }

        DesignApproach.MINIMAL_DESIGN,
        DesignApproach.DYNAMIC_DESIGN -> {
            // 未来的设计方法：暂时使用分层设计
            Scaffold(
                modifier = modifier,
                topBar = topBar,
                bottomBar = bottomBar,
                snackbarHost = snackbarHost,
                floatingActionButton = floatingActionButton,
                floatingActionButtonPosition = floatingActionButtonPosition,
                containerColor = if (containerColor != Color.Unspecified) containerColor else androidx.compose.material3.MaterialTheme.colorScheme.background,
                contentColor = if (contentColor != Color.Unspecified) contentColor else contentColorFor(androidx.compose.material3.MaterialTheme.colorScheme.background),
                contentWindowInsets = contentWindowInsets,
                content = content
            )
        }
    }
}

/**
 * 分层设计的脚手架实现
 *
 * 特点：传统的Material Design布局，清晰的层次分离
 */
@Composable
private fun LayeredScaffold(
    modifier: Modifier = Modifier,
    topBar: @Composable () -> Unit = {},
    bottomBar: @Composable () -> Unit = {},
    snackbarHost: @Composable () -> Unit = {},
    floatingActionButton: @Composable () -> Unit = {},
    floatingActionButtonPosition: FabPosition = FabPosition.End,
    containerColor: Color = Color.Unspecified,
    contentColor: Color = Color.Unspecified,
    contentWindowInsets: WindowInsets = ScaffoldDefaults.contentWindowInsets,
    content: @Composable (PaddingValues) -> Unit
) {
    // 使用标准的Material 3 Scaffold
    Scaffold(
        modifier = modifier,
        topBar = topBar,
        bottomBar = bottomBar,
        snackbarHost = snackbarHost,
        floatingActionButton = floatingActionButton,
        floatingActionButtonPosition = floatingActionButtonPosition,
        containerColor = if (containerColor != Color.Unspecified) containerColor else androidx.compose.material3.MaterialTheme.colorScheme.background,
        contentColor = if (contentColor != Color.Unspecified) contentColor else contentColorFor(androidx.compose.material3.MaterialTheme.colorScheme.background),
        contentWindowInsets = contentWindowInsets,
        content = content
    )
}

/**
 * 整合设计的脚手架实现
 *
 * 特点：内容与导航栏融合，支持模糊效果，统一的视觉体验
 */
@Composable
private fun IntegratedScaffold(
    modifier: Modifier = Modifier,
    topBar: @Composable () -> Unit = {},
    bottomBar: @Composable () -> Unit = {},
    snackbarHost: @Composable () -> Unit = {},
    floatingActionButton: @Composable () -> Unit = {},
    floatingActionButtonPosition: FabPosition = FabPosition.End,
    containerColor: Color = Color.Unspecified,
    contentColor: Color = Color.Unspecified,
    contentWindowInsets: WindowInsets = ScaffoldDefaults.contentWindowInsets,
    content: @Composable (PaddingValues) -> Unit
) {
    // 整合设计使用特殊的布局逻辑，支持模糊效果
    // 为FloatingActionButton添加底部margin，避免被底部导航栏遮挡
    Scaffold(
        modifier = modifier,
        topBar = topBar,
        bottomBar = bottomBar,
        snackbarHost = snackbarHost,
        floatingActionButton = {
            // 为FloatingActionButton添加底部padding，避免被底部导航栏遮挡
            Box(
                modifier = Modifier.padding(bottom = 80.dp) // 为底部导航栏留出空间
            ) {
                floatingActionButton()
            }
        },
        floatingActionButtonPosition = floatingActionButtonPosition,
        containerColor = if (containerColor != Color.Unspecified) containerColor else androidx.compose.material3.MaterialTheme.colorScheme.background,
        contentColor = if (contentColor != Color.Unspecified) contentColor else contentColorFor(androidx.compose.material3.MaterialTheme.colorScheme.background),
        contentWindowInsets = contentWindowInsets,
        content = content
    )
}

/**
 * 极简设计的脚手架实现
 *
 * 特点：最小化UI元素，专注于内容
 */
@Composable
private fun MinimalScaffold(
    modifier: Modifier = Modifier,
    topBar: @Composable () -> Unit = {},
    bottomBar: @Composable () -> Unit = {},
    snackbarHost: @Composable () -> Unit = {},
    floatingActionButton: @Composable () -> Unit = {},
    floatingActionButtonPosition: FabPosition = FabPosition.End,
    containerColor: Color = Color.Unspecified,
    contentColor: Color = Color.Unspecified,
    contentWindowInsets: WindowInsets = ScaffoldDefaults.contentWindowInsets,
    content: @Composable (PaddingValues) -> Unit
) {
    // 极简设计可能会隐藏某些UI元素或使用不同的布局
    Scaffold(
        modifier = modifier,
        topBar = topBar,
        bottomBar = bottomBar,
        snackbarHost = snackbarHost,
        floatingActionButton = floatingActionButton,
        floatingActionButtonPosition = floatingActionButtonPosition,
        containerColor = if (containerColor != Color.Unspecified) containerColor else androidx.compose.material3.MaterialTheme.colorScheme.background,
        contentColor = if (contentColor != Color.Unspecified) contentColor else contentColorFor(androidx.compose.material3.MaterialTheme.colorScheme.background),
        contentWindowInsets = contentWindowInsets,
        content = content
    )
}

/**
 * 动态设计的脚手架实现
 *
 * 特点：根据内容和上下文动态调整布局
 */
@Composable
private fun DynamicScaffold(
    modifier: Modifier = Modifier,
    topBar: @Composable () -> Unit = {},
    bottomBar: @Composable () -> Unit = {},
    snackbarHost: @Composable () -> Unit = {},
    floatingActionButton: @Composable () -> Unit = {},
    floatingActionButtonPosition: FabPosition = FabPosition.End,
    containerColor: Color = Color.Unspecified,
    contentColor: Color = Color.Unspecified,
    contentWindowInsets: WindowInsets = ScaffoldDefaults.contentWindowInsets,
    content: @Composable (PaddingValues) -> Unit
) {
    // 动态设计可能会根据内容类型或设备状态调整布局
    Scaffold(
        modifier = modifier,
        topBar = topBar,
        bottomBar = bottomBar,
        snackbarHost = snackbarHost,
        floatingActionButton = floatingActionButton,
        floatingActionButtonPosition = floatingActionButtonPosition,
        containerColor = if (containerColor != Color.Unspecified) containerColor else androidx.compose.material3.MaterialTheme.colorScheme.background,
        contentColor = if (contentColor != Color.Unspecified) contentColor else contentColorFor(androidx.compose.material3.MaterialTheme.colorScheme.background),
        contentWindowInsets = contentWindowInsets,
        content = content
    )
}
