package com.weinuo.quickcommands22.smartreminder

import android.util.Log

/**
 * 地址检测器
 *
 * 检测文本中是否包含地址信息，支持中英文地址格式。
 * 设计原则：
 * - 简单有效：使用基本的正则表达式检测常见地址格式
 * - 国际化支持：支持中文和英文地址格式
 * - 避免复杂化：不做过度复杂的地址解析
 */
object AddressDetector {

    private const val TAG = "AddressDetector"

    /**
     * 中文地址模式
     * 匹配包含省市区县、街道、路、号等关键词的地址
     */
    private val chineseAddressPatterns = listOf(
        // 完整地址格式：省市区+街道+号码
        Regex("""[\u4e00-\u9fa5]{2,}[省市区县][^\s]{0,20}[街道路巷弄里村镇][^\s]{0,20}[号栋楼室][\d\-\u4e00-\u9fa5]*"""),
        // 街道地址格式
        Regex("""[\u4e00-\u9fa5]{2,}[街道路巷弄里村镇][^\s]{0,30}[号栋楼室][\d\-\u4e00-\u9fa5]*"""),
        // 包含"市"和"路/街"的地址
        Regex("""[\u4e00-\u9fa5]{2,}市[^\s]{0,20}[街道路巷弄里][^\s]{0,20}[号栋楼室]?[\d\-\u4e00-\u9fa5]*"""),
        // 包含"区"和"路/街"的地址
        Regex("""[\u4e00-\u9fa5]{2,}区[^\s]{0,20}[街道路巷弄里][^\s]{0,20}[号栋楼室]?[\d\-\u4e00-\u9fa5]*""")
    )

    /**
     * 英文地址模式
     * 匹配常见的英文地址格式
     */
    private val englishAddressPatterns = listOf(
        // 包含街道号码的地址：123 Main Street
        Regex("""\d+\s+[A-Za-z\s]+(?:Street|St|Avenue|Ave|Road|Rd|Drive|Dr|Lane|Ln|Boulevard|Blvd|Way|Place|Pl|Court|Ct)""", RegexOption.IGNORE_CASE),
        // 包含邮政编码的地址
        Regex("""\b\d{5}(?:-\d{4})?\b"""),
        // 包含州名缩写的地址
        Regex("""\b[A-Z]{2}\s+\d{5}(?:-\d{4})?\b"""),
        // 包含城市、州、邮编的格式
        Regex("""[A-Za-z\s]+,\s*[A-Z]{2}\s+\d{5}(?:-\d{4})?""")
    )

    /**
     * 检测结果数据类
     */
    data class DetectionResult(
        val isAddress: Boolean,
        val originalText: String = "",
        val detectedAddress: String = "",
        val addressType: AddressType = AddressType.UNKNOWN
    )

    /**
     * 地址类型枚举
     */
    enum class AddressType {
        CHINESE,    // 中文地址
        ENGLISH,    // 英文地址
        UNKNOWN     // 未知类型
    }

    /**
     * 检测文本中是否包含地址
     *
     * @param text 要检测的文本内容
     * @return 检测结果，包含是否为地址和相关信息
     */
    fun detectAddress(text: String): DetectionResult {
        if (text.isBlank()) {
            return DetectionResult(false)
        }

        try {
            Log.d(TAG, "Detecting address in text: ${text.take(100)}...")

            // 预处理文本，移除多余的空白字符
            val cleanText = text.trim()

            // 检测中文地址
            val chineseResult = detectChineseAddress(cleanText)
            if (chineseResult.isAddress) {
                Log.d(TAG, "Detected Chinese address: ${chineseResult.detectedAddress}")
                return chineseResult
            }

            // 检测英文地址
            val englishResult = detectEnglishAddress(cleanText)
            if (englishResult.isAddress) {
                Log.d(TAG, "Detected English address: ${englishResult.detectedAddress}")
                return englishResult
            }

            Log.d(TAG, "No address detected")
            return DetectionResult(false, originalText = cleanText)

        } catch (e: Exception) {
            Log.e(TAG, "Error detecting address", e)
            return DetectionResult(false, originalText = text)
        }
    }

    /**
     * 检测中文地址
     */
    private fun detectChineseAddress(text: String): DetectionResult {
        for (pattern in chineseAddressPatterns) {
            val match = pattern.find(text)
            if (match != null) {
                return DetectionResult(
                    isAddress = true,
                    originalText = text,
                    detectedAddress = match.value,
                    addressType = AddressType.CHINESE
                )
            }
        }
        return DetectionResult(false, originalText = text)
    }

    /**
     * 检测英文地址
     */
    private fun detectEnglishAddress(text: String): DetectionResult {
        for (pattern in englishAddressPatterns) {
            val match = pattern.find(text)
            if (match != null) {
                return DetectionResult(
                    isAddress = true,
                    originalText = text,
                    detectedAddress = match.value,
                    addressType = AddressType.ENGLISH
                )
            }
        }
        return DetectionResult(false, originalText = text)
    }
}
