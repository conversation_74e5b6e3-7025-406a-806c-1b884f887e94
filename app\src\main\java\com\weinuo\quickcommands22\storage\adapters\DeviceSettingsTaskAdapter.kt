package com.weinuo.quickcommands22.storage.adapters

import android.util.Log
import com.weinuo.quickcommands22.model.*
import com.weinuo.quickcommands22.storage.NativeTypeStorageManager
import com.weinuo.quickcommands22.storage.StorageDomain
import com.weinuo.quickcommands22.storage.StorageOperation

/**
 * 设备设置任务存储适配器
 *
 * 负责DeviceSettingsTask的原生数据类型存储和重建。
 * 将复杂的设备设置任务对象拆分为独立的原生字段进行存储，
 * 完全避免JSON序列化问题。
 *
 * 支持的字段类型：
 * - 基础字段：id、operation
 * - 颜色反转相关：invertColorsOperation
 * - 字体大小相关：fontSizePercentage
 * - 自动旋转相关：autoRotateOperation
 * - 无障碍服务相关：accessibilityServiceOperation、accessibilityServicePackage
 * - 显示密度相关：displayDensityPercentage
 * - 沉浸模式相关：immersiveModeType
 * - 深色主题相关：darkThemeOperation
 * - 演示模式相关：demoModeOperation
 * - 环境显示相关：ambientDisplayMode
 * - 省电模式相关：powerSaveModeOperation
 * - 系统设置相关：systemSettingsTable、systemSettingsKey、systemSettingsValue、systemSettingsValueType
 * - 壁纸相关：wallpaperType、wallpaperImagePath、wallpaperLocation、liveWallpaperPackage
 * - 屏幕锁定相关：screenLockOperation
 * - 数字助手相关：digitalAssistantPackage
 * - 键盘相关：keyboardPackage
 * - 驾驶模式相关：drivingModeOperation
 *
 * 存储格式示例：
 * task_{id}_type = "device_settings"
 * task_{id}_operation = "INVERT_COLORS"
 * task_{id}_invert_colors_operation = "TOGGLE"
 * task_{id}_font_size_percentage = 100
 * task_{id}_auto_rotate_operation = "TOGGLE"
 * task_{id}_accessibility_service_operation = "TOGGLE"
 * task_{id}_accessibility_service_package = "com.example.service"
 *
 * @param storageManager 原生类型存储管理器
 */
class DeviceSettingsTaskAdapter(
    storageManager: NativeTypeStorageManager
) : BaseTaskAdapter<DeviceSettingsTask>(storageManager) {

    companion object {
        private const val TAG = "DeviceSettingsTaskAdapter"

        // 字段名常量
        private const val FIELD_OPERATION = "operation"
        private const val FIELD_INVERT_COLORS_OPERATION = "invert_colors_operation"
        private const val FIELD_FONT_SIZE_PERCENTAGE = "font_size_percentage"
        private const val FIELD_AUTO_ROTATE_OPERATION = "auto_rotate_operation"
        private const val FIELD_ACCESSIBILITY_SERVICE_OPERATION = "accessibility_service_operation"
        private const val FIELD_ACCESSIBILITY_SERVICE_PACKAGE = "accessibility_service_package"
        private const val FIELD_DISPLAY_DENSITY_PERCENTAGE = "display_density_percentage"
        private const val FIELD_IMMERSIVE_MODE_TYPE = "immersive_mode_type"
        private const val FIELD_DARK_THEME_OPERATION = "dark_theme_operation"
        private const val FIELD_DEMO_MODE_OPERATION = "demo_mode_operation"
        private const val FIELD_AMBIENT_DISPLAY_MODE = "ambient_display_mode"
        private const val FIELD_POWER_SAVE_MODE_OPERATION = "power_save_mode_operation"
        private const val FIELD_SYSTEM_SETTINGS_TABLE = "system_settings_table"
        private const val FIELD_SYSTEM_SETTINGS_KEY = "system_settings_key"
        private const val FIELD_SYSTEM_SETTINGS_VALUE = "system_settings_value"
        private const val FIELD_SYSTEM_SETTINGS_VALUE_TYPE = "system_settings_value_type"
        private const val FIELD_WALLPAPER_TYPE = "wallpaper_type"
        private const val FIELD_WALLPAPER_IMAGE_PATH = "wallpaper_image_path"
        private const val FIELD_WALLPAPER_LOCATION = "wallpaper_location"
        private const val FIELD_LIVE_WALLPAPER_PACKAGE = "live_wallpaper_package"
        private const val FIELD_SCREEN_LOCK_OPERATION = "screen_lock_operation"
        private const val FIELD_DIGITAL_ASSISTANT_PACKAGE = "digital_assistant_package"
        private const val FIELD_KEYBOARD_PACKAGE = "keyboard_package"
        private const val FIELD_DRIVING_MODE_OPERATION = "driving_mode_operation"
    }

    override fun getTaskType() = "device_settings"

    /**
     * 保存设备设置任务
     * 将DeviceSettingsTask的所有字段拆分为原生数据类型存储
     *
     * @param task 要保存的设备设置任务
     * @return 操作是否成功
     */
    override fun save(task: DeviceSettingsTask): Boolean {
        if (!isValidTaskId(task.id)) {
            logSaveError(task.id, "Invalid task ID")
            return false
        }

        Log.d(TAG, "开始保存设备设置任务: ${task.id}")

        return try {
            val operations = mutableListOf<StorageOperation>()

            // 保存基础字段
            operations.addAll(saveBaseFields(task.id, task))

            // 保存设备设置任务特有字段
            operations.addAll(listOf(
                saveEnum(generateKey(task.id, FIELD_OPERATION), task.operation),
                saveEnum(generateKey(task.id, FIELD_INVERT_COLORS_OPERATION), task.invertColorsOperation),
                StorageOperation.createIntOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_FONT_SIZE_PERCENTAGE),
                    task.fontSizePercentage
                ),
                saveEnum(generateKey(task.id, FIELD_AUTO_ROTATE_OPERATION), task.autoRotateOperation),
                saveEnum(generateKey(task.id, FIELD_ACCESSIBILITY_SERVICE_OPERATION), task.accessibilityServiceOperation),
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_ACCESSIBILITY_SERVICE_PACKAGE),
                    task.accessibilityServicePackage
                ),
                StorageOperation.createIntOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_DISPLAY_DENSITY_PERCENTAGE),
                    task.displayDensityPercentage
                ),
                saveEnum(generateKey(task.id, FIELD_IMMERSIVE_MODE_TYPE), task.immersiveModeType),
                saveEnum(generateKey(task.id, FIELD_DARK_THEME_OPERATION), task.darkThemeOperation),
                saveEnum(generateKey(task.id, FIELD_DEMO_MODE_OPERATION), task.demoModeOperation),
                saveEnum(generateKey(task.id, FIELD_AMBIENT_DISPLAY_MODE), task.ambientDisplayMode),
                saveEnum(generateKey(task.id, FIELD_POWER_SAVE_MODE_OPERATION), task.powerSaveModeOperation),
                saveEnum(generateKey(task.id, FIELD_SYSTEM_SETTINGS_TABLE), task.systemSettingsTable),
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_SYSTEM_SETTINGS_KEY),
                    task.systemSettingsKey
                ),
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_SYSTEM_SETTINGS_VALUE),
                    task.systemSettingsValue
                ),
                saveEnum(generateKey(task.id, FIELD_SYSTEM_SETTINGS_VALUE_TYPE), task.systemSettingsValueType),
                saveEnum(generateKey(task.id, FIELD_WALLPAPER_TYPE), task.wallpaperType),
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_WALLPAPER_IMAGE_PATH),
                    task.wallpaperImagePath
                ),
                saveEnum(generateKey(task.id, FIELD_WALLPAPER_LOCATION), task.wallpaperLocation),
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_LIVE_WALLPAPER_PACKAGE),
                    task.liveWallpaperPackage
                ),
                saveEnum(generateKey(task.id, FIELD_SCREEN_LOCK_OPERATION), task.screenLockOperation),
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_DIGITAL_ASSISTANT_PACKAGE),
                    task.digitalAssistantPackage
                ),
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_KEYBOARD_PACKAGE),
                    task.keyboardPackage
                ),
                saveEnum(generateKey(task.id, FIELD_DRIVING_MODE_OPERATION), task.drivingModeOperation)
            ))

            val success = storageManager.executeBatch(operations)
            if (success) {
                Log.d(TAG, "设备设置任务保存成功: ${task.id}")
            } else {
                logSaveError(task.id, "Batch operation failed")
            }
            success

        } catch (e: Exception) {
            logSaveError(task.id, "Exception during save: ${e.message}")
            false
        }
    }

    /**
     * 加载设备设置任务
     * 从原生数据类型重建DeviceSettingsTask对象
     *
     * @param taskId 任务ID
     * @return 加载的设备设置任务，失败时返回null
     */
    override fun load(taskId: String): DeviceSettingsTask? {
        if (!isValidTaskId(taskId)) {
            logLoadError(taskId, "Invalid task ID")
            return null
        }

        Log.d(TAG, "开始加载设备设置任务: $taskId")

        return try {
            // 检查任务是否存在
            if (!exists(taskId)) {
                Log.d(TAG, "设备设置任务不存在: $taskId")
                return null
            }

            DeviceSettingsTask(
                id = taskId,
                operation = loadEnum(generateKey(taskId, FIELD_OPERATION)) { DeviceSettingsOperation.valueOf(it) }
                    ?: DeviceSettingsOperation.INVERT_COLORS,
                invertColorsOperation = loadEnum(generateKey(taskId, FIELD_INVERT_COLORS_OPERATION)) { TriStateOperation.valueOf(it) }
                    ?: TriStateOperation.TOGGLE,
                fontSizePercentage = storageManager.loadInt(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_FONT_SIZE_PERCENTAGE),
                    100
                ),
                autoRotateOperation = loadEnum(generateKey(taskId, FIELD_AUTO_ROTATE_OPERATION)) { TriStateOperation.valueOf(it) }
                    ?: TriStateOperation.TOGGLE,
                accessibilityServiceOperation = loadEnum(generateKey(taskId, FIELD_ACCESSIBILITY_SERVICE_OPERATION)) { TriStateOperation.valueOf(it) }
                    ?: TriStateOperation.TOGGLE,
                accessibilityServicePackage = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_ACCESSIBILITY_SERVICE_PACKAGE),
                    ""
                ),
                displayDensityPercentage = storageManager.loadInt(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_DISPLAY_DENSITY_PERCENTAGE),
                    100
                ),
                immersiveModeType = loadEnum(generateKey(taskId, FIELD_IMMERSIVE_MODE_TYPE)) { ImmersiveModeType.valueOf(it) }
                    ?: ImmersiveModeType.FULL_IMMERSIVE,
                darkThemeOperation = loadEnum(generateKey(taskId, FIELD_DARK_THEME_OPERATION)) { TriStateOperation.valueOf(it) }
                    ?: TriStateOperation.TOGGLE,
                demoModeOperation = loadEnum(generateKey(taskId, FIELD_DEMO_MODE_OPERATION)) { TriStateOperation.valueOf(it) }
                    ?: TriStateOperation.TOGGLE,
                ambientDisplayMode = loadEnum(generateKey(taskId, FIELD_AMBIENT_DISPLAY_MODE)) { AmbientDisplayMode.valueOf(it) }
                    ?: AmbientDisplayMode.NOTIFICATION_WAKE,
                powerSaveModeOperation = loadEnum(generateKey(taskId, FIELD_POWER_SAVE_MODE_OPERATION)) { TriStateOperation.valueOf(it) }
                    ?: TriStateOperation.TOGGLE,
                systemSettingsTable = loadEnum(generateKey(taskId, FIELD_SYSTEM_SETTINGS_TABLE)) { SystemSettingsTable.valueOf(it) }
                    ?: SystemSettingsTable.SYSTEM,
                systemSettingsKey = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_SYSTEM_SETTINGS_KEY),
                    ""
                ),
                systemSettingsValue = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_SYSTEM_SETTINGS_VALUE),
                    ""
                ),
                systemSettingsValueType = loadEnum(generateKey(taskId, FIELD_SYSTEM_SETTINGS_VALUE_TYPE)) { SystemSettingsValueType.valueOf(it) }
                    ?: SystemSettingsValueType.STRING,
                wallpaperType = loadEnum(generateKey(taskId, FIELD_WALLPAPER_TYPE)) { WallpaperType.valueOf(it) }
                    ?: WallpaperType.IMAGE,
                wallpaperImagePath = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_WALLPAPER_IMAGE_PATH),
                    ""
                ),
                wallpaperLocation = loadEnum(generateKey(taskId, FIELD_WALLPAPER_LOCATION)) { WallpaperLocation.valueOf(it) }
                    ?: WallpaperLocation.HOME_SCREEN,
                liveWallpaperPackage = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_LIVE_WALLPAPER_PACKAGE),
                    ""
                ),
                screenLockOperation = loadEnum(generateKey(taskId, FIELD_SCREEN_LOCK_OPERATION)) { TriStateOperation.valueOf(it) }
                    ?: TriStateOperation.ENABLE,
                digitalAssistantPackage = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_DIGITAL_ASSISTANT_PACKAGE),
                    ""
                ),
                keyboardPackage = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_KEYBOARD_PACKAGE),
                    ""
                ),
                drivingModeOperation = loadEnum(generateKey(taskId, FIELD_DRIVING_MODE_OPERATION)) { TriStateOperation.valueOf(it) }
                    ?: TriStateOperation.TOGGLE
            ).also {
                Log.d(TAG, "设备设置任务加载成功: $taskId")
            }

        } catch (e: Exception) {
            logLoadError(taskId, "Exception during load: ${e.message}")
            null
        }
    }
}
