package com.weinuo.quickcommands22.shortcut

import android.os.Bundle
import android.widget.Toast
import androidx.activity.ComponentActivity
import com.weinuo.quickcommands22.R
import com.weinuo.quickcommands22.data.QuickCommandRepository
import com.weinuo.quickcommands22.execution.SharedExecutionHandler

/**
 * 快捷指令执行器活动
 *
 * 当用户点击桌面快捷方式时启动，执行对应的快捷指令
 * 支持时间有效性检查和快捷指令的特殊逻辑
 */
class QuickCommandExecutorActivity : ComponentActivity() {

    private lateinit var quickCommandRepository: QuickCommandRepository
    private lateinit var executionHandler: SharedExecutionHandler

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        quickCommandRepository = QuickCommandRepository.getInstance(applicationContext)
        executionHandler = SharedExecutionHandler(applicationContext)

        // 获取要执行的指令ID
        val commandId = intent.getStringExtra(ShortcutManager.EXTRA_COMMAND_ID)

        if (commandId != null) {
            executeCommand(commandId)
        } else {
            Toast.makeText(this, getString(R.string.invalid_command), Toast.LENGTH_SHORT).show()
            finish()
        }
    }

    /**
     * 执行快捷指令
     */
    private fun executeCommand(commandId: String) {
        // 从内存中的数据流获取快捷指令
        val command = quickCommandRepository.quickCommands.value.find { it.id == commandId }

        if (command != null) {
            // 显示提示信息
            Toast.makeText(
                this,
                getString(R.string.executing_quick_command, command.name, command.tasks.size),
                Toast.LENGTH_LONG
            ).show()

            // 使用共享执行处理器手动执行快捷指令（跳过触发条件检查）
            executionHandler.executeQuickCommandManually(
                command = command,
                onTaskStarted = { task ->
                    // 任务开始执行时的回调
                    // 可以在此处添加任务开始执行的通知或UI更新
                },
                onTaskCompleted = { task, success ->
                    // 任务完成时的回调
                    // 可以在此处添加任务完成的通知或UI更新
                },
                onExecutionCompleted = {
                    // 所有任务执行完成时的回调
                    runOnUiThread {
                        Toast.makeText(
                            this,
                            getString(R.string.quick_command_completed, command.name),
                            Toast.LENGTH_SHORT
                        ).show()
                        finish()
                    }
                },
                onExecutionAborted = { abortConditions ->
                    // 执行被中止时的回调
                    val conditionNames = abortConditions.joinToString(", ") { it.displayName }
                    runOnUiThread {
                        Toast.makeText(
                            this,
                            getString(R.string.quick_command_aborted, command.name, conditionNames),
                            Toast.LENGTH_LONG
                        ).show()
                        finish()
                    }
                }
            )
        } else {
            Toast.makeText(this, getString(R.string.quick_command_not_found_simple), Toast.LENGTH_SHORT).show()
            finish()
        }
    }
}
