package com.weinuo.quickcommands22.ui.configuration

import android.app.Activity
import android.content.Context
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.saveable.rememberSaveable

import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp

import com.weinuo.quickcommands22.R
import com.weinuo.quickcommands22.model.*
import com.weinuo.quickcommands22.ui.activities.ContactSelectionActivity
import com.weinuo.quickcommands22.ui.components.ConfigurationCardItem
import com.weinuo.quickcommands22.utils.ContactsHelper

/**
 * 通信状态配置数据提供器
 *
 * 提供通信状态特定的配置项列表，为每个状态类型定义配置内容组件，
 * 复用现有的配置逻辑，支持编辑模式的数据预填充。
 */
object CommunicationStateConfigProvider {

    /**
     * 获取通信状态配置项列表
     *
     * @param context 上下文，用于获取字符串资源
     * @return 通信状态配置项列表
     */
    fun getConfigurationItems(context: Context): List<ConfigurationCardItem<CommunicationStateType>> {
        return listOf(
            // 收到短信配置项
            ConfigurationCardItem(
                id = "sms_received",
                title = context.getString(R.string.comm_sms_received),
                description = context.getString(R.string.comm_sms_received_description),
                operationType = CommunicationStateType.SMS_RECEIVED,
                permissionRequired = true,
                content = { type, onComplete ->
                    CommunicationStateConfigContent(type, onComplete)
                }
            ),

            // 发送短信配置项
            ConfigurationCardItem(
                id = "sms_sent",
                title = context.getString(R.string.comm_sms_sent),
                description = context.getString(R.string.comm_sms_sent_description),
                operationType = CommunicationStateType.SMS_SENT,
                permissionRequired = true,
                content = { type, onComplete ->
                    CommunicationStateConfigContent(type, onComplete)
                }
            ),

            // 接到呼叫配置项
            ConfigurationCardItem(
                id = "incoming_call",
                title = context.getString(R.string.comm_incoming_call),
                description = context.getString(R.string.comm_incoming_call_description),
                operationType = CommunicationStateType.INCOMING_CALL,
                permissionRequired = true,
                content = { type, onComplete ->
                    CommunicationStateConfigContent(type, onComplete)
                }
            ),

            // 拨出电话配置项
            ConfigurationCardItem(
                id = "outgoing_call",
                title = context.getString(R.string.comm_outgoing_call),
                description = context.getString(R.string.comm_outgoing_call_description),
                operationType = CommunicationStateType.OUTGOING_CALL,
                permissionRequired = true,
                content = { type, onComplete ->
                    CommunicationStateConfigContent(type, onComplete)
                }
            ),

            // 通话中配置项
            ConfigurationCardItem(
                id = "call_active",
                title = context.getString(R.string.comm_call_active),
                description = context.getString(R.string.comm_call_active_description),
                operationType = CommunicationStateType.CALL_ACTIVE,
                permissionRequired = true,
                content = { type, onComplete ->
                    CommunicationStateConfigContent(type, onComplete)
                }
            ),

            // 呼叫结束配置项
            ConfigurationCardItem(
                id = "call_ended",
                title = context.getString(R.string.comm_call_ended),
                description = context.getString(R.string.comm_call_ended_description),
                operationType = CommunicationStateType.CALL_ENDED,
                permissionRequired = true,
                content = { type, onComplete ->
                    CommunicationStateConfigContent(type, onComplete)
                }
            ),

            // 未接来电配置项
            ConfigurationCardItem(
                id = "missed_call",
                title = "未接来电",
                description = "当有未接来电时触发条件",
                operationType = CommunicationStateType.MISSED_CALL,
                permissionRequired = true,
                content = { type, onComplete ->
                    CommunicationStateConfigContent(type, onComplete)
                }
            )
        )
    }
}

/**
 * 通信状态配置内容组件
 *
 * 通用的通信状态条件配置组件，支持所有通信状态类型（短信、通话等）。
 * 通信状态需要通信相关权限，权限检查已在ExpandableConfigurationCard中统一处理。
 */
@Composable
private fun CommunicationStateConfigContent(
    type: CommunicationStateType,
    onComplete: (Any) -> Unit
) {
    val context = LocalContext.current

    // 使用 rememberSaveable 来保持状态，即使在导航过程中也不会丢失
    var selectedFilterType by rememberSaveable { mutableStateOf(ContactFilterType.ANY_CONTACT) }
    var selectedFilterMode by rememberSaveable { mutableStateOf(ContactFilterMode.INCLUDE) }
    var selectedContactIds by rememberSaveable { mutableStateOf<List<String>>(emptyList()) }
    var phoneNumber by rememberSaveable { mutableStateOf("") }
    var selectedGroupId by rememberSaveable { mutableStateOf("") }
    var selectedGroupName by rememberSaveable { mutableStateOf("") }

    // 联系人选择ActivityResultLauncher
    val contactSelectionLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            val contactData = result.data?.getSerializableExtra(
                ContactSelectionActivity.RESULT_SELECTED_CONTACTS
            ) as? ArrayList<Map<String, String>> ?: arrayListOf()

            if (contactData.isNotEmpty()) {
                // 自动切换到指定联系人模式
                selectedFilterType = ContactFilterType.SPECIFIC_CONTACTS
                // 更新选中的联系人ID
                selectedContactIds = contactData.map { it["id"] ?: "" }
            }
        }
    }

    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 根据通信状态类型显示相应的配置标题
        val configTitle = when (type) {
            CommunicationStateType.SMS_RECEIVED -> "配置收到短信的触发条件"
            CommunicationStateType.SMS_SENT -> "配置发送短信的触发条件"
            CommunicationStateType.INCOMING_CALL -> "配置接到呼叫的触发条件"
            CommunicationStateType.OUTGOING_CALL -> "配置拨出电话的触发条件"
            CommunicationStateType.CALL_ACTIVE -> "配置通话中的触发条件"
            CommunicationStateType.CALL_ENDED -> "配置呼叫结束的触发条件"
            CommunicationStateType.MISSED_CALL -> "配置未接来电的触发条件"
            CommunicationStateType.DIAL_NUMBER -> "配置拨打电话号码的触发条件"
        }

        Text(
            text = configTitle,
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        // 联系人筛选类型选择
        ContactFilterTypeSelector(
            selectedFilterType = selectedFilterType,
            onFilterTypeChanged = { selectedFilterType = it }
        )

        // 根据筛选类型显示相应的配置选项
        when (selectedFilterType) {
            ContactFilterType.SPECIFIC_CONTACTS -> {
                ContactSelector(
                    selectedContactIds = selectedContactIds,
                    onContactsChanged = { selectedContactIds = it },
                    contactSelectionLauncher = contactSelectionLauncher
                )
            }
            ContactFilterType.CONTACT_GROUP -> {
                // 联系人分组选择已迁移到Activity架构
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.3f)
                    )
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Text(
                            text = "联系人分组选择",
                            style = MaterialTheme.typography.titleSmall
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = "联系人分组选择功能已迁移到新的界面架构。当前选择的分组：${if (selectedGroupName.isNotEmpty()) selectedGroupName else "未选择"}",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
            }
            ContactFilterType.SPECIFIC_NUMBER -> {
                PhoneNumberInput(
                    specificNumber = phoneNumber,
                    onSpecificNumberChanged = { phoneNumber = it }
                )
            }
            else -> {
                // ANY_CONTACT, ANY_NUMBER 不需要额外配置
            }
        }

        // 包含/排除模式选择（仅在有具体筛选时显示）
        if (selectedFilterType != ContactFilterType.ANY_CONTACT && selectedFilterType != ContactFilterType.ANY_NUMBER) {
            FilterModeSelector(
                selectedFilterMode = selectedFilterMode,
                onFilterModeChanged = { selectedFilterMode = it }
            )
        }

        // 确认按钮
        Button(
            onClick = {
                val condition = CommunicationStateCondition(
                    stateType = type,
                    filterType = selectedFilterType,
                    filterMode = selectedFilterMode,
                    selectedContactIds = selectedContactIds,
                    selectedGroupId = selectedGroupId,
                    selectedGroupName = selectedGroupName,
                    specificNumber = phoneNumber.takeIf { it.isNotBlank() } ?: ""
                )
                onComplete(condition)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}



/**
 * 辅助组件实现
 * 通信状态配置界面的通用UI组件
 */
@Composable
private fun ContactFilterTypeSelector(
    selectedFilterType: ContactFilterType,
    onFilterTypeChanged: (ContactFilterType) -> Unit
) {
    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Text(
            text = "联系人筛选类型",
            style = MaterialTheme.typography.labelMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        val filterOptions = listOf(
            ContactFilterType.ANY_CONTACT to "任何联系人",
            ContactFilterType.NO_CONTACT to "陌生号码",
            ContactFilterType.SPECIFIC_CONTACTS to "指定联系人",
            ContactFilterType.CONTACT_GROUP to "指定分组",
            ContactFilterType.ANY_NUMBER to "任何号码",
            ContactFilterType.SPECIFIC_NUMBER to "指定号码"
        )

        filterOptions.forEach { (type, label) ->
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable(
                        interactionSource = remember { MutableInteractionSource() },
                        indication = null // 移除涟漪效果
                    ) { onFilterTypeChanged(type) }
                    .padding(vertical = 8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                RadioButton(
                    selected = selectedFilterType == type,
                    onClick = { onFilterTypeChanged(type) }
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = label,
                    style = MaterialTheme.typography.bodyMedium
                )
            }
        }
    }
}

@Composable
private fun ContactSelector(
    selectedContactIds: List<String>,
    onContactsChanged: (List<String>) -> Unit,
    contactSelectionLauncher: androidx.activity.result.ActivityResultLauncher<android.content.Intent>
) {
    val context = LocalContext.current
    var selectedContactNames by rememberSaveable { mutableStateOf<List<String>>(emptyList()) }

    // 根据联系人ID获取联系人姓名
    LaunchedEffect(selectedContactIds) {
        if (selectedContactIds.isNotEmpty()) {
            try {
                val allContacts = ContactsHelper.getAllContacts(context)
                selectedContactNames = selectedContactIds.mapNotNull { id ->
                    allContacts.find { it.id == id }?.name
                }
            } catch (e: Exception) {
                selectedContactNames = emptyList()
            }
        } else {
            selectedContactNames = emptyList()
        }
    }

    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Text(
            text = "选择联系人",
            style = MaterialTheme.typography.labelMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        OutlinedButton(
            onClick = {
                // 使用launcher启动联系人选择Activity
                val intent = android.content.Intent(context, ContactSelectionActivity::class.java).apply {
                    putExtra("selection_mode", com.weinuo.quickcommands22.ui.screens.ContactSelectionMode.MULTI.name)
                    putStringArrayListExtra("selected_contact_ids", ArrayList(selectedContactIds))
                }
                contactSelectionLauncher.launch(intent)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text(
                if (selectedContactIds.isNotEmpty()) {
                    "已选择 ${selectedContactIds.size} 个联系人"
                } else {
                    "点击选择联系人"
                }
            )
        }

        if (selectedContactNames.isNotEmpty()) {
            Text(
                text = "已选择联系人：${selectedContactNames.joinToString(", ")}",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }


}

@Composable
private fun PhoneNumberInput(
    specificNumber: String,
    onSpecificNumberChanged: (String) -> Unit
) {
    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Text(
            text = "电话号码",
            style = MaterialTheme.typography.labelMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        OutlinedTextField(
            value = specificNumber,
            onValueChange = onSpecificNumberChanged,
            label = { Text("输入电话号码") },
            placeholder = { Text("例如：13800138000") },
            modifier = Modifier.fillMaxWidth(),
            singleLine = true
        )
    }
}

@Composable
private fun FilterModeSelector(
    selectedFilterMode: ContactFilterMode,
    onFilterModeChanged: (ContactFilterMode) -> Unit
) {
    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Text(
            text = "筛选模式",
            style = MaterialTheme.typography.labelMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        val modeOptions = listOf(
            ContactFilterMode.INCLUDE to "包含（仅来自指定联系人）",
            ContactFilterMode.EXCLUDE to "排除（排除指定联系人）"
        )

        modeOptions.forEach { (mode, label) ->
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable(
                        interactionSource = remember { MutableInteractionSource() },
                        indication = null // 移除涟漪效果
                    ) { onFilterModeChanged(mode) }
                    .padding(vertical = 8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                RadioButton(
                    selected = selectedFilterMode == mode,
                    onClick = { onFilterModeChanged(mode) }
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = label,
                    style = MaterialTheme.typography.bodyMedium
                )
            }
        }
    }
}

/**
 * 联系人分组选择器组件
 * 可复用的联系人分组选择组件，支持单选模式
 *
 * @param selectedGroupId 当前选中的分组ID
 * @param selectedGroupName 当前选中的分组名称（用于显示）
 * @param onGroupChanged 分组选择变化回调
 */
// ContactGroupSelector 已迁移到Activity架构，此函数已删除


