package com.weinuo.quickcommands22.receiver

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Build
import android.util.Log
import com.weinuo.quickcommands22.data.AppRepository
import com.weinuo.quickcommands22.data.SettingsRepository
import com.weinuo.quickcommands22.model.AppStateCondition
import com.weinuo.quickcommands22.model.AppStateType
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch

/**
 * 应用安装/卸载广播接收器，用于监听应用安装和卸载事件
 * 支持触发应用状态条件中的包管理类事件
 */
class PackageReceiver(
    private val context: Context,
    private val appRepository: AppRepository,
    private val settingsRepository: SettingsRepository,
    private val onAppStateConditionTriggered: ((AppStateCondition, Map<String, Any>) -> Unit)? = null
) : BroadcastReceiver() {

    private val receiverScope = CoroutineScope(Dispatchers.Default + Job())

    /**
     * 注册广播接收器
     */
    fun register() {
        val filter = IntentFilter().apply {
            addAction(Intent.ACTION_PACKAGE_ADDED)
            addAction(Intent.ACTION_PACKAGE_REMOVED)
            addAction(Intent.ACTION_PACKAGE_REPLACED)
            addDataScheme("package")
        }
        // 注册广播接收器，Android 14+ 需要指定导出标志
        // 包管理广播是系统广播，需要导出
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            context.registerReceiver(this, filter, Context.RECEIVER_EXPORTED)
        } else {
            context.registerReceiver(this, filter)
        }
    }

    /**
     * 注销广播接收器
     */
    fun unregister() {
        try {
            context.unregisterReceiver(this)
            Log.d(TAG, "PackageReceiver unregistered")
        } catch (e: Exception) {
            Log.e(TAG, "Error unregistering PackageReceiver", e)
        }
    }

    override fun onReceive(context: Context, intent: Intent) {
        val packageName = intent.data?.schemeSpecificPart ?: return

        when (intent.action) {
            Intent.ACTION_PACKAGE_ADDED -> {
                if (!intent.getBooleanExtra(Intent.EXTRA_REPLACING, false)) {
                    // 新安装的应用，不是替换/更新
                    receiverScope.launch {
                        handleNewPackage(packageName)
                        // 触发应用安装条件
                        triggerAppStateCondition(AppStateType.INSTALLED, packageName)
                    }
                }
            }
            Intent.ACTION_PACKAGE_REPLACED -> {
                // 应用更新
                receiverScope.launch {
                    // 触发应用更新条件
                    triggerAppStateCondition(AppStateType.UPDATED, packageName)
                }
            }
            Intent.ACTION_PACKAGE_REMOVED -> {
                if (!intent.getBooleanExtra(Intent.EXTRA_REPLACING, false)) {
                    // 应用卸载，不是替换/更新
                    Log.d(TAG, "Package removed: $packageName")
                    receiverScope.launch {
                        // 触发应用卸载条件
                        triggerAppStateCondition(AppStateType.UNINSTALLED, packageName)
                    }
                }
            }
        }
    }

    /**
     * 处理新安装的应用
     */
    private suspend fun handleNewPackage(packageName: String) {
        // 处理自动包含新安装应用到相关快捷指令条件和任务
        updateConditionsAndTasksWithNewApp(packageName)
    }

    /**
     * 更新启用了自动包含新安装应用的条件和任务
     */
    private suspend fun updateConditionsAndTasksWithNewApp(packageName: String) {
        try {
            // 获取应用信息
            val appInfo = try {
                val packageManager = context.packageManager
                val applicationInfo = packageManager.getApplicationInfo(packageName, 0)
                val appName = packageManager.getApplicationLabel(applicationInfo).toString()
                val isSystemApp = applicationInfo.flags and android.content.pm.ApplicationInfo.FLAG_SYSTEM != 0

                com.weinuo.quickcommands22.model.SimpleAppInfo(
                    packageName = packageName,
                    appName = appName,
                    isSystemApp = isSystemApp
                )
            } catch (e: Exception) {
                Log.e(TAG, "Failed to get app info for $packageName", e)
                return
            }

            // 获取快捷指令仓库（使用单例）
            val quickCommandRepository = com.weinuo.quickcommands22.data.QuickCommandRepository.getInstance(context)
            val allCommands = quickCommandRepository.quickCommands.value

            var hasUpdates = false

            // 遍历所有快捷指令
            for (command in allCommands) {
                var commandUpdated = false

                // 更新后台时间条件（现在在STATE_CHANGE类别下）
                val updatedTriggerConditions = command.triggerConditions.map { condition ->
                    if (condition is com.weinuo.quickcommands22.model.AppStateCondition &&
                        condition.categoryType == com.weinuo.quickcommands22.model.AppStateCategoryType.STATE_CHANGE &&
                        condition.stateType == com.weinuo.quickcommands22.model.AppStateType.BACKGROUND_TIME_EXCEEDED &&
                        condition.autoIncludeNewApps) {

                        val updatedApps = condition.selectedApps.toMutableList()
                        if (!updatedApps.any { it.packageName == packageName }) {
                            updatedApps.add(appInfo)
                            commandUpdated = true
                            Log.d(TAG, "Added $packageName to background time condition in command: ${command.name}")
                        }

                        condition.copy(selectedApps = updatedApps)
                    } else {
                        condition
                    }
                }

                // 更新强制停止应用任务
                val updatedTasks = command.tasks.map { task ->
                    if (task is com.weinuo.quickcommands22.model.ApplicationTask &&
                        task.operation == com.weinuo.quickcommands22.model.ApplicationOperation.FORCE_STOP_APP &&
                        task.autoIncludeNewApps) {

                        val updatedApps = task.forceStopSelectedApps.toMutableList()
                        if (!updatedApps.any { it.packageName == packageName }) {
                            updatedApps.add(appInfo)
                            commandUpdated = true
                            Log.d(TAG, "Added $packageName to force stop task in command: ${command.name}")
                        }

                        task.copy(forceStopSelectedApps = updatedApps)
                    } else {
                        task
                    }
                }

                // 如果有更新，保存快捷指令
                if (commandUpdated) {
                    val updatedCommand = command.copy(
                        triggerConditions = updatedTriggerConditions,
                        tasks = updatedTasks
                    )
                    // 使用协程保存快捷指令
                    kotlinx.coroutines.CoroutineScope(kotlinx.coroutines.Dispatchers.IO).launch {
                        quickCommandRepository.saveCommand(updatedCommand)
                    }
                    hasUpdates = true
                }
            }

            if (hasUpdates) {
                Log.d(TAG, "Updated quick commands with new app: $packageName")
            }

        } catch (e: Exception) {
            Log.e(TAG, "Error updating conditions and tasks with new app: $packageName", e)
        }
    }

    /**
     * 触发应用状态条件
     */
    private fun triggerAppStateCondition(stateType: AppStateType, packageName: String) {
        onAppStateConditionTriggered?.let { callback ->
            try {
                // 获取应用名称
                val appName = try {
                    val packageManager = context.packageManager
                    val appInfo = packageManager.getApplicationInfo(packageName, 0)
                    packageManager.getApplicationLabel(appInfo).toString()
                } catch (e: Exception) {
                    packageName // 如果获取失败，使用包名作为应用名
                }

                // 创建事件数据
                val eventData = mapOf(
                    "packageName" to packageName,
                    "appName" to appName,
                    "stateType" to stateType.name,
                    "timestamp" to System.currentTimeMillis()
                )

                // 创建虚拟的应用状态条件用于触发（实际的条件匹配由监听器处理）
                val condition = AppStateCondition(
                    stateType = stateType,
                    targetPackageName = packageName,
                    targetAppName = appName
                )

                Log.d(TAG, "Triggering app state condition: ${condition.getDescription()}")
                callback(condition, eventData)

            } catch (e: Exception) {
                Log.e(TAG, "Error triggering app state condition", e)
            }
        }
    }

    companion object {
        private const val TAG = "PackageReceiver"
    }
}
