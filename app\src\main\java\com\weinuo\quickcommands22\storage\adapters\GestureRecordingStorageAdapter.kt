package com.weinuo.quickcommands22.storage.adapters

import android.util.Log
import com.weinuo.quickcommands22.model.GestureRecording
import com.weinuo.quickcommands22.model.TouchEvent
import com.weinuo.quickcommands22.model.TouchEventType
import com.weinuo.quickcommands22.model.TouchPosition
import com.weinuo.quickcommands22.storage.NativeTypeStorageManager
import com.weinuo.quickcommands22.storage.StorageDomain
import com.weinuo.quickcommands22.storage.StorageOperation

/**
 * 手势录制存储适配器
 *
 * 负责GestureRecording、TouchEvent、TouchPosition的原生数据类型存储和重建。
 * 将复杂的手势录制对象拆分为独立的原生字段进行存储，
 * 完全避免JSON序列化问题。
 *
 * 存储结构：
 * - 录制基本信息：gesture_{id}_name、gesture_{id}_description等
 * - 事件列表：gesture_{id}_event_count、gesture_{id}_event_{index}_*
 * - 触摸位置：gesture_{id}_event_{index}_position_*
 *
 * 存储格式示例：
 * gesture_123_name = "录制_1234567890"
 * gesture_123_description = "全屏录制的手势操作"
 * gesture_123_created_time = 1234567890L
 * gesture_123_duration = 5000L
 * gesture_123_screen_width = 1080
 * gesture_123_screen_height = 2340
 * gesture_123_version = 1
 * gesture_123_event_count = 2
 * gesture_123_event_0_type = "TAP"
 * gesture_123_event_0_duration = 0L
 * gesture_123_event_0_pressure = 1.0f
 * gesture_123_event_0_description = ""
 * gesture_123_event_0_point_count = 1
 * gesture_123_event_0_point_0_x = 0.5f
 * gesture_123_event_0_point_0_y = 0.5f
 * gesture_123_event_0_point_0_pressure = 1.0f
 * gesture_123_event_0_point_0_size = 1.0f
 * gesture_123_event_0_point_0_pointer_id = 0
 */
class GestureRecordingStorageAdapter(
    private val storageManager: NativeTypeStorageManager
) {
    companion object {
        private const val TAG = "GestureRecordingStorageAdapter"

        // 录制基本信息字段
        private const val FIELD_NAME = "name"
        private const val FIELD_DESCRIPTION = "description"
        private const val FIELD_CREATED_TIME = "created_time"
        private const val FIELD_DURATION = "duration"
        private const val FIELD_SCREEN_WIDTH = "screen_width"
        private const val FIELD_SCREEN_HEIGHT = "screen_height"
        private const val FIELD_VERSION = "version"
        private const val FIELD_EVENT_COUNT = "event_count"

        // 事件字段
        private const val FIELD_EVENT_ID = "id"
        private const val FIELD_EVENT_TYPE = "type"
        private const val FIELD_EVENT_DURATION = "duration"
        private const val FIELD_EVENT_DELAY_AFTER = "delay_after"
        private const val FIELD_EVENT_DESCRIPTION = "description"

        // 触摸位置字段
        private const val FIELD_POSITION_START_X = "start_x"
        private const val FIELD_POSITION_START_Y = "start_y"
        private const val FIELD_POSITION_END_X = "end_x"
        private const val FIELD_POSITION_END_Y = "end_y"
        private const val FIELD_POSITION_TOUCH_POINTS = "touch_points"
    }

    /**
     * 生成录制字段的键名
     *
     * @param recordingId 录制ID
     * @param fieldName 字段名
     * @return 完整的键名
     */
    private fun generateRecordingKey(recordingId: String, fieldName: String): String {
        return "gesture_${recordingId}_$fieldName"
    }

    /**
     * 生成事件字段的键名
     *
     * @param recordingId 录制ID
     * @param eventIndex 事件索引
     * @param fieldName 字段名
     * @return 完整的键名
     */
    private fun generateEventKey(recordingId: String, eventIndex: Int, fieldName: String): String {
        return "gesture_${recordingId}_event_${eventIndex}_$fieldName"
    }



    /**
     * 保存手势录制数据
     *
     * @param recordingId 录制ID（唯一标识符）
     * @param recording 要保存的手势录制对象
     * @return 操作是否成功
     */
    fun saveGestureRecording(recordingId: String, recording: GestureRecording): Boolean {
        if (recordingId.isBlank()) {
            Log.e(TAG, "录制ID不能为空")
            return false
        }

        Log.d(TAG, "开始保存手势录制数据: $recordingId")

        return try {
            val operations = mutableListOf<StorageOperation>()

            // 保存录制基本信息
            operations.addAll(listOf(
                StorageOperation.createStringOperation(
                    StorageDomain.GESTURE_RECORDINGS,
                    generateRecordingKey(recordingId, FIELD_NAME),
                    recording.name
                ),
                StorageOperation.createStringOperation(
                    StorageDomain.GESTURE_RECORDINGS,
                    generateRecordingKey(recordingId, FIELD_DESCRIPTION),
                    recording.description
                ),
                StorageOperation.createLongOperation(
                    StorageDomain.GESTURE_RECORDINGS,
                    generateRecordingKey(recordingId, FIELD_CREATED_TIME),
                    recording.createdTime
                ),
                StorageOperation.createLongOperation(
                    StorageDomain.GESTURE_RECORDINGS,
                    generateRecordingKey(recordingId, FIELD_DURATION),
                    recording.duration
                ),
                StorageOperation.createIntOperation(
                    StorageDomain.GESTURE_RECORDINGS,
                    generateRecordingKey(recordingId, FIELD_SCREEN_WIDTH),
                    recording.screenWidth
                ),
                StorageOperation.createIntOperation(
                    StorageDomain.GESTURE_RECORDINGS,
                    generateRecordingKey(recordingId, FIELD_SCREEN_HEIGHT),
                    recording.screenHeight
                ),
                StorageOperation.createIntOperation(
                    StorageDomain.GESTURE_RECORDINGS,
                    generateRecordingKey(recordingId, FIELD_VERSION),
                    recording.version
                ),
                StorageOperation.createIntOperation(
                    StorageDomain.GESTURE_RECORDINGS,
                    generateRecordingKey(recordingId, FIELD_EVENT_COUNT),
                    recording.events.size
                )
            ))

            // 保存事件列表
            recording.events.forEachIndexed { eventIndex, event ->
                operations.addAll(saveEventOperations(recordingId, eventIndex, event))
            }

            val success = storageManager.executeBatch(operations)
            if (success) {
                Log.d(TAG, "手势录制数据保存成功: $recordingId")
            } else {
                Log.e(TAG, "手势录制数据保存失败: $recordingId")
            }
            success

        } catch (e: Exception) {
            Log.e(TAG, "保存手势录制数据时发生异常: $recordingId", e)
            false
        }
    }

    /**
     * 生成保存事件的操作列表
     *
     * @param recordingId 录制ID
     * @param eventIndex 事件索引
     * @param event 触摸事件
     * @return 存储操作列表
     */
    private fun saveEventOperations(recordingId: String, eventIndex: Int, event: TouchEvent): List<StorageOperation> {
        val operations = mutableListOf<StorageOperation>()

        // 保存事件基本信息
        operations.addAll(listOf(
            StorageOperation.createStringOperation(
                StorageDomain.GESTURE_RECORDINGS,
                generateEventKey(recordingId, eventIndex, FIELD_EVENT_ID),
                event.id
            ),
            StorageOperation.createStringOperation(
                StorageDomain.GESTURE_RECORDINGS,
                generateEventKey(recordingId, eventIndex, FIELD_EVENT_TYPE),
                event.type.name
            ),
            StorageOperation.createLongOperation(
                StorageDomain.GESTURE_RECORDINGS,
                generateEventKey(recordingId, eventIndex, FIELD_EVENT_DURATION),
                event.duration
            ),
            StorageOperation.createLongOperation(
                StorageDomain.GESTURE_RECORDINGS,
                generateEventKey(recordingId, eventIndex, FIELD_EVENT_DELAY_AFTER),
                event.delayAfter
            ),
            StorageOperation.createStringOperation(
                StorageDomain.GESTURE_RECORDINGS,
                generateEventKey(recordingId, eventIndex, FIELD_EVENT_DESCRIPTION),
                event.description
            )
        ))

        // 保存触摸位置信息
        operations.addAll(savePositionOperations(recordingId, eventIndex, event.position))

        return operations
    }

    /**
     * 生成保存触摸位置的操作列表
     *
     * @param recordingId 录制ID
     * @param eventIndex 事件索引
     * @param position 触摸位置
     * @return 存储操作列表
     */
    private fun savePositionOperations(recordingId: String, eventIndex: Int, position: TouchPosition): List<StorageOperation> {
        return listOf(
            StorageOperation.createFloatOperation(
                StorageDomain.GESTURE_RECORDINGS,
                generateEventKey(recordingId, eventIndex, FIELD_POSITION_START_X),
                position.startX
            ),
            StorageOperation.createFloatOperation(
                StorageDomain.GESTURE_RECORDINGS,
                generateEventKey(recordingId, eventIndex, FIELD_POSITION_START_Y),
                position.startY
            ),
            StorageOperation.createFloatOperation(
                StorageDomain.GESTURE_RECORDINGS,
                generateEventKey(recordingId, eventIndex, FIELD_POSITION_END_X),
                position.endX
            ),
            StorageOperation.createFloatOperation(
                StorageDomain.GESTURE_RECORDINGS,
                generateEventKey(recordingId, eventIndex, FIELD_POSITION_END_Y),
                position.endY
            ),
            StorageOperation.createIntOperation(
                StorageDomain.GESTURE_RECORDINGS,
                generateEventKey(recordingId, eventIndex, FIELD_POSITION_TOUCH_POINTS),
                position.touchPoints
            )
        )
    }

    /**
     * 加载手势录制数据
     *
     * @param recordingId 录制ID
     * @return 加载的手势录制对象，失败时返回null
     */
    fun loadGestureRecording(recordingId: String): GestureRecording? {
        if (recordingId.isBlank()) {
            Log.e(TAG, "录制ID不能为空")
            return null
        }

        Log.d(TAG, "开始加载手势录制数据: $recordingId")

        return try {
            // 检查录制是否存在
            if (!existsGestureRecording(recordingId)) {
                Log.d(TAG, "手势录制数据不存在: $recordingId")
                return null
            }

            // 加载录制基本信息
            val name = storageManager.loadString(
                StorageDomain.GESTURE_RECORDINGS,
                generateRecordingKey(recordingId, FIELD_NAME),
                ""
            )
            val description = storageManager.loadString(
                StorageDomain.GESTURE_RECORDINGS,
                generateRecordingKey(recordingId, FIELD_DESCRIPTION),
                ""
            )
            val createdTime = storageManager.loadLong(
                StorageDomain.GESTURE_RECORDINGS,
                generateRecordingKey(recordingId, FIELD_CREATED_TIME),
                System.currentTimeMillis()
            )
            val duration = storageManager.loadLong(
                StorageDomain.GESTURE_RECORDINGS,
                generateRecordingKey(recordingId, FIELD_DURATION),
                0L
            )
            val screenWidth = storageManager.loadInt(
                StorageDomain.GESTURE_RECORDINGS,
                generateRecordingKey(recordingId, FIELD_SCREEN_WIDTH),
                0
            )
            val screenHeight = storageManager.loadInt(
                StorageDomain.GESTURE_RECORDINGS,
                generateRecordingKey(recordingId, FIELD_SCREEN_HEIGHT),
                0
            )
            val version = storageManager.loadInt(
                StorageDomain.GESTURE_RECORDINGS,
                generateRecordingKey(recordingId, FIELD_VERSION),
                1
            )
            val eventCount = storageManager.loadInt(
                StorageDomain.GESTURE_RECORDINGS,
                generateRecordingKey(recordingId, FIELD_EVENT_COUNT),
                0
            )

            // 加载事件列表
            val events = mutableListOf<TouchEvent>()
            for (eventIndex in 0 until eventCount) {
                val event = loadEvent(recordingId, eventIndex)
                if (event != null) {
                    events.add(event)
                } else {
                    Log.w(TAG, "加载事件失败: $recordingId, eventIndex: $eventIndex")
                }
            }

            val recording = GestureRecording(
                name = name,
                description = description,
                createdTime = createdTime,
                duration = duration,
                screenWidth = screenWidth,
                screenHeight = screenHeight,
                events = events,
                version = version
            )

            Log.d(TAG, "手势录制数据加载成功: $recordingId")
            recording

        } catch (e: Exception) {
            Log.e(TAG, "加载手势录制数据时发生异常: $recordingId", e)
            null
        }
    }

    /**
     * 加载触摸事件
     *
     * @param recordingId 录制ID
     * @param eventIndex 事件索引
     * @return 加载的触摸事件，失败时返回null
     */
    private fun loadEvent(recordingId: String, eventIndex: Int): TouchEvent? {
        return try {
            val id = storageManager.loadString(
                StorageDomain.GESTURE_RECORDINGS,
                generateEventKey(recordingId, eventIndex, FIELD_EVENT_ID),
                ""
            )
            val typeString = storageManager.loadString(
                StorageDomain.GESTURE_RECORDINGS,
                generateEventKey(recordingId, eventIndex, FIELD_EVENT_TYPE),
                TouchEventType.TAP.name
            )
            val type = try {
                TouchEventType.valueOf(typeString)
            } catch (e: IllegalArgumentException) {
                Log.w(TAG, "未知的触摸事件类型: $typeString，使用默认值TAP")
                TouchEventType.TAP
            }
            val duration = storageManager.loadLong(
                StorageDomain.GESTURE_RECORDINGS,
                generateEventKey(recordingId, eventIndex, FIELD_EVENT_DURATION),
                0L
            )
            val delayAfter = storageManager.loadLong(
                StorageDomain.GESTURE_RECORDINGS,
                generateEventKey(recordingId, eventIndex, FIELD_EVENT_DELAY_AFTER),
                0L
            )
            val description = storageManager.loadString(
                StorageDomain.GESTURE_RECORDINGS,
                generateEventKey(recordingId, eventIndex, FIELD_EVENT_DESCRIPTION),
                ""
            )

            // 加载触摸位置信息
            val position = loadPosition(recordingId, eventIndex)

            TouchEvent(
                id = id.ifEmpty { java.util.UUID.randomUUID().toString() },
                type = type,
                position = position,
                duration = duration,
                delayAfter = delayAfter,
                description = description
            )

        } catch (e: Exception) {
            Log.e(TAG, "加载触摸事件时发生异常: $recordingId, eventIndex: $eventIndex", e)
            null
        }
    }

    /**
     * 加载触摸位置
     *
     * @param recordingId 录制ID
     * @param eventIndex 事件索引
     * @return 加载的触摸位置
     */
    private fun loadPosition(recordingId: String, eventIndex: Int): TouchPosition {
        return try {
            val startX = storageManager.loadFloat(
                StorageDomain.GESTURE_RECORDINGS,
                generateEventKey(recordingId, eventIndex, FIELD_POSITION_START_X),
                0.0f
            )
            val startY = storageManager.loadFloat(
                StorageDomain.GESTURE_RECORDINGS,
                generateEventKey(recordingId, eventIndex, FIELD_POSITION_START_Y),
                0.0f
            )
            val endX = storageManager.loadFloat(
                StorageDomain.GESTURE_RECORDINGS,
                generateEventKey(recordingId, eventIndex, FIELD_POSITION_END_X),
                startX
            )
            val endY = storageManager.loadFloat(
                StorageDomain.GESTURE_RECORDINGS,
                generateEventKey(recordingId, eventIndex, FIELD_POSITION_END_Y),
                startY
            )
            val touchPoints = storageManager.loadInt(
                StorageDomain.GESTURE_RECORDINGS,
                generateEventKey(recordingId, eventIndex, FIELD_POSITION_TOUCH_POINTS),
                1
            )

            TouchPosition(
                startX = startX,
                startY = startY,
                endX = endX,
                endY = endY,
                touchPoints = touchPoints
            )

        } catch (e: Exception) {
            Log.e(TAG, "加载触摸位置时发生异常: $recordingId, eventIndex: $eventIndex", e)
            TouchPosition(0.0f, 0.0f) // 返回默认位置
        }
    }

    /**
     * 检查手势录制是否存在
     *
     * @param recordingId 录制ID
     * @return 是否存在
     */
    fun existsGestureRecording(recordingId: String): Boolean {
        return try {
            storageManager.containsKey(
                StorageDomain.GESTURE_RECORDINGS,
                generateRecordingKey(recordingId, FIELD_NAME)
            )
        } catch (e: Exception) {
            Log.e(TAG, "检查手势录制存在性时发生异常: $recordingId", e)
            false
        }
    }

    /**
     * 删除手势录制数据
     *
     * @param recordingId 录制ID
     * @return 操作是否成功
     */
    fun deleteGestureRecording(recordingId: String): Boolean {
        if (recordingId.isBlank()) {
            Log.e(TAG, "录制ID不能为空")
            return false
        }

        Log.d(TAG, "开始删除手势录制数据: $recordingId")

        return try {
            // 先检查录制是否存在
            if (!existsGestureRecording(recordingId)) {
                Log.d(TAG, "手势录制数据不存在，无需删除: $recordingId")
                return true
            }

            // 获取事件数量
            val eventCount = storageManager.loadInt(
                StorageDomain.GESTURE_RECORDINGS,
                generateRecordingKey(recordingId, FIELD_EVENT_COUNT),
                0
            )

            val operations = mutableListOf<StorageOperation>()

            // 删除录制基本信息
            operations.addAll(listOf(
                StorageOperation.createDeleteOperation(
                    StorageDomain.GESTURE_RECORDINGS,
                    generateRecordingKey(recordingId, FIELD_NAME)
                ),
                StorageOperation.createDeleteOperation(
                    StorageDomain.GESTURE_RECORDINGS,
                    generateRecordingKey(recordingId, FIELD_DESCRIPTION)
                ),
                StorageOperation.createDeleteOperation(
                    StorageDomain.GESTURE_RECORDINGS,
                    generateRecordingKey(recordingId, FIELD_CREATED_TIME)
                ),
                StorageOperation.createDeleteOperation(
                    StorageDomain.GESTURE_RECORDINGS,
                    generateRecordingKey(recordingId, FIELD_DURATION)
                ),
                StorageOperation.createDeleteOperation(
                    StorageDomain.GESTURE_RECORDINGS,
                    generateRecordingKey(recordingId, FIELD_SCREEN_WIDTH)
                ),
                StorageOperation.createDeleteOperation(
                    StorageDomain.GESTURE_RECORDINGS,
                    generateRecordingKey(recordingId, FIELD_SCREEN_HEIGHT)
                ),
                StorageOperation.createDeleteOperation(
                    StorageDomain.GESTURE_RECORDINGS,
                    generateRecordingKey(recordingId, FIELD_VERSION)
                ),
                StorageOperation.createDeleteOperation(
                    StorageDomain.GESTURE_RECORDINGS,
                    generateRecordingKey(recordingId, FIELD_EVENT_COUNT)
                )
            ))

            // 删除所有事件数据
            for (eventIndex in 0 until eventCount) {
                operations.addAll(deleteEventOperations(recordingId, eventIndex))
            }

            val success = storageManager.executeBatch(operations)
            if (success) {
                Log.d(TAG, "手势录制数据删除成功: $recordingId")
            } else {
                Log.e(TAG, "手势录制数据删除失败: $recordingId")
            }
            success

        } catch (e: Exception) {
            Log.e(TAG, "删除手势录制数据时发生异常: $recordingId", e)
            false
        }
    }

    /**
     * 生成删除事件的操作列表
     *
     * @param recordingId 录制ID
     * @param eventIndex 事件索引
     * @return 删除操作列表
     */
    private fun deleteEventOperations(recordingId: String, eventIndex: Int): List<StorageOperation> {
        val operations = mutableListOf<StorageOperation>()

        // 不再需要获取触摸点数量，因为我们使用位置信息

        // 删除事件基本信息
        operations.addAll(listOf(
            StorageOperation.createDeleteOperation(
                StorageDomain.GESTURE_RECORDINGS,
                generateEventKey(recordingId, eventIndex, FIELD_EVENT_ID)
            ),
            StorageOperation.createDeleteOperation(
                StorageDomain.GESTURE_RECORDINGS,
                generateEventKey(recordingId, eventIndex, FIELD_EVENT_TYPE)
            ),
            StorageOperation.createDeleteOperation(
                StorageDomain.GESTURE_RECORDINGS,
                generateEventKey(recordingId, eventIndex, FIELD_EVENT_DURATION)
            ),
            StorageOperation.createDeleteOperation(
                StorageDomain.GESTURE_RECORDINGS,
                generateEventKey(recordingId, eventIndex, FIELD_EVENT_DELAY_AFTER)
            ),
            StorageOperation.createDeleteOperation(
                StorageDomain.GESTURE_RECORDINGS,
                generateEventKey(recordingId, eventIndex, FIELD_EVENT_DESCRIPTION)
            )
        ))

        // 删除位置信息
        operations.addAll(deletePositionOperations(recordingId, eventIndex))

        return operations
    }

    /**
     * 生成删除触摸位置的操作列表
     *
     * @param recordingId 录制ID
     * @param eventIndex 事件索引
     * @return 删除操作列表
     */
    private fun deletePositionOperations(recordingId: String, eventIndex: Int): List<StorageOperation> {
        return listOf(
            StorageOperation.createDeleteOperation(
                StorageDomain.GESTURE_RECORDINGS,
                generateEventKey(recordingId, eventIndex, FIELD_POSITION_START_X)
            ),
            StorageOperation.createDeleteOperation(
                StorageDomain.GESTURE_RECORDINGS,
                generateEventKey(recordingId, eventIndex, FIELD_POSITION_START_Y)
            ),
            StorageOperation.createDeleteOperation(
                StorageDomain.GESTURE_RECORDINGS,
                generateEventKey(recordingId, eventIndex, FIELD_POSITION_END_X)
            ),
            StorageOperation.createDeleteOperation(
                StorageDomain.GESTURE_RECORDINGS,
                generateEventKey(recordingId, eventIndex, FIELD_POSITION_END_Y)
            ),
            StorageOperation.createDeleteOperation(
                StorageDomain.GESTURE_RECORDINGS,
                generateEventKey(recordingId, eventIndex, FIELD_POSITION_TOUCH_POINTS)
            )
        )
    }

    /**
     * 获取所有手势录制ID列表
     *
     * @return 录制ID列表
     */
    fun getAllGestureRecordingIds(): List<String> {
        return try {
            val prefs = storageManager.getPreferences(StorageDomain.GESTURE_RECORDINGS)
            val recordingIds = mutableSetOf<String>()

            // 遍历所有键，提取录制ID
            prefs.all.keys.forEach { key ->
                if (key.startsWith("gesture_") && key.endsWith("_$FIELD_NAME")) {
                    val recordingId = key.removePrefix("gesture_").removeSuffix("_$FIELD_NAME")
                    recordingIds.add(recordingId)
                }
            }

            recordingIds.toList().sorted()

        } catch (e: Exception) {
            Log.e(TAG, "获取手势录制ID列表时发生异常", e)
            emptyList()
        }
    }
}
