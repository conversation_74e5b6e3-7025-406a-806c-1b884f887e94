package com.weinuo.quickcommands22.storage

import android.content.Context
import android.util.Log
import com.weinuo.quickcommands22.model.SharedTriggerCondition
import com.weinuo.quickcommands22.model.SharedTask
import com.weinuo.quickcommands22.storage.adapters.ConditionAdapterManager
import com.weinuo.quickcommands22.storage.adapters.TaskAdapterManager

/**
 * 导航数据存储管理器
 *
 * 专门用于管理导航过程中传递的数据，替代savedStateHandle中的JSON序列化。
 * 使用原生数据类型存储，确保导航数据传递的可靠性。
 *
 * 核心功能：
 * - 条件编辑数据传递
 * - 任务编辑数据传递
 * - 配置数据传递
 * - 导航结果回传
 *
 * @param context Android上下文
 */
class NavigationDataStorageManager(context: Context) {

    companion object {
        private const val TAG = "NavigationDataManager"

        // 导航数据类型
        private const val TYPE_CONDITION = "condition"
        private const val TYPE_TASK = "task"
        private const val TYPE_SIMPLE_DATA = "simple_data"
    }

    private val storageManager = NativeTypeStorageManager(context)
    private val conditionAdapterManager = ConditionAdapterManager(storageManager, AppListStorageEngine(storageManager))
    private val taskAdapterManager = TaskAdapterManager(storageManager)

    /**
     * 保存条件编辑数据
     *
     * @param navigationKey 导航键名，用于标识不同的导航流程
     * @param condition 条件对象
     * @param editIndex 编辑索引，null表示新增
     * @return 保存是否成功
     */
    fun saveConditionEditData(navigationKey: String, condition: SharedTriggerCondition, editIndex: Int? = null): Boolean {
        return try {
            val operations = mutableListOf<StorageOperation>()

            // 保存数据类型
            operations.add(StorageOperation.createStringOperation(
                StorageDomain.NAVIGATION_DATA,
                "nav_${navigationKey}_type",
                TYPE_CONDITION
            ))

            // 保存条件ID
            operations.add(StorageOperation.createStringOperation(
                StorageDomain.NAVIGATION_DATA,
                "nav_${navigationKey}_condition_id",
                condition.id
            ))

            // 保存编辑索引
            if (editIndex != null) {
                operations.add(StorageOperation.createIntOperation(
                    StorageDomain.NAVIGATION_DATA,
                    "nav_${navigationKey}_edit_index",
                    editIndex
                ))
            }

            // 保存条件到条件存储域
            conditionAdapterManager.saveCondition(condition)

            storageManager.executeBatch(operations)
        } catch (e: Exception) {
            Log.e(TAG, "保存条件编辑数据失败: $navigationKey", e)
            false
        }
    }

    /**
     * 加载条件编辑数据
     *
     * @param navigationKey 导航键名
     * @return 条件编辑数据，失败返回null
     */
    fun loadConditionEditData(navigationKey: String): ConditionEditData? {
        return try {
            val type = storageManager.loadString(StorageDomain.NAVIGATION_DATA, "nav_${navigationKey}_type")
            if (type != TYPE_CONDITION) return null

            val conditionId = storageManager.loadString(StorageDomain.NAVIGATION_DATA, "nav_${navigationKey}_condition_id")
            if (conditionId.isEmpty()) return null

            val condition = conditionAdapterManager.loadConditionByStoredType(conditionId) ?: return null

            val editIndex = storageManager.loadInt(StorageDomain.NAVIGATION_DATA, "nav_${navigationKey}_edit_index", -1)

            ConditionEditData(
                condition = condition,
                editIndex = if (editIndex >= 0) editIndex else null
            )
        } catch (e: Exception) {
            Log.e(TAG, "加载条件编辑数据失败: $navigationKey", e)
            null
        }
    }

    /**
     * 保存任务编辑数据
     *
     * @param navigationKey 导航键名
     * @param task 任务对象
     * @param editIndex 编辑索引，null表示新增
     * @return 保存是否成功
     */
    fun saveTaskEditData(navigationKey: String, task: SharedTask, editIndex: Int? = null): Boolean {
        return try {
            val operations = mutableListOf<StorageOperation>()

            // 保存数据类型
            operations.add(StorageOperation.createStringOperation(
                StorageDomain.NAVIGATION_DATA,
                "nav_${navigationKey}_type",
                TYPE_TASK
            ))

            // 保存任务ID
            operations.add(StorageOperation.createStringOperation(
                StorageDomain.NAVIGATION_DATA,
                "nav_${navigationKey}_task_id",
                task.id
            ))

            // 保存编辑索引
            if (editIndex != null) {
                operations.add(StorageOperation.createIntOperation(
                    StorageDomain.NAVIGATION_DATA,
                    "nav_${navigationKey}_edit_index",
                    editIndex
                ))
            }

            // 保存任务到任务存储域
            taskAdapterManager.saveTask(task)

            storageManager.executeBatch(operations)
        } catch (e: Exception) {
            Log.e(TAG, "保存任务编辑数据失败: $navigationKey", e)
            false
        }
    }

    /**
     * 加载任务编辑数据
     *
     * @param navigationKey 导航键名
     * @return 任务编辑数据，失败返回null
     */
    fun loadTaskEditData(navigationKey: String): TaskEditData? {
        return try {
            val type = storageManager.loadString(StorageDomain.NAVIGATION_DATA, "nav_${navigationKey}_type")
            if (type != TYPE_TASK) return null

            val taskId = storageManager.loadString(StorageDomain.NAVIGATION_DATA, "nav_${navigationKey}_task_id")
            if (taskId.isEmpty()) return null

            val task = taskAdapterManager.loadTaskByStoredType(taskId) ?: return null

            val editIndex = storageManager.loadInt(StorageDomain.NAVIGATION_DATA, "nav_${navigationKey}_edit_index", -1)

            TaskEditData(
                task = task,
                editIndex = if (editIndex >= 0) editIndex else null
            )
        } catch (e: Exception) {
            Log.e(TAG, "加载任务编辑数据失败: $navigationKey", e)
            null
        }
    }

    /**
     * 保存简单导航数据
     *
     * @param navigationKey 导航键名
     * @param data 数据映射
     * @return 保存是否成功
     */
    fun saveSimpleNavigationData(navigationKey: String, data: Map<String, Any>): Boolean {
        return try {
            val operations = mutableListOf<StorageOperation>()

            // 保存数据类型
            operations.add(StorageOperation.createStringOperation(
                StorageDomain.NAVIGATION_DATA,
                "nav_${navigationKey}_type",
                TYPE_SIMPLE_DATA
            ))

            // 保存数据项数量
            operations.add(StorageOperation.createIntOperation(
                StorageDomain.NAVIGATION_DATA,
                "nav_${navigationKey}_data_count",
                data.size
            ))

            // 保存每个数据项
            data.entries.forEachIndexed { index, (key, value) ->
                val keyPrefix = "nav_${navigationKey}_data_${index}"

                // 保存键名
                operations.add(StorageOperation.createStringOperation(
                    StorageDomain.NAVIGATION_DATA,
                    "${keyPrefix}_key",
                    key
                ))

                // 保存值和类型
                when (value) {
                    is String -> {
                        operations.add(StorageOperation.createStringOperation(
                            StorageDomain.NAVIGATION_DATA,
                            "${keyPrefix}_type",
                            "string"
                        ))
                        operations.add(StorageOperation.createStringOperation(
                            StorageDomain.NAVIGATION_DATA,
                            "${keyPrefix}_value",
                            value
                        ))
                    }
                    is Int -> {
                        operations.add(StorageOperation.createStringOperation(
                            StorageDomain.NAVIGATION_DATA,
                            "${keyPrefix}_type",
                            "int"
                        ))
                        operations.add(StorageOperation.createIntOperation(
                            StorageDomain.NAVIGATION_DATA,
                            "${keyPrefix}_value",
                            value
                        ))
                    }
                    is Boolean -> {
                        operations.add(StorageOperation.createStringOperation(
                            StorageDomain.NAVIGATION_DATA,
                            "${keyPrefix}_type",
                            "boolean"
                        ))
                        operations.add(StorageOperation.createBooleanOperation(
                            StorageDomain.NAVIGATION_DATA,
                            "${keyPrefix}_value",
                            value
                        ))
                    }
                    is Float -> {
                        operations.add(StorageOperation.createStringOperation(
                            StorageDomain.NAVIGATION_DATA,
                            "${keyPrefix}_type",
                            "float"
                        ))
                        operations.add(StorageOperation.createFloatOperation(
                            StorageDomain.NAVIGATION_DATA,
                            "${keyPrefix}_value",
                            value
                        ))
                    }
                    is Long -> {
                        operations.add(StorageOperation.createStringOperation(
                            StorageDomain.NAVIGATION_DATA,
                            "${keyPrefix}_type",
                            "long"
                        ))
                        operations.add(StorageOperation.createLongOperation(
                            StorageDomain.NAVIGATION_DATA,
                            "${keyPrefix}_value",
                            value
                        ))
                    }
                    else -> {
                        Log.w(TAG, "不支持的导航数据类型: ${value::class.java.simpleName}")
                        return false
                    }
                }
            }

            storageManager.executeBatch(operations)
        } catch (e: Exception) {
            Log.e(TAG, "保存简单导航数据失败: $navigationKey", e)
            false
        }
    }

    /**
     * 加载简单导航数据
     *
     * @param navigationKey 导航键名
     * @return 数据映射，失败返回空映射
     */
    fun loadSimpleNavigationData(navigationKey: String): Map<String, Any> {
        return try {
            val type = storageManager.loadString(StorageDomain.NAVIGATION_DATA, "nav_${navigationKey}_type")
            if (type != TYPE_SIMPLE_DATA) return emptyMap()

            val count = storageManager.loadInt(StorageDomain.NAVIGATION_DATA, "nav_${navigationKey}_data_count", 0)
            if (count == 0) return emptyMap()

            val data = mutableMapOf<String, Any>()

            for (index in 0 until count) {
                val keyPrefix = "nav_${navigationKey}_data_${index}"

                val key = storageManager.loadString(StorageDomain.NAVIGATION_DATA, "${keyPrefix}_key")
                val valueType = storageManager.loadString(StorageDomain.NAVIGATION_DATA, "${keyPrefix}_type")

                if (key.isNotEmpty() && valueType.isNotEmpty()) {
                    val value = when (valueType) {
                        "string" -> storageManager.loadString(StorageDomain.NAVIGATION_DATA, "${keyPrefix}_value")
                        "int" -> storageManager.loadInt(StorageDomain.NAVIGATION_DATA, "${keyPrefix}_value")
                        "boolean" -> storageManager.loadBoolean(StorageDomain.NAVIGATION_DATA, "${keyPrefix}_value")
                        "float" -> storageManager.loadFloat(StorageDomain.NAVIGATION_DATA, "${keyPrefix}_value")
                        "long" -> storageManager.loadLong(StorageDomain.NAVIGATION_DATA, "${keyPrefix}_value")
                        else -> continue
                    }
                    data[key] = value
                }
            }

            data
        } catch (e: Exception) {
            Log.e(TAG, "加载简单导航数据失败: $navigationKey", e)
            emptyMap()
        }
    }

    /**
     * 清除导航数据
     *
     * @param navigationKey 导航键名
     * @return 清除是否成功
     */
    fun clearNavigationData(navigationKey: String): Boolean {
        return try {
            storageManager.deleteByPrefix(StorageDomain.NAVIGATION_DATA, "nav_${navigationKey}_")
        } catch (e: Exception) {
            Log.e(TAG, "清除导航数据失败: $navigationKey", e)
            false
        }
    }
}

/**
 * 条件编辑数据
 */
data class ConditionEditData(
    val condition: SharedTriggerCondition,
    val editIndex: Int?
)

/**
 * 任务编辑数据
 */
data class TaskEditData(
    val task: SharedTask,
    val editIndex: Int?
)
