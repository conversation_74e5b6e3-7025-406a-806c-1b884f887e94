package com.weinuo.quickcommands22.ui.components

import android.content.Context
import android.graphics.*
import android.util.AttributeSet
import android.view.GestureDetector
import android.view.MotionEvent
import android.view.ScaleGestureDetector
import android.view.View
import kotlin.math.max
import kotlin.math.min

/**
 * 圆形裁剪图片视图
 * 
 * 支持功能：
 * - 圆形裁剪框显示
 * - 图片拖拽和缩放
 * - 圆形遮罩层
 * - 裁剪结果输出
 * 
 * 使用方式：
 * 1. 设置图片：setImageBitmap() 或 setImageUri()
 * 2. 用户进行拖拽缩放操作
 * 3. 调用getCroppedBitmap()获取裁剪结果
 */
class CircleCropImageView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    companion object {
        private const val MIN_SCALE = 0.5f
        private const val MAX_SCALE = 5.0f
        private const val CROP_CIRCLE_MARGIN = 50f // 裁剪圆形边距
    }

    // 图片变换矩阵
    private val imageMatrix = Matrix()
    private val tempMatrix = Matrix()
    
    // 裁剪圆形相关
    private var cropCircleRadius = 0f
    private var cropCircleCenterX = 0f
    private var cropCircleCenterY = 0f
    private val cropCirclePath = Path()
    
    // 绘制相关
    private val dimmedPaint = Paint().apply {
        color = Color.parseColor("#80000000") // 半透明黑色
        style = Paint.Style.FILL
    }
    
    private val cropCirclePaint = Paint().apply {
        color = Color.WHITE
        style = Paint.Style.STROKE
        strokeWidth = 4f
        isAntiAlias = true
    }
    
    // 手势检测
    private val gestureDetector = GestureDetector(context, GestureListener())
    private val scaleGestureDetector = ScaleGestureDetector(context, ScaleGestureListener())
    
    // 当前缩放和平移状态
    private var currentScale = 1f
    private var currentTranslateX = 0f
    private var currentTranslateY = 0f
    
    // 图片原始尺寸
    private var originalBitmap: Bitmap? = null
    private var imageWidth = 0f
    private var imageHeight = 0f

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        setupCropCircle()
        initializeImageMatrix()
    }

    fun setImageBitmap(bm: Bitmap?) {
        originalBitmap = bm

        bm?.let {
            imageWidth = it.width.toFloat()
            imageHeight = it.height.toFloat()
            initializeImageMatrix()
        }
        invalidate()
    }

    /**
     * 设置裁剪圆形
     */
    private fun setupCropCircle() {
        val size = min(width, height)
        cropCircleRadius = (size - CROP_CIRCLE_MARGIN * 2) / 2f
        cropCircleCenterX = width / 2f
        cropCircleCenterY = height / 2f
        
        // 创建圆形路径
        cropCirclePath.reset()
        cropCirclePath.addCircle(cropCircleCenterX, cropCircleCenterY, cropCircleRadius, Path.Direction.CW)
    }

    /**
     * 初始化图片矩阵
     */
    private fun initializeImageMatrix() {
        if (originalBitmap == null || width == 0 || height == 0) return
        
        // 计算初始缩放比例，使图片能够填满裁剪圆形
        val scaleX = (cropCircleRadius * 2) / imageWidth
        val scaleY = (cropCircleRadius * 2) / imageHeight
        currentScale = max(scaleX, scaleY)
        
        // 计算居中位置
        currentTranslateX = cropCircleCenterX - (imageWidth * currentScale) / 2f
        currentTranslateY = cropCircleCenterY - (imageHeight * currentScale) / 2f
        
        updateImageMatrix()
    }

    /**
     * 更新图片矩阵
     */
    private fun updateImageMatrix() {
        imageMatrix.reset()
        imageMatrix.postScale(currentScale, currentScale)
        imageMatrix.postTranslate(currentTranslateX, currentTranslateY)
        invalidate()
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        // 绘制图片
        originalBitmap?.let { bitmap ->
            canvas.save()
            canvas.concat(imageMatrix)
            canvas.drawBitmap(bitmap, 0f, 0f, null)
            canvas.restore()
        }

        // 绘制遮罩层（裁剪圆形外的区域）
        canvas.save()
        canvas.clipPath(cropCirclePath, Region.Op.DIFFERENCE)
        canvas.drawColor(dimmedPaint.color)
        canvas.restore()

        // 绘制裁剪圆形边框
        canvas.drawCircle(cropCircleCenterX, cropCircleCenterY, cropCircleRadius, cropCirclePaint)
    }

    override fun onTouchEvent(event: MotionEvent): Boolean {
        var handled = scaleGestureDetector.onTouchEvent(event)
        if (!scaleGestureDetector.isInProgress) {
            handled = gestureDetector.onTouchEvent(event) || handled
        }
        return handled || super.onTouchEvent(event)
    }

    /**
     * 手势监听器
     */
    private inner class GestureListener : GestureDetector.SimpleOnGestureListener() {
        
        override fun onDown(e: MotionEvent): Boolean = true

        override fun onScroll(
            e1: MotionEvent?,
            e2: MotionEvent,
            distanceX: Float,
            distanceY: Float
        ): Boolean {
            // 平移图片
            currentTranslateX -= distanceX
            currentTranslateY -= distanceY
            
            // 限制平移范围，确保图片不会移出裁剪圆形
            constrainTranslation()
            updateImageMatrix()
            return true
        }

        override fun onDoubleTap(e: MotionEvent): Boolean {
            // 双击重置到初始状态
            initializeImageMatrix()
            return true
        }
    }

    /**
     * 缩放手势监听器
     */
    private inner class ScaleGestureListener : ScaleGestureDetector.SimpleOnScaleGestureListener() {
        
        override fun onScale(detector: ScaleGestureDetector): Boolean {
            val scaleFactor = detector.scaleFactor
            val newScale = currentScale * scaleFactor
            
            // 限制缩放范围
            if (newScale in MIN_SCALE..MAX_SCALE) {
                val focusX = detector.focusX
                val focusY = detector.focusY
                
                // 以焦点为中心进行缩放
                currentTranslateX = focusX + (currentTranslateX - focusX) * scaleFactor
                currentTranslateY = focusY + (currentTranslateY - focusY) * scaleFactor
                currentScale = newScale
                
                // 限制平移范围
                constrainTranslation()
                updateImageMatrix()
            }
            return true
        }
    }

    /**
     * 限制平移范围，确保图片始终覆盖裁剪圆形
     */
    private fun constrainTranslation() {
        if (originalBitmap == null) return
        
        val scaledWidth = imageWidth * currentScale
        val scaledHeight = imageHeight * currentScale
        
        // 计算图片边界
        val imageLeft = currentTranslateX
        val imageTop = currentTranslateY
        val imageRight = imageLeft + scaledWidth
        val imageBottom = imageTop + scaledHeight
        
        // 计算裁剪圆形边界
        val cropLeft = cropCircleCenterX - cropCircleRadius
        val cropTop = cropCircleCenterY - cropCircleRadius
        val cropRight = cropCircleCenterX + cropCircleRadius
        val cropBottom = cropCircleCenterY + cropCircleRadius
        
        // 限制平移，确保图片完全覆盖裁剪圆形
        if (imageLeft > cropLeft) {
            currentTranslateX = cropLeft
        }
        if (imageTop > cropTop) {
            currentTranslateY = cropTop
        }
        if (imageRight < cropRight) {
            currentTranslateX = cropRight - scaledWidth
        }
        if (imageBottom < cropBottom) {
            currentTranslateY = cropBottom - scaledHeight
        }
    }

    /**
     * 获取裁剪后的圆形Bitmap
     * 
     * @param outputSize 输出图片的尺寸（正方形）
     * @return 裁剪后的圆形Bitmap
     */
    fun getCroppedBitmap(outputSize: Int = 200): Bitmap? {
        val bitmap = originalBitmap ?: return null
        
        // 创建输出Bitmap
        val output = Bitmap.createBitmap(outputSize, outputSize, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(output)
        
        // 创建圆形裁剪路径
        val clipPath = Path()
        clipPath.addCircle(outputSize / 2f, outputSize / 2f, outputSize / 2f, Path.Direction.CW)
        canvas.clipPath(clipPath)
        
        // 计算从裁剪圆形到输出尺寸的缩放比例
        val scale = outputSize / (cropCircleRadius * 2f)
        
        // 计算图片在裁剪圆形中的位置
        val offsetX = (currentTranslateX - (cropCircleCenterX - cropCircleRadius)) * scale
        val offsetY = (currentTranslateY - (cropCircleCenterY - cropCircleRadius)) * scale
        
        // 应用变换并绘制
        val matrix = Matrix()
        matrix.postScale(currentScale * scale, currentScale * scale)
        matrix.postTranslate(offsetX, offsetY)
        
        val paint = Paint().apply {
            isAntiAlias = true
            isFilterBitmap = true
        }
        
        canvas.drawBitmap(bitmap, matrix, paint)
        
        return output
    }
}
