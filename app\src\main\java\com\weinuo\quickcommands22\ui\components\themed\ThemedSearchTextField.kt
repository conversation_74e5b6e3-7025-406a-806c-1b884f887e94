package com.weinuo.quickcommands22.ui.components.themed

import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.input.ImeAction
import com.weinuo.quickcommands22.ui.theme.config.SearchTextFieldConfig
import com.weinuo.quickcommands22.ui.theme.manager.LocalThemeContext

/**
 * 主题感知的搜索框组件
 *
 * 根据当前主题自动选择合适的搜索框实现：
 * - 海洋蓝主题：使用分层设计风格的搜索框 - 带边框和阴影
 * - 天空蓝主题：使用整合设计风格的搜索框 - 无边框，大圆角
 * - 未来主题：可以使用各自独有的搜索框实现
 *
 * 支持搜索特有的功能：
 * - 搜索图标
 * - 清除按钮（当有输入时显示）
 * - 键盘搜索操作
 * - 主题感知的样式和颜色
 */
@Composable
fun ThemedSearchTextField(
    config: SearchTextFieldConfig,
    modifier: Modifier = Modifier
) {
    val themeContext = LocalThemeContext.current

    // 使用当前主题的组件工厂创建搜索框
    // 如果传入了modifier参数，则使用它；否则使用config中的modifier
    val finalModifier = if (modifier != Modifier) modifier else config.modifier
    themeContext.componentFactory.createSearchTextField()(
        config.copy(modifier = finalModifier)
    )
}

/**
 * 主题感知的搜索框组件 - 便捷版本
 *
 * 提供更简洁的API，自动处理常见的搜索框配置
 */
@Composable
fun ThemedSearchTextField(
    searchQuery: String,
    onSearchQueryChange: (String) -> Unit,
    onClearSearch: () -> Unit,
    placeholder: String,
    modifier: Modifier = Modifier,
    enabled: Boolean = true
) {
    val config = SearchTextFieldConfig(
        searchQuery = searchQuery,
        onSearchQueryChange = onSearchQueryChange,
        onClearSearch = onClearSearch,
        placeholder = placeholder,
        modifier = modifier,
        enabled = enabled,
        keyboardOptions = KeyboardOptions(imeAction = ImeAction.Search),
        keyboardActions = KeyboardActions(
            onSearch = {
                // 搜索时可以执行额外操作，比如隐藏键盘
                // 具体实现由各主题的搜索框组件处理
            }
        )
    )

    // 使用主题感知组件
    ThemedSearchTextField(config = config)
}

/**
 * 主题感知的简单搜索框 - 最简版本
 *
 * 只需要基本搜索功能的最简单版本
 */
@Composable
fun ThemedSimpleSearchTextField(
    searchQuery: String,
    onSearchQueryChange: (String) -> Unit,
    onClearSearch: () -> Unit,
    modifier: Modifier = Modifier,
    placeholder: String = "搜索..."
) {
    ThemedSearchTextField(
        searchQuery = searchQuery,
        onSearchQueryChange = onSearchQueryChange,
        onClearSearch = onClearSearch,
        placeholder = placeholder,
        modifier = modifier
    )
}
