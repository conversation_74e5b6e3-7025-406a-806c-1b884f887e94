package com.weinuo.quickcommands22.ui.configuration

import android.content.Context
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.selection.selectableGroup
import androidx.compose.foundation.selection.toggleable
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import com.weinuo.quickcommands22.R
import com.weinuo.quickcommands22.model.*
import com.weinuo.quickcommands22.navigation.Screen
import com.weinuo.quickcommands22.storage.UIStateStorageManager
import com.weinuo.quickcommands22.ui.components.ConfigurationCardItem
import com.weinuo.quickcommands22.ui.screens.LocalNavController

/**
 * 任务配置数据提供器
 *
 * 提供任务特定的配置项列表，为每个任务类型定义配置内容组件，
 * 复用现有的配置逻辑，支持编辑模式的数据预填充。
 */
object TaskConfigProvider {

    /**
     * 获取任务配置项列表
     *
     * @param context 上下文，用于获取字符串资源
     * @return 任务配置项列表
     */
    fun getConfigurationItems(context: Context): List<ConfigurationCardItem<String>> {
        return listOf(
            // 应用任务
            ConfigurationCardItem(
                id = "app_management",
                title = context.getString(R.string.task_app_management),
                description = context.getString(R.string.task_app_management_description),
                operationType = "AppManagementTask",
                permissionRequired = true,
                content = { type, onComplete ->
                    AppManagementConfigContent(type, onComplete)
                }
            ),

            // 设备设置任务 - 使用新的模块化配置系统
            ConfigurationCardItem(
                id = "device_settings",
                title = context.getString(R.string.task_device_settings),
                description = context.getString(R.string.task_device_settings_description),
                operationType = "DeviceSettingsTask",
                permissionRequired = true,
                content = { type, onComplete ->
                    DeviceSettingsConfigContent(type, onComplete)
                }
            ),

            // 通知任务
            ConfigurationCardItem(
                id = "notification",
                title = "通知任务",
                description = "发送系统通知",
                operationType = "NotificationTask",
                permissionRequired = true, // 需要通知权限
                content = { type, onComplete ->
                    NotificationConfigContent(type, onComplete)
                }
            ),

            // 媒体任务
            ConfigurationCardItem(
                id = "media",
                title = context.getString(R.string.task_media_task),
                description = context.getString(R.string.task_media_task_description),
                operationType = "MediaTask",
                permissionRequired = true,
                content = { type, onComplete ->
                    MediaConfigContent(type, onComplete)
                }
            ),

            // 相机任务 - 已迁移到新的模块化配置系统
            // 注意：相机任务现在使用CameraTaskConfigProvider和DetailConfigurationScreen
            // 在MainActivity中直接处理，不再通过TaskConfigProvider

            // 信息任务 - 已迁移到新的模块化配置系统
            // 注意：信息任务现在使用InformationTaskConfigProvider和DetailConfigurationScreen
            // 在MainActivity中直接处理，不再通过TaskConfigProvider

            // 闹钟提醒任务
            ConfigurationCardItem(
                id = "alarm_reminder",
                title = context.getString(R.string.task_alarm_reminder),
                description = context.getString(R.string.task_alarm_reminder_description),
                operationType = "AlarmReminderTask",
                permissionRequired = true,
                content = { type, onComplete ->
                    AlarmReminderConfigContent(type, onComplete)
                }
            ),

            // 文件操作任务
            ConfigurationCardItem(
                id = "file_operation",
                title = context.getString(R.string.task_file_operation),
                description = context.getString(R.string.task_file_operation_description),
                operationType = "FileOperationTask",
                permissionRequired = true,
                content = { type, onComplete ->
                    FileOperationConfigContent(type, onComplete)
                }
            ),

            // 网络任务
            ConfigurationCardItem(
                id = "network",
                title = context.getString(R.string.task_network),
                description = context.getString(R.string.task_network_description),
                operationType = "NetworkTask",
                permissionRequired = true,
                content = { type, onComplete ->
                    NetworkConfigContent(type, onComplete)
                }
            ),

            // 位置任务
            ConfigurationCardItem(
                id = "location",
                title = context.getString(R.string.task_location),
                description = context.getString(R.string.task_location_description),
                operationType = "LocationTask",
                permissionRequired = true,
                content = { type, onComplete ->
                    LocationConfigContent(type, onComplete)
                }
            ),

            // 剪贴板任务
            ConfigurationCardItem(
                id = "clipboard",
                title = "剪贴板任务",
                description = "读取、写入剪贴板内容",
                operationType = "ClipboardTask",
                permissionRequired = false,
                content = { type, onComplete ->
                    ClipboardConfigContent(type, onComplete)
                }
            ),

            // 延时任务
            ConfigurationCardItem(
                id = "delay",
                title = "延时任务",
                description = "等待指定时间",
                operationType = "DelayTask",
                permissionRequired = false,
                content = { type, onComplete ->
                    DelayConfigContent(type, onComplete)
                }
            ),

            // 脚本任务
            ConfigurationCardItem(
                id = "script",
                title = "脚本任务",
                description = "执行自定义脚本",
                operationType = "ScriptTask",
                permissionRequired = true,
                content = { type, onComplete ->
                    ScriptConfigContent(type, onComplete)
                }
            ),

            // 日志任务
            ConfigurationCardItem(
                id = "log",
                title = context.getString(R.string.task_log_task),
                description = context.getString(R.string.task_log_task_description),
                operationType = "LogTask",
                permissionRequired = false,
                content = { type, onComplete ->
                    LogConfigContent(type, onComplete)
                }
            ),

            // 电话任务
            ConfigurationCardItem(
                id = "phone",
                title = context.getString(R.string.task_phone_task),
                description = context.getString(R.string.task_phone_task_description),
                operationType = "PhoneTask",
                permissionRequired = true,
                content = { type, onComplete ->
                    PhoneConfigContent(type, onComplete)
                }
            ),

            // 屏幕控制任务
            ConfigurationCardItem(
                id = "screen_control",
                title = context.getString(R.string.task_screen_control_task),
                description = context.getString(R.string.task_screen_control_task_description),
                operationType = "ScreenControlTask",
                permissionRequired = true,
                content = { type, onComplete ->
                    ScreenControlConfigContent(type, onComplete)
                }
            )
        )
    }
}

/**
 * 应用管理任务配置内容组件
 */
@Composable
private fun AppManagementConfigContent(
    type: String,
    onComplete: (Any) -> Unit
) {
    // 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
    var selectedOperation by rememberSaveable { mutableStateOf(ApplicationOperation.LAUNCH_APP) }
    // 分别保存SimpleAppInfo的各个字段，避免自定义Saver的复杂性
    var selectedAppPackageName by rememberSaveable { mutableStateOf("") }
    var selectedAppName by rememberSaveable { mutableStateOf("") }
    var selectedAppIsSystemApp by rememberSaveable { mutableStateOf(false) }

    // 根据保存的字段重建SimpleAppInfo对象
    val selectedApp = if (selectedAppPackageName.isNotEmpty()) {
        SimpleAppInfo(
            packageName = selectedAppPackageName,
            appName = selectedAppName,
            isSystemApp = selectedAppIsSystemApp
        )
    } else {
        null
    }

    val navController = LocalNavController.current
    val context = LocalContext.current

    // 监听应用选择结果（使用原生数据类型存储）
    LaunchedEffect(Unit) {
        val savedStateHandle = navController?.currentBackStackEntry?.savedStateHandle
        val navigationKey = savedStateHandle?.get<String>("selected_apps_navigation_key")

        if (navigationKey != null) {
            val uiStateManager = UIStateStorageManager(context)
            val selectedAppsResult = uiStateManager.loadAppListState(navigationKey, "selected_apps")

            if (selectedAppsResult.isNotEmpty()) {
                val app = selectedAppsResult.first()
                selectedAppPackageName = app.packageName
                selectedAppName = app.appName
                selectedAppIsSystemApp = app.isSystemApp
            }

            // 清除结果，避免重复处理
            savedStateHandle.remove<String>("selected_apps_navigation_key")
            uiStateManager.clearAppListState(navigationKey, "selected_apps")
        }
    }

    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "配置应用任务",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        // 操作类型选择
        AppOperationSelector(
            selectedOperation = selectedOperation,
            onOperationChanged = { selectedOperation = it }
        )

        // 应用选择
        Text(
            text = "选择应用",
            style = MaterialTheme.typography.titleMedium
        )

        OutlinedButton(
            onClick = {
                if (navController != null) {
                    val route = Screen.AppSelection.createSingleSelectionRoute()
                    navController.navigate(route)
                }
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text(
                text = if (selectedApp != null) {
                    "已选择: ${selectedApp.appName}"
                } else {
                    "点击选择应用"
                }
            )
        }

        if (selectedApp != null) {
            Text(
                text = "包名: ${selectedApp.packageName}",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }

        // 备用应用选择（可选）
        if (selectedOperation == ApplicationOperation.FORCE_STOP_APP && selectedApp == null) {
            Text(
                text = "或者选择备用应用",
                style = MaterialTheme.typography.titleMedium
            )

            OutlinedButton(
                onClick = {
                    if (navController != null) {
                        val route = Screen.AppSelection.createSingleSelectionRoute()
                        navController.navigate(route)
                    }
                },
                modifier = Modifier.fillMaxWidth()
            ) {
                Text("选择备用应用")
            }
        }

        // 确认按钮
        Button(
            onClick = {
                val task = ApplicationTask(
                    operation = selectedOperation,
                    appPackageName = selectedApp?.packageName ?: "",
                    appName = selectedApp?.appName ?: ""
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth(),
            enabled = selectedApp != null
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 其他任务配置内容组件的占位符实现
 * 实际实现中应该从现有的任务配置组件中提取相应逻辑
 */
@Composable
private fun DeviceSettingsConfigContent(type: String, onComplete: (Any) -> Unit) {
    // 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
    // 显示设备设置任务选择列表，避免嵌套DetailConfigurationScreen
    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Text(
            text = "选择设备设置任务类型",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        val context = LocalContext.current
        val configurationItems = DeviceSettingsTaskConfigProvider.getConfigurationItems(context)
        configurationItems.forEach { item ->
            Surface(
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable(
                        interactionSource = remember { MutableInteractionSource() },
                        indication = null // 移除涟漪效果
                    ) {
                        // 创建一个简单的配置对象并完成
                        onComplete("DeviceSettingsTask:${item.id}")
                    },
                shape = RoundedCornerShape(8.dp),
                color = MaterialTheme.colorScheme.surfaceContainerLow,
                tonalElevation = 1.dp
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp)
                ) {
                    Text(
                        text = item.title,
                        style = MaterialTheme.typography.titleMedium
                    )
                    Text(
                        text = item.description,
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }
    }
}

@Composable
private fun NotificationConfigContent(type: String, onComplete: (Any) -> Unit) {
    // 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
    // 显示通知任务选择列表，避免嵌套DetailConfigurationScreen
    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Text(
            text = "选择通知任务类型",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        val context = LocalContext.current
        val configurationItems = NotificationTaskConfigProvider.getConfigurationItems(context)
        configurationItems.forEach { item ->
            Surface(
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable(
                        interactionSource = remember { MutableInteractionSource() },
                        indication = null // 移除涟漪效果
                    ) {
                        // 创建一个简单的配置对象并完成
                        onComplete("NotificationTask:${item.id}")
                    },
                shape = RoundedCornerShape(8.dp),
                color = MaterialTheme.colorScheme.surfaceContainerLow,
                tonalElevation = 1.dp
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp)
                ) {
                    Text(
                        text = item.title,
                        style = MaterialTheme.typography.titleMedium
                    )
                    Text(
                        text = item.description,
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }
    }
}

@Composable
private fun MediaConfigContent(type: String, onComplete: (Any) -> Unit) {
    // 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
    // 使用新的模块化媒体任务配置系统
    MediaTaskConfigContent(type, onComplete)
}

@Composable
private fun CameraConfigContent(type: String, onComplete: (Any) -> Unit) {
    // 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
    // 直接显示配置选项，避免嵌套DetailConfigurationScreen
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "选择相机操作类型",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        val context = LocalContext.current
        val configurationItems = CameraTaskConfigProvider.getConfigurationItems(context)
        configurationItems.forEach { item ->
            Surface(
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable(
                        interactionSource = remember { MutableInteractionSource() },
                        indication = null // 移除涟漪效果
                    ) {
                        // 根据具体的操作类型创建对应的配置对象
                        val cameraTask = when (item.operationType) {
                            CameraOperation.OPEN_LAST_PHOTO -> CameraTask(
                                operation = CameraOperation.OPEN_LAST_PHOTO
                            )
                            CameraOperation.TAKE_PHOTO -> CameraTask(
                                operation = CameraOperation.TAKE_PHOTO,
                                cameraType = CameraType.BACK,
                                photoSaveLocation = SaveLocation.DCIM_CAMERA
                            )
                            CameraOperation.RECORD_VIDEO -> CameraTask(
                                operation = CameraOperation.RECORD_VIDEO,
                                cameraType = CameraType.BACK,
                                videoOperation = VideoRecordingOperation.START,
                                videoSaveLocation = SaveLocation.MOVIES
                            )
                            CameraOperation.SCREENSHOT -> CameraTask(
                                operation = CameraOperation.SCREENSHOT
                            )
                        }
                        onComplete(cameraTask)
                    },
                shape = RoundedCornerShape(12.dp),
                color = MaterialTheme.colorScheme.surfaceContainerLow
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp)
                ) {
                    Text(
                        text = item.title,
                        style = MaterialTheme.typography.titleMedium
                    )
                    Text(
                        text = item.description,
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }
    }
}

// InformationConfigContent 已删除 - 信息任务已迁移到新的模块化配置系统
// 现在使用 InformationTaskConfigProvider 和 DetailConfigurationScreen

@Composable
private fun AlarmReminderConfigContent(type: String, onComplete: (Any) -> Unit) {
    // 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
    // 直接显示配置选项，避免嵌套DetailConfigurationScreen
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "选择日期时间操作类型",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        val context = LocalContext.current
        val configurationItems = DateTimeTaskConfigProvider.getConfigurationItems(context)
        configurationItems.forEach { item ->
            Surface(
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable(
                        interactionSource = remember { MutableInteractionSource() },
                        indication = null // 移除涟漪效果
                    ) {
                        // 创建一个简单的配置对象并完成
                        val dateTimeTask = DateTimeTask(
                            taskType = DateTimeTaskType.ALARM,
                            alarmHour = 8,
                            alarmMinute = 0
                        )
                        onComplete(dateTimeTask)
                    },
                shape = RoundedCornerShape(8.dp),
                color = MaterialTheme.colorScheme.surfaceContainerLow,
                tonalElevation = 1.dp
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp)
                ) {
                    Text(
                        text = item.title,
                        style = MaterialTheme.typography.titleMedium
                    )
                    Text(
                        text = item.description,
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }
    }
}

@Composable
private fun FileOperationConfigContent(type: String, onComplete: (Any) -> Unit) {
    // 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
    // 显示文件操作任务选择列表，避免嵌套DetailConfigurationScreen
    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Text(
            text = "选择文件操作任务类型",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        val context = LocalContext.current
        val configurationItems = FileOperationTaskConfigProvider.getConfigurationItems(context)
        configurationItems.forEach { item ->
            Surface(
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable(
                        interactionSource = remember { MutableInteractionSource() },
                        indication = null // 移除涟漪效果
                    ) {
                        // 创建一个简单的配置对象并完成
                        onComplete("FileOperationTask:${item.id}")
                    },
                shape = RoundedCornerShape(8.dp),
                color = MaterialTheme.colorScheme.surfaceContainerLow,
                tonalElevation = 1.dp
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp)
                ) {
                    Text(
                        text = item.title,
                        style = MaterialTheme.typography.titleMedium
                    )
                    Text(
                        text = item.description,
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }
    }
}

@Composable
private fun NetworkConfigContent(type: String, onComplete: (Any) -> Unit) {
    // 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
    // 直接显示配置选项，避免嵌套DetailConfigurationScreen
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "选择网络操作类型",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        val context = LocalContext.current
        val configurationItems = ConnectivityTaskConfigProvider.getConfigurationItems(context)
        configurationItems.forEach { item ->
            Surface(
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable(
                        interactionSource = remember { MutableInteractionSource() },
                        indication = null // 移除涟漪效果
                    ) {
                        // 创建一个简单的配置对象并完成
                        val connectivityTask = ConnectivityTask(
                            operation = ConnectivityOperation.WIFI_CONTROL,
                            wifiOperation = SwitchOperation.ENABLE
                        )
                        onComplete(connectivityTask)
                    },
                shape = RoundedCornerShape(8.dp),
                color = MaterialTheme.colorScheme.surfaceContainerLow,
                tonalElevation = 1.dp
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp)
                ) {
                    Text(
                        text = item.title,
                        style = MaterialTheme.typography.titleMedium
                    )
                    Text(
                        text = item.description,
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }
    }
}

@Composable
private fun LocationConfigContent(type: String, onComplete: (Any) -> Unit) {
    // 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
    // 直接显示配置选项，避免嵌套DetailConfigurationScreen
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "选择位置操作类型",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        val context = LocalContext.current
        val configurationItems = LocationTaskConfigProvider.getConfigurationItems(context)
        configurationItems.forEach { item ->
            Surface(
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable(
                        interactionSource = remember { MutableInteractionSource() },
                        indication = null // 移除涟漪效果
                    ) {
                        // 根据具体的操作类型创建对应的配置对象
                        val locationTask = when (item.operationType) {
                            LocationOperation.SHARE_LOCATION -> LocationTask(
                                operation = LocationOperation.SHARE_LOCATION,
                                shareMethod = LocationShareMethod.SMS,
                                phoneNumber = ""
                            )
                            LocationOperation.TOGGLE_LOCATION_SERVICE -> LocationTask(
                                operation = LocationOperation.TOGGLE_LOCATION_SERVICE,
                                locationServiceOperation = SwitchOperation.TOGGLE,
                                locationServiceControlMethod = LocationServiceControlMethod.SYSTEM_SETTINGS
                            )
                            LocationOperation.FORCE_LOCATION_UPDATE -> LocationTask(
                                operation = LocationOperation.FORCE_LOCATION_UPDATE
                            )
                            LocationOperation.SET_LOCATION_UPDATE_FREQUENCY -> LocationTask(
                                operation = LocationOperation.SET_LOCATION_UPDATE_FREQUENCY,
                                updateFrequencyValue = 30,
                                updateFrequencyUnit = LocationUpdateFrequencyUnit.SECONDS
                            )
                        }
                        onComplete(locationTask)
                    },
                shape = RoundedCornerShape(8.dp),
                color = MaterialTheme.colorScheme.surfaceContainerLow,
                tonalElevation = 1.dp
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp)
                ) {
                    Text(
                        text = item.title,
                        style = MaterialTheme.typography.titleMedium
                    )
                    Text(
                        text = item.description,
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }
    }
}

@Composable
private fun ClipboardConfigContent(type: String, onComplete: (Any) -> Unit) {
    // 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
    var clipboardText by rememberSaveable { mutableStateOf("") }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "剪贴板设置",
            style = MaterialTheme.typography.titleMedium
        )

        OutlinedTextField(
            value = clipboardText,
            onValueChange = { clipboardText = it },
            label = { Text("剪贴板内容") },
            placeholder = { Text("输入要复制到剪贴板的文本") },
            modifier = Modifier.fillMaxWidth(),
            minLines = 3,
            maxLines = 6
        )

        // 确认按钮
        Button(
            onClick = {
                val task = DeviceActionTask(
                    operation = DeviceActionOperation.SET_CLIPBOARD,
                    clipboardText = clipboardText
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth(),
            enabled = clipboardText.isNotBlank()
        ) {
            Text("确认配置")
        }
    }
}

@Composable
private fun DelayConfigContent(type: String, onComplete: (Any) -> Unit) {
    // 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
    var waitMinutes by rememberSaveable { mutableStateOf("0") }
    var waitSeconds by rememberSaveable { mutableStateOf("5") }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "延时等待设置",
            style = MaterialTheme.typography.titleMedium
        )

        // 等待时间输入
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            OutlinedTextField(
                value = waitMinutes,
                onValueChange = { waitMinutes = it },
                label = { Text("分钟") },
                modifier = Modifier.weight(1f),
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number)
            )
            OutlinedTextField(
                value = waitSeconds,
                onValueChange = { waitSeconds = it },
                label = { Text("秒") },
                modifier = Modifier.weight(1f),
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number)
            )
        }

        Text(
            text = "总等待时间：${(waitMinutes.toIntOrNull() ?: 0) * 60 + (waitSeconds.toIntOrNull() ?: 0)} 秒",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        // 确认按钮
        Button(
            onClick = {
                val task = DeviceActionTask(
                    operation = DeviceActionOperation.WAIT_DELAY,
                    waitMinutes = waitMinutes.toIntOrNull() ?: 0,
                    waitSeconds = waitSeconds.toIntOrNull() ?: 0
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth(),
            enabled = (waitMinutes.toIntOrNull() ?: 0) > 0 || (waitSeconds.toIntOrNull() ?: 0) > 0
        ) {
            Text("确认配置")
        }
    }
}

@Composable
private fun ScriptConfigContent(type: String, onComplete: (Any) -> Unit) {
    // 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
    var shellScript by rememberSaveable { mutableStateOf("") }
    var shellExecutionMode by rememberSaveable { mutableStateOf(ShellExecutionMode.NORMAL) }
    var shellTimeoutMinutes by rememberSaveable { mutableStateOf("0") }
    var shellTimeoutSeconds by rememberSaveable { mutableStateOf("30") }
    var shellWaitForCompletion by rememberSaveable { mutableStateOf(false) }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "Shell脚本设置",
            style = MaterialTheme.typography.titleMedium
        )

        OutlinedTextField(
            value = shellScript,
            onValueChange = { shellScript = it },
            label = { Text("Shell脚本") },
            placeholder = { Text("输入要执行的Shell命令或脚本") },
            modifier = Modifier.fillMaxWidth(),
            minLines = 4,
            maxLines = 8
        )

        // 执行模式选择
        Text(
            text = "执行模式",
            style = MaterialTheme.typography.bodyMedium
        )

        ShellExecutionMode.values().forEach { mode ->
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .selectable(
                        selected = (shellExecutionMode == mode),
                        onClick = { shellExecutionMode = mode }
                    ),
                verticalAlignment = Alignment.CenterVertically
            ) {
                RadioButton(
                    selected = (shellExecutionMode == mode),
                    onClick = { shellExecutionMode = mode }
                )
                Text(
                    text = when (mode) {
                        ShellExecutionMode.NORMAL -> "普通模式"
                        ShellExecutionMode.SHIZUKU -> "Shizuku模式"
                    },
                    modifier = Modifier.padding(start = 8.dp)
                )
            }
        }

        // 超时设置
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            OutlinedTextField(
                value = shellTimeoutMinutes,
                onValueChange = { shellTimeoutMinutes = it },
                label = { Text("超时分钟") },
                modifier = Modifier.weight(1f),
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number)
            )
            OutlinedTextField(
                value = shellTimeoutSeconds,
                onValueChange = { shellTimeoutSeconds = it },
                label = { Text("超时秒数") },
                modifier = Modifier.weight(1f),
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number)
            )
        }

        // 等待完成选项
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .toggleable(
                    value = shellWaitForCompletion,
                    onValueChange = { shellWaitForCompletion = it },
                    role = Role.Checkbox
                )
                .padding(vertical = 4.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Checkbox(
                checked = shellWaitForCompletion,
                onCheckedChange = { shellWaitForCompletion = it }
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = "等待脚本执行完成",
                style = MaterialTheme.typography.bodyMedium
            )
        }

        // 确认按钮
        Button(
            onClick = {
                val task = ApplicationTask(
                    operation = ApplicationOperation.SHELL_SCRIPT,
                    shellScript = shellScript,
                    shellExecutionMode = shellExecutionMode,
                    shellTimeoutMinutes = shellTimeoutMinutes.toIntOrNull() ?: 0,
                    shellTimeoutSeconds = shellTimeoutSeconds.toIntOrNull() ?: 30,
                    shellWaitForCompletion = shellWaitForCompletion
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth(),
            enabled = shellScript.isNotBlank()
        ) {
            Text("确认配置")
        }
    }
}

// 简单的日志级别枚举
enum class LogLevel(val displayName: String) {
    DEBUG("调试 (DEBUG)"),
    INFO("信息 (INFO)"),
    WARN("警告 (WARN)"),
    ERROR("错误 (ERROR)")
}

@Composable
private fun LogConfigContent(type: String, onComplete: (Any) -> Unit) {
    // 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
    var logMessage by rememberSaveable { mutableStateOf("") }
    var logLevel by rememberSaveable { mutableStateOf(LogLevel.INFO) }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "日志记录设置",
            style = MaterialTheme.typography.titleMedium
        )

        OutlinedTextField(
            value = logMessage,
            onValueChange = { logMessage = it },
            label = { Text("日志消息") },
            placeholder = { Text("输入要记录的日志内容") },
            modifier = Modifier.fillMaxWidth(),
            minLines = 2,
            maxLines = 4
        )

        // 日志级别选择
        Text(
            text = "日志级别",
            style = MaterialTheme.typography.bodyMedium
        )

        LogLevel.values().forEach { level ->
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .selectable(
                        selected = (logLevel == level),
                        onClick = { logLevel = level }
                    ),
                verticalAlignment = Alignment.CenterVertically
            ) {
                RadioButton(
                    selected = (logLevel == level),
                    onClick = { logLevel = level }
                )
                Text(
                    text = level.displayName,
                    modifier = Modifier.padding(start = 8.dp)
                )
            }
        }

        // 确认按钮
        Button(
            onClick = {
                // 日志功能现在通过DeviceActionTask的自定义操作实现
                // 使用Shizuku命令来记录日志
                val task = DeviceActionTask(
                    operation = DeviceActionOperation.SHIZUKU_COMMAND,
                    shizukuCommands = "echo \"[${logLevel.name}] $logMessage\" >> /sdcard/app_log.txt"
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth(),
            enabled = logMessage.isNotBlank()
        ) {
            Text("确认配置")
        }
    }
}

@Composable
private fun PhoneConfigContent(type: String, onComplete: (Any) -> Unit) {
    // 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
    // 显示电话任务选择列表，避免嵌套DetailConfigurationScreen
    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Text(
            text = "选择电话任务类型",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        val context = LocalContext.current
        val configurationItems = PhoneTaskConfigProvider.getConfigurationItems(context)
        configurationItems.forEach { item ->
            Surface(
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable(
                        interactionSource = remember { MutableInteractionSource() },
                        indication = null // 移除涟漪效果
                    ) {
                        // 创建一个简单的配置对象并完成
                        onComplete("PhoneTask:${item.id}")
                    },
                shape = RoundedCornerShape(12.dp),
                color = MaterialTheme.colorScheme.surfaceContainerLow
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp)
                ) {
                    Text(
                        text = item.title,
                        style = MaterialTheme.typography.titleMedium
                    )
                    Text(
                        text = item.description,
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }
    }
}

@Composable
private fun ScreenControlConfigContent(type: String, onComplete: (Any) -> Unit) {
    // 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
    // 显示屏幕控制任务选择列表，避免嵌套DetailConfigurationScreen
    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Text(
            text = "选择屏幕控制任务类型",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        val context = LocalContext.current
        val configurationItems = ScreenControlTaskConfigProvider.getConfigurationItems(context)
        configurationItems.forEach { item ->
            Surface(
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable(
                        interactionSource = remember { MutableInteractionSource() },
                        indication = null // 移除涟漪效果
                    ) {
                        // 创建一个简单的配置对象并完成
                        onComplete("ScreenControlTask:${item.id}")
                    },
                shape = RoundedCornerShape(8.dp),
                color = MaterialTheme.colorScheme.surfaceContainerLow,
                tonalElevation = 1.dp
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp)
                ) {
                    Text(
                        text = item.title,
                        style = MaterialTheme.typography.titleMedium
                    )
                    Text(
                        text = item.description,
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }
    }
}

/**
 * 辅助组件占位符
 * 实际实现中应该从现有的配置组件中提取
 */
@Composable
private fun AppOperationSelector(
    selectedOperation: ApplicationOperation,
    onOperationChanged: (ApplicationOperation) -> Unit
) {
    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Text(
            text = "操作类型",
            style = MaterialTheme.typography.titleMedium
        )

        Column(modifier = Modifier.selectableGroup()) {
            ApplicationOperation.values().forEach { operation ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clickable(
                            interactionSource = remember { MutableInteractionSource() },
                            indication = null
                        ) { onOperationChanged(operation) }
                        .padding(vertical = 4.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = selectedOperation == operation,
                        onClick = { onOperationChanged(operation) }
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = operation.displayName,
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            }
        }
    }
}

@Composable
private fun AppSelector(
    selectedApp: SimpleAppInfo?,
    onAppChanged: (SimpleAppInfo?) -> Unit
) {
    val navController = LocalNavController.current

    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Text(
            text = "选择应用",
            style = MaterialTheme.typography.titleMedium
        )

        // 应用选择按钮
        OutlinedButton(
            onClick = {
                if (navController != null) {
                    val route = Screen.AppSelection.createSingleSelectionRoute()
                    navController.navigate(route)
                }
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text(
                text = if (selectedApp != null) {
                    "已选择: ${selectedApp.appName}"
                } else {
                    "点击选择应用"
                }
            )
        }

        if (selectedApp != null) {
            Text(
                text = "包名: ${selectedApp.packageName}",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}


