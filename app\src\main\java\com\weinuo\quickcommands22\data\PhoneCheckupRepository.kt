package com.weinuo.quickcommands22.data

import android.app.ActivityManager
import android.content.Context
import android.util.Log
import com.weinuo.quickcommands22.execution.SharedExecutionHandler
import com.weinuo.quickcommands22.model.SimpleAppInfo
import com.weinuo.quickcommands22.utils.UsageStatsPermissionUtil
import com.weinuo.quickcommands22.utils.KillBackgroundProcessesPermissionUtil
import com.weinuo.quickcommands22.shizuku.ShizukuManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.withContext

/**
 * 手机体检数据仓库
 * 
 * 负责手机体检相关的数据获取和业务逻辑处理，包括：
 * - 获取正在运行的应用数量
 * - 计算健康分数
 * - 执行一键优化功能
 * - 权限检查和状态管理
 */
class PhoneCheckupRepository(
    private val context: Context,
    private val appRepository: AppRepository,
    private val sharedExecutionHandler: SharedExecutionHandler
) {
    companion object {
        private const val TAG = "PhoneCheckupRepository"
        
        // 分数计算常量
        private const val MAX_SCORE = 100
        private const val SCORE_DEDUCTION_PER_APP = 2
        private const val MIN_SCORE = 0
    }

    /**
     * 获取正在运行的应用数量
     * 
     * @return 正在运行的应用数量（包括用户应用和系统应用）
     */
    suspend fun getRunningAppsCount(): Int = withContext(Dispatchers.IO) {
        try {
            // 确保应用数据已加载
            appRepository.loadApps()
            
            // 获取用户应用和系统应用
            val userApps = appRepository.userApps.first()
            val systemApps = appRepository.systemApps.first()
            
            // 计算正在运行的应用数量
            val runningUserApps = userApps.count { it.isRunning }
            val runningSystemApps = systemApps.count { it.isRunning }
            val totalRunning = runningUserApps + runningSystemApps
            
            Log.d(TAG, "Running apps count - User: $runningUserApps, System: $runningSystemApps, Total: $totalRunning")
            totalRunning
        } catch (e: Exception) {
            Log.e(TAG, "Error getting running apps count", e)
            0
        }
    }

    /**
     * 获取正在运行的应用列表
     * 
     * @return 正在运行的应用信息列表
     */
    suspend fun getRunningAppsList(): List<SimpleAppInfo> = withContext(Dispatchers.IO) {
        try {
            // 确保应用数据已加载
            appRepository.loadApps()
            
            // 获取用户应用和系统应用
            val userApps = appRepository.userApps.first()
            val systemApps = appRepository.systemApps.first()
            
            // 筛选正在运行的应用并转换为SimpleAppInfo
            val runningApps = (userApps + systemApps)
                .filter { it.isRunning }
                .map { appInfo ->
                    SimpleAppInfo(
                        packageName = appInfo.packageName,
                        appName = appInfo.appName,
                        isSystemApp = appInfo.isSystemApp,
                        isRunning = true,
                        icon = null // 图标在需要时重新加载
                    )
                }
            
            Log.d(TAG, "Found ${runningApps.size} running apps")
            runningApps
        } catch (e: Exception) {
            Log.e(TAG, "Error getting running apps list", e)
            emptyList()
        }
    }

    /**
     * 计算健康分数
     *
     * 分数计算规则：
     * - 没有使用情况统计权限时：0分
     * - 有权限时：基础分数100分，每个后台运行的应用扣2分，最低分数0分
     *
     * @param runningAppsCount 正在运行的应用数量
     * @return 健康分数（0-100）
     */
    fun calculateHealthScore(runningAppsCount: Int): Int {
        // 没有使用情况统计权限时强制返回0分
        if (!hasUsageStatsPermission()) {
            Log.d(TAG, "No usage stats permission, health score set to 0")
            return 0
        }

        val score = MAX_SCORE - (runningAppsCount * SCORE_DEDUCTION_PER_APP)
        val finalScore = maxOf(MIN_SCORE, score)

        Log.d(TAG, "Health score calculated: $finalScore (running apps: $runningAppsCount)")
        return finalScore
    }

    /**
     * 执行一键优化
     *
     * 优化流程：
     * 1. 检查基础权限（使用情况统计权限）
     * 2. 根据Shizuku权限选择优化方式：
     *    - 有Shizuku权限：精准强制停止应用
     *    - 无Shizuku权限：使用ActivityManager.killBackgroundProcesses清理后台进程
     * 3. 更新应用运行状态
     *
     * @return 优化结果
     */
    suspend fun optimizeSystem(): OptimizeResult = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Starting system optimization")

            // 检查基础权限
            if (!hasUsageStatsPermission()) {
                return@withContext OptimizeResult(
                    success = false,
                    stoppedAppsCount = 0,
                    totalAppsCount = 0,
                    message = "需要使用情况访问权限才能执行优化"
                )
            }

            // 根据Shizuku权限选择优化方式
            return@withContext if (hasShizukuPermission()) {
                performShizukuOptimization()
            } else {
                performSystemOptimization()
            }

        } catch (e: Exception) {
            Log.e(TAG, "Error during system optimization", e)
            OptimizeResult(
                success = false,
                stoppedAppsCount = 0,
                totalAppsCount = 0,
                message = "优化过程中发生错误: ${e.message}"
            )
        }
    }

    /**
     * 使用Shizuku权限执行精准优化
     *
     * @return 优化结果
     */
    private suspend fun performShizukuOptimization(): OptimizeResult {
        val runningApps = getRunningAppsList()

        if (runningApps.isEmpty()) {
            return OptimizeResult(
                success = true,
                stoppedAppsCount = 0,
                totalAppsCount = 0,
                message = "系统已经很干净了"
            )
        }

        Log.d(TAG, "Found ${runningApps.size} running apps, starting Shizuku force stop")

        // 获取当前应用包名，避免停止自己
        val currentAppPackage = context.packageName
        Log.d(TAG, "Current app package: $currentAppPackage")

        var successCount = 0
        for (app in runningApps) {
            try {
                // 跳过当前应用（避免停止自己导致崩溃）
                if (app.packageName == currentAppPackage) {
                    Log.d(TAG, "Skipping current app: ${app.packageName}")
                    continue
                }

                val success = forceStopSingleAppSafely(app.packageName)
                if (success) {
                    successCount++
                    Log.d(TAG, "Successfully stopped app: ${app.packageName}")
                } else {
                    Log.w(TAG, "Failed to stop app: ${app.packageName}")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error stopping app: ${app.packageName}", e)
            }
        }

        appRepository.updateRunningStatus()

        return OptimizeResult(
            success = successCount > 0,
            stoppedAppsCount = successCount,
            totalAppsCount = runningApps.size,
            message = "成功优化了 $successCount 个应用"
        )
    }

    /**
     * 安全地强制停止单个应用
     * 直接使用Shizuku执行命令（普通应用没有FORCE_STOP_PACKAGES权限）
     *
     * @param packageName 要停止的应用包名
     * @return 是否成功停止应用
     */
    private suspend fun forceStopSingleAppSafely(packageName: String): Boolean {
        return try {
            // 直接使用Shizuku执行强制停止命令
            val result = ShizukuManager.executeCommand("am force-stop $packageName")
            val success = !result.contains("Error:") && !result.contains("错误") &&
                         !result.contains("permission") && !result.contains("权限") &&
                         !result.contains("Failed")
            Log.d(TAG, "Shizuku command result for $packageName: $result, success: $success")
            success
        } catch (e: Exception) {
            Log.e(TAG, "Error force stopping app: $packageName", e)
            false
        }
    }



    /**
     * 使用ActivityManager.killBackgroundProcesses清理后台进程
     *
     * @return 优化结果
     */
    private suspend fun performSystemOptimization(): OptimizeResult {
        try {
            Log.d(TAG, "Starting system optimization using killBackgroundProcesses")

            // 检查是否有KILL_BACKGROUND_PROCESSES权限
            if (!KillBackgroundProcessesPermissionUtil.hasKillBackgroundProcessesPermission(context)) {
                return OptimizeResult(
                    success = false,
                    stoppedAppsCount = 0,
                    totalAppsCount = 0,
                    message = "缺少后台进程管理权限，无法执行优化"
                )
            }

            val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
            val runningApps = getRunningAppsList()

            if (runningApps.isEmpty()) {
                return OptimizeResult(
                    success = true,
                    stoppedAppsCount = 0,
                    totalAppsCount = 0,
                    message = "系统已经很干净了"
                )
            }

            Log.d(TAG, "Found ${runningApps.size} running apps, starting killBackgroundProcesses optimization")

            // 获取当前应用包名，避免停止自己
            val currentAppPackage = context.packageName
            Log.d(TAG, "Current app package: $currentAppPackage")

            var successCount = 0
            val totalAppsToStop = runningApps.filter { it.packageName != currentAppPackage }

            for (app in totalAppsToStop) {
                try {
                    // 使用ActivityManager.killBackgroundProcesses清理后台进程
                    activityManager.killBackgroundProcesses(app.packageName)
                    successCount++
                    Log.d(TAG, "Successfully killed background processes for: ${app.packageName}")

                    // 短暂延迟，避免过于频繁的调用
                    delay(50)
                } catch (e: Exception) {
                    Log.w(TAG, "Failed to kill background processes for: ${app.packageName}", e)
                }
            }

            // 等待系统稳定
            delay(1000)

            // 更新应用运行状态
            appRepository.updateRunningStatus()

            val message = if (successCount > 0) {
                "已清理 $successCount 个应用的后台进程"
            } else {
                "未能清理任何应用的后台进程"
            }

            return OptimizeResult(
                success = successCount > 0,
                stoppedAppsCount = successCount,
                totalAppsCount = totalAppsToStop.size,
                message = message
            )

        } catch (e: Exception) {
            Log.e(TAG, "Error in system optimization", e)
            return OptimizeResult(
                success = false,
                stoppedAppsCount = 0,
                totalAppsCount = 0,
                message = "系统清理失败: ${e.message}"
            )
        }
    }

    /**
     * 检查是否有使用情况访问权限
     * 
     * @return 是否有权限
     */
    fun hasUsageStatsPermission(): Boolean {
        return UsageStatsPermissionUtil.hasUsageStatsPermission(context)
    }

    /**
     * 检查是否有Shizuku权限
     *
     * @return 是否有权限
     */
    fun hasShizukuPermission(): Boolean {
        return ShizukuManager.checkShizukuPermission()
    }

    /**
     * 检查是否有后台进程管理权限
     *
     * @return 是否有权限
     */
    fun hasKillBackgroundProcessesPermission(): Boolean {
        return KillBackgroundProcessesPermissionUtil.hasKillBackgroundProcessesPermission(context)
    }

    /**
     * 检查是否具备执行优化的基础权限
     *
     * @return 是否具备基础必要权限（使用情况统计权限 + 后台进程管理权限）
     */
    fun hasAllRequiredPermissions(): Boolean {
        val hasUsageStats = hasUsageStatsPermission()
        val hasKillBgProcesses = KillBackgroundProcessesPermissionUtil.hasKillBackgroundProcessesPermission(context)

        Log.d(TAG, "Permission check - UsageStats: $hasUsageStats, KillBgProcesses: $hasKillBgProcesses")
        return hasUsageStats && hasKillBgProcesses
    }

    /**
     * 检查是否有可选的Shizuku权限
     *
     * @return 是否有Shizuku权限（可选，用于更精准的优化）
     */
    fun hasOptionalShizukuPermission(): Boolean {
        return hasShizukuPermission()
    }

    /**
     * 刷新应用运行状态
     * 
     * 用于在界面显示前确保数据是最新的
     */
    suspend fun refreshAppRunningStatus() = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Refreshing app running status")
            appRepository.updateRunningStatus()
        } catch (e: Exception) {
            Log.e(TAG, "Error refreshing app running status", e)
        }
    }
}

/**
 * 优化结果数据类
 * 
 * @param success 是否成功
 * @param stoppedAppsCount 成功停止的应用数量
 * @param totalAppsCount 总共尝试停止的应用数量
 * @param message 结果消息
 */
data class OptimizeResult(
    val success: Boolean,
    val stoppedAppsCount: Int,
    val totalAppsCount: Int,
    val message: String
)
