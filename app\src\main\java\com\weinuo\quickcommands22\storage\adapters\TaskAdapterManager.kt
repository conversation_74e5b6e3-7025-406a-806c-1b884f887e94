package com.weinuo.quickcommands22.storage.adapters

import android.util.Log
import com.weinuo.quickcommands22.model.SharedTask
import com.weinuo.quickcommands22.model.PhoneTask
import com.weinuo.quickcommands22.model.MediaTask
import com.weinuo.quickcommands22.model.ApplicationTask
import com.weinuo.quickcommands22.model.VolumeTask
import com.weinuo.quickcommands22.model.ConnectivityTask
import com.weinuo.quickcommands22.model.DateTimeTask
import com.weinuo.quickcommands22.model.DeviceActionTask
import com.weinuo.quickcommands22.model.InformationTask
import com.weinuo.quickcommands22.model.CameraTask
import com.weinuo.quickcommands22.model.FileOperationTask
import com.weinuo.quickcommands22.model.ScreenControlTask
import com.weinuo.quickcommands22.model.NotificationTask
import com.weinuo.quickcommands22.model.DeviceSettingsTask
import com.weinuo.quickcommands22.model.LocationTask
import com.weinuo.quickcommands22.storage.NativeTypeStorageManager

/**
 * 任务适配器管理器
 *
 * 统一管理所有任务类型的存储适配器，提供类型安全的任务存储和加载功能。
 * 支持动态注册新的任务适配器，便于扩展新的任务类型。
 *
 * 设计原则：
 * - 类型安全：通过泛型确保任务类型匹配
 * - 可扩展：支持动态注册新的适配器
 * - 统一接口：提供一致的存储和加载API
 * - 错误处理：完善的异常处理和日志记录
 *
 * @param storageManager 原生类型存储管理器
 */
class TaskAdapterManager(
    private val storageManager: NativeTypeStorageManager
) {
    companion object {
        private const val TAG = "TaskAdapterManager"
    }

    // 任务适配器注册表
    private val adapters = mutableMapOf<String, TaskStorageAdapter<*>>()

    init {
        // 注册默认的任务适配器
        registerDefaultAdapters()
    }

    /**
     * 注册默认的任务适配器
     */
    private fun registerDefaultAdapters() {
        try {
            // 注册电话任务适配器
            registerAdapter(PhoneTaskAdapter(storageManager))

            // 注册媒体任务适配器
            registerAdapter(MediaTaskAdapter(storageManager))

            // 注册应用程序任务适配器
            registerAdapter(ApplicationTaskAdapter(storageManager))

            // 注册音量任务适配器
            registerAdapter(VolumeTaskAdapter(storageManager))

            // 注册连接任务适配器
            registerAdapter(ConnectivityTaskAdapter(storageManager))

            // 注册日期时间任务适配器
            registerAdapter(DateTimeTaskAdapter(storageManager))

            // 注册设备动作任务适配器
            registerAdapter(DeviceActionTaskAdapter(storageManager))

            // 注册信息任务适配器
            registerAdapter(InformationTaskAdapter(storageManager))

            // 注册相机任务适配器
            registerAdapter(CameraTaskAdapter(storageManager))

            // 注册文件操作任务适配器
            registerAdapter(FileOperationTaskAdapter(storageManager))

            // 注册屏幕控制任务适配器
            registerAdapter(ScreenControlTaskAdapter(storageManager))

            // 注册通知任务适配器
            registerAdapter(NotificationTaskAdapter(storageManager))

            // 注册设备设置任务适配器
            registerAdapter(DeviceSettingsTaskAdapter(storageManager))

            // 注册位置任务适配器
            registerAdapter(LocationTaskAdapter(storageManager))

            Log.d(TAG, "默认任务适配器注册完成，共注册 ${adapters.size} 个适配器")
        } catch (e: Exception) {
            Log.e(TAG, "注册默认任务适配器时发生错误", e)
        }
    }

    /**
     * 注册任务适配器
     *
     * @param adapter 要注册的适配器
     */
    fun <T : SharedTask> registerAdapter(adapter: TaskStorageAdapter<T>) {
        val taskType = adapter.getTaskType()
        adapters[taskType] = adapter
        Log.d(TAG, "已注册任务适配器: $taskType")
    }

    /**
     * 获取任务适配器
     *
     * @param taskType 任务类型
     * @return 对应的适配器，如果不存在则返回null
     */
    @Suppress("UNCHECKED_CAST")
    private fun <T : SharedTask> getAdapter(taskType: String): TaskStorageAdapter<T>? {
        return adapters[taskType] as? TaskStorageAdapter<T>
    }

    /**
     * 保存任务
     *
     * @param task 要保存的任务
     * @return 操作是否成功
     */
    fun <T : SharedTask> saveTask(task: T): Boolean {
        return try {
            val taskType = task.type
            val adapter = getAdapter<T>(taskType)

            if (adapter != null) {
                val success = adapter.save(task)
                if (success) {
                    Log.d(TAG, "任务保存成功: ${task.id} (类型: $taskType)")
                } else {
                    Log.e(TAG, "任务保存失败: ${task.id} (类型: $taskType)")
                }
                success
            } else {
                Log.e(TAG, "未找到任务类型 '$taskType' 的适配器")
                false
            }
        } catch (e: Exception) {
            Log.e(TAG, "保存任务时发生异常: ${task.id}", e)
            false
        }
    }

    /**
     * 加载任务
     *
     * @param taskId 任务ID
     * @param taskType 任务类型
     * @return 加载的任务，失败时返回null
     */
    @Suppress("UNCHECKED_CAST")
    fun <T : SharedTask> loadTask(taskId: String, taskType: String): T? {
        return try {
            val adapter = getAdapter<T>(taskType)

            if (adapter != null) {
                val task = adapter.load(taskId)
                if (task != null) {
                    Log.d(TAG, "任务加载成功: $taskId (类型: $taskType)")
                } else {
                    Log.d(TAG, "任务不存在或加载失败: $taskId (类型: $taskType)")
                }
                task as? T
            } else {
                Log.e(TAG, "未找到任务类型 '$taskType' 的适配器")
                null
            }
        } catch (e: Exception) {
            Log.e(TAG, "加载任务时发生异常: $taskId (类型: $taskType)", e)
            null
        }
    }

    /**
     * 删除任务
     *
     * @param taskId 任务ID
     * @param taskType 任务类型
     * @return 操作是否成功
     */
    fun deleteTask(taskId: String, taskType: String): Boolean {
        return try {
            val adapter = getAdapter<SharedTask>(taskType)

            if (adapter != null) {
                val success = adapter.delete(taskId)
                if (success) {
                    Log.d(TAG, "任务删除成功: $taskId (类型: $taskType)")
                } else {
                    Log.e(TAG, "任务删除失败: $taskId (类型: $taskType)")
                }
                success
            } else {
                Log.e(TAG, "未找到任务类型 '$taskType' 的适配器")
                false
            }
        } catch (e: Exception) {
            Log.e(TAG, "删除任务时发生异常: $taskId (类型: $taskType)", e)
            false
        }
    }

    /**
     * 检查任务是否存在
     *
     * @param taskId 任务ID
     * @param taskType 任务类型
     * @return 任务是否存在
     */
    fun taskExists(taskId: String, taskType: String): Boolean {
        return try {
            val adapter = getAdapter<SharedTask>(taskType)

            if (adapter != null) {
                adapter.exists(taskId)
            } else {
                Log.e(TAG, "未找到任务类型 '$taskType' 的适配器")
                false
            }
        } catch (e: Exception) {
            Log.e(TAG, "检查任务存在性时发生异常: $taskId (类型: $taskType)", e)
            false
        }
    }

    /**
     * 获取所有已注册的任务类型
     *
     * @return 任务类型列表
     */
    fun getRegisteredTaskTypes(): List<String> {
        return adapters.keys.toList()
    }

    /**
     * 获取适配器统计信息
     *
     * @return 统计信息字符串
     */
    fun getAdapterStats(): String {
        return buildString {
            appendLine("任务适配器统计信息:")
            appendLine("已注册适配器数量: ${adapters.size}")
            adapters.forEach { (type, adapter) ->
                appendLine("- $type: ${adapter.javaClass.simpleName}")
            }
        }
    }

    /**
     * 类型安全的任务保存方法（针对具体任务类型）
     */
    fun savePhoneTask(task: PhoneTask): Boolean = saveTask(task)
    fun saveMediaTask(task: MediaTask): Boolean = saveTask(task)
    fun saveApplicationTask(task: ApplicationTask): Boolean = saveTask(task)
    fun saveVolumeTask(task: VolumeTask): Boolean = saveTask(task)
    fun saveConnectivityTask(task: ConnectivityTask): Boolean = saveTask(task)
    fun saveDateTimeTask(task: DateTimeTask): Boolean = saveTask(task)
    fun saveDeviceActionTask(task: DeviceActionTask): Boolean = saveTask(task)
    fun saveInformationTask(task: InformationTask): Boolean = saveTask(task)
    fun saveCameraTask(task: CameraTask): Boolean = saveTask(task)
    fun saveFileOperationTask(task: FileOperationTask): Boolean = saveTask(task)
    fun saveScreenControlTask(task: ScreenControlTask): Boolean = saveTask(task)
    fun saveNotificationTask(task: NotificationTask): Boolean = saveTask(task)
    fun saveDeviceSettingsTask(task: DeviceSettingsTask): Boolean = saveTask(task)
    fun saveLocationTask(task: LocationTask): Boolean = saveTask(task)

    /**
     * 类型安全的任务加载方法（针对具体任务类型）
     */
    fun loadPhoneTask(taskId: String): PhoneTask? = loadTask(taskId, "phone")
    fun loadMediaTask(taskId: String): MediaTask? = loadTask(taskId, "media")
    fun loadApplicationTask(taskId: String): ApplicationTask? = loadTask(taskId, "application")
    fun loadVolumeTask(taskId: String): VolumeTask? = loadTask(taskId, "volume")
    fun loadConnectivityTask(taskId: String): ConnectivityTask? = loadTask(taskId, "connectivity")
    fun loadDateTimeTask(taskId: String): DateTimeTask? = loadTask(taskId, "date_time")
    fun loadDeviceActionTask(taskId: String): DeviceActionTask? = loadTask(taskId, "device_action")
    fun loadInformationTask(taskId: String): InformationTask? = loadTask(taskId, "information")
    fun loadCameraTask(taskId: String): CameraTask? = loadTask(taskId, "camera")
    fun loadFileOperationTask(taskId: String): FileOperationTask? = loadTask(taskId, "file_operation")
    fun loadScreenControlTask(taskId: String): ScreenControlTask? = loadTask(taskId, "screen_control")
    fun loadNotificationTask(taskId: String): NotificationTask? = loadTask(taskId, "notification")
    fun loadDeviceSettingsTask(taskId: String): DeviceSettingsTask? = loadTask(taskId, "device_settings")
    fun loadLocationTask(taskId: String): LocationTask? = loadTask(taskId, "location")

    /**
     * 通用任务加载方法
     * 根据存储的类型信息自动选择正确的适配器
     *
     * @param taskId 任务ID
     * @return 加载的任务，失败时返回null
     */
    fun loadTaskByStoredType(taskId: String): SharedTask? {
        return try {
            // 首先读取存储的任务类型
            val typeKey = "task_${taskId}_type"
            val storedType = storageManager.loadString(com.weinuo.quickcommands22.storage.StorageDomain.TASKS, typeKey)

            if (storedType.isEmpty()) {
                Log.w(TAG, "任务类型信息不存在: $taskId")
                return null
            }

            // 根据存储的类型加载任务
            when (storedType) {
                "phone" -> loadPhoneTask(taskId)
                "media" -> loadMediaTask(taskId)
                "application" -> loadApplicationTask(taskId)
                "volume" -> loadVolumeTask(taskId)
                "connectivity" -> loadConnectivityTask(taskId)
                "date_time" -> loadDateTimeTask(taskId)
                "device_action" -> loadDeviceActionTask(taskId)
                "information" -> loadInformationTask(taskId)
                "camera" -> loadCameraTask(taskId)
                "file_operation" -> loadFileOperationTask(taskId)
                "screen_control" -> loadScreenControlTask(taskId)
                "notification" -> loadNotificationTask(taskId)
                "device_settings" -> loadDeviceSettingsTask(taskId)
                "location" -> loadLocationTask(taskId)
                else -> {
                    Log.w(TAG, "未知的存储任务类型: $storedType for task: $taskId")
                    null
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "根据存储类型加载任务时发生异常: $taskId", e)
            null
        }
    }
}
