package com.weinuo.quickcommands22.ui.configuration

import android.app.Activity
import android.content.Context
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.selection.selectableGroup
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.LocationOn
import androidx.compose.material.icons.filled.Person
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.saveable.rememberSaveable

import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp

import com.weinuo.quickcommands22.R
import com.weinuo.quickcommands22.model.*
import com.weinuo.quickcommands22.ui.activities.ContactSelectionActivity
import com.weinuo.quickcommands22.ui.components.ConfigurationCardItem
import kotlinx.coroutines.launch

/**
 * 位置任务配置数据提供器
 *
 * 提供位置任务的配置项列表，支持位置分享、定位服务控制、
 * 强制位置更新和位置更新频率设置等功能。
 * 使用模块化配置系统架构，支持高度复用和扩展。
 */
object LocationTaskConfigProvider {

    /**
     * 获取位置任务配置项列表
     *
     * @param context 上下文，用于获取字符串资源
     * @return 位置任务配置项列表
     */
    fun getConfigurationItems(context: Context): List<ConfigurationCardItem<LocationOperation>> {
        return listOf(
            // 位置分享配置项
            ConfigurationCardItem(
                id = "share_location",
                title = context.getString(R.string.location_share_location),
                description = context.getString(R.string.location_share_location_description),
                operationType = LocationOperation.SHARE_LOCATION,
                permissionRequired = true, // 需要位置权限
                content = { operation, onComplete ->
                    ShareLocationConfigContent(operation, onComplete)
                }
            ),

            // 定位服务控制配置项
            ConfigurationCardItem(
                id = "toggle_location_service",
                title = context.getString(R.string.location_toggle_location_service),
                description = context.getString(R.string.location_toggle_location_service_description),
                operationType = LocationOperation.TOGGLE_LOCATION_SERVICE,
                permissionRequired = true, // 需要系统设置权限
                content = { operation, onComplete ->
                    ToggleLocationServiceConfigContent(operation, onComplete)
                }
            ),

            // 强制位置更新配置项
            ConfigurationCardItem(
                id = "force_location_update",
                title = context.getString(R.string.location_force_location_update),
                description = context.getString(R.string.location_force_location_update_description),
                operationType = LocationOperation.FORCE_LOCATION_UPDATE,
                permissionRequired = true, // 需要位置权限
                content = { operation, onComplete ->
                    ForceLocationUpdateConfigContent(operation, onComplete)
                }
            ),

            // 设置位置更新频率配置项
            ConfigurationCardItem(
                id = "set_location_update_frequency",
                title = context.getString(R.string.location_set_location_update_frequency),
                description = context.getString(R.string.location_set_location_update_frequency_description),
                operationType = LocationOperation.SET_LOCATION_UPDATE_FREQUENCY,
                permissionRequired = true, // 需要位置权限
                content = { operation, onComplete ->
                    SetLocationUpdateFrequencyConfigContent(operation, onComplete)
                }
            )
        )
    }
}

/**
 * 位置分享配置内容组件
 *
 * 提供位置分享方式选择、联系人选择、手机号码输入和分享消息设置界面
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 * 自动获取当前位置信息，无需用户手动输入位置
 */
@Composable
private fun ShareLocationConfigContent(
    operation: LocationOperation,
    onComplete: (Any) -> Unit
) {
    val context = LocalContext.current
    val scope = rememberCoroutineScope()

    // 使用 rememberSaveable 来保持状态，即使在导航过程中也不会丢失
    var shareMethod by rememberSaveable { mutableStateOf(LocationShareMethod.SMS) }
    var contactId by rememberSaveable { mutableStateOf("") }
    var contactName by rememberSaveable { mutableStateOf("") }
    var phoneNumber by rememberSaveable { mutableStateOf("") }
    var shareMessage by rememberSaveable { mutableStateOf("我的位置：") }

    // 联系人选择ActivityResultLauncher
    val contactSelectionLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            val contactData = result.data?.getSerializableExtra(
                ContactSelectionActivity.RESULT_SELECTED_CONTACTS
            ) as? ArrayList<Map<String, String>> ?: arrayListOf()

            if (contactData.isNotEmpty()) {
                val selectedContact = contactData.first() // 单选模式只取第一个
                // 关键：自动切换到联系人分享模式
                shareMethod = LocationShareMethod.CONTACT
                // 更新联系人信息
                contactId = selectedContact["id"] ?: ""
                contactName = selectedContact["name"] ?: ""
            }
        }
    }

    // 位置获取状态
    var currentLocation by rememberSaveable { mutableStateOf<android.location.Location?>(null) }
    var isLoadingLocation by rememberSaveable { mutableStateOf(false) }
    var locationError by rememberSaveable { mutableStateOf<String?>(null) }



    // 自动获取当前位置
    LaunchedEffect(Unit) {
        isLoadingLocation = true
        locationError = null

        try {
            // 使用SunriseSunsetCalculator中的getCurrentLocation方法获取位置
            val location = com.weinuo.quickcommands22.utils.SunriseSunsetCalculator.getCurrentLocation(context)
            currentLocation = location
            if (location == null) {
                locationError = "无法获取当前位置，请检查位置服务是否开启"
            }
        } catch (e: Exception) {
            locationError = "获取位置时发生错误：${e.message}"
        } finally {
            isLoadingLocation = false
        }
    }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "配置位置分享",
            style = MaterialTheme.typography.titleMedium
        )

        // 当前位置信息显示
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceContainerLow
            )
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.LocationOn,
                        contentDescription = "位置",
                        tint = MaterialTheme.colorScheme.primary
                    )
                    Text(
                        text = "当前位置",
                        style = MaterialTheme.typography.titleSmall,
                        color = MaterialTheme.colorScheme.onSurface
                    )

                    if (isLoadingLocation) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(16.dp),
                            strokeWidth = 2.dp
                        )
                    }
                }

                when {
                    isLoadingLocation -> {
                        Text(
                            text = "正在获取位置信息...",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                    locationError != null -> {
                        Text(
                            text = locationError!!,
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.error
                        )

                        Button(
                            onClick = {
                                // 重新获取位置
                                scope.launch {
                                    isLoadingLocation = true
                                    locationError = null

                                    try {
                                        val location = com.weinuo.quickcommands22.utils.SunriseSunsetCalculator.getCurrentLocation(context)
                                        currentLocation = location
                                        if (location == null) {
                                            locationError = "无法获取当前位置，请检查位置服务是否开启"
                                        }
                                    } catch (e: Exception) {
                                        locationError = "获取位置时发生错误：${e.message}"
                                    } finally {
                                        isLoadingLocation = false
                                    }
                                }
                            },
                            modifier = Modifier.padding(top = 8.dp)
                        ) {
                            Text("重新获取位置")
                        }
                    }
                    currentLocation != null -> {
                        val location = currentLocation!!
                        Text(
                            text = "纬度: ${String.format("%.6f", location.latitude)}",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                        Text(
                            text = "经度: ${String.format("%.6f", location.longitude)}",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                        Text(
                            text = "精度: ${String.format("%.1f", location.accuracy)}米",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
            }
        }

        // 分享方式选择
        Text(
            text = "分享方式",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        Column(modifier = Modifier.selectableGroup()) {
            LocationShareMethod.values().forEach { method ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .selectable(
                            selected = shareMethod == method,
                            onClick = { shareMethod = method },
                            role = Role.RadioButton
                        )
                        .padding(vertical = 4.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = shareMethod == method,
                        onClick = { shareMethod = method }
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = method.displayName,
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            }
        }

        // 根据分享方式显示不同的输入选项
        when (shareMethod) {
            LocationShareMethod.CONTACT -> {
                Text(
                    text = "选择联系人",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )

                OutlinedButton(
                    onClick = {
                        // 使用launcher启动联系人选择Activity
                        val intent = android.content.Intent(context, ContactSelectionActivity::class.java).apply {
                            putExtra("selection_mode", com.weinuo.quickcommands22.ui.screens.ContactSelectionMode.SINGLE.name)
                        }
                        contactSelectionLauncher.launch(intent)
                    },
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Text(
                            text = if (contactName.isNotEmpty()) contactName else "点击选择联系人",
                            modifier = Modifier.weight(1f),
                            color = if (contactName.isNotEmpty())
                                MaterialTheme.colorScheme.onSurface
                            else
                                MaterialTheme.colorScheme.onSurfaceVariant
                        )
                        Icon(
                            imageVector = Icons.Default.Person,
                            contentDescription = "选择联系人"
                        )
                    }
                }
            }
            LocationShareMethod.PHONE_NUMBER -> {
                Text(
                    text = "手机号码",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )

                OutlinedTextField(
                    value = phoneNumber,
                    onValueChange = { phoneNumber = it },
                    modifier = Modifier.fillMaxWidth(),
                    placeholder = { Text("请输入手机号码") },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Phone)
                )
            }
            LocationShareMethod.SMS -> {
                Text(
                    text = "将使用系统默认短信应用发送位置信息",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }

        // 分享消息内容
        Text(
            text = "分享消息",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        OutlinedTextField(
            value = shareMessage,
            onValueChange = { shareMessage = it },
            modifier = Modifier.fillMaxWidth(),
            placeholder = { Text("我的位置：") },
            maxLines = 3
        )

        // 确认按钮
        Button(
            onClick = {
                // 构建包含位置信息的分享消息
                val locationText = if (currentLocation != null) {
                    val location = currentLocation!!
                    "纬度: ${String.format("%.6f", location.latitude)}, 经度: ${String.format("%.6f", location.longitude)}"
                } else {
                    "位置信息获取失败"
                }

                val finalShareMessage = if (shareMessage.isNotEmpty()) {
                    "$shareMessage $locationText"
                } else {
                    locationText
                }

                val task = LocationTask(
                    operation = operation,
                    shareMethod = shareMethod,
                    contactId = contactId,
                    contactName = contactName,
                    phoneNumber = phoneNumber,
                    shareMessage = finalShareMessage
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth(),
            enabled = currentLocation != null && !isLoadingLocation // 只有成功获取位置且不在加载中时才能确认
        ) {
            Text(
                if (isLoadingLocation) "正在获取位置..."
                else if (currentLocation == null) "请先获取位置信息"
                else "确认配置"
            )
        }
    }


}

/**
 * 定位服务控制配置内容组件
 *
 * 提供定位服务操作类型选择和控制方式选择界面
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun ToggleLocationServiceConfigContent(
    operation: LocationOperation,
    onComplete: (Any) -> Unit
) {

    var locationServiceOperation by rememberSaveable { mutableStateOf(SwitchOperation.TOGGLE) }
    var locationServiceControlMethod by rememberSaveable { mutableStateOf(LocationServiceControlMethod.SYSTEM_SETTINGS) }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "配置定位服务控制",
            style = MaterialTheme.typography.titleMedium
        )

        // 定位服务操作选择
        Text(
            text = "定位服务操作",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        Column(modifier = Modifier.selectableGroup()) {
            SwitchOperation.values().forEach { op ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .selectable(
                            selected = locationServiceOperation == op,
                            onClick = { locationServiceOperation = op },
                            role = Role.RadioButton
                        )
                        .padding(vertical = 4.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = locationServiceOperation == op,
                        onClick = { locationServiceOperation = op }
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = op.displayName,
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            }
        }

        // 控制方式选择
        Text(
            text = "控制方式",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        Column(modifier = Modifier.selectableGroup()) {
            LocationServiceControlMethod.values().forEach { method ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .selectable(
                            selected = locationServiceControlMethod == method,
                            onClick = { locationServiceControlMethod = method },
                            role = Role.RadioButton
                        )
                        .padding(vertical = 4.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = locationServiceControlMethod == method,
                        onClick = { locationServiceControlMethod = method }
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = method.displayName,
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            }
        }

        // 确认按钮
        Button(
            onClick = {
                val task = LocationTask(
                    operation = operation,
                    locationServiceOperation = locationServiceOperation,
                    locationServiceControlMethod = locationServiceControlMethod
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 强制位置更新配置内容组件
 *
 * 提供强制位置更新的简单确认界面
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun ForceLocationUpdateConfigContent(
    operation: LocationOperation,
    onComplete: (Any) -> Unit
) {

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "强制位置更新",
            style = MaterialTheme.typography.titleMedium
        )

        Text(
            text = "此操作将立即强制更新当前位置信息，确保获取最新的位置数据。",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        // 确认按钮
        Button(
            onClick = {
                val task = LocationTask(
                    operation = operation
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 位置更新频率设置配置内容组件
 *
 * 提供位置更新频率数值和单位设置界面
 */
@Composable
private fun SetLocationUpdateFrequencyConfigContent(
    operation: LocationOperation,
    onComplete: (Any) -> Unit
) {
    var updateFrequencyValue by rememberSaveable { mutableStateOf("30") }
    var updateFrequencyUnit by rememberSaveable { mutableStateOf(LocationUpdateFrequencyUnit.SECONDS) }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "设置位置更新频率",
            style = MaterialTheme.typography.titleMedium
        )

        Text(
            text = "更新频率",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.fillMaxWidth()
        ) {
            OutlinedTextField(
                value = updateFrequencyValue,
                onValueChange = { updateFrequencyValue = it },
                modifier = Modifier.weight(1f),
                placeholder = { Text("30") },
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number)
            )

            Spacer(modifier = Modifier.width(8.dp))

        }

        // 时间单位选择
        Text(
            text = "时间单位",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        Column(modifier = Modifier.selectableGroup()) {
            LocationUpdateFrequencyUnit.values().forEach { unit ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .selectable(
                            selected = updateFrequencyUnit == unit,
                            onClick = { updateFrequencyUnit = unit },
                            role = Role.RadioButton
                        )
                        .padding(vertical = 4.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = updateFrequencyUnit == unit,
                        onClick = { updateFrequencyUnit = unit }
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = unit.displayName,
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            }
        }

        // 确认按钮
        Button(
            onClick = {
                val task = LocationTask(
                    operation = operation,
                    updateFrequencyValue = updateFrequencyValue.toIntOrNull() ?: 30,
                    updateFrequencyUnit = updateFrequencyUnit
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}


