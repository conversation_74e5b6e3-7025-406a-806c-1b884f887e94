package com.weinuo.quickcommands22.ui.configuration

import android.content.Context
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.selection.toggleable
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.ui.unit.dp
import com.weinuo.quickcommands22.R
import com.weinuo.quickcommands22.model.*
import com.weinuo.quickcommands22.navigation.Screen
import com.weinuo.quickcommands22.ui.components.ConfigurationCardItem
import com.weinuo.quickcommands22.ui.components.RingtoneConfigurationContent
import com.weinuo.quickcommands22.ui.components.RingtoneSelector
import com.weinuo.quickcommands22.ui.screens.LocalNavController
import com.weinuo.quickcommands22.utils.RingtoneHelper

/**
 * 秒表选择模式枚举
 */
enum class StopwatchSelectionMode {
    CREATE_NEW,  // 创建新秒表
    EXISTING     // 选择现有秒表
}

/**
 * 日期时间任务配置数据提供器
 *
 * 提供日期时间任务特定的配置项列表，为每个操作类型定义配置内容组件，
 * 复用现有的配置逻辑，支持编辑模式的数据预填充。
 */
object DateTimeTaskConfigProvider {

    /**
     * 获取日期时间任务配置项列表
     *
     * @param context 上下文，用于获取字符串资源
     * @return 日期时间任务配置项列表
     */
    fun getConfigurationItems(context: Context): List<ConfigurationCardItem<DateTimeTaskType>> {
        return listOf(
            // 秒表操作
            ConfigurationCardItem(
                id = "stopwatch",
                title = context.getString(R.string.datetime_stopwatch),
                description = context.getString(R.string.datetime_stopwatch_description),
                operationType = DateTimeTaskType.STOPWATCH,
                permissionRequired = false,
                content = { operation, onComplete ->
                    StopwatchConfigContent(operation, onComplete)
                }
            ),

            // 闹钟操作
            ConfigurationCardItem(
                id = "alarm",
                title = context.getString(R.string.datetime_alarm),
                description = context.getString(R.string.datetime_alarm_description),
                operationType = DateTimeTaskType.ALARM,
                permissionRequired = true,
                content = { operation, onComplete ->
                    AlarmConfigContent(operation, onComplete)
                }
            ),

            // 系统闹钟铃声设置
            ConfigurationCardItem(
                id = "system_alarm_ringtone",
                title = "系统闹钟铃声",
                description = "直接修改系统闹钟铃声设置",
                operationType = DateTimeTaskType.SYSTEM_ALARM_RINGTONE,
                permissionRequired = false,
                content = { operation, onComplete ->
                    SystemAlarmRingtoneConfigContent(operation, onComplete)
                }
            ),

            // 语音报时
            ConfigurationCardItem(
                id = "voice_time_announcement",
                title = context.getString(R.string.datetime_voice_time_announcement),
                description = context.getString(R.string.datetime_voice_time_announcement_description),
                operationType = DateTimeTaskType.VOICE_TIME_ANNOUNCEMENT,
                permissionRequired = false,
                content = { operation, onComplete ->
                    VoiceTimeAnnouncementConfigContent(operation, onComplete)
                }
            )
        )
    }
}

/**
 * 秒表配置内容组件
 *
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun StopwatchConfigContent(
    operation: DateTimeTaskType,
    onComplete: (Any) -> Unit
) {
    val navController = LocalNavController.current

    // 使用rememberSaveable保持导航过程中的状态
    var stopwatchOperation by rememberSaveable { mutableStateOf(StopwatchOperation.START) }
    var stopwatchSelectionMode by rememberSaveable { mutableStateOf(StopwatchSelectionMode.CREATE_NEW) }
    var selectedStopwatchId by rememberSaveable { mutableStateOf("") }
    var selectedStopwatchName by rememberSaveable { mutableStateOf("") }
    var newStopwatchName by rememberSaveable { mutableStateOf("") }

    // 监听秒表选择结果并自动切换
    LaunchedEffect(Unit) {
        val savedStateHandle = navController?.currentBackStackEntry?.savedStateHandle
        val selectedStopwatch = savedStateHandle?.get<Stopwatch>("selected_stopwatch")
        if (selectedStopwatch != null) {
            // 🔑 关键：自动切换到现有秒表模式
            stopwatchSelectionMode = StopwatchSelectionMode.EXISTING
            // 更新选中的秒表信息
            selectedStopwatchId = selectedStopwatch.id
            selectedStopwatchName = selectedStopwatch.name
            // 清除结果，避免重复处理
            savedStateHandle.remove<Stopwatch>("selected_stopwatch")
        }
    }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "秒表操作设置",
            style = MaterialTheme.typography.titleMedium
        )

        // 秒表操作选择
        Text(
            text = "操作类型",
            style = MaterialTheme.typography.bodyLarge
        )

        StopwatchOperation.values().forEach { stopwatchOp ->
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .selectable(
                        selected = (stopwatchOperation == stopwatchOp),
                        onClick = { stopwatchOperation = stopwatchOp }
                    ),
                verticalAlignment = Alignment.CenterVertically
            ) {
                RadioButton(
                    selected = (stopwatchOperation == stopwatchOp),
                    onClick = { stopwatchOperation = stopwatchOp }
                )
                Text(
                    text = when (stopwatchOp) {
                        StopwatchOperation.START -> "启动秒表"
                        StopwatchOperation.PAUSE -> "暂停秒表"
                        StopwatchOperation.STOP -> "停止秒表"
                        StopwatchOperation.LAP -> "记录圈数"
                        StopwatchOperation.RESET -> "重置秒表"
                        StopwatchOperation.RESET_AND_START -> "重置并重启秒表"
                    },
                    modifier = Modifier.padding(start = 8.dp)
                )
            }
        }

        // 秒表选择模式
        Text(
            text = "秒表选择",
            style = MaterialTheme.typography.bodyLarge
        )

        StopwatchSelectionMode.values().forEach { mode ->
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .selectable(
                        selected = (stopwatchSelectionMode == mode),
                        onClick = { stopwatchSelectionMode = mode }
                    ),
                verticalAlignment = Alignment.CenterVertically
            ) {
                RadioButton(
                    selected = (stopwatchSelectionMode == mode),
                    onClick = { stopwatchSelectionMode = mode }
                )
                Text(
                    text = when (mode) {
                        StopwatchSelectionMode.CREATE_NEW -> "创建新秒表"
                        StopwatchSelectionMode.EXISTING -> "选择现有秒表"
                    },
                    modifier = Modifier.padding(start = 8.dp)
                )
            }
        }

        // 根据选择模式显示不同的配置选项
        when (stopwatchSelectionMode) {
            StopwatchSelectionMode.CREATE_NEW -> {
                // 新秒表名称输入
                OutlinedTextField(
                    value = newStopwatchName,
                    onValueChange = { newStopwatchName = it },
                    label = { Text("新秒表名称") },
                    placeholder = { Text("输入秒表名称") },
                    modifier = Modifier.fillMaxWidth()
                )
            }
            StopwatchSelectionMode.EXISTING -> {
                // 现有秒表选择按钮
                OutlinedButton(
                    onClick = {
                        if (navController != null) {
                            val route = Screen.StopwatchSelection.createSingleSelectionRoute()
                            navController.navigate(route)
                        }
                    },
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text(
                        text = if (selectedStopwatchName.isNotEmpty()) {
                            "已选择: $selectedStopwatchName"
                        } else {
                            "点击选择秒表"
                        }
                    )
                }
            }
        }

        // 确认按钮
        Button(
            onClick = {
                val task = DateTimeTask(
                    taskType = operation,
                    stopwatchOperation = stopwatchOperation,
                    stopwatchId = when (stopwatchSelectionMode) {
                        StopwatchSelectionMode.EXISTING -> selectedStopwatchId
                        StopwatchSelectionMode.CREATE_NEW -> ""
                    },
                    stopwatchName = when (stopwatchSelectionMode) {
                        StopwatchSelectionMode.EXISTING -> selectedStopwatchName
                        StopwatchSelectionMode.CREATE_NEW -> newStopwatchName
                    }
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth(),
            enabled = when (stopwatchSelectionMode) {
                StopwatchSelectionMode.CREATE_NEW -> newStopwatchName.isNotBlank()
                StopwatchSelectionMode.EXISTING -> selectedStopwatchId.isNotEmpty()
            }
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 闹钟配置内容组件
 *
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun AlarmConfigContent(
    operation: DateTimeTaskType,
    onComplete: (Any) -> Unit
) {
    // 使用rememberSaveable保持导航过程中的状态
    var alarmOperation by rememberSaveable { mutableStateOf(AlarmOperation.SET_ALARM) }
    var alarmTimeType by rememberSaveable { mutableStateOf(AlarmTimeType.ABSOLUTE) }
    var alarmHour by rememberSaveable { mutableStateOf("8") }
    var alarmMinute by rememberSaveable { mutableStateOf("0") }
    var alarmRelativeHours by rememberSaveable { mutableStateOf("0") }
    var alarmRelativeMinutes by rememberSaveable { mutableStateOf("5") }
    var alarmMessage by rememberSaveable { mutableStateOf("") }
    var alarmDays by rememberSaveable { mutableStateOf(setOf<Int>()) }
    var alarmVibrate by rememberSaveable { mutableStateOf(true) }
    var alarmSkipUI by rememberSaveable { mutableStateOf(false) }
    var requireTask by rememberSaveable { mutableStateOf(false) }
    var clicksCount by rememberSaveable { mutableStateOf("1") }
    var enableSound by rememberSaveable { mutableStateOf(true) }
    var selectedRingtoneUri by rememberSaveable { mutableStateOf("") }
    var selectedRingtoneName by rememberSaveable { mutableStateOf("") }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "闹钟操作设置",
            style = MaterialTheme.typography.titleMedium
        )

        // 闹钟操作选择
        Text(
            text = "操作类型",
            style = MaterialTheme.typography.bodyLarge
        )

        AlarmOperation.values().forEach { alarmOp ->
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .selectable(
                        selected = (alarmOperation == alarmOp),
                        onClick = { alarmOperation = alarmOp }
                    ),
                verticalAlignment = Alignment.CenterVertically
            ) {
                RadioButton(
                    selected = (alarmOperation == alarmOp),
                    onClick = { alarmOperation = alarmOp }
                )
                Text(
                    text = when (alarmOp) {
                        AlarmOperation.SET_ALARM -> "设置闹钟"
                        AlarmOperation.CANCEL_ALARM -> "取消闹钟"
                        AlarmOperation.SNOOZE_ALARM -> "延迟闹钟"
                        AlarmOperation.DISMISS_ALARM -> "关闭闹钟"
                    },
                    modifier = Modifier.padding(start = 8.dp)
                )
            }
        }

        if (alarmOperation == AlarmOperation.SET_ALARM) {
            // 时间类型选择
            Text(
                text = "时间类型",
                style = MaterialTheme.typography.bodyLarge
            )

            AlarmTimeType.values().forEach { timeType ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .selectable(
                            selected = (alarmTimeType == timeType),
                            onClick = { alarmTimeType = timeType }
                        ),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = (alarmTimeType == timeType),
                        onClick = { alarmTimeType = timeType }
                    )
                    Text(
                        text = timeType.displayName,
                        modifier = Modifier.padding(start = 8.dp)
                    )
                }
            }

            // 时间设置
            when (alarmTimeType) {
                AlarmTimeType.ABSOLUTE -> {
                    Text(
                        text = "设置固定时间",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        OutlinedTextField(
                            value = alarmHour,
                            onValueChange = { alarmHour = it },
                            label = { Text("小时 (0-23)") },
                            modifier = Modifier.weight(1f),
                            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number)
                        )
                        OutlinedTextField(
                            value = alarmMinute,
                            onValueChange = { alarmMinute = it },
                            label = { Text("分钟 (0-59)") },
                            modifier = Modifier.weight(1f),
                            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number)
                        )
                    }
                }
                AlarmTimeType.RELATIVE -> {
                    Text(
                        text = "设置相对时间（从现在开始计算）",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        OutlinedTextField(
                            value = alarmRelativeHours,
                            onValueChange = { alarmRelativeHours = it },
                            label = { Text("小时 (0-23)") },
                            modifier = Modifier.weight(1f),
                            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number)
                        )
                        OutlinedTextField(
                            value = alarmRelativeMinutes,
                            onValueChange = { alarmRelativeMinutes = it },
                            label = { Text("分钟 (1-59)") },
                            modifier = Modifier.weight(1f),
                            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number)
                        )
                    }
                }
            }

            // 闹钟消息
            OutlinedTextField(
                value = alarmMessage,
                onValueChange = { alarmMessage = it },
                label = { Text("闹钟消息 (可选)") },
                placeholder = { Text("闹钟提醒文本") },
                modifier = Modifier.fillMaxWidth()
            )

            // 重复天数选择（仅在绝对时间模式下显示）
            if (alarmTimeType == AlarmTimeType.ABSOLUTE) {
                Text(
                    text = "重复天数",
                    style = MaterialTheme.typography.bodyLarge
                )

                val dayNames = listOf("周日", "周一", "周二", "周三", "周四", "周五", "周六")
                LazyRow(
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    items(7) { dayIndex ->
                        FilterChip(
                            onClick = {
                                alarmDays = if (alarmDays.contains(dayIndex)) {
                                    alarmDays - dayIndex
                                } else {
                                    alarmDays + dayIndex
                                }
                            },
                            label = { Text(dayNames[dayIndex]) },
                            selected = alarmDays.contains(dayIndex)
                        )
                    }
                }
            } else {
                // 相对时间模式下的说明
                Text(
                    text = "相对时间闹钟为一次性闹钟，不支持重复",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }

            // 振动选项
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .toggleable(
                        value = alarmVibrate,
                        onValueChange = { alarmVibrate = it },
                        role = Role.Checkbox
                    )
                    .padding(vertical = 4.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Checkbox(
                    checked = alarmVibrate,
                    onCheckedChange = { alarmVibrate = it }
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "启用振动",
                    style = MaterialTheme.typography.bodyMedium
                )
            }

            // 铃声选项
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .toggleable(
                        value = enableSound,
                        onValueChange = { enableSound = it },
                        role = Role.Checkbox
                    )
                    .padding(vertical = 4.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Checkbox(
                    checked = enableSound,
                    onCheckedChange = { enableSound = it }
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "启用铃声",
                    style = MaterialTheme.typography.bodyMedium
                )
            }

            // 铃声选择器（仅在启用铃声时显示）
            if (enableSound) {
                RingtoneSelector(
                    ringtoneType = RingtoneHelper.RingtoneType.ALARM,
                    selectedRingtoneUri = selectedRingtoneUri,
                    selectedRingtoneName = selectedRingtoneName,
                    onRingtoneChanged = { uri, name ->
                        selectedRingtoneUri = uri
                        selectedRingtoneName = name
                    },
                    label = "选择闹钟铃声"
                )
            }

            // 跳过UI选项
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .toggleable(
                        value = alarmSkipUI,
                        onValueChange = { alarmSkipUI = it },
                        role = Role.Checkbox
                    )
                    .padding(vertical = 4.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Checkbox(
                    checked = alarmSkipUI,
                    onCheckedChange = { alarmSkipUI = it }
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "跳过闹钟界面",
                    style = MaterialTheme.typography.bodyMedium
                )
            }
        }

        // 任务要求选项
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .toggleable(
                    value = requireTask,
                    onValueChange = { requireTask = it },
                    role = Role.Checkbox
                )
                .padding(vertical = 4.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Checkbox(
                checked = requireTask,
                onCheckedChange = { requireTask = it }
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = "需要完成任务",
                style = MaterialTheme.typography.bodyMedium
            )
        }

        if (requireTask) {
            OutlinedTextField(
                value = clicksCount,
                onValueChange = { clicksCount = it },
                label = { Text("点击次数") },
                modifier = Modifier.fillMaxWidth(),
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number)
            )
        }

        // 确认按钮
        Button(
            onClick = {
                val task = DateTimeTask(
                    taskType = operation,
                    alarmOperation = alarmOperation,
                    alarmTimeType = alarmTimeType,
                    alarmHour = alarmHour.toIntOrNull() ?: 8,
                    alarmMinute = alarmMinute.toIntOrNull() ?: 0,
                    alarmRelativeHours = alarmRelativeHours.toIntOrNull() ?: 0,
                    alarmRelativeMinutes = alarmRelativeMinutes.toIntOrNull() ?: 5,
                    alarmMessage = alarmMessage,
                    // 相对时间模式下不设置重复天数
                    alarmDays = if (alarmTimeType == AlarmTimeType.ABSOLUTE) alarmDays else emptySet(),
                    alarmVibrate = alarmVibrate,
                    alarmSkipUI = alarmSkipUI,
                    requireTask = requireTask,
                    clicksCount = clicksCount.toIntOrNull() ?: 1,
                    enableSound = enableSound,
                    selectedRingtoneUri = selectedRingtoneUri,
                    selectedRingtoneName = selectedRingtoneName
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 系统闹钟铃声配置内容组件
 *
 * 专门用于直接修改系统闹钟铃声设置的配置组件
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun SystemAlarmRingtoneConfigContent(
    operation: DateTimeTaskType,
    onComplete: (Any) -> Unit
) {
    // 使用通用铃声配置组件，固定为闹钟铃声类型
    RingtoneConfigurationContent(
        title = "配置系统闹钟铃声",
        description = "直接修改系统默认闹钟铃声设置",
        ringtoneType = RingtoneHelper.RingtoneType.ALARM,
        showTypeSelection = false, // 不显示类型选择，固定为闹钟铃声
        onComplete = { selectedRingtoneUri, selectedRingtoneName ->
            val task = DateTimeTask(
                taskType = operation,
                selectedRingtoneUri = selectedRingtoneUri,
                selectedRingtoneName = selectedRingtoneName
            )
            onComplete(task)
        }
    )
}

/**
 * 语音报时配置内容组件
 *
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun VoiceTimeAnnouncementConfigContent(
    operation: DateTimeTaskType,
    onComplete: (Any) -> Unit
) {
    var timeFormat by rememberSaveable { mutableStateOf("24") }
    var language by rememberSaveable { mutableStateOf("zh-CN") }
    var pitch by rememberSaveable { mutableStateOf("1.0") }
    var speed by rememberSaveable { mutableStateOf("1.0") }
    var audioStream by rememberSaveable { mutableStateOf("MUSIC") }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "语音报时设置",
            style = MaterialTheme.typography.titleMedium
        )

        // 时间格式选择
        Text(
            text = "时间格式",
            style = MaterialTheme.typography.bodyMedium
        )
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                RadioButton(
                    selected = timeFormat == "24",
                    onClick = { timeFormat = "24" }
                )
                Text("24小时制")
            }
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                RadioButton(
                    selected = timeFormat == "12",
                    onClick = { timeFormat = "12" }
                )
                Text("12小时制")
            }
        }

        // 语言选择
        Text(
            text = "语音语言",
            style = MaterialTheme.typography.bodyMedium
        )
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                RadioButton(
                    selected = language == "zh-CN",
                    onClick = { language = "zh-CN" }
                )
                Text("中文")
            }
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                RadioButton(
                    selected = language == "en-US",
                    onClick = { language = "en-US" }
                )
                Text("英文")
            }
        }

        // 音调设置
        OutlinedTextField(
            value = pitch,
            onValueChange = {
                val floatValue = it.toFloatOrNull()
                if (floatValue != null && floatValue in 0.5f..2.0f) {
                    pitch = it
                }
            },
            label = { Text("音调 (0.5-2.0)") },
            placeholder = { Text("1.0") },
            modifier = Modifier.fillMaxWidth(),
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal)
        )

        // 语速设置
        OutlinedTextField(
            value = speed,
            onValueChange = {
                val floatValue = it.toFloatOrNull()
                if (floatValue != null && floatValue in 0.5f..2.0f) {
                    speed = it
                }
            },
            label = { Text("语速 (0.5-2.0)") },
            placeholder = { Text("1.0") },
            modifier = Modifier.fillMaxWidth(),
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal)
        )

        // 音频流类型
        Text(
            text = "音频流类型",
            style = MaterialTheme.typography.bodyMedium
        )
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            listOf("MUSIC" to "音乐", "NOTIFICATION" to "通知", "ALARM" to "闹钟").forEach { (value, label) ->
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = audioStream == value,
                        onClick = { audioStream = value }
                    )
                    Text(label)
                }
            }
        }

        // 确认按钮
        Button(
            onClick = {
                val task = DateTimeTask(
                    taskType = operation,
                    voiceTimeFormat = timeFormat,
                    voiceLanguage = language,
                    voicePitch = pitch.toFloatOrNull() ?: 1.0f,
                    voiceSpeed = speed.toFloatOrNull() ?: 1.0f,
                    voiceAudioStream = audioStream
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认")
        }
    }
}