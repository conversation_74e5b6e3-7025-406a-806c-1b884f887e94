package com.weinuo.quickcommands22.ui.components.layered

import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.width
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.weinuo.quickcommands22.ui.theme.config.ButtonConfig
import com.weinuo.quickcommands22.ui.theme.config.IconPosition

/**
 * 分层设计风格的按钮
 *
 * 特点：
 * - 使用标准Material 3 Button设计
 * - 简洁清晰的视觉分离
 * - 遵循Material Design 3规范
 * - 保持与old项目一致的简洁风格
 */
@Composable
fun LayeredButton(
    config: ButtonConfig,
    modifier: Modifier = Modifier
) {
    if (config.icon != null) {
        // 带图标的按钮
        when (config.iconPosition) {
            IconPosition.START -> {
                Button(
                    onClick = config.onClick,
                    enabled = config.enabled,
                    modifier = modifier
                ) {
                    Icon(
                        imageVector = config.icon,
                        contentDescription = null
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(config.text)
                }
            }
            IconPosition.END -> {
                Button(
                    onClick = config.onClick,
                    enabled = config.enabled,
                    modifier = modifier
                ) {
                    Text(config.text)
                    Spacer(modifier = Modifier.width(8.dp))
                    Icon(
                        imageVector = config.icon,
                        contentDescription = null
                    )
                }
            }
            IconPosition.TOP -> {
                // 如果需要支持顶部图标，可以在这里实现
                // 目前使用默认的START位置
                Button(
                    onClick = config.onClick,
                    enabled = config.enabled,
                    modifier = modifier
                ) {
                    Icon(
                        imageVector = config.icon,
                        contentDescription = null
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(config.text)
                }
            }
        }
    } else {
        // 纯文本按钮
        Button(
            onClick = config.onClick,
            enabled = config.enabled,
            modifier = modifier
        ) {
            Text(config.text)
        }
    }
}


