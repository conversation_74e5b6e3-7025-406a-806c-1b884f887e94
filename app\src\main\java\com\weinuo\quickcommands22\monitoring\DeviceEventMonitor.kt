package com.weinuo.quickcommands22.monitoring

import android.content.Context
import android.content.Intent
import android.util.Log
import com.weinuo.quickcommands22.model.*
import kotlinx.coroutines.*

/**
 * 设备事件监听器
 * 统一管理17种设备事件类型的监听和触发
 *
 * 支持的设备事件类型：
 * - GPS状态变化
 * - Logcat消息监听
 * - 剪贴板变化
 * - 屏幕状态变化
 * - 底座连接状态
 * - Intent接收
 * - SIM卡状态
 * - 深色主题变化
 * - 登录失败检测
 * - 系统设置变化
 * - 自动同步状态
 * - 设备启动完成
 * - 通知事件
 * - 静音模式变化
 * - 音乐播放状态
 * - 飞行模式状态
 * - 音量变化
 * - 内存状态（已有独立监听器）
 */
class DeviceEventMonitor(
    private val context: Context,
    private val onConditionTriggered: (DeviceEventCondition, Map<String, Any>) -> Unit
) {

    companion object {
        private const val TAG = "DeviceEventMonitor"
        private const val PREFS_NAME = "device_event_monitor_prefs"
        private const val PREFIX_CONDITION_STATE = "device_event_state_"
    }

    // 协程作用域
    private val monitorScope = CoroutineScope(Dispatchers.Default + SupervisorJob())

    // 监听器状态
    private var isMonitoring = false

    // 注册的条件列表
    private val registeredConditions = mutableListOf<DeviceEventCondition>()

    // SharedPreferences用于持久化状态
    private val sharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)

    // 上次检查的设备事件状态缓存
    private val lastDeviceEventStates = mutableMapOf<String, Any>()

    init {
        // 启动时加载持久化状态
        loadPersistedStates()
    }

    /**
     * 加载持久化的状态
     */
    private fun loadPersistedStates() {
        try {
            val allPrefs = sharedPreferences.all
            for ((key, value) in allPrefs) {
                if (key.startsWith(PREFIX_CONDITION_STATE)) {
                    val stateKey = key.removePrefix(PREFIX_CONDITION_STATE)
                    value?.let { lastDeviceEventStates[stateKey] = it }
                }
            }
            Log.d(TAG, "Loaded ${lastDeviceEventStates.size} persisted device event states")
        } catch (e: Exception) {
            Log.e(TAG, "Error loading persisted states", e)
        }
    }

    /**
     * 保存状态到持久化存储
     */
    private fun saveState(key: String, value: Any) {
        try {
            lastDeviceEventStates[key] = value

            val editor = sharedPreferences.edit()
            when (value) {
                is Boolean -> editor.putBoolean("$PREFIX_CONDITION_STATE$key", value)
                is Long -> editor.putLong("$PREFIX_CONDITION_STATE$key", value)
                is Int -> editor.putInt("$PREFIX_CONDITION_STATE$key", value)
                is String -> editor.putString("$PREFIX_CONDITION_STATE$key", value)
                else -> {
                    Log.w(TAG, "Unsupported state value type: ${value::class.simpleName}")
                    return
                }
            }
            editor.apply()
        } catch (e: Exception) {
            Log.e(TAG, "Error saving state: $key = $value", e)
        }
    }

    /**
     * 注册设备事件条件
     */
    fun registerCondition(condition: DeviceEventCondition) {
        synchronized(registeredConditions) {
            if (!registeredConditions.any { it.id == condition.id }) {
                registeredConditions.add(condition)
                Log.d(TAG, "Registered device event condition: ${condition.getDescription()}")

                // 预初始化条件状态
                preInitializeCondition(condition)
            }
        }
    }

    /**
     * 取消注册设备事件条件
     */
    fun unregisterCondition(conditionId: String) {
        synchronized(registeredConditions) {
            registeredConditions.removeAll { it.id == conditionId }
            // 清理相关缓存
            lastDeviceEventStates.keys.removeAll { it.contains(conditionId) }
            Log.d(TAG, "Unregistered device event condition: $conditionId")
        }
    }

    /**
     * 清除所有注册的条件
     */
    fun clearAllConditions() {
        synchronized(registeredConditions) {
            registeredConditions.clear()
            lastDeviceEventStates.clear()
            Log.d(TAG, "Cleared all device event conditions")
        }
    }

    /**
     * 启动监听
     */
    fun startMonitoring() {
        if (isMonitoring) {
            Log.d(TAG, "Device event monitoring already started")
            return
        }

        isMonitoring = true
        Log.d(TAG, "Starting device event monitoring...")

        // 启动各种设备事件监听器
        startGpsStateMonitoring()
        startClipboardMonitoring()
        startScreenStateMonitoring()
        startDockStateMonitoring()
        startIntentReceivedMonitoring()
        startSimCardStateMonitoring()
        startDarkThemeMonitoring()
        startSystemSettingMonitoring()
        startAutoSyncStateMonitoring()
        startDeviceBootMonitoring()
        startNotificationEventMonitoring()
        startRingerModeMonitoring()
        startMusicPlaybackMonitoring()
        startAirplaneModeMonitoring()
        startVolumeChangeMonitoring()

        Log.d(TAG, "Device event monitoring started with ${registeredConditions.size} conditions")
    }

    /**
     * 停止监听
     */
    fun stopMonitoring() {
        if (!isMonitoring) {
            Log.d(TAG, "Device event monitoring already stopped")
            return
        }

        isMonitoring = false
        Log.d(TAG, "Stopping device event monitoring...")

        // 停止所有监听器
        stopAllMonitoring()

        Log.d(TAG, "Device event monitoring stopped")
    }

    /**
     * 预初始化条件状态
     */
    private fun preInitializeCondition(condition: DeviceEventCondition) {
        when (condition.eventType) {
            DeviceEventType.GPS_STATE -> {
                // GPS状态预初始化
                try {
                    val locationManager = context.getSystemService(Context.LOCATION_SERVICE) as android.location.LocationManager
                    val isGpsEnabled = locationManager.isProviderEnabled(android.location.LocationManager.GPS_PROVIDER)
                    val conditionKey = "gps_state_${condition.id}"
                    saveState(conditionKey, isGpsEnabled)
                    Log.d(TAG, "Pre-initialized GPS state for condition ${condition.id}: $isGpsEnabled")
                } catch (e: Exception) {
                    Log.w(TAG, "Failed to pre-initialize GPS state for condition ${condition.id}", e)
                }
            }
            DeviceEventType.VOLUME_CHANGED -> {
                // 音量变化预初始化
                try {
                    val audioManager = context.getSystemService(Context.AUDIO_SERVICE) as android.media.AudioManager
                    val streamType = when (condition.volumeStreamType) {
                        VolumeStreamType.ALARM -> android.media.AudioManager.STREAM_ALARM
                        VolumeStreamType.MEDIA_MUSIC -> android.media.AudioManager.STREAM_MUSIC
                        VolumeStreamType.NOTIFICATION -> android.media.AudioManager.STREAM_NOTIFICATION
                        VolumeStreamType.RING -> android.media.AudioManager.STREAM_RING
                        VolumeStreamType.SYSTEM -> android.media.AudioManager.STREAM_SYSTEM
                        VolumeStreamType.VOICE_CALL -> android.media.AudioManager.STREAM_VOICE_CALL
                        VolumeStreamType.BLUETOOTH -> 6 // AudioManager.STREAM_BLUETOOTH_SCO
                        else -> android.media.AudioManager.STREAM_MUSIC
                    }
                    val currentVolume = audioManager.getStreamVolume(streamType)
                    val conditionKey = "volume_${condition.volumeStreamType.name}_${condition.id}"
                    saveState(conditionKey, currentVolume)
                    Log.d(TAG, "Pre-initialized volume state for condition ${condition.id}: $currentVolume")
                } catch (e: Exception) {
                    Log.w(TAG, "Failed to pre-initialize volume state for condition ${condition.id}", e)
                }
            }
            else -> {
                // 其他设备事件条件暂不需要预初始化
            }
        }
    }

    // 广播接收器
    private var gpsStateReceiver: android.content.BroadcastReceiver? = null
    private var screenStateReceiver: android.content.BroadcastReceiver? = null
    private var dockStateReceiver: android.content.BroadcastReceiver? = null
    private var intentReceiver: android.content.BroadcastReceiver? = null
    private var simCardStateReceiver: android.content.BroadcastReceiver? = null
    private var darkThemeReceiver: android.content.BroadcastReceiver? = null
    private var autoSyncStateReceiver: android.content.BroadcastReceiver? = null
    private var deviceBootReceiver: android.content.BroadcastReceiver? = null
    private var ringerModeReceiver: android.content.BroadcastReceiver? = null
    private var musicPlaybackReceiver: android.content.BroadcastReceiver? = null
    private var airplaneModeReceiver: android.content.BroadcastReceiver? = null
    private var volumeChangeReceiver: android.content.BroadcastReceiver? = null

    /**
     * 启动GPS状态监听
     */
    private fun startGpsStateMonitoring() {
        val gpsConditions = registeredConditions.filter { it.eventType == DeviceEventType.GPS_STATE }
        if (gpsConditions.isEmpty()) return

        gpsStateReceiver = object : android.content.BroadcastReceiver() {
            override fun onReceive(context: Context, intent: Intent) {
                monitorScope.launch {
                    handleGpsStateChange(intent)
                }
            }
        }

        val gpsFilter = android.content.IntentFilter().apply {
            addAction(android.location.LocationManager.PROVIDERS_CHANGED_ACTION)
        }

        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            context.registerReceiver(gpsStateReceiver, gpsFilter, Context.RECEIVER_EXPORTED)
        } else {
            context.registerReceiver(gpsStateReceiver, gpsFilter)
        }

        Log.d(TAG, "GPS state monitoring started for ${gpsConditions.size} conditions")
    }

    /**
     * 处理GPS状态变化
     */
    private fun handleGpsStateChange(intent: Intent) {
        try {
            val locationManager = context.getSystemService(Context.LOCATION_SERVICE) as android.location.LocationManager
            val isGpsEnabled = locationManager.isProviderEnabled(android.location.LocationManager.GPS_PROVIDER)

            val gpsConditions = registeredConditions.filter { it.eventType == DeviceEventType.GPS_STATE }

            for (condition in gpsConditions) {
                val conditionKey = "gps_state_${condition.id}"
                val lastGpsState = lastDeviceEventStates[conditionKey] as? Boolean

                val isTriggered = when (condition.gpsStateType) {
                    GpsStateType.ENABLED -> !isGpsEnabled && lastGpsState == false && isGpsEnabled
                    GpsStateType.DISABLED -> isGpsEnabled && lastGpsState == true && !isGpsEnabled
                    else -> false
                }

                if (isTriggered) {
                    Log.d(TAG, "GPS state condition triggered: ${condition.getDescription()}")

                    val eventData = mapOf<String, Any>(
                        "eventType" to "GPS_STATE",
                        "gpsStateType" to condition.gpsStateType.name,
                        "isGpsEnabled" to isGpsEnabled,
                        "lastGpsState" to (lastGpsState ?: false),
                        "timestamp" to System.currentTimeMillis()
                    )

                    triggerCondition(condition, eventData)
                }

                // 更新状态
                saveState(conditionKey, isGpsEnabled)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error handling GPS state change", e)
        }
    }

    /**
     * 启动剪贴板监听
     */
    private fun startClipboardMonitoring() {
        val clipboardConditions = registeredConditions.filter { it.eventType == DeviceEventType.CLIPBOARD_CHANGED }
        if (clipboardConditions.isEmpty()) return

        // 剪贴板监听需要通过ClipboardManager.OnPrimaryClipChangedListener实现
        val clipboardManager = context.getSystemService(Context.CLIPBOARD_SERVICE) as android.content.ClipboardManager

        val clipboardListener = android.content.ClipboardManager.OnPrimaryClipChangedListener {
            monitorScope.launch {
                handleClipboardChange()
            }
        }

        clipboardManager.addPrimaryClipChangedListener(clipboardListener)

        Log.d(TAG, "Clipboard monitoring started for ${clipboardConditions.size} conditions")
    }

    /**
     * 处理剪贴板变化
     */
    private fun handleClipboardChange() {
        try {
            val clipboardManager = context.getSystemService(Context.CLIPBOARD_SERVICE) as android.content.ClipboardManager
            val clipData = clipboardManager.primaryClip
            val clipText = clipData?.getItemAt(0)?.text?.toString() ?: ""

            val clipboardConditions = registeredConditions.filter { it.eventType == DeviceEventType.CLIPBOARD_CHANGED }

            for (condition in clipboardConditions) {
                val isMatched = if (condition.clipboardUseRegex) {
                    try {
                        val regex = if (condition.clipboardCaseSensitive) {
                            Regex(condition.clipboardText)
                        } else {
                            Regex(condition.clipboardText, RegexOption.IGNORE_CASE)
                        }
                        regex.containsMatchIn(clipText)
                    } catch (e: Exception) {
                        Log.w(TAG, "Invalid regex pattern: ${condition.clipboardText}", e)
                        false
                    }
                } else {
                    if (condition.clipboardCaseSensitive) {
                        clipText.contains(condition.clipboardText)
                    } else {
                        clipText.contains(condition.clipboardText, ignoreCase = true)
                    }
                }

                if (isMatched) {
                    Log.d(TAG, "Clipboard condition triggered: ${condition.getDescription()}")

                    val eventData = mapOf<String, Any>(
                        "eventType" to "CLIPBOARD_CHANGED",
                        "clipboardText" to clipText,
                        "matchPattern" to condition.clipboardText,
                        "useRegex" to condition.clipboardUseRegex,
                        "caseSensitive" to condition.clipboardCaseSensitive,
                        "timestamp" to System.currentTimeMillis()
                    )

                    triggerCondition(condition, eventData)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error handling clipboard change", e)
        }
    }

    /**
     * 启动屏幕状态监听
     */
    private fun startScreenStateMonitoring() {
        val screenConditions = registeredConditions.filter { it.eventType == DeviceEventType.SCREEN_STATE }
        if (screenConditions.isEmpty()) return

        screenStateReceiver = object : android.content.BroadcastReceiver() {
            override fun onReceive(context: Context, intent: Intent) {
                monitorScope.launch {
                    handleScreenStateChange(intent)
                }
            }
        }

        val screenFilter = android.content.IntentFilter().apply {
            addAction(Intent.ACTION_SCREEN_ON)
            addAction(Intent.ACTION_SCREEN_OFF)
            addAction(Intent.ACTION_USER_PRESENT)
        }

        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            context.registerReceiver(screenStateReceiver, screenFilter, Context.RECEIVER_EXPORTED)
        } else {
            context.registerReceiver(screenStateReceiver, screenFilter)
        }

        Log.d(TAG, "Screen state monitoring started for ${screenConditions.size} conditions")
    }

    /**
     * 处理屏幕状态变化
     */
    private fun handleScreenStateChange(intent: Intent) {
        try {
            val action = intent.action
            val screenConditions = registeredConditions.filter { it.eventType == DeviceEventType.SCREEN_STATE }

            for (condition in screenConditions) {
                val isTriggered = when (condition.screenEventType) {
                    ScreenEventType.ON -> action == Intent.ACTION_SCREEN_ON
                    ScreenEventType.OFF -> action == Intent.ACTION_SCREEN_OFF
                    ScreenEventType.UNLOCKED -> action == Intent.ACTION_USER_PRESENT
                    else -> false
                }

                if (isTriggered) {
                    Log.d(TAG, "Screen state condition triggered: ${condition.getDescription()}")

                    val eventData = mapOf<String, Any>(
                        "eventType" to "SCREEN_STATE",
                        "screenEventType" to condition.screenEventType.name,
                        "action" to (action ?: ""),
                        "timestamp" to System.currentTimeMillis()
                    )

                    triggerCondition(condition, eventData)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error handling screen state change", e)
        }
    }

    /**
     * 启动底座状态监听
     */
    private fun startDockStateMonitoring() {
        val dockConditions = registeredConditions.filter { it.eventType == DeviceEventType.DOCK_STATE }
        if (dockConditions.isEmpty()) return

        dockStateReceiver = object : android.content.BroadcastReceiver() {
            override fun onReceive(context: Context, intent: Intent) {
                monitorScope.launch {
                    handleDockStateChange(intent)
                }
            }
        }

        val dockFilter = android.content.IntentFilter().apply {
            addAction(Intent.ACTION_DOCK_EVENT)
        }

        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            context.registerReceiver(dockStateReceiver, dockFilter, Context.RECEIVER_EXPORTED)
        } else {
            context.registerReceiver(dockStateReceiver, dockFilter)
        }

        Log.d(TAG, "Dock state monitoring started for ${dockConditions.size} conditions")
    }

    /**
     * 处理底座状态变化
     */
    private fun handleDockStateChange(intent: Intent) {
        try {
            val dockState = intent.getIntExtra(Intent.EXTRA_DOCK_STATE, Intent.EXTRA_DOCK_STATE_UNDOCKED)
            val dockConditions = registeredConditions.filter { it.eventType == DeviceEventType.DOCK_STATE }

            for (condition in dockConditions) {
                val isTriggered = when (condition.dockStateType) {
                    DockStateType.ANY_DOCK, DockStateType.DESK_DOCK, DockStateType.CAR_DOCK -> dockState != Intent.EXTRA_DOCK_STATE_UNDOCKED
                    DockStateType.UNDOCKED -> dockState == Intent.EXTRA_DOCK_STATE_UNDOCKED
                    else -> false
                }

                if (isTriggered) {
                    Log.d(TAG, "Dock state condition triggered: ${condition.getDescription()}")

                    val eventData = mapOf<String, Any>(
                        "eventType" to "DOCK_STATE",
                        "dockStateType" to condition.dockStateType.name,
                        "dockState" to dockState,
                        "timestamp" to System.currentTimeMillis()
                    )

                    triggerCondition(condition, eventData)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error handling dock state change", e)
        }
    }

    /**
     * 启动Intent接收监听
     */
    private fun startIntentReceivedMonitoring() {
        val intentConditions = registeredConditions.filter { it.eventType == DeviceEventType.INTENT_RECEIVED }
        if (intentConditions.isEmpty()) return

        // 为每个不同的Intent动作创建单独的接收器
        val uniqueActions = intentConditions.map { it.intentAction }.distinct()

        for (action in uniqueActions) {
            if (action.isNotEmpty()) {
                val receiver = object : android.content.BroadcastReceiver() {
                    override fun onReceive(context: Context, intent: Intent) {
                        monitorScope.launch {
                            handleIntentReceived(intent, action)
                        }
                    }
                }

                val filter = android.content.IntentFilter(action)

                try {
                    if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
                        context.registerReceiver(receiver, filter, Context.RECEIVER_EXPORTED)
                    } else {
                        context.registerReceiver(receiver, filter)
                    }
                    Log.d(TAG, "Intent receiver registered for action: $action")
                } catch (e: Exception) {
                    Log.w(TAG, "Failed to register intent receiver for action: $action", e)
                }
            }
        }

        Log.d(TAG, "Intent received monitoring started for ${intentConditions.size} conditions")
    }

    /**
     * 处理Intent接收
     */
    private fun handleIntentReceived(intent: Intent, expectedAction: String) {
        try {
            val intentConditions = registeredConditions.filter {
                it.eventType == DeviceEventType.INTENT_RECEIVED && it.intentAction == expectedAction
            }

            for (condition in intentConditions) {
                var isMatched = true

                // 检查Extra键值对匹配
                if (condition.intentExtraKey.isNotEmpty()) {
                    val extraValue = intent.getStringExtra(condition.intentExtraKey)
                    if (condition.intentExtraValue.isNotEmpty()) {
                        isMatched = when {
                            condition.intentExtraValue.contains("*") || condition.intentExtraValue.contains("?") -> {
                                // 通配符匹配
                                val pattern = condition.intentExtraValue
                                    .replace("*", ".*")
                                    .replace("?", ".")
                                extraValue?.matches(Regex(pattern)) == true
                            }
                            else -> extraValue == condition.intentExtraValue
                        }
                    } else {
                        // 只检查键是否存在
                        isMatched = intent.hasExtra(condition.intentExtraKey)
                    }
                }

                if (isMatched) {
                    Log.d(TAG, "Intent received condition triggered: ${condition.getDescription()}")

                    val eventData = mapOf<String, Any>(
                        "eventType" to "INTENT_RECEIVED",
                        "intentAction" to expectedAction,
                        "intentExtraKey" to condition.intentExtraKey,
                        "intentExtraValue" to condition.intentExtraValue,
                        "actualExtraValue" to (intent.getStringExtra(condition.intentExtraKey) ?: ""),
                        "timestamp" to System.currentTimeMillis()
                    )

                    triggerCondition(condition, eventData)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error handling intent received", e)
        }
    }

    /**
     * 启动SIM卡状态监听
     */
    private fun startSimCardStateMonitoring() {
        val simConditions = registeredConditions.filter { it.eventType == DeviceEventType.SIM_CARD_STATE }
        if (simConditions.isEmpty()) return

        simCardStateReceiver = object : android.content.BroadcastReceiver() {
            override fun onReceive(context: Context, intent: Intent) {
                monitorScope.launch {
                    handleSimCardStateChange(intent)
                }
            }
        }

        val simFilter = android.content.IntentFilter().apply {
            addAction("android.intent.action.SIM_STATE_CHANGED")
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
                try {
                    addAction("android.telephony.action.SIM_CARD_STATE_CHANGED")
                    addAction("android.telephony.action.SIM_APPLICATION_STATE_CHANGED")
                } catch (e: Exception) {
                    Log.w(TAG, "Failed to add newer SIM state actions", e)
                }
            }
        }

        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            context.registerReceiver(simCardStateReceiver, simFilter, Context.RECEIVER_EXPORTED)
        } else {
            context.registerReceiver(simCardStateReceiver, simFilter)
        }

        Log.d(TAG, "SIM card state monitoring started for ${simConditions.size} conditions")
    }

    /**
     * 处理SIM卡状态变化
     */
    private fun handleSimCardStateChange(intent: Intent) {
        try {
            val telephonyManager = context.getSystemService(Context.TELEPHONY_SERVICE) as android.telephony.TelephonyManager
            val simState = telephonyManager.simState
            val simConditions = registeredConditions.filter { it.eventType == DeviceEventType.SIM_CARD_STATE }

            for (condition in simConditions) {
                val isTriggered = when (condition.simCardStateType) {
                    SimCardStateType.INSERTED -> simState == android.telephony.TelephonyManager.SIM_STATE_READY
                    SimCardStateType.REMOVED -> simState == android.telephony.TelephonyManager.SIM_STATE_ABSENT
                    SimCardStateType.STATE_CHANGED -> true // 任何状态变化都触发
                    else -> false
                }

                if (isTriggered) {
                    Log.d(TAG, "SIM card state condition triggered: ${condition.getDescription()}")

                    val eventData = mapOf<String, Any>(
                        "eventType" to "SIM_CARD_STATE",
                        "simCardStateType" to condition.simCardStateType.name,
                        "simState" to simState,
                        "timestamp" to System.currentTimeMillis()
                    )

                    triggerCondition(condition, eventData)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error handling SIM card state change", e)
        }
    }

    /**
     * 触发条件
     */
    private fun triggerCondition(condition: DeviceEventCondition, eventData: Map<String, Any>) {
        try {
            onConditionTriggered(condition, eventData)
        } catch (e: Exception) {
            Log.e(TAG, "Error triggering condition: ${condition.getDescription()}", e)
        }
    }

    /**
     * 启动深色主题监听
     */
    private fun startDarkThemeMonitoring() {
        val darkThemeConditions = registeredConditions.filter { it.eventType == DeviceEventType.DARK_THEME_CHANGED }
        if (darkThemeConditions.isEmpty()) return

        darkThemeReceiver = object : android.content.BroadcastReceiver() {
            override fun onReceive(context: Context, intent: Intent) {
                monitorScope.launch {
                    handleDarkThemeChange()
                }
            }
        }

        val darkThemeFilter = android.content.IntentFilter().apply {
            addAction(Intent.ACTION_CONFIGURATION_CHANGED)
        }

        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            context.registerReceiver(darkThemeReceiver, darkThemeFilter, Context.RECEIVER_EXPORTED)
        } else {
            context.registerReceiver(darkThemeReceiver, darkThemeFilter)
        }

        Log.d(TAG, "Dark theme monitoring started for ${darkThemeConditions.size} conditions")
    }

    /**
     * 处理深色主题变化
     */
    private fun handleDarkThemeChange() {
        try {
            val currentNightMode = context.resources.configuration.uiMode and android.content.res.Configuration.UI_MODE_NIGHT_MASK
            val isDarkTheme = currentNightMode == android.content.res.Configuration.UI_MODE_NIGHT_YES

            val darkThemeConditions = registeredConditions.filter { it.eventType == DeviceEventType.DARK_THEME_CHANGED }

            for (condition in darkThemeConditions) {
                val conditionKey = "dark_theme_${condition.id}"
                val lastDarkThemeState = lastDeviceEventStates[conditionKey] as? Boolean

                val isTriggered = when (condition.darkThemeStateType) {
                    DarkThemeStateType.ENABLED -> !isDarkTheme && lastDarkThemeState == false && isDarkTheme
                    DarkThemeStateType.DISABLED -> isDarkTheme && lastDarkThemeState == true && !isDarkTheme
                    else -> false
                }

                if (isTriggered) {
                    Log.d(TAG, "Dark theme condition triggered: ${condition.getDescription()}")

                    val eventData = mapOf<String, Any>(
                        "eventType" to "DARK_THEME_CHANGED",
                        "darkThemeStateType" to condition.darkThemeStateType.name,
                        "isDarkTheme" to isDarkTheme,
                        "lastDarkThemeState" to (lastDarkThemeState ?: false),
                        "timestamp" to System.currentTimeMillis()
                    )

                    triggerCondition(condition, eventData)
                }

                // 更新状态
                saveState(conditionKey, isDarkTheme)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error handling dark theme change", e)
        }
    }

    /**
     * 启动系统设置监听
     */
    private fun startSystemSettingMonitoring() {
        val systemSettingConditions = registeredConditions.filter { it.eventType == DeviceEventType.SYSTEM_SETTING_CHANGED }
        if (systemSettingConditions.isEmpty()) return

        // 系统设置变化监听需要通过ContentObserver实现
        // 这里简化实现，使用广播接收器监听一些常见的设置变化
        val settingActions = listOf(
            android.provider.Settings.ACTION_SETTINGS,
            android.provider.Settings.ACTION_WIFI_SETTINGS,
            android.provider.Settings.ACTION_BLUETOOTH_SETTINGS,
            android.provider.Settings.ACTION_LOCATION_SOURCE_SETTINGS
        )

        for (action in settingActions) {
            try {
                val receiver = object : android.content.BroadcastReceiver() {
                    override fun onReceive(context: Context, intent: Intent) {
                        monitorScope.launch {
                            handleSystemSettingChange(intent)
                        }
                    }
                }

                val filter = android.content.IntentFilter(action)

                if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
                    context.registerReceiver(receiver, filter, Context.RECEIVER_EXPORTED)
                } else {
                    context.registerReceiver(receiver, filter)
                }
            } catch (e: Exception) {
                Log.w(TAG, "Failed to register system setting receiver for action: $action", e)
            }
        }

        Log.d(TAG, "System setting monitoring started for ${systemSettingConditions.size} conditions")
    }

    /**
     * 处理系统设置变化
     */
    private fun handleSystemSettingChange(intent: Intent) {
        try {
            val systemSettingConditions = registeredConditions.filter { it.eventType == DeviceEventType.SYSTEM_SETTING_CHANGED }

            for (condition in systemSettingConditions) {
                // 检查设置键值匹配
                if (condition.systemSettingKey.isNotEmpty()) {
                    try {
                        val settingValue = when {
                            condition.systemSettingTypes.contains(SystemSettingType.SYSTEM) -> {
                                android.provider.Settings.System.getString(context.contentResolver, condition.systemSettingKey)
                            }
                            condition.systemSettingTypes.contains(SystemSettingType.SECURE) -> {
                                android.provider.Settings.Secure.getString(context.contentResolver, condition.systemSettingKey)
                            }
                            condition.systemSettingTypes.contains(SystemSettingType.GLOBAL) -> {
                                android.provider.Settings.Global.getString(context.contentResolver, condition.systemSettingKey)
                            }
                            else -> null
                        }

                        val isMatched = if (condition.systemSettingValue.isNotEmpty()) {
                            if (condition.systemSettingUseRegex) {
                                try {
                                    val regex = Regex(condition.systemSettingValue)
                                    settingValue?.let { regex.containsMatchIn(it) } == true
                                } catch (e: Exception) {
                                    Log.w(TAG, "Invalid regex pattern: ${condition.systemSettingValue}", e)
                                    false
                                }
                            } else {
                                settingValue == condition.systemSettingValue
                            }
                        } else {
                            settingValue != null // 只检查设置是否存在
                        }

                        if (isMatched) {
                            Log.d(TAG, "System setting condition triggered: ${condition.getDescription()}")

                            val eventData = mapOf<String, Any>(
                                "eventType" to "SYSTEM_SETTING_CHANGED",
                                "systemSettingKey" to condition.systemSettingKey,
                                "systemSettingValue" to condition.systemSettingValue,
                                "actualSettingValue" to (settingValue ?: ""),
                                "useRegex" to condition.systemSettingUseRegex,
                                "timestamp" to System.currentTimeMillis()
                            )

                            triggerCondition(condition, eventData)
                        }
                    } catch (e: Exception) {
                        Log.w(TAG, "Failed to check system setting: ${condition.systemSettingKey}", e)
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error handling system setting change", e)
        }
    }

    /**
     * 启动自动同步状态监听
     */
    private fun startAutoSyncStateMonitoring() {
        val autoSyncConditions = registeredConditions.filter { it.eventType == DeviceEventType.AUTO_SYNC_STATE }
        if (autoSyncConditions.isEmpty()) return

        autoSyncStateReceiver = object : android.content.BroadcastReceiver() {
            override fun onReceive(context: Context, intent: Intent) {
                monitorScope.launch {
                    handleAutoSyncStateChange()
                }
            }
        }

        val autoSyncFilter = android.content.IntentFilter().apply {
            addAction("android.content.syncmanager.SYNC_CONN_STATUS_CHANGED")
        }

        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            context.registerReceiver(autoSyncStateReceiver, autoSyncFilter, Context.RECEIVER_EXPORTED)
        } else {
            context.registerReceiver(autoSyncStateReceiver, autoSyncFilter)
        }

        Log.d(TAG, "Auto sync state monitoring started for ${autoSyncConditions.size} conditions")
    }

    /**
     * 处理自动同步状态变化
     */
    private fun handleAutoSyncStateChange() {
        try {
            val isAutoSyncEnabled = android.content.ContentResolver.getMasterSyncAutomatically()
            val autoSyncConditions = registeredConditions.filter { it.eventType == DeviceEventType.AUTO_SYNC_STATE }

            for (condition in autoSyncConditions) {
                val conditionKey = "auto_sync_${condition.id}"
                val lastAutoSyncState = lastDeviceEventStates[conditionKey] as? Boolean

                val isTriggered = when (condition.autoSyncStateType) {
                    AutoSyncStateType.ENABLED -> !isAutoSyncEnabled && lastAutoSyncState == false && isAutoSyncEnabled
                    AutoSyncStateType.DISABLED -> isAutoSyncEnabled && lastAutoSyncState == true && !isAutoSyncEnabled
                }

                if (isTriggered) {
                    Log.d(TAG, "Auto sync state condition triggered: ${condition.getDescription()}")

                    val eventData = mapOf<String, Any>(
                        "eventType" to "AUTO_SYNC_STATE",
                        "autoSyncStateType" to condition.autoSyncStateType.name,
                        "isAutoSyncEnabled" to isAutoSyncEnabled,
                        "lastAutoSyncState" to (lastAutoSyncState ?: false),
                        "timestamp" to System.currentTimeMillis()
                    )

                    triggerCondition(condition, eventData)
                }

                // 更新状态
                saveState(conditionKey, isAutoSyncEnabled)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error handling auto sync state change", e)
        }
    }

    /**
     * 启动设备启动监听
     */
    private fun startDeviceBootMonitoring() {
        val bootConditions = registeredConditions.filter { it.eventType == DeviceEventType.DEVICE_BOOT_COMPLETED }
        if (bootConditions.isEmpty()) return

        deviceBootReceiver = object : android.content.BroadcastReceiver() {
            override fun onReceive(context: Context, intent: Intent) {
                monitorScope.launch {
                    handleDeviceBootCompleted(intent)
                }
            }
        }

        val bootFilter = android.content.IntentFilter().apply {
            addAction(Intent.ACTION_BOOT_COMPLETED)
            addAction(Intent.ACTION_LOCKED_BOOT_COMPLETED)
        }

        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            context.registerReceiver(deviceBootReceiver, bootFilter, Context.RECEIVER_EXPORTED)
        } else {
            context.registerReceiver(deviceBootReceiver, bootFilter)
        }

        Log.d(TAG, "Device boot monitoring started for ${bootConditions.size} conditions")
    }

    /**
     * 处理设备启动完成
     */
    private fun handleDeviceBootCompleted(intent: Intent) {
        try {
            val bootConditions = registeredConditions.filter { it.eventType == DeviceEventType.DEVICE_BOOT_COMPLETED }

            for (condition in bootConditions) {
                Log.d(TAG, "Device boot completed condition triggered: ${condition.getDescription()}")

                val eventData = mapOf<String, Any>(
                    "eventType" to "DEVICE_BOOT_COMPLETED",
                    "action" to (intent.action ?: ""),
                    "timestamp" to System.currentTimeMillis()
                )

                triggerCondition(condition, eventData)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error handling device boot completed", e)
        }
    }

    /**
     * 启动通知事件监听
     */
    private fun startNotificationEventMonitoring() {
        val notificationConditions = registeredConditions.filter { it.eventType == DeviceEventType.NOTIFICATION_EVENT }
        if (notificationConditions.isEmpty()) return

        // 通知事件监听需要通过NotificationListenerService实现
        // 这里只是占位实现，实际需要在NotificationListenerService中处理
    }

    /**
     * 启动铃声模式监听
     */
    private fun startRingerModeMonitoring() {
        val ringerModeConditions = registeredConditions.filter { it.eventType == DeviceEventType.RINGER_MODE_CHANGED }
        if (ringerModeConditions.isEmpty()) return

        ringerModeReceiver = object : android.content.BroadcastReceiver() {
            override fun onReceive(context: Context, intent: Intent) {
                monitorScope.launch {
                    handleRingerModeChange()
                }
            }
        }

        val ringerModeFilter = android.content.IntentFilter().apply {
            addAction(android.media.AudioManager.RINGER_MODE_CHANGED_ACTION)
        }

        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            context.registerReceiver(ringerModeReceiver, ringerModeFilter, Context.RECEIVER_EXPORTED)
        } else {
            context.registerReceiver(ringerModeReceiver, ringerModeFilter)
        }

        Log.d(TAG, "Ringer mode monitoring started for ${ringerModeConditions.size} conditions")
    }

    /**
     * 处理铃声模式变化
     */
    private fun handleRingerModeChange() {
        try {
            val audioManager = context.getSystemService(Context.AUDIO_SERVICE) as android.media.AudioManager
            val currentRingerMode = audioManager.ringerMode
            val ringerModeConditions = registeredConditions.filter { it.eventType == DeviceEventType.RINGER_MODE_CHANGED }

            for (condition in ringerModeConditions) {
                val isTriggered = when (condition.ringerModeType) {
                    RingerModeType.NORMAL -> currentRingerMode == android.media.AudioManager.RINGER_MODE_NORMAL
                    RingerModeType.VIBRATE -> currentRingerMode == android.media.AudioManager.RINGER_MODE_VIBRATE
                    RingerModeType.SILENT -> currentRingerMode == android.media.AudioManager.RINGER_MODE_SILENT
                }

                if (isTriggered) {
                    Log.d(TAG, "Ringer mode condition triggered: ${condition.getDescription()}")

                    val eventData = mapOf<String, Any>(
                        "eventType" to "RINGER_MODE_CHANGED",
                        "ringerModeType" to condition.ringerModeType.name,
                        "currentRingerMode" to currentRingerMode,
                        "timestamp" to System.currentTimeMillis()
                    )

                    triggerCondition(condition, eventData)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error handling ringer mode change", e)
        }
    }

    /**
     * 启动音乐播放监听
     */
    private fun startMusicPlaybackMonitoring() {
        val musicPlaybackConditions = registeredConditions.filter { it.eventType == DeviceEventType.MUSIC_PLAYBACK_STATE }
        if (musicPlaybackConditions.isEmpty()) return

        musicPlaybackReceiver = object : android.content.BroadcastReceiver() {
            override fun onReceive(context: Context, intent: Intent) {
                monitorScope.launch {
                    handleMusicPlaybackChange(intent)
                }
            }
        }

        val musicPlaybackFilter = android.content.IntentFilter().apply {
            addAction("com.android.music.metachanged")
            addAction("com.android.music.playstatechanged")
            addAction("com.android.music.playbackcomplete")
            addAction("com.android.music.queuechanged")
        }

        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            context.registerReceiver(musicPlaybackReceiver, musicPlaybackFilter, Context.RECEIVER_EXPORTED)
        } else {
            context.registerReceiver(musicPlaybackReceiver, musicPlaybackFilter)
        }

        Log.d(TAG, "Music playback monitoring started for ${musicPlaybackConditions.size} conditions")
    }

    /**
     * 处理音乐播放状态变化
     */
    private fun handleMusicPlaybackChange(intent: Intent) {
        try {
            val isPlaying = intent.getBooleanExtra("playing", false)
            val musicPlaybackConditions = registeredConditions.filter { it.eventType == DeviceEventType.MUSIC_PLAYBACK_STATE }

            for (condition in musicPlaybackConditions) {
                val isTriggered = when (condition.musicPlaybackType) {
                    MusicPlaybackType.STARTED -> isPlaying
                    MusicPlaybackType.STOPPED -> !isPlaying
                    else -> false
                }

                if (isTriggered) {
                    Log.d(TAG, "Music playback condition triggered: ${condition.getDescription()}")

                    val eventData = mapOf<String, Any>(
                        "eventType" to "MUSIC_PLAYBACK_STATE",
                        "musicPlaybackType" to condition.musicPlaybackType.name,
                        "isPlaying" to isPlaying,
                        "action" to (intent.action ?: ""),
                        "timestamp" to System.currentTimeMillis()
                    )

                    triggerCondition(condition, eventData)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error handling music playback change", e)
        }
    }

    /**
     * 启动飞行模式监听
     */
    private fun startAirplaneModeMonitoring() {
        val airplaneModeConditions = registeredConditions.filter { it.eventType == DeviceEventType.AIRPLANE_MODE_STATE }
        if (airplaneModeConditions.isEmpty()) return

        airplaneModeReceiver = object : android.content.BroadcastReceiver() {
            override fun onReceive(context: Context, intent: Intent) {
                monitorScope.launch {
                    handleAirplaneModeChange(intent)
                }
            }
        }

        val airplaneModeFilter = android.content.IntentFilter().apply {
            addAction(Intent.ACTION_AIRPLANE_MODE_CHANGED)
        }

        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            context.registerReceiver(airplaneModeReceiver, airplaneModeFilter, Context.RECEIVER_EXPORTED)
        } else {
            context.registerReceiver(airplaneModeReceiver, airplaneModeFilter)
        }

        Log.d(TAG, "Airplane mode monitoring started for ${airplaneModeConditions.size} conditions")
    }

    /**
     * 处理飞行模式变化
     */
    private fun handleAirplaneModeChange(intent: Intent) {
        try {
            val isAirplaneModeOn = intent.getBooleanExtra("state", false)
            val airplaneModeConditions = registeredConditions.filter { it.eventType == DeviceEventType.AIRPLANE_MODE_STATE }

            for (condition in airplaneModeConditions) {
                val isTriggered = when (condition.airplaneModeType) {
                    AirplaneModeType.ENABLED -> isAirplaneModeOn
                    AirplaneModeType.DISABLED -> !isAirplaneModeOn
                }

                if (isTriggered) {
                    Log.d(TAG, "Airplane mode condition triggered: ${condition.getDescription()}")

                    val eventData = mapOf<String, Any>(
                        "eventType" to "AIRPLANE_MODE_STATE",
                        "airplaneModeType" to condition.airplaneModeType.name,
                        "isAirplaneModeOn" to isAirplaneModeOn,
                        "timestamp" to System.currentTimeMillis()
                    )

                    triggerCondition(condition, eventData)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error handling airplane mode change", e)
        }
    }

    /**
     * 启动音量变化监听
     */
    private fun startVolumeChangeMonitoring() {
        val volumeChangeConditions = registeredConditions.filter { it.eventType == DeviceEventType.VOLUME_CHANGED }
        if (volumeChangeConditions.isEmpty()) return

        volumeChangeReceiver = object : android.content.BroadcastReceiver() {
            override fun onReceive(context: Context, intent: Intent) {
                monitorScope.launch {
                    handleVolumeChange(intent)
                }
            }
        }

        val volumeChangeFilter = android.content.IntentFilter().apply {
            addAction("android.media.VOLUME_CHANGED_ACTION")
        }

        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            context.registerReceiver(volumeChangeReceiver, volumeChangeFilter, Context.RECEIVER_EXPORTED)
        } else {
            context.registerReceiver(volumeChangeReceiver, volumeChangeFilter)
        }

        Log.d(TAG, "Volume change monitoring started for ${volumeChangeConditions.size} conditions")
    }

    /**
     * 处理音量变化
     */
    private fun handleVolumeChange(intent: Intent) {
        try {
            val audioManager = context.getSystemService(Context.AUDIO_SERVICE) as android.media.AudioManager
            val volumeChangeConditions = registeredConditions.filter { it.eventType == DeviceEventType.VOLUME_CHANGED }

            for (condition in volumeChangeConditions) {
                val streamType = when (condition.volumeStreamType) {
                    VolumeStreamType.ALARM -> android.media.AudioManager.STREAM_ALARM
                    VolumeStreamType.MEDIA_MUSIC -> android.media.AudioManager.STREAM_MUSIC
                    VolumeStreamType.NOTIFICATION -> android.media.AudioManager.STREAM_NOTIFICATION
                    VolumeStreamType.RING -> android.media.AudioManager.STREAM_RING
                    VolumeStreamType.SYSTEM -> android.media.AudioManager.STREAM_SYSTEM
                    VolumeStreamType.VOICE_CALL -> android.media.AudioManager.STREAM_VOICE_CALL
                    VolumeStreamType.BLUETOOTH -> 6 // AudioManager.STREAM_BLUETOOTH_SCO
                    else -> android.media.AudioManager.STREAM_MUSIC
                }

                val currentVolume = audioManager.getStreamVolume(streamType)
                val conditionKey = "volume_${condition.volumeStreamType.name}_${condition.id}"
                val lastVolume = lastDeviceEventStates[conditionKey] as? Int ?: currentVolume

                val volumeChange = kotlin.math.abs(currentVolume - lastVolume)

                if (volumeChange >= condition.volumeThreshold) {
                    Log.d(TAG, "Volume change condition triggered: ${condition.getDescription()}")

                    val eventData = mapOf<String, Any>(
                        "eventType" to "VOLUME_CHANGED",
                        "volumeStreamType" to condition.volumeStreamType.name,
                        "volumeThreshold" to condition.volumeThreshold,
                        "actualVolumeChange" to volumeChange,
                        "currentVolume" to currentVolume,
                        "lastVolume" to lastVolume,
                        "timestamp" to System.currentTimeMillis()
                    )

                    triggerCondition(condition, eventData)
                }

                // 更新状态
                saveState(conditionKey, currentVolume)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error handling volume change", e)
        }
    }

    /**
     * 停止所有监听
     */
    private fun stopAllMonitoring() {
        try {
            // 取消注册所有广播接收器
            gpsStateReceiver?.let {
                context.unregisterReceiver(it)
                gpsStateReceiver = null
            }

            screenStateReceiver?.let {
                context.unregisterReceiver(it)
                screenStateReceiver = null
            }

            dockStateReceiver?.let {
                context.unregisterReceiver(it)
                dockStateReceiver = null
            }

            intentReceiver?.let {
                context.unregisterReceiver(it)
                intentReceiver = null
            }

            simCardStateReceiver?.let {
                context.unregisterReceiver(it)
                simCardStateReceiver = null
            }

            darkThemeReceiver?.let {
                context.unregisterReceiver(it)
                darkThemeReceiver = null
            }

            autoSyncStateReceiver?.let {
                context.unregisterReceiver(it)
                autoSyncStateReceiver = null
            }

            deviceBootReceiver?.let {
                context.unregisterReceiver(it)
                deviceBootReceiver = null
            }

            ringerModeReceiver?.let {
                context.unregisterReceiver(it)
                ringerModeReceiver = null
            }

            musicPlaybackReceiver?.let {
                context.unregisterReceiver(it)
                musicPlaybackReceiver = null
            }

            airplaneModeReceiver?.let {
                context.unregisterReceiver(it)
                airplaneModeReceiver = null
            }

            volumeChangeReceiver?.let {
                context.unregisterReceiver(it)
                volumeChangeReceiver = null
            }

            // 取消协程作用域
            monitorScope.cancel()

            Log.d(TAG, "All device event monitoring stopped")
        } catch (e: Exception) {
            Log.e(TAG, "Error stopping device event monitoring", e)
        }
    }

    /**
     * 获取监听状态
     */
    fun isMonitoring(): Boolean = isMonitoring

    /**
     * 获取注册的条件数量
     */
    fun getRegisteredConditionCount(): Int = registeredConditions.size
}
