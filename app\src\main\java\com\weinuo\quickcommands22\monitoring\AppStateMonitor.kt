package com.weinuo.quickcommands22.monitoring

import android.app.ActivityManager
import android.app.usage.UsageEvents
import android.app.usage.UsageStatsManager
import android.content.Context
import android.util.Log
import kotlinx.coroutines.*
import com.weinuo.quickcommands22.utils.DebugLogManager

/**
 * 应用状态监听器
 *
 * 监听应用的前台/后台切换和启动事件，为事件驱动的内存检测提供触发信号
 */
class AppStateMonitor(
    private val context: Context
) {
    companion object {
        private const val TAG = "AppStateMonitor"
        private const val MONITOR_INTERVAL = 1000L // 1秒检查间隔
    }

    private val debugLogManager = DebugLogManager.getInstance(context)

    private val usageStatsManager = context.getSystemService(Context.USAGE_STATS_SERVICE) as UsageStatsManager
    private val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
    private val monitorScope = CoroutineScope(Dispatchers.Default + SupervisorJob())

    // 应用状态回调
    private val appStateCallbacks = mutableMapOf<String, AppStateCallback>()

    // 上次检查的时间戳
    private var lastCheckTime = System.currentTimeMillis()

    // 当前前台应用
    private var currentForegroundApp: String? = null

    // 监控任务
    private var monitorJob: Job? = null

    /**
     * 应用状态回调接口
     */
    interface AppStateCallback {
        fun onAppLaunched(packageName: String)
        fun onAppForeground(packageName: String)
        fun onAppBackground(packageName: String)
    }

    /**
     * 应用事件数据类
     */
    data class AppEvent(
        val packageName: String,
        val className: String?,
        val eventType: Int,
        val timeStamp: Long
    )

    /**
     * 开始监听应用状态
     */
    fun startMonitoring() {
        if (monitorJob?.isActive == true) {
            Log.d(TAG, "App state monitoring already started")
            return
        }

        Log.d(TAG, "Starting app state monitoring")
        lastCheckTime = System.currentTimeMillis()
        currentForegroundApp = getCurrentForegroundApp()

        monitorJob = monitorScope.launch {
            while (isActive) {
                try {
                    checkAppStateChanges()
                    delay(MONITOR_INTERVAL)
                } catch (e: Exception) {
                    Log.e(TAG, "Error in app state monitoring", e)
                    delay(5000) // 错误时等待5秒再重试
                }
            }
        }
    }

    /**
     * 停止监听应用状态
     */
    fun stopMonitoring() {
        Log.d(TAG, "Stopping app state monitoring")
        monitorJob?.cancel()
        monitorJob = null
    }

    /**
     * 注册应用状态回调
     */
    fun registerCallback(id: String, callback: AppStateCallback) {
        appStateCallbacks[id] = callback
        Log.d(TAG, "Registered app state callback: $id")
    }

    /**
     * 取消注册应用状态回调
     */
    fun unregisterCallback(id: String) {
        appStateCallbacks.remove(id)
        Log.d(TAG, "Unregistered app state callback: $id")
    }

    /**
     * 检查应用状态变化
     */
    private suspend fun checkAppStateChanges() {
        val currentTime = System.currentTimeMillis()
        val events = getUsageEvents(lastCheckTime, currentTime)

        for (event in events) {
            when (event.eventType) {
                UsageEvents.Event.ACTIVITY_RESUMED -> {
                    handleAppForeground(event.packageName)
                }
                UsageEvents.Event.ACTIVITY_PAUSED -> {
                    handleAppBackground(event.packageName)
                }
                UsageEvents.Event.ACTIVITY_STOPPED -> {
                    handleAppBackground(event.packageName)
                }
            }
        }

        // 检查前台应用变化
        val newForegroundApp = getCurrentForegroundApp()
        if (newForegroundApp != currentForegroundApp) {
            currentForegroundApp?.let { handleAppBackground(it) }
            newForegroundApp?.let { handleAppForeground(it) }
            currentForegroundApp = newForegroundApp
        }

        lastCheckTime = currentTime
    }

    /**
     * 获取使用事件
     */
    private fun getUsageEvents(startTime: Long, endTime: Long): List<AppEvent> {
        val events = mutableListOf<AppEvent>()

        try {
            val usageEvents = usageStatsManager.queryEvents(startTime, endTime)
            val event = UsageEvents.Event()

            while (usageEvents.hasNextEvent()) {
                usageEvents.getNextEvent(event)

                // 只关注应用活动事件
                if (event.eventType in listOf(
                    UsageEvents.Event.ACTIVITY_RESUMED,
                    UsageEvents.Event.ACTIVITY_PAUSED,
                    UsageEvents.Event.ACTIVITY_STOPPED
                )) {
                    events.add(AppEvent(
                        packageName = event.packageName,
                        className = event.className,
                        eventType = event.eventType,
                        timeStamp = event.timeStamp
                    ))
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting usage events", e)
        }

        return events
    }

    /**
     * 获取当前前台应用
     */
    private fun getCurrentForegroundApp(): String? {
        try {
            val runningTasks = activityManager.getRunningTasks(1)
            if (runningTasks.isNotEmpty()) {
                return runningTasks[0].topActivity?.packageName
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting foreground app", e)
        }
        return null
    }

    /**
     * 处理应用进入前台
     */
    private fun handleAppForeground(packageName: String) {
        Log.d(TAG, "App entered foreground: $packageName")
        debugLogManager.logInfo(TAG, "检测到应用进入前台: $packageName")

        // 检查是否是新启动的应用
        val isNewLaunch = !isAppRunning(packageName)
        if (isNewLaunch) {
            debugLogManager.logInfo(TAG, "检测到新应用启动: $packageName")
        }

        appStateCallbacks.values.forEach { callback ->
            try {
                if (isNewLaunch) {
                    callback.onAppLaunched(packageName)
                }
                callback.onAppForeground(packageName)
            } catch (e: Exception) {
                Log.e(TAG, "Error in app state callback", e)
                debugLogManager.logError(TAG, "应用状态回调错误: ${e.message}")
            }
        }
    }

    /**
     * 处理应用进入后台
     */
    private fun handleAppBackground(packageName: String) {
        Log.d(TAG, "App entered background: $packageName")
        debugLogManager.logInfo(TAG, "检测到应用进入后台: $packageName")

        appStateCallbacks.values.forEach { callback ->
            try {
                callback.onAppBackground(packageName)
            } catch (e: Exception) {
                Log.e(TAG, "Error in app state callback", e)
                debugLogManager.logError(TAG, "应用状态回调错误: ${e.message}")
            }
        }
    }

    /**
     * 检查应用是否正在运行
     */
    private fun isAppRunning(packageName: String): Boolean {
        try {
            val runningProcesses = activityManager.runningAppProcesses ?: return false
            return runningProcesses.any { it.processName == packageName }
        } catch (e: Exception) {
            Log.e(TAG, "Error checking if app is running", e)
        }
        return false
    }

    /**
     * 清理资源
     */
    fun shutdown() {
        Log.d(TAG, "Shutting down app state monitor")
        stopMonitoring()
        appStateCallbacks.clear()
        monitorScope.cancel()
    }
}
