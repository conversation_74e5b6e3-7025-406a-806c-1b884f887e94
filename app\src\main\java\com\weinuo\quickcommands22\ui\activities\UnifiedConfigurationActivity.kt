package com.weinuo.quickcommands22.ui.activities

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.runtime.Composable
import androidx.compose.ui.platform.LocalContext
import com.weinuo.quickcommands22.ui.configuration.ConfigurationMode
import com.weinuo.quickcommands22.ui.screens.UnifiedConfigurationContent
import com.weinuo.quickcommands22.ui.theme.QuickCommandsTheme
import com.weinuo.quickcommands22.storage.NavigationDataStorageManager
import com.weinuo.quickcommands22.model.SharedTriggerCondition
import com.weinuo.quickcommands22.model.SharedTask

/**
 * 统一配置Activity
 * 
 * 独立的Activity，用于统一配置界面，不包含底部导航栏。
 * 支持三种配置模式：触发条件、中止条件、任务
 * 支持编辑模式，可以预填充现有配置数据进行修改。
 */
class UnifiedConfigurationActivity : ComponentActivity() {

    // ActivityResultLauncher用于处理DetailedConfigurationActivity的结果
    private val detailedConfigurationLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        android.util.Log.d("UnifiedConfigurationActivity", "DetailedConfigurationActivity result received: resultCode=${result.resultCode}")
        if (result.resultCode == Activity.RESULT_OK) {
            val navigationKey = result.data?.getStringExtra("navigation_key")
            val editIndex = result.data?.getIntExtra("edit_index", -1)?.takeIf { it != -1 }
            val configMode = result.data?.getStringExtra("config_mode")

            android.util.Log.d("UnifiedConfigurationActivity", "DetailedConfigurationActivity result data: navigationKey=$navigationKey, editIndex=$editIndex, configMode=$configMode")

            if (navigationKey != null && configMode != null) {
                // 配置完成，直接返回结果给QuickCommandFormActivity
                val resultIntent = Intent().apply {
                    putExtra("navigation_key", navigationKey)
                    putExtra("edit_index", editIndex)
                    putExtra("config_mode", configMode)
                }

                android.util.Log.d("UnifiedConfigurationActivity", "Setting result: RESULT_OK and finishing UnifiedConfigurationActivity")
                setResult(Activity.RESULT_OK, resultIntent)
                finish()
            }
        }
    }

    companion object {
        private const val EXTRA_CONFIGURATION_MODE = "configuration_mode"
        private const val EXTRA_EDIT_DATA = "edit_data"
        private const val EXTRA_EDIT_INDEX = "edit_index"
        
        /**
         * 启动触发条件配置Activity
         */
        fun startForTriggerCondition(
            context: Context, 
            editData: String? = null, 
            editIndex: Int? = null
        ) {
            val intent = Intent(context, UnifiedConfigurationActivity::class.java).apply {
                putExtra(EXTRA_CONFIGURATION_MODE, ConfigurationMode.TRIGGER_CONDITION.name)
                editData?.let { putExtra(EXTRA_EDIT_DATA, it) }
                editIndex?.let { putExtra(EXTRA_EDIT_INDEX, it) }
            }
            context.startActivity(intent)
        }
        
        /**
         * 启动中止条件配置Activity
         */
        fun startForAbortCondition(
            context: Context, 
            editData: String? = null, 
            editIndex: Int? = null
        ) {
            val intent = Intent(context, UnifiedConfigurationActivity::class.java).apply {
                putExtra(EXTRA_CONFIGURATION_MODE, ConfigurationMode.ABORT_CONDITION.name)
                editData?.let { putExtra(EXTRA_EDIT_DATA, it) }
                editIndex?.let { putExtra(EXTRA_EDIT_INDEX, it) }
            }
            context.startActivity(intent)
        }
        
        /**
         * 启动任务配置Activity
         */
        fun startForTask(
            context: Context, 
            editData: String? = null, 
            editIndex: Int? = null
        ) {
            val intent = Intent(context, UnifiedConfigurationActivity::class.java).apply {
                putExtra(EXTRA_CONFIGURATION_MODE, ConfigurationMode.TASK.name)
                editData?.let { putExtra(EXTRA_EDIT_DATA, it) }
                editIndex?.let { putExtra(EXTRA_EDIT_INDEX, it) }
            }
            context.startActivity(intent)
        }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 获取传入的参数
        val configurationModeString = intent.getStringExtra(EXTRA_CONFIGURATION_MODE)
        val editData = intent.getStringExtra(EXTRA_EDIT_DATA)
        val editIndex = intent.getIntExtra(EXTRA_EDIT_INDEX, -1).takeIf { it != -1 }
        
        // 解析配置模式
        val configurationMode = try {
            ConfigurationMode.valueOf(configurationModeString ?: ConfigurationMode.TRIGGER_CONDITION.name)
        } catch (e: IllegalArgumentException) {
            ConfigurationMode.TRIGGER_CONDITION
        }
        
        setContent {
            QuickCommandsTheme {
                UnifiedConfigurationActivityContent(
                    configurationMode = configurationMode,
                    editData = editData,
                    editIndex = editIndex,
                    detailedConfigurationLauncher = detailedConfigurationLauncher,
                    onFinish = { finish() },
                    onConfigurationComplete = { navigationKey, editIdx ->
                        android.util.Log.d("UnifiedConfigurationActivity", "onConfigurationComplete called: navigationKey=$navigationKey, editIdx=$editIdx")

                        // 通过Intent返回结果
                        val resultIntent = Intent().apply {
                            putExtra("navigation_key", navigationKey)
                            putExtra("edit_index", editIdx)
                            putExtra("config_mode", configurationMode.name)
                        }

                        android.util.Log.d("UnifiedConfigurationActivity", "Setting result: RESULT_OK with intent extras")
                        setResult(Activity.RESULT_OK, resultIntent)

                        android.util.Log.d("UnifiedConfigurationActivity", "Finishing UnifiedConfigurationActivity")
                        finish()
                    }
                )
            }
        }
    }
}

/**
 * 统一配置Activity的内容组件
 */
@Composable
private fun UnifiedConfigurationActivityContent(
    configurationMode: ConfigurationMode,
    editData: String?,
    editIndex: Int?,
    detailedConfigurationLauncher: androidx.activity.result.ActivityResultLauncher<Intent>,
    onFinish: () -> Unit,
    onConfigurationComplete: (String, Int?) -> Unit
) {
    val context = LocalContext.current
    
    UnifiedConfigurationContent(
        configurationMode = configurationMode,
        initialConfigData = editData,
        editIndex = editIndex,
        onNavigateBack = onFinish,
        onNavigateToDetailedConfiguration = { configurationItem, initialData, editIdx ->
            // 使用ActivityResultLauncher启动详细配置Activity
            android.util.Log.d("UnifiedConfigurationActivity", "Launching DetailedConfigurationActivity with launcher")
            val intent = Intent(context, DetailedConfigurationActivity::class.java).apply {
                putExtra("configuration_mode", configurationMode.name)
                putExtra("item_type", configurationItem.id)
                initialData?.let { putExtra("edit_data", it) }
                editIdx?.let { putExtra("edit_index", it) }
            }
            detailedConfigurationLauncher.launch(intent)
        },
        onItemConfigured = { configuredItem, editIdx ->
            android.util.Log.d("UnifiedConfigurationActivity", "onItemConfigured called: configuredItem=$configuredItem, editIdx=$editIdx")

            // 配置完成，保存配置数据到NavigationDataStorageManager
            val navigationDataManager = NavigationDataStorageManager(context)
            val navigationKey = when (configurationMode) {
                ConfigurationMode.TRIGGER_CONDITION -> {
                    val key = "trigger_condition_edit_${System.currentTimeMillis()}"
                    val saveResult = navigationDataManager.saveConditionEditData(
                        key,
                        configuredItem as SharedTriggerCondition,
                        editIdx
                    )
                    android.util.Log.d("UnifiedConfigurationActivity", "Saved trigger condition data: key=$key, success=$saveResult")
                    key
                }
                ConfigurationMode.ABORT_CONDITION -> {
                    val key = "abort_condition_edit_${System.currentTimeMillis()}"
                    val saveResult = navigationDataManager.saveConditionEditData(
                        key,
                        configuredItem as SharedTriggerCondition,
                        editIdx
                    )
                    android.util.Log.d("UnifiedConfigurationActivity", "Saved abort condition data: key=$key, success=$saveResult")
                    key
                }
                ConfigurationMode.TASK -> {
                    val key = "task_edit_${System.currentTimeMillis()}"
                    val saveResult = navigationDataManager.saveTaskEditData(
                        key,
                        configuredItem as SharedTask,
                        editIdx
                    )
                    android.util.Log.d("UnifiedConfigurationActivity", "Saved task data: key=$key, success=$saveResult")
                    key
                }
            }

            android.util.Log.d("UnifiedConfigurationActivity", "About to call onConfigurationComplete with navigationKey=$navigationKey, editIdx=$editIdx")

            // 通过回调返回结果并立即关闭Activity
            onConfigurationComplete(navigationKey, editIdx)
        }
    )
}
