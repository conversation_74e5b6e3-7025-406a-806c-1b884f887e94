package com.weinuo.quickcommands22.model

import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.pm.ApplicationInfo
import android.content.pm.PackageManager
import android.content.pm.ResolveInfo
import android.os.Parcelable
import android.util.Log
import androidx.compose.runtime.Immutable
import kotlinx.parcelize.Parcelize

/**
 * 分享目标信息
 * 
 * 表示一个具体的分享目标，包含应用信息和具体的Activity信息
 * 用于分享文本功能，支持显示具体的分享选项（如"发送给好友"、"分享到朋友圈"）
 */
@Parcelize
@Immutable
data class ShareTarget(
    /** 应用包名 */
    val packageName: String,
    /** 应用名称 */
    val appName: String,
    /** Activity类名 */
    val activityName: String,
    /** 分享目标显示标签（如"发送给好友"、"分享到朋友圈"） */
    val targetLabel: String,
    /** 是否为系统应用 */
    val isSystemApp: Boolean = false
) : Parcelable {
    
    /**
     * 获取完整的ComponentName
     */
    fun getComponentName(): ComponentName {
        return ComponentName(packageName, activityName)
    }
    
    /**
     * 获取显示文本
     * 格式：应用名 - 分享选项
     */
    fun getDisplayText(): String {
        return if (targetLabel.isNotEmpty() && targetLabel != appName) {
            "$appName - $targetLabel"
        } else {
            appName
        }
    }
    
    companion object {
        private const val TAG = "ShareTarget"
        
        /**
         * 从ResolveInfo创建ShareTarget
         */
        fun fromResolveInfo(resolveInfo: ResolveInfo, packageManager: PackageManager): ShareTarget? {
            return try {
                val activityInfo = resolveInfo.activityInfo
                val packageName = activityInfo.packageName
                val activityName = activityInfo.name
                
                // 获取应用名称
                val appInfo = packageManager.getApplicationInfo(packageName, 0)
                val appName = packageManager.getApplicationLabel(appInfo).toString()
                
                // 获取Activity标签
                val targetLabel = resolveInfo.loadLabel(packageManager)?.toString() ?: appName
                
                // 判断是否为系统应用
                val isSystemApp = (appInfo.flags and ApplicationInfo.FLAG_SYSTEM) != 0
                
                ShareTarget(
                    packageName = packageName,
                    appName = appName,
                    activityName = activityName,
                    targetLabel = targetLabel,
                    isSystemApp = isSystemApp
                )
            } catch (e: Exception) {
                Log.w(TAG, "Failed to create ShareTarget from ResolveInfo", e)
                null
            }
        }
        
        /**
         * 查询所有支持分享文本的目标
         */
        fun queryShareTextTargets(context: Context): List<ShareTarget> {
            return try {
                val packageManager = context.packageManager
                val shareIntent = Intent().apply {
                    action = Intent.ACTION_SEND
                    type = "text/plain"
                }
                
                val resolveInfos = packageManager.queryIntentActivities(shareIntent, PackageManager.MATCH_DEFAULT_ONLY)
                
                resolveInfos.mapNotNull { resolveInfo ->
                    fromResolveInfo(resolveInfo, packageManager)
                }.sortedWith(
                    compareBy<ShareTarget> { it.isSystemApp }
                        .thenBy { it.appName }
                        .thenBy { it.targetLabel }
                )
            } catch (e: Exception) {
                Log.e(TAG, "Failed to query share text targets", e)
                emptyList()
            }
        }
    }
}

/**
 * 分享目标选择模式
 */
enum class ShareTargetSelectionMode {
    SINGLE  // 单选模式
}
