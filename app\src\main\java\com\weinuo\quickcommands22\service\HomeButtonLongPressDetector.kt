package com.weinuo.quickcommands22.service

import android.content.Context
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.KeyEvent
import com.weinuo.quickcommands22.model.ManualTriggerCondition
import com.weinuo.quickcommands22.model.ManualTriggerType
import com.weinuo.quickcommands22.repository.ConditionRepository
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * 主屏幕按钮长按检测器
 *
 * 负责检测主屏幕按钮的长按操作，并触发相应的条件。
 * 需要在辅助服务中使用，通过监听按键事件来实现。
 *
 * 功能特性：
 * - 检测主屏幕按钮长按事件
 * - 支持可配置的长按阈值
 * - 防止误触发机制
 * - 与条件评估器集成
 *
 * @param context Android上下文
 * @param conditionRepository 条件仓库，用于获取相关条件
 */
class HomeButtonLongPressDetector(
    private val context: Context,
    private val conditionRepository: ConditionRepository
) {

    companion object {
        private const val TAG = "HomeButtonLongPressDetector"

        // 默认长按阈值（毫秒）
        private const val DEFAULT_LONG_PRESS_THRESHOLD = 1000L

        // 最小长按阈值（防止误触发）
        private const val MIN_LONG_PRESS_THRESHOLD = 500L

        // 最大长按阈值
        private const val MAX_LONG_PRESS_THRESHOLD = 5000L
    }

    // 主线程Handler，用于处理长按检测
    private val mainHandler = Handler(Looper.getMainLooper())

    // 协程作用域
    private val coroutineScope = CoroutineScope(Dispatchers.Main)

    // 当前按下状态
    private var isHomeButtonPressed = false

    // 按下开始时间
    private var pressStartTime = 0L

    // 长按检测任务
    private var longPressRunnable: Runnable? = null

    // 已注册的主屏幕按钮长按条件
    private val registeredConditions = mutableSetOf<ManualTriggerCondition>()

    /**
     * 主屏幕按钮长按回调接口
     */
    interface HomeButtonLongPressCallback {
        fun onHomeButtonLongPress(condition: ManualTriggerCondition)
    }

    // 注册的回调
    private val callbacks = mutableSetOf<HomeButtonLongPressCallback>()

    /**
     * 注册主屏幕按钮长按回调
     */
    fun registerCallback(callback: HomeButtonLongPressCallback) {
        callbacks.add(callback)
        Log.d(TAG, "注册主屏幕按钮长按回调，当前回调数量: ${callbacks.size}")
    }

    /**
     * 取消注册主屏幕按钮长按回调
     */
    fun unregisterCallback(callback: HomeButtonLongPressCallback) {
        callbacks.remove(callback)
        Log.d(TAG, "取消注册主屏幕按钮长按回调，当前回调数量: ${callbacks.size}")
    }

    /**
     * 处理按键事件
     *
     * @param keyCode 按键代码
     * @param event 按键事件
     * @return 是否处理了该事件
     */
    fun onKeyEvent(keyCode: Int, event: KeyEvent): Boolean {
        // 只处理主屏幕按钮
        if (keyCode != KeyEvent.KEYCODE_HOME) {
            return false
        }

        when (event.action) {
            KeyEvent.ACTION_DOWN -> {
                handleHomeButtonDown(event)
                return true
            }
            KeyEvent.ACTION_UP -> {
                handleHomeButtonUp(event)
                return true
            }
        }

        return false
    }

    /**
     * 处理主屏幕按钮按下事件
     */
    private fun handleHomeButtonDown(event: KeyEvent) {
        // 防止重复按下
        if (isHomeButtonPressed) {
            return
        }

        isHomeButtonPressed = true
        pressStartTime = event.eventTime

        Log.d(TAG, "主屏幕按钮按下，开始时间: $pressStartTime")

        // 加载所有相关的长按条件
        loadHomeButtonLongPressConditions()

        // 为每个条件设置长按检测任务
        registeredConditions.forEach { condition ->
            val threshold = condition.homeButtonLongPressThreshold.coerceIn(
                MIN_LONG_PRESS_THRESHOLD,
                MAX_LONG_PRESS_THRESHOLD
            )

            val runnable = Runnable {
                if (isHomeButtonPressed) {
                    Log.d(TAG, "检测到主屏幕按钮长按，阈值: ${threshold}ms")
                    triggerLongPressCondition(condition)
                }
            }

            mainHandler.postDelayed(runnable, threshold)
            longPressRunnable = runnable
        }
    }

    /**
     * 处理主屏幕按钮释放事件
     */
    private fun handleHomeButtonUp(event: KeyEvent) {
        if (!isHomeButtonPressed) {
            return
        }

        val pressDuration = event.eventTime - pressStartTime
        Log.d(TAG, "主屏幕按钮释放，按下时长: ${pressDuration}ms")

        // 取消长按检测任务
        longPressRunnable?.let { runnable ->
            mainHandler.removeCallbacks(runnable)
        }
        longPressRunnable = null

        // 重置状态
        isHomeButtonPressed = false
        pressStartTime = 0L
    }

    /**
     * 加载主屏幕按钮长按条件
     */
    private fun loadHomeButtonLongPressConditions() {
        coroutineScope.launch {
            try {
                val allConditions = conditionRepository.loadAllConditions()
                val homeButtonConditions = allConditions.filterIsInstance<ManualTriggerCondition>()
                    .filter { it.triggerType == ManualTriggerType.HOME_BUTTON_LONG_PRESS }

                registeredConditions.clear()
                registeredConditions.addAll(homeButtonConditions)

                Log.d(TAG, "加载了 ${registeredConditions.size} 个主屏幕按钮长按条件")
            } catch (e: Exception) {
                Log.e(TAG, "加载主屏幕按钮长按条件失败", e)
            }
        }
    }

    /**
     * 触发长按条件
     */
    private fun triggerLongPressCondition(condition: ManualTriggerCondition) {
        Log.d(TAG, "触发主屏幕按钮长按条件: ${condition.id}")

        // 通知所有注册的回调
        callbacks.forEach { callback ->
            try {
                callback.onHomeButtonLongPress(condition)
            } catch (e: Exception) {
                Log.e(TAG, "主屏幕按钮长按回调执行失败", e)
            }
        }
    }

    /**
     * 清理资源
     */
    fun cleanup() {
        // 取消所有待执行的任务
        longPressRunnable?.let { runnable ->
            mainHandler.removeCallbacks(runnable)
        }
        longPressRunnable = null

        // 清理状态
        isHomeButtonPressed = false
        pressStartTime = 0L
        registeredConditions.clear()
        callbacks.clear()

        Log.d(TAG, "主屏幕按钮长按检测器已清理")
    }
}
