package com.weinuo.quickcommands22.service

import android.app.Service

import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.IBinder
import android.util.Log
import com.weinuo.quickcommands22.data.AppRepository
import com.weinuo.quickcommands22.data.SettingsRepository

import com.weinuo.quickcommands22.model.CommunicationStateCondition
import com.weinuo.quickcommands22.monitoring.CommunicationStateMonitor
import com.weinuo.quickcommands22.monitoring.ConnectionStateMonitor
import com.weinuo.quickcommands22.monitoring.TimeConditionMonitor
import com.weinuo.quickcommands22.monitoring.MemoryStateMonitor
import com.weinuo.quickcommands22.monitoring.DeviceEventMonitor
import com.weinuo.quickcommands22.monitoring.AppStateConditionMonitor
import com.weinuo.quickcommands22.monitoring.SensorStateMonitor
import com.weinuo.quickcommands22.monitoring.BatteryStateMonitor
import com.weinuo.quickcommands22.receiver.PackageReceiver
import com.weinuo.quickcommands22.smartreminder.SmartReminderManager
import com.weinuo.quickcommands22.utils.NotificationHelper

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import java.util.Calendar

// 新增导入：条件指令相关
import com.weinuo.quickcommands22.data.QuickCommandRepository
import com.weinuo.quickcommands22.model.QuickCommand
import com.weinuo.quickcommands22.execution.SharedExecutionHandler
import com.weinuo.quickcommands22.execution.SharedConditionEvaluator
import com.weinuo.quickcommands22.model.SharedTriggerCondition
import com.weinuo.quickcommands22.model.DeviceEventCondition
import com.weinuo.quickcommands22.model.DeviceEventType
import com.weinuo.quickcommands22.model.TimeBasedCondition
import com.weinuo.quickcommands22.model.AppStateCondition
import com.weinuo.quickcommands22.model.AppStateCategoryType
import com.weinuo.quickcommands22.model.AppStateType
import com.weinuo.quickcommands22.model.ConnectionStateCondition
import com.weinuo.quickcommands22.model.SensorStateCondition
import com.weinuo.quickcommands22.model.BatteryStateCondition


/**
 * 快捷指令监控服务，负责条件指令的监控和执行
 */
class QuickCommandsService : Service() {

    private val serviceScope = CoroutineScope(Dispatchers.Default + Job())
    private lateinit var appRepository: AppRepository
    private lateinit var settingsRepository: SettingsRepository
    private lateinit var packageReceiver: PackageReceiver
    private lateinit var communicationStateMonitor: CommunicationStateMonitor
    private lateinit var connectionStateMonitor: ConnectionStateMonitor
    private lateinit var timeConditionMonitor: TimeConditionMonitor
    private lateinit var memoryStateMonitor: MemoryStateMonitor
    private lateinit var deviceEventMonitor: DeviceEventMonitor
    private lateinit var appStateConditionMonitor: AppStateConditionMonitor
    private lateinit var sensorStateMonitor: SensorStateMonitor
    private lateinit var batteryStateMonitor: BatteryStateMonitor

    // 智慧提醒管理器
    private lateinit var smartReminderManager: SmartReminderManager



    // 条件指令相关属性
    private lateinit var quickCommandRepository: QuickCommandRepository
    private lateinit var executionHandler: SharedExecutionHandler
    private lateinit var conditionEvaluator: SharedConditionEvaluator

    // 广播接收器已移至各个监听器中

    // 监控任务（已移除，使用新的监听器替代）

    // 执行中的指令集合
    private val executingCommands = mutableSetOf<String>()

    // 已触发的延迟条件集合（用于一次性触发条件）
    private val triggeredDelayedConditions = mutableSetOf<String>()

    // 前台服务状态标记
    private var isForegroundServiceStarted = false

    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "QuickCommandsService onCreate started")

        // 立即启动前台服务，避免超时
        // 这必须在任何其他操作之前完成，使用最简单的方式
        startForegroundServiceImmediately()

        // 在后台线程中进行其他初始化，避免阻塞前台服务启动
        serviceScope.launch {
            try {
                Log.d(TAG, "Starting background initialization...")

                appRepository = AppRepository(applicationContext)
                settingsRepository = SettingsRepository(applicationContext)

                // 清理过期的网络状态数据
                settingsRepository.cleanupExpiredNetworkState()

                // 初始化条件指令相关组件（使用单例Repository）
                quickCommandRepository = QuickCommandRepository.getInstance(applicationContext)
                executionHandler = SharedExecutionHandler(applicationContext)
                conditionEvaluator = SharedConditionEvaluator(applicationContext)

                // 预初始化条件状态以避免首次检查无法触发的问题
                initializeConditionStates()

                // 强制更新应用状态以提高检测准确性
                conditionEvaluator.forceUpdateAppState()

                // 屏幕状态接收器已删除（主功能相关）

                // 初始化并注册应用安装/卸载接收器
                packageReceiver = PackageReceiver(
                    context = applicationContext,
                    appRepository = appRepository,
                    settingsRepository = settingsRepository,
                    onAppStateConditionTriggered = { condition, eventData ->
                        // 处理应用状态条件触发
                        handleAppStateConditionTriggered(condition, eventData)
                    }
                )
                packageReceiver.register()

                // 初始化通信状态监听器
                communicationStateMonitor = CommunicationStateMonitor(applicationContext) { condition, eventData ->
                    // 处理通信状态条件触发
                    handleCommunicationConditionTriggered(condition, eventData)
                }

                // 初始化连接状态监听器
                connectionStateMonitor = ConnectionStateMonitor(applicationContext) { condition, eventData ->
                    // 处理连接状态条件触发
                    handleConnectionConditionTriggered(condition, eventData)
                }

                // 初始化时间条件监听器
                timeConditionMonitor = TimeConditionMonitor(applicationContext) { condition, eventData ->
                    // 处理时间条件触发
                    handleTimeConditionTriggered(condition, eventData)
                }

                // 初始化内存状态监听器
                memoryStateMonitor = MemoryStateMonitor(applicationContext) { condition, eventData ->
                    // 处理内存状态条件触发
                    handleMemoryConditionTriggered(condition, eventData)
                }

                // 初始化设备事件监听器
                deviceEventMonitor = DeviceEventMonitor(applicationContext) { condition, eventData ->
                    // 处理设备事件条件触发
                    handleDeviceEventConditionTriggered(condition, eventData)
                }

                // 初始化应用状态条件监听器
                appStateConditionMonitor = AppStateConditionMonitor(applicationContext) { condition, eventData ->
                    // 处理应用状态条件触发
                    handleAppStateConditionTriggered(condition, eventData)
                }

                // 初始化传感器状态监听器
                sensorStateMonitor = SensorStateMonitor(applicationContext) { condition, eventData ->
                    // 处理传感器状态条件触发
                    handleSensorStateConditionTriggered(condition, eventData)
                }

                // 初始化电池状态监听器
                batteryStateMonitor = BatteryStateMonitor(applicationContext) { condition, eventData ->
                    // 处理电池状态条件触发
                    handleBatteryStateConditionTriggered(condition, eventData)
                }

                // 初始化智慧提醒管理器
                smartReminderManager = SmartReminderManager(applicationContext)
                smartReminderManager.startManager()

                // 启动条件指令监控
                startConditionalCommandMonitoring()

                // 启动快捷指令数据流监听（确保数据变化时自动更新监听器）
                startQuickCommandDataFlowMonitoring()

            } catch (e: Exception) {
                // 即使初始化失败，也要保持前台服务运行
            }
        }
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Log.d(TAG, "QuickCommandsService started")

        // 确保前台服务已启动（防止超时异常）
        try {
            // 如果服务已经在运行，确保前台服务状态正确
            startForegroundServiceImmediately()
        } catch (e: Exception) {
            Log.e(TAG, "Error ensuring foreground service in onStartCommand", e)
        }

        // 处理特定的 Intent 动作（来自原ConditionalCommandService）
        intent?.action?.let { action ->
            when (action) {
                "RECHECK_MEMORY_MONITORING" -> {
                    recheckMemoryMonitoring()
                }
                "RECHECK_DEVICE_EVENT_MONITORING" -> {
                    recheckDeviceEventMonitoring()
                }
                "RECHECK_APP_STATE_MONITORING" -> {
                    recheckAppStateMonitoring()
                }
                "RECHECK_BATTERY_STATE_MONITORING" -> {
                    recheckBatteryStateMonitoring()
                }
                "RECHECK_SMART_REMINDER_MONITORING" -> {
                    recheckSmartReminderMonitoring()
                }
            }
        }

        return START_STICKY
    }

    override fun onBind(intent: Intent?): IBinder? {
        return null
    }

    /**
     * 立即启动前台服务，使用最简单的方式避免超时
     */
    private fun startForegroundServiceImmediately() {
        // 避免重复启动前台服务
        if (isForegroundServiceStarted) {
            Log.d(TAG, "Foreground service already started, skipping")
            return
        }

        try {
            Log.d(TAG, "Starting foreground service immediately...")

            // 使用最简单的通知创建方式，避免复杂的初始化
            val notification = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                // Android 8.0+ 需要通知渠道
                val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as android.app.NotificationManager

                // 创建通知渠道（如果不存在）
                val channelId = "foreground_service_channel"
                val channel = android.app.NotificationChannel(
                    channelId,
                    "前台服务",
                    android.app.NotificationManager.IMPORTANCE_LOW
                ).apply {
                    description = "前台服务运行状态通知"
                    setShowBadge(false)
                    enableLights(false)
                    enableVibration(false)
                    setSound(null, null)
                }
                notificationManager.createNotificationChannel(channel)

                // 创建通知
                android.app.Notification.Builder(this, channelId)
                    .setSmallIcon(android.R.drawable.ic_dialog_info)
                    .setContentTitle("快捷指令服务")
                    .setContentText("正在监控快捷指令条件")
                    .setOngoing(true)
                    .setAutoCancel(false)
                    .setShowWhen(false)
                    .build()
            } else {
                // Android 7.1 及以下
                @Suppress("DEPRECATION")
                android.app.Notification.Builder(this)
                    .setSmallIcon(android.R.drawable.ic_dialog_info)
                    .setContentTitle("快捷指令服务")
                    .setContentText("正在监控快捷指令条件")
                    .setOngoing(true)
                    .setAutoCancel(false)
                    .setShowWhen(false)
                    .build()
            }

            startForeground(NotificationHelper.QUICK_COMMANDS_NOTIFICATION_ID, notification)
            isForegroundServiceStarted = true
            Log.d(TAG, "Quick commands service started as foreground service successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Error starting foreground service immediately", e)
            // 最后的备用方案：使用系统默认图标和最简单的通知
            try {
                @Suppress("DEPRECATION")
                val fallbackNotification = android.app.Notification.Builder(this)
                    .setSmallIcon(android.R.drawable.ic_dialog_info)
                    .setContentTitle("服务运行中")
                    .setContentText("快捷指令服务")
                    .build()
                startForeground(NotificationHelper.QUICK_COMMANDS_NOTIFICATION_ID, fallbackNotification)
                isForegroundServiceStarted = true
                Log.d(TAG, "Fallback foreground service started")
            } catch (fallbackException: Exception) {
                Log.e(TAG, "All foreground service attempts failed", fallbackException)
            }
        }
    }

    /**
     * 启动前台服务（保留原方法用于后续优化通知）
     */
    private fun startForegroundService() {
        try {
            Log.d(TAG, "Starting foreground service...")

            // 确保通知渠道已初始化
            NotificationHelper.initializeNotificationChannels(applicationContext)

            val notification = NotificationHelper.createForegroundServiceNotification(
                context = applicationContext,
                title = "快捷指令服务",
                text = "正在监控快捷指令条件"
            )

            startForeground(NotificationHelper.QUICK_COMMANDS_NOTIFICATION_ID, notification)
            Log.d(TAG, "Quick commands service started as foreground service successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Error starting foreground service", e)
        }
    }















    override fun onDestroy() {
        super.onDestroy()

        // 重置前台服务状态标记
        isForegroundServiceStarted = false

        // 屏幕状态接收器已删除（主功能相关）
        // 注销应用安装/卸载接收器
        if (::packageReceiver.isInitialized) {
            packageReceiver.unregister()
        }
        // 清理通信状态监听器
        if (::communicationStateMonitor.isInitialized) {
            communicationStateMonitor.cleanup()
        }
        // 清理连接状态监听器
        if (::connectionStateMonitor.isInitialized) {
            connectionStateMonitor.cleanup()
        }
        // 清理时间条件监听器
        if (::timeConditionMonitor.isInitialized) {
            timeConditionMonitor.stopMonitoring()
        }
        // 清理内存状态监听器
        if (::memoryStateMonitor.isInitialized) {
            memoryStateMonitor.stopMonitoring()
        }
        // 清理应用状态条件监听器
        if (::appStateConditionMonitor.isInitialized) {
            appStateConditionMonitor.stopMonitoring()
        }
        // 清理传感器状态监听器
        if (::sensorStateMonitor.isInitialized) {
            sensorStateMonitor.cleanup()
        }
        // 清理电池状态监听器
        if (::batteryStateMonitor.isInitialized) {
            batteryStateMonitor.stopMonitoring()
        }
        // 清理智慧提醒管理器
        if (::smartReminderManager.isInitialized) {
            smartReminderManager.stopManager()
        }

        // 清理条件指令相关资源

        // 广播接收器注销已移至各个监听器中

        // 监控任务已由新的监听器管理

        // 取消服务协程
        serviceScope.cancel()
    }

    /**
     * 处理通信状态条件触发
     */
    private fun handleCommunicationConditionTriggered(condition: CommunicationStateCondition, eventData: Map<String, Any>) {
        serviceScope.launch {
            try {
                // 触发相应的快捷指令
                handleTriggerCondition(condition)

            } catch (e: Exception) {
            }
        }
    }

    /**
     * 处理连接状态条件触发
     */
    private fun handleConnectionConditionTriggered(condition: ConnectionStateCondition, eventData: Map<String, Any>) {
        serviceScope.launch {
            try {
                // 触发相应的快捷指令
                handleTriggerCondition(condition)

            } catch (e: Exception) {
            }
        }
    }

    /**
     * 处理时间条件触发
     */
    private fun handleTimeConditionTriggered(condition: TimeBasedCondition, eventData: Map<String, Any>) {
        serviceScope.launch {
            try {
                // 触发相应的快捷指令
                handleTriggerCondition(condition)

            } catch (e: Exception) {
            }
        }
    }

    /**
     * 处理内存状态条件触发
     */
    private fun handleMemoryConditionTriggered(condition: DeviceEventCondition, eventData: Map<String, Any>) {
        serviceScope.launch {
            try {
                // 触发相应的快捷指令
                handleTriggerCondition(condition)

            } catch (e: Exception) {
            }
        }
    }

    /**
     * 处理设备事件条件触发
     */
    private fun handleDeviceEventConditionTriggered(condition: DeviceEventCondition, eventData: Map<String, Any>) {
        serviceScope.launch {
            try {
                // 触发相应的快捷指令
                handleTriggerCondition(condition)

            } catch (e: Exception) {
            }
        }
    }

    /**
     * 处理应用状态条件触发
     */
    private fun handleAppStateConditionTriggered(condition: AppStateCondition, eventData: Map<String, Any>) {
        serviceScope.launch {
            try {

                // 检查条件来源，决定如何处理
                if (condition.categoryType == AppStateCategoryType.PACKAGE_MANAGEMENT) {
                    // 包管理类型的条件来自PackageReceiver，需要转发给AppStateConditionMonitor进行匹配
                    val packageName = eventData["packageName"] as? String ?: ""
                    val appName = eventData["appName"] as? String ?: ""

                    if (packageName.isNotEmpty() && ::appStateConditionMonitor.isInitialized) {
                        Log.d(TAG, "Forwarding package management condition to AppStateConditionMonitor: ${condition.stateType} for $packageName")
                        appStateConditionMonitor.triggerPackageManagementCondition(
                            condition.stateType,
                            packageName,
                            appName
                        )
                    } else {
                        Log.w(TAG, "Cannot forward package management condition: packageName=$packageName, monitor initialized=${::appStateConditionMonitor.isInitialized}")
                    }
                } else {
                    // 对于其他类型的应用状态条件（前台/后台/状态变化/启动/关闭），直接触发快捷指令
                    Log.d(TAG, "Directly triggering app state condition: ${condition.getDescription()}")
                    // 只有后台时间条件需要传递事件数据（现在在STATE_CHANGE类别下）
                    if (condition.categoryType == AppStateCategoryType.STATE_CHANGE &&
                        condition.stateType == AppStateType.BACKGROUND_TIME_EXCEEDED) {
                        handleTriggerCondition(condition, eventData)
                    } else {
                        handleTriggerCondition(condition)
                    }
                }

            } catch (e: Exception) {
                Log.e(TAG, "Error handling app state condition triggered", e)
            }
        }
    }

    /**
     * 处理传感器状态条件触发
     */
    private fun handleSensorStateConditionTriggered(condition: SensorStateCondition, eventData: Map<String, Any>) {
        serviceScope.launch {
            try {
                // 触发相应的快捷指令
                handleTriggerCondition(condition)

            } catch (e: Exception) {
            }
        }
    }

    /**
     * 处理电池状态条件触发
     */
    private fun handleBatteryStateConditionTriggered(condition: BatteryStateCondition, eventData: Map<String, Any>) {
        serviceScope.launch {
            try {
                // 触发相应的快捷指令
                handleTriggerCondition(condition)

            } catch (e: Exception) {
            }
        }
    }

    /**
     * 获取通信状态监听器
     * 供外部组件使用
     */
    fun getCommunicationStateMonitor(): CommunicationStateMonitor? {
        return if (::communicationStateMonitor.isInitialized) {
            communicationStateMonitor
        } else {
            null
        }
    }

    /**
     * 获取连接状态监听器
     * 供外部组件使用
     */
    fun getConnectionStateMonitor(): ConnectionStateMonitor? {
        return if (::connectionStateMonitor.isInitialized) {
            connectionStateMonitor
        } else {
            null
        }
    }

    // ==================== 条件指令监控相关方法 ====================

    /**
     * 启动快捷指令数据流监听
     * 监听快捷指令数据变化，自动重新注册条件监听器
     */
    private fun startQuickCommandDataFlowMonitoring() {
        serviceScope.launch {
            try {
                // 监听快捷指令数据流变化
                quickCommandRepository.quickCommands.collect { commands ->
                    // 延迟一小段时间，避免频繁重新注册（防抖处理）
                    delay(500)

                    // 重新注册所有条件监听器
                    reregisterAllConditionMonitors(commands)
                }
            } catch (e: Exception) {
            }
        }
    }

    /**
     * 重新注册所有条件监听器
     * 当快捷指令数据发生变化时调用
     *
     * @param commands 最新的快捷指令列表
     */
    private suspend fun reregisterAllConditionMonitors(commands: List<QuickCommand>) {
        try {
            Log.d(TAG, "Reregistering all condition monitors for ${commands.size} commands")

            val enabledCommands = commands.filter { it.isEnabled }
            Log.d(TAG, "Found ${enabledCommands.size} enabled commands")

            // 重新注册时间条件监听器
            reregisterTimeConditionMonitoring(enabledCommands)

            // 重新注册内存状态监听器
            reregisterMemoryStateMonitoring(enabledCommands)

            // 重新注册设备事件监听器
            reregisterDeviceEventMonitoring(enabledCommands)

            // 重新注册应用状态条件监听器
            reregisterAppStateConditionMonitoring(enabledCommands)

            // 重新注册连接状态监听器
            reregisterConnectionStateMonitoring(enabledCommands)

            // 重新注册通信状态监听器
            reregisterCommunicationStateMonitoring(enabledCommands)

            // 重新注册传感器状态监听器
            reregisterSensorStateMonitoring(enabledCommands)

            // 重新注册电池状态监听器
            reregisterBatteryStateMonitoring(enabledCommands)

            Log.d(TAG, "All condition monitors reregistered successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Error reregistering condition monitors", e)
        }
    }

    /**
     * 启动条件指令监控
     */
    private fun startConditionalCommandMonitoring() {
        serviceScope.launch {
            try {
                Log.d(TAG, "Starting conditional command monitoring...")

                // 广播接收器注册已移至各个监听器中

                // 启动时间条件监控（仅在有时间条件时）
                startTimeConditionMonitoringIfNeeded()

                // 启动内存状态监控（仅在有内存条件时）
                startMemoryStateMonitoringIfNeeded()

                // 启动设备事件监控（仅在有设备事件条件时）
                startDeviceEventMonitoringIfNeeded()

                // 启动应用状态监控（仅在有应用状态条件时）
                startAppStateConditionMonitoringIfNeeded()

                // 启动连接状态监控（仅在有连接状态条件时）
                startConnectionStateMonitoringIfNeeded()

                // 启动通信状态监控（仅在有通信状态条件时）
                startCommunicationStateMonitoringIfNeeded()

                // 启动传感器状态监控（仅在有传感器状态条件时）
                startSensorStateMonitoringIfNeeded()

                // 启动电池状态监控（仅在有电池状态条件时）
                startBatteryStateMonitoringIfNeeded()

                Log.d(TAG, "Conditional command monitoring started successfully")
            } catch (e: Exception) {
                Log.e(TAG, "Error starting conditional command monitoring", e)
            }
        }
    }

    // 广播接收器注册已移至各个监听器中

    // 时间检查任务已由TimeConditionMonitor处理

    // 时间条件检查已由TimeConditionMonitor处理

    // 时间条件触发检查已由TimeConditionMonitor处理

    /**
     * 重新注册时间条件监听器
     * 用于数据变化时的动态重新注册
     *
     * @param enabledCommands 启用的快捷指令列表
     */
    private suspend fun reregisterTimeConditionMonitoring(enabledCommands: List<QuickCommand>) {
        try {
            val hasTimeConditions = enabledCommands.any { command ->
                command.triggerConditions.filterIsInstance<TimeBasedCondition>().isNotEmpty() ||
                command.abortConditions.filterIsInstance<TimeBasedCondition>().isNotEmpty()
            }

            if (hasTimeConditions) {
                Log.d(TAG, "Reregistering time condition monitoring")

                // 清除现有条件并重新注册
                timeConditionMonitor.clearAllConditions()
                timeConditionMonitor.startMonitoring()

                // 注册所有时间条件
                enabledCommands.forEach { command ->
                    command.triggerConditions.filterIsInstance<TimeBasedCondition>()
                        .forEach { timeConditionMonitor.registerCondition(it) }
                    command.abortConditions.filterIsInstance<TimeBasedCondition>()
                        .forEach { timeConditionMonitor.registerCondition(it) }
                }

                Log.d(TAG, "Time condition monitoring reregistered with ${timeConditionMonitor.getRegisteredConditionCount()} conditions")
            } else {
                Log.d(TAG, "No time conditions found, stopping time condition monitoring")
                timeConditionMonitor.stopMonitoring()
                timeConditionMonitor.clearAllConditions()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error reregistering time condition monitoring", e)
        }
    }

    /**
     * 检查是否需要启动时间条件监控
     * 只有在有时间相关条件时才启动监控，避免不必要的电量消耗
     */
    private fun startTimeConditionMonitoringIfNeeded() {
        serviceScope.launch {
            try {
                // 检查是否有启用的快捷指令包含时间条件
                val quickCommands = quickCommandRepository.quickCommands.first()
                    .filter { it.isEnabled }

                val hasTimeConditions = quickCommands.any { command ->
                    command.triggerConditions.filterIsInstance<TimeBasedCondition>().isNotEmpty() ||
                    command.abortConditions.filterIsInstance<TimeBasedCondition>().isNotEmpty()
                }

                if (hasTimeConditions) {
                    Log.d(TAG, "Found time conditions, starting time condition monitoring")

                    // 启动时间条件监听器
                    timeConditionMonitor.startMonitoring()

                    // 注册所有时间条件
                    quickCommands.forEach { command ->
                        command.triggerConditions.filterIsInstance<TimeBasedCondition>()
                            .forEach { timeConditionMonitor.registerCondition(it) }
                        command.abortConditions.filterIsInstance<TimeBasedCondition>()
                            .forEach { timeConditionMonitor.registerCondition(it) }
                    }

                    Log.d(TAG, "Time condition monitoring started with ${timeConditionMonitor.getRegisteredConditionCount()} conditions")
                } else {
                    Log.d(TAG, "No time conditions found, skipping time condition monitoring")
                    timeConditionMonitor.stopMonitoring()
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error checking time condition monitoring requirements", e)
            }
        }
    }

    /**
     * 重新注册内存状态监听器
     * 用于数据变化时的动态重新注册
     *
     * @param enabledCommands 启用的快捷指令列表
     */
    private suspend fun reregisterMemoryStateMonitoring(enabledCommands: List<QuickCommand>) {
        try {
            val hasMemoryConditions = enabledCommands.any { command ->
                command.triggerConditions.filterIsInstance<DeviceEventCondition>()
                    .any { it.eventType == DeviceEventType.MEMORY_STATE } ||
                command.abortConditions.filterIsInstance<DeviceEventCondition>()
                    .any { it.eventType == DeviceEventType.MEMORY_STATE }
            }

            if (hasMemoryConditions) {
                Log.d(TAG, "Reregistering memory state monitoring")

                // 清除现有条件并重新注册
                memoryStateMonitor.clearAllConditions()
                memoryStateMonitor.startMonitoring()

                // 注册所有内存条件
                enabledCommands.forEach { command ->
                    command.triggerConditions.filterIsInstance<DeviceEventCondition>()
                        .filter { it.eventType == DeviceEventType.MEMORY_STATE }
                        .forEach { memoryStateMonitor.registerCondition(it) }
                    command.abortConditions.filterIsInstance<DeviceEventCondition>()
                        .filter { it.eventType == DeviceEventType.MEMORY_STATE }
                        .forEach { memoryStateMonitor.registerCondition(it) }
                }

                Log.d(TAG, "Memory state monitoring reregistered with ${memoryStateMonitor.getRegisteredConditionCount()} conditions")
            } else {
                Log.d(TAG, "No memory conditions found, stopping memory state monitoring")
                memoryStateMonitor.stopMonitoring()
                memoryStateMonitor.clearAllConditions()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error reregistering memory state monitoring", e)
        }
    }

    /**
     * 检查是否需要启动内存状态监控
     * 只有在有内存相关条件时才启动监控，避免不必要的电量消耗
     */
    private fun startMemoryStateMonitoringIfNeeded() {
        serviceScope.launch {
            try {
                // 检查是否有启用的快捷指令包含内存条件
                val quickCommands = quickCommandRepository.quickCommands.first()
                    .filter { it.isEnabled }

                val hasMemoryConditions = quickCommands.any { command ->
                    command.triggerConditions.filterIsInstance<DeviceEventCondition>()
                        .any { it.eventType == DeviceEventType.MEMORY_STATE } ||
                    command.abortConditions.filterIsInstance<DeviceEventCondition>()
                        .any { it.eventType == DeviceEventType.MEMORY_STATE }
                }

                if (hasMemoryConditions) {
                    Log.d(TAG, "Found memory conditions, starting memory state monitoring")

                    // 启动内存状态监听器
                    memoryStateMonitor.startMonitoring()

                    // 注册所有内存条件
                    quickCommands.forEach { command ->
                        command.triggerConditions.filterIsInstance<DeviceEventCondition>()
                            .filter { it.eventType == DeviceEventType.MEMORY_STATE }
                            .forEach { memoryStateMonitor.registerCondition(it) }
                        command.abortConditions.filterIsInstance<DeviceEventCondition>()
                            .filter { it.eventType == DeviceEventType.MEMORY_STATE }
                            .forEach { memoryStateMonitor.registerCondition(it) }
                    }

                    Log.d(TAG, "Memory state monitoring started with ${memoryStateMonitor.getRegisteredConditionCount()} conditions")
                } else {
                    Log.d(TAG, "No memory conditions found, skipping memory state monitoring")
                    memoryStateMonitor.stopMonitoring()
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error checking memory state monitoring requirements", e)
            }
        }
    }

    /**
     * 重新注册设备事件监听器
     * 用于数据变化时的动态重新注册
     *
     * @param enabledCommands 启用的快捷指令列表
     */
    private suspend fun reregisterDeviceEventMonitoring(enabledCommands: List<QuickCommand>) {
        try {
            val hasDeviceEventConditions = enabledCommands.any { command ->
                command.triggerConditions.filterIsInstance<DeviceEventCondition>()
                    .any { it.eventType != DeviceEventType.MEMORY_STATE } ||
                command.abortConditions.filterIsInstance<DeviceEventCondition>()
                    .any { it.eventType != DeviceEventType.MEMORY_STATE }
            }

            if (hasDeviceEventConditions) {
                Log.d(TAG, "Reregistering device event monitoring")

                // 清除现有条件并重新注册
                deviceEventMonitor.clearAllConditions()
                deviceEventMonitor.startMonitoring()

                // 注册所有设备事件条件（除了内存状态）
                enabledCommands.forEach { command ->
                    command.triggerConditions.filterIsInstance<DeviceEventCondition>()
                        .filter { it.eventType != DeviceEventType.MEMORY_STATE }
                        .forEach { deviceEventMonitor.registerCondition(it) }
                    command.abortConditions.filterIsInstance<DeviceEventCondition>()
                        .filter { it.eventType != DeviceEventType.MEMORY_STATE }
                        .forEach { deviceEventMonitor.registerCondition(it) }
                }

                Log.d(TAG, "Device event monitoring reregistered with ${deviceEventMonitor.getRegisteredConditionCount()} conditions")
            } else {
                Log.d(TAG, "No device event conditions found, stopping device event monitoring")
                deviceEventMonitor.stopMonitoring()
                deviceEventMonitor.clearAllConditions()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error reregistering device event monitoring", e)
        }
    }

    /**
     * 重新注册应用状态条件监听器
     * 用于数据变化时的动态重新注册
     *
     * @param enabledCommands 启用的快捷指令列表
     */
    private suspend fun reregisterAppStateConditionMonitoring(enabledCommands: List<QuickCommand>) {
        try {
            val hasAppStateConditions = enabledCommands.any { command ->
                command.triggerConditions.filterIsInstance<AppStateCondition>().isNotEmpty() ||
                command.abortConditions.filterIsInstance<AppStateCondition>().isNotEmpty()
            }

            if (hasAppStateConditions) {
                Log.d(TAG, "Reregistering app state condition monitoring")

                // 清除现有条件并重新注册
                appStateConditionMonitor.clearAllConditions()
                appStateConditionMonitor.startMonitoring()

                // 注册所有应用状态条件
                var registeredCount = 0
                enabledCommands.forEach { command ->
                    command.triggerConditions.filterIsInstance<AppStateCondition>()
                        .forEach { condition ->
                            appStateConditionMonitor.registerCondition(condition)
                            registeredCount++
                        }
                    command.abortConditions.filterIsInstance<AppStateCondition>()
                        .forEach { condition ->
                            appStateConditionMonitor.registerCondition(condition)
                            registeredCount++
                        }
                }

                Log.d(TAG, "App state condition monitoring reregistered with $registeredCount conditions")
            } else {
                Log.d(TAG, "No app state conditions found, stopping app state condition monitoring")
                appStateConditionMonitor.stopMonitoring()
                appStateConditionMonitor.clearAllConditions()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error reregistering app state condition monitoring", e)
        }
    }

    /**
     * 检查是否需要启动设备事件监控
     * 只有在有设备事件相关条件时才启动监控，避免不必要的电量消耗
     */
    private fun startDeviceEventMonitoringIfNeeded() {
        serviceScope.launch {
            try {
                // 检查是否有启用的快捷指令包含设备事件条件（除了内存状态，因为内存状态有独立监听器）
                val quickCommands = quickCommandRepository.quickCommands.first()
                    .filter { it.isEnabled }

                val hasDeviceEventConditions = quickCommands.any { command ->
                    command.triggerConditions.filterIsInstance<DeviceEventCondition>()
                        .any { it.eventType != DeviceEventType.MEMORY_STATE } ||
                    command.abortConditions.filterIsInstance<DeviceEventCondition>()
                        .any { it.eventType != DeviceEventType.MEMORY_STATE }
                }

                if (hasDeviceEventConditions) {
                    Log.d(TAG, "Found device event conditions, starting device event monitoring")

                    // 先清除所有已注册的条件，避免重复注册
                    deviceEventMonitor.clearAllConditions()

                    // 启动设备事件监听器
                    deviceEventMonitor.startMonitoring()

                    // 注册所有设备事件条件（除了内存状态）
                    quickCommands.forEach { command ->
                        command.triggerConditions.filterIsInstance<DeviceEventCondition>()
                            .filter { it.eventType != DeviceEventType.MEMORY_STATE }
                            .forEach { deviceEventMonitor.registerCondition(it) }
                        command.abortConditions.filterIsInstance<DeviceEventCondition>()
                            .filter { it.eventType != DeviceEventType.MEMORY_STATE }
                            .forEach { deviceEventMonitor.registerCondition(it) }
                    }

                    Log.d(TAG, "Device event monitoring started with ${deviceEventMonitor.getRegisteredConditionCount()} conditions")
                } else {
                    Log.d(TAG, "No device event conditions found, skipping device event monitoring")
                    deviceEventMonitor.stopMonitoring()
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error checking device event monitoring requirements", e)
            }
        }
    }

    /**
     * 检查是否需要启动应用状态条件监控
     * 只有在有应用状态相关条件时才启动监控，避免不必要的电量消耗
     */
    private fun startAppStateConditionMonitoringIfNeeded() {
        serviceScope.launch {
            try {
                // 检查是否有启用的快捷指令包含应用状态条件
                val quickCommands = quickCommandRepository.quickCommands.first()
                    .filter { it.isEnabled }

                val hasAppStateConditions = quickCommands.any { command ->
                    command.triggerConditions.filterIsInstance<AppStateCondition>().isNotEmpty() ||
                    command.abortConditions.filterIsInstance<AppStateCondition>().isNotEmpty()
                }

                if (hasAppStateConditions) {
                    Log.d(TAG, "Found app state conditions, starting app state condition monitoring")

                    // 先清除所有已注册的条件，避免重复注册
                    appStateConditionMonitor.clearAllConditions()

                    // 启动应用状态条件监听器
                    appStateConditionMonitor.startMonitoring()

                    // 注册所有应用状态条件
                    var registeredCount = 0
                    quickCommands.forEach { command ->
                        command.triggerConditions.filterIsInstance<AppStateCondition>()
                            .forEach { condition ->
                                appStateConditionMonitor.registerCondition(condition)
                                registeredCount++
                                Log.d(TAG, "Registered trigger condition: ${condition.getDescription()}")
                            }
                        command.abortConditions.filterIsInstance<AppStateCondition>()
                            .forEach { condition ->
                                appStateConditionMonitor.registerCondition(condition)
                                registeredCount++
                                Log.d(TAG, "Registered abort condition: ${condition.getDescription()}")
                            }
                    }

                    Log.d(TAG, "App state condition monitoring started with $registeredCount conditions")
                } else {
                    Log.d(TAG, "No app state conditions found, stopping app state condition monitoring")
                    appStateConditionMonitor.stopMonitoring()
                    appStateConditionMonitor.clearAllConditions()
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error checking app state condition monitoring requirements", e)
            }
        }
    }

    // 内存监控任务已由MemoryStateMonitor处理

    // 内存检查任务已由MemoryStateMonitor处理

    // 内存条件检查已由MemoryStateMonitor处理

    // 应用状态监控任务已由AppStateConditionMonitor处理

    // 应用状态检查任务已由AppStateConditionMonitor处理

    // 应用状态条件检查已由AppStateConditionMonitor处理

    /**
     * 重新检查内存监控需求
     */
    private fun recheckMemoryMonitoring() {
        serviceScope.launch {
            try {
                startMemoryStateMonitoringIfNeeded()
            } catch (e: Exception) {
                Log.e(TAG, "Error rechecking memory monitoring", e)
            }
        }
    }

    /**
     * 重新检查设备事件监控需求
     */
    private fun recheckDeviceEventMonitoring() {
        serviceScope.launch {
            try {
                startDeviceEventMonitoringIfNeeded()
            } catch (e: Exception) {
                Log.e(TAG, "Error rechecking device event monitoring", e)
            }
        }
    }

    /**
     * 重新检查应用状态监控需求
     */
    private fun recheckAppStateMonitoring() {
        serviceScope.launch {
            try {
                startAppStateConditionMonitoringIfNeeded()
            } catch (e: Exception) {
                Log.e(TAG, "Error rechecking app state monitoring", e)
            }
        }
    }

    /**
     * 重新检查电池状态监控需求
     */
    private fun recheckBatteryStateMonitoring() {
        serviceScope.launch {
            try {
                startBatteryStateMonitoringIfNeeded()
            } catch (e: Exception) {
                Log.e(TAG, "Error rechecking battery state monitoring", e)
            }
        }
    }

    /**
     * 重新检查智慧提醒监控需求
     */
    private fun recheckSmartReminderMonitoring() {
        serviceScope.launch {
            try {
                if (::smartReminderManager.isInitialized) {
                    smartReminderManager.reloadConfigs()
                } else {
                    Log.w(TAG, "SmartReminderManager not initialized")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error rechecking smart reminder monitoring", e)
            }
        }
    }

    /**
     * 重新注册连接状态监听器
     * 用于数据变化时的动态重新注册
     *
     * @param enabledCommands 启用的快捷指令列表
     */
    private suspend fun reregisterConnectionStateMonitoring(enabledCommands: List<QuickCommand>) {
        try {
            val hasConnectionConditions = enabledCommands.any { command ->
                command.triggerConditions.filterIsInstance<ConnectionStateCondition>().isNotEmpty() ||
                command.abortConditions.filterIsInstance<ConnectionStateCondition>().isNotEmpty()
            }

            if (hasConnectionConditions) {
                Log.d(TAG, "Reregistering connection state monitoring")

                // 清除现有条件并重新注册
                connectionStateMonitor.clearAllConditions()
                connectionStateMonitor.startMonitoring()

                // 注册所有连接状态条件
                enabledCommands.forEach { command ->
                    command.triggerConditions.filterIsInstance<ConnectionStateCondition>()
                        .forEach { connectionStateMonitor.registerCondition(it) }
                    command.abortConditions.filterIsInstance<ConnectionStateCondition>()
                        .forEach { connectionStateMonitor.registerCondition(it) }
                }

                Log.d(TAG, "Connection state monitoring reregistered with ${connectionStateMonitor.getRegisteredConditionCount()} conditions")
            } else {
                Log.d(TAG, "No connection conditions found, stopping connection state monitoring")
                connectionStateMonitor.stopMonitoring()
                connectionStateMonitor.clearAllConditions()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error reregistering connection state monitoring", e)
        }
    }

    /**
     * 重新注册通信状态监听器
     * 用于数据变化时的动态重新注册
     *
     * @param enabledCommands 启用的快捷指令列表
     */
    private suspend fun reregisterCommunicationStateMonitoring(enabledCommands: List<QuickCommand>) {
        try {
            val hasCommunicationConditions = enabledCommands.any { command ->
                command.triggerConditions.filterIsInstance<CommunicationStateCondition>().isNotEmpty() ||
                command.abortConditions.filterIsInstance<CommunicationStateCondition>().isNotEmpty()
            }

            if (hasCommunicationConditions) {
                Log.d(TAG, "Reregistering communication state monitoring")

                // 清除现有条件并重新注册
                communicationStateMonitor.clearConditions()
                communicationStateMonitor.startMonitoring()

                // 注册所有通信状态条件
                enabledCommands.forEach { command ->
                    command.triggerConditions.filterIsInstance<CommunicationStateCondition>()
                        .forEach { communicationStateMonitor.registerCondition(it) }
                    command.abortConditions.filterIsInstance<CommunicationStateCondition>()
                        .forEach { communicationStateMonitor.registerCondition(it) }
                }

                Log.d(TAG, "Communication state monitoring reregistered with ${communicationStateMonitor.getRegisteredConditionCount()} conditions")
            } else {
                Log.d(TAG, "No communication conditions found, stopping communication state monitoring")
                communicationStateMonitor.stopMonitoring()
                communicationStateMonitor.clearConditions()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error reregistering communication state monitoring", e)
        }
    }

    /**
     * 重新注册传感器状态监听器
     * 用于数据变化时的动态重新注册
     *
     * @param enabledCommands 启用的快捷指令列表
     */
    private suspend fun reregisterSensorStateMonitoring(enabledCommands: List<QuickCommand>) {
        try {
            val hasSensorConditions = enabledCommands.any { command ->
                command.triggerConditions.filterIsInstance<SensorStateCondition>().isNotEmpty() ||
                command.abortConditions.filterIsInstance<SensorStateCondition>().isNotEmpty()
            }

            if (hasSensorConditions) {
                Log.d(TAG, "Reregistering sensor state monitoring")

                // 清除现有条件并重新注册
                sensorStateMonitor.clearAllConditions()
                sensorStateMonitor.startMonitoring()

                // 注册所有传感器状态条件
                enabledCommands.forEach { command ->
                    command.triggerConditions.filterIsInstance<SensorStateCondition>()
                        .forEach { sensorStateMonitor.registerCondition(it) }
                    command.abortConditions.filterIsInstance<SensorStateCondition>()
                        .forEach { sensorStateMonitor.registerCondition(it) }
                }

                Log.d(TAG, "Sensor state monitoring reregistered with ${sensorStateMonitor.getRegisteredConditionCount()} conditions")
            } else {
                Log.d(TAG, "No sensor conditions found, stopping sensor state monitoring")
                sensorStateMonitor.stopMonitoring()
                sensorStateMonitor.clearAllConditions()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error reregistering sensor state monitoring", e)
        }
    }

    /**
     * 重新注册电池状态监听器
     * 用于数据变化时的动态重新注册
     *
     * @param enabledCommands 启用的快捷指令列表
     */
    private suspend fun reregisterBatteryStateMonitoring(enabledCommands: List<QuickCommand>) {
        try {
            val hasBatteryConditions = enabledCommands.any { command ->
                command.triggerConditions.filterIsInstance<BatteryStateCondition>().isNotEmpty() ||
                command.abortConditions.filterIsInstance<BatteryStateCondition>().isNotEmpty()
            }

            if (hasBatteryConditions) {
                Log.d(TAG, "Reregistering battery state monitoring")

                // 清除现有条件并重新注册
                batteryStateMonitor.clearAllConditions()
                batteryStateMonitor.startMonitoring()

                // 注册所有电池状态条件
                enabledCommands.forEach { command ->
                    command.triggerConditions.filterIsInstance<BatteryStateCondition>()
                        .forEach { batteryStateMonitor.registerCondition(it) }
                    command.abortConditions.filterIsInstance<BatteryStateCondition>()
                        .forEach { batteryStateMonitor.registerCondition(it) }
                }

                Log.d(TAG, "Battery state monitoring reregistered with ${batteryStateMonitor.getRegisteredConditionCount()} conditions")
            } else {
                Log.d(TAG, "No battery conditions found, stopping battery state monitoring")
                batteryStateMonitor.stopMonitoring()
                batteryStateMonitor.clearAllConditions()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error reregistering battery state monitoring", e)
        }
    }

    /**
     * 检查是否需要启动连接状态监控
     * 只有在有连接状态相关条件时才启动监控，避免不必要的电量消耗
     */
    private fun startConnectionStateMonitoringIfNeeded() {
        serviceScope.launch {
            try {
                // 检查是否有启用的快捷指令包含连接状态条件
                val quickCommands = quickCommandRepository.quickCommands.first()
                    .filter { it.isEnabled }

                val hasConnectionConditions = quickCommands.any { command ->
                    command.triggerConditions.filterIsInstance<ConnectionStateCondition>().isNotEmpty() ||
                    command.abortConditions.filterIsInstance<ConnectionStateCondition>().isNotEmpty()
                }

                if (hasConnectionConditions) {
                    Log.d(TAG, "Found connection conditions, starting connection state monitoring")

                    // 启动连接状态监听器
                    connectionStateMonitor.startMonitoring()

                    // 注册所有连接状态条件
                    quickCommands.forEach { command ->
                        command.triggerConditions.filterIsInstance<ConnectionStateCondition>()
                            .forEach { connectionStateMonitor.registerCondition(it) }
                        command.abortConditions.filterIsInstance<ConnectionStateCondition>()
                            .forEach { connectionStateMonitor.registerCondition(it) }
                    }

                    Log.d(TAG, "Connection state monitoring started with ${connectionStateMonitor.getRegisteredConditionCount()} conditions")
                } else {
                    Log.d(TAG, "No connection conditions found, skipping connection state monitoring")
                    connectionStateMonitor.stopMonitoring()
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error checking connection state monitoring requirements", e)
            }
        }
    }

    /**
     * 检查是否需要启动通信状态监控
     * 只有在有通信状态相关条件时才启动监控，避免不必要的电量消耗
     */
    private fun startCommunicationStateMonitoringIfNeeded() {
        serviceScope.launch {
            try {
                // 检查是否有启用的快捷指令包含通信状态条件
                val quickCommands = quickCommandRepository.quickCommands.first()
                    .filter { it.isEnabled }

                val hasCommunicationConditions = quickCommands.any { command ->
                    command.triggerConditions.filterIsInstance<CommunicationStateCondition>().isNotEmpty() ||
                    command.abortConditions.filterIsInstance<CommunicationStateCondition>().isNotEmpty()
                }

                if (hasCommunicationConditions) {
                    Log.d(TAG, "Found communication conditions, starting communication state monitoring")

                    // 启动通信状态监听器
                    communicationStateMonitor.startMonitoring()

                    // 注册所有通信状态条件
                    quickCommands.forEach { command ->
                        command.triggerConditions.filterIsInstance<CommunicationStateCondition>()
                            .forEach { communicationStateMonitor.registerCondition(it) }
                        command.abortConditions.filterIsInstance<CommunicationStateCondition>()
                            .forEach { communicationStateMonitor.registerCondition(it) }
                    }

                    Log.d(TAG, "Communication state monitoring started with ${communicationStateMonitor.getRegisteredConditionCount()} conditions")
                } else {
                    Log.d(TAG, "No communication conditions found, skipping communication state monitoring")
                    communicationStateMonitor.stopMonitoring()
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error checking communication state monitoring requirements", e)
            }
        }
    }

    /**
     * 检查是否需要启动传感器状态监控
     * 只有在有传感器状态相关条件时才启动监控，避免不必要的电量消耗
     */
    private fun startSensorStateMonitoringIfNeeded() {
        serviceScope.launch {
            try {
                // 检查是否有启用的快捷指令包含传感器状态条件
                val quickCommands = quickCommandRepository.quickCommands.first()
                    .filter { it.isEnabled }

                val hasSensorConditions = quickCommands.any { command ->
                    command.triggerConditions.filterIsInstance<SensorStateCondition>().isNotEmpty() ||
                    command.abortConditions.filterIsInstance<SensorStateCondition>().isNotEmpty()
                }

                if (hasSensorConditions) {
                    Log.d(TAG, "Found sensor conditions, starting sensor state monitoring")

                    // 启动传感器状态监听器
                    sensorStateMonitor.startMonitoring()

                    // 注册所有传感器状态条件
                    quickCommands.forEach { command ->
                        command.triggerConditions.filterIsInstance<SensorStateCondition>()
                            .forEach { sensorStateMonitor.registerCondition(it) }
                        command.abortConditions.filterIsInstance<SensorStateCondition>()
                            .forEach { sensorStateMonitor.registerCondition(it) }
                    }

                    Log.d(TAG, "Sensor state monitoring started with ${sensorStateMonitor.getRegisteredConditionCount()} conditions")
                } else {
                    Log.d(TAG, "No sensor conditions found, skipping sensor state monitoring")
                    sensorStateMonitor.stopMonitoring()
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error checking sensor state monitoring requirements", e)
            }
        }
    }

    /**
     * 检查是否需要启动电池状态监控
     * 只有在有电池状态相关条件时才启动监控，避免不必要的电量消耗
     */
    private fun startBatteryStateMonitoringIfNeeded() {
        serviceScope.launch {
            try {
                // 检查是否有启用的快捷指令包含电池状态条件
                val quickCommands = quickCommandRepository.quickCommands.first()
                    .filter { it.isEnabled }

                val hasBatteryConditions = quickCommands.any { command ->
                    command.triggerConditions.filterIsInstance<BatteryStateCondition>().isNotEmpty() ||
                    command.abortConditions.filterIsInstance<BatteryStateCondition>().isNotEmpty()
                }

                if (hasBatteryConditions) {
                    Log.d(TAG, "Found battery conditions, starting battery state monitoring")

                    // 启动电池状态监听器
                    batteryStateMonitor.startMonitoring()

                    // 注册所有电池状态条件
                    quickCommands.forEach { command ->
                        command.triggerConditions.filterIsInstance<BatteryStateCondition>()
                            .forEach { batteryStateMonitor.registerCondition(it) }
                        command.abortConditions.filterIsInstance<BatteryStateCondition>()
                            .forEach { batteryStateMonitor.registerCondition(it) }
                    }

                    Log.d(TAG, "Battery state monitoring started with ${batteryStateMonitor.getRegisteredConditionCount()} conditions")
                } else {
                    Log.d(TAG, "No battery conditions found, skipping battery state monitoring")
                    batteryStateMonitor.stopMonitoring()
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error checking battery state monitoring requirements", e)
            }
        }
    }

    /**
     * 初始化条件状态
     * 预初始化所有条件指令的触发条件状态，避免首次检查无法触发的问题
     */
    private fun initializeConditionStates() {
        try {
            serviceScope.launch {
                // 获取所有条件指令
                val commands = quickCommandRepository.quickCommands.value
                val allConditions = mutableListOf<SharedTriggerCondition>()

                // 收集所有触发条件
                for (command in commands) {
                    allConditions.addAll(command.triggerConditions)
                    allConditions.addAll(command.abortConditions)
                }

                // 预初始化条件状态
                if (allConditions.isNotEmpty()) {
                    conditionEvaluator.preInitializeConditionStates(allConditions)
                    Log.d(TAG, "Initialized ${allConditions.size} condition states")
                } else {
                    Log.d(TAG, "No conditions to initialize")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error initializing condition states", e)
        }
    }

    /**
     * 处理触发条件
     */
    private suspend fun handleTriggerCondition(triggerCondition: SharedTriggerCondition, eventData: Map<String, Any>? = null) {
        try {
            Log.d(TAG, "Handling trigger condition: ${triggerCondition.getDescription()}")

            // 获取所有启用的快捷指令
            val quickCommands = quickCommandRepository.quickCommands.first()
                .filter { it.isEnabled }

            for (command in quickCommands) {
                // 检查指令是否已在执行中
                if (executingCommands.contains(command.id)) {
                    Log.d(TAG, "Command ${command.name} is already executing, skipping")
                    continue
                }

                // 检查是否在生效时间范围内
                if (!isCommandEffective(command)) {
                    Log.d(TAG, "Command ${command.name} is not effective at current time, skipping")
                    continue
                }

                // 检查触发条件 - 简化逻辑
                val matchingConditions = command.triggerConditions.filter { it.id == triggerCondition.id }
                if (matchingConditions.isEmpty()) {
                    continue
                }

                // 检查其他触发条件是否满足
                val otherConditions = command.triggerConditions.filter { it.id != triggerCondition.id }
                val shouldExecute = if (command.requireAllConditions) {
                    // 所有条件都需要满足
                    otherConditions.isEmpty() || conditionEvaluator.areConditionsSatisfied(otherConditions, true)
                } else {
                    // 任一条件满足即可
                    true
                }

                if (shouldExecute) {
                    // 标记指令为执行中
                    executingCommands.add(command.id)

                    // 执行指令，只有后台时间条件才传递事件数据
                    executionHandler.executeQuickCommand(
                        command = command,
                        triggerEventData = eventData, // 只有后台时间条件调用时才有值
                        onExecutionCompleted = {
                            // 执行完成后移除标记
                            executingCommands.remove(command.id)
                        },
                        onExecutionAborted = { abortConditions ->
                            // 执行被中止后移除标记
                            executingCommands.remove(command.id)
                        }
                    )
                }
            }
        } catch (e: Exception) {
        }
    }



    /**
     * 检查指令是否在生效时间范围内
     */
    private fun isCommandEffective(command: com.weinuo.quickcommands22.model.QuickCommand): Boolean {
        return try {
            if (command.isAllDayEffective) {
                // 全天生效
                true
            } else {
                // 检查当前时间是否在生效时间范围内
                val calendar = Calendar.getInstance()
                val currentHour = calendar.get(Calendar.HOUR_OF_DAY)
                val currentMinute = calendar.get(Calendar.MINUTE)
                val currentTimeInMinutes = currentHour * 60 + currentMinute

                // 解析开始时间和结束时间
                val startTimeParts = command.effectiveStartTime.split(":")
                val endTimeParts = command.effectiveEndTime.split(":")

                if (startTimeParts.size != 2 || endTimeParts.size != 2) {
                    Log.w(TAG, "Invalid time format for command ${command.name}")
                    return true // 时间格式错误时默认生效
                }

                val startHour = startTimeParts[0].toIntOrNull() ?: 0
                val startMinute = startTimeParts[1].toIntOrNull() ?: 0
                val endHour = endTimeParts[0].toIntOrNull() ?: 23
                val endMinute = endTimeParts[1].toIntOrNull() ?: 59

                val startTimeInMinutes = startHour * 60 + startMinute
                val endTimeInMinutes = endHour * 60 + endMinute

                if (startTimeInMinutes <= endTimeInMinutes) {
                    // 同一天内的时间范围
                    currentTimeInMinutes in startTimeInMinutes..endTimeInMinutes
                } else {
                    // 跨天的时间范围（如 22:00 到 06:00）
                    currentTimeInMinutes >= startTimeInMinutes || currentTimeInMinutes <= endTimeInMinutes
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error checking command effective time", e)
            true // 出错时默认生效
        }
    }

    companion object {
        private const val TAG = "QuickCommandsService"

        /**
         * 启动快捷指令服务
         */
        fun start(context: Context) {
            val intent = Intent(context, QuickCommandsService::class.java)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(intent)
            } else {
                context.startService(intent)
            }
        }

        /**
         * 停止快捷指令服务
         */
        fun stop(context: Context) {
            val intent = Intent(context, QuickCommandsService::class.java)
            context.stopService(intent)
        }

        /**
         * 重新检查内存监控需求
         * 当条件指令发生变化时调用此方法
         */
        fun recheckMemoryMonitoring(context: Context) {
            val intent = Intent(context, QuickCommandsService::class.java).apply {
                action = "RECHECK_MEMORY_MONITORING"
            }
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(intent)
            } else {
                context.startService(intent)
            }
        }

        /**
         * 重新检查设备事件监控需求
         * 当条件指令发生变化时调用此方法
         */
        fun recheckDeviceEventMonitoring(context: Context) {
            val intent = Intent(context, QuickCommandsService::class.java).apply {
                action = "RECHECK_DEVICE_EVENT_MONITORING"
            }
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(intent)
            } else {
                context.startService(intent)
            }
        }

        /**
         * 重新检查应用状态监控需求
         * 当条件指令发生变化时调用此方法
         */
        fun recheckAppStateMonitoring(context: Context) {
            val intent = Intent(context, QuickCommandsService::class.java).apply {
                action = "RECHECK_APP_STATE_MONITORING"
            }
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(intent)
            } else {
                context.startService(intent)
            }
        }

        /**
         * 重新检查电池状态监控需求
         * 当条件指令发生变化时调用此方法
         */
        fun recheckBatteryStateMonitoring(context: Context) {
            val intent = Intent(context, QuickCommandsService::class.java).apply {
                action = "RECHECK_BATTERY_STATE_MONITORING"
            }
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(intent)
            } else {
                context.startService(intent)
            }
        }

        /**
         * 重新检查智慧提醒监控需求
         * 当智慧提醒配置发生变化时调用此方法
         */
        fun recheckSmartReminderMonitoring(context: Context) {
            val intent = Intent(context, QuickCommandsService::class.java).apply {
                action = "RECHECK_SMART_REMINDER_MONITORING"
            }
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(intent)
            } else {
                context.startService(intent)
            }
        }
    }
}
