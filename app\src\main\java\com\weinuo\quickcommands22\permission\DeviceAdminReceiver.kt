package com.weinuo.quickcommands22.permission

import android.app.admin.DeviceAdminReceiver
import android.content.Context
import android.content.Intent
import android.util.Log

/**
 * 设备管理器权限申请的正确实现
 * 继承DeviceAdminReceiver，用于处理设备管理器权限的启用和禁用事件
 */
class DeviceAdminReceiver : DeviceAdminReceiver() {
    
    companion object {
        private const val TAG = "DeviceAdminReceiver"
    }
    
    /**
     * 设备管理器权限已启用
     */
    override fun onEnabled(context: Context, intent: Intent) {
        super.onEnabled(context, intent)
        Log.d(TAG, "设备管理器权限已启用")
        
        // 可以在这里添加权限启用后的处理逻辑
        // 例如：通知GlobalPermissionManager权限状态已变更
    }
    
    /**
     * 设备管理器权限已禁用
     */
    override fun onDisabled(context: Context, intent: Intent) {
        super.onDisabled(context, intent)
        Log.d(TAG, "设备管理器权限已禁用")
        
        // 可以在这里添加权限禁用后的处理逻辑
        // 例如：通知GlobalPermissionManager权限状态已变更
    }
    
    /**
     * 密码更改事件
     */
    override fun onPasswordChanged(context: Context, intent: Intent) {
        super.onPasswordChanged(context, intent)
        Log.d(TAG, "设备密码已更改")
    }
    
    /**
     * 密码失败事件
     */
    override fun onPasswordFailed(context: Context, intent: Intent) {
        super.onPasswordFailed(context, intent)
        Log.d(TAG, "设备密码输入失败")
        
        // 这里可以用于登录失败检测功能
        // 可以通知相关的条件评估器
    }
    
    /**
     * 密码成功事件
     */
    override fun onPasswordSucceeded(context: Context, intent: Intent) {
        super.onPasswordSucceeded(context, intent)
        Log.d(TAG, "设备密码输入成功")
    }
}
