package com.weinuo.quickcommands22.permission

/**
 * 智慧提醒操作类型枚举
 *
 * 专门用于智慧提醒功能的权限检查。
 * 这个枚举定义了智慧提醒功能中需要权限检查的操作类型，
 * 符合统一权限检查策略的要求。
 *
 * 使用场景：
 * - 用户在智慧提醒卡片中开启开关时
 * - 通过PermissionAwareOperationSelector进行权限检查
 * - 权限通过后才允许开启智慧提醒功能
 *
 * 权限要求：
 * - ENABLE_REMINDER: 需要悬浮窗权限（用于显示智慧提醒弹窗）和Shizuku权限（用于直接开启屏幕旋转等系统设置）
 * - ENABLE_FLASHLIGHT_REMINDER: 需要悬浮窗权限、Shizuku权限和相机权限（用于监听手电筒状态）
 *
 * <AUTHOR>
 * @since 1.0.0
 */
enum class SmartReminderOperation {
    /**
     * 启用智慧提醒功能
     * 需要悬浮窗权限 - 用于显示智慧提醒弹窗
     * 需要Shizuku权限 - 用于直接开启屏幕旋转等系统设置
     */
    ENABLE_REMINDER,

    /**
     * 启用手电筒提醒功能
     * 需要悬浮窗权限 - 用于显示智慧提醒弹窗
     * 需要Shizuku权限 - 用于直接开启屏幕旋转等系统设置
     * 需要相机权限 - 用于监听手电筒状态变化
     */
    ENABLE_FLASHLIGHT_REMINDER
}
