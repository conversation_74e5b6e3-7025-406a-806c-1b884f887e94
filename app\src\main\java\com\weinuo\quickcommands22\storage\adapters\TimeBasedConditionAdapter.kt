package com.weinuo.quickcommands22.storage.adapters

import android.util.Log
import com.weinuo.quickcommands22.model.DayOfWeek
import com.weinuo.quickcommands22.model.ScheduledRepeatMode
import com.weinuo.quickcommands22.model.SunEventType
import com.weinuo.quickcommands22.model.TimeBasedCondition
import com.weinuo.quickcommands22.model.TimeConditionType
import com.weinuo.quickcommands22.model.TimeIntervalUnit
import com.weinuo.quickcommands22.model.TimeRepeatMode
import com.weinuo.quickcommands22.storage.AppListStorageEngine
import com.weinuo.quickcommands22.storage.NativeTypeStorageManager
import com.weinuo.quickcommands22.storage.StorageDomain
import com.weinuo.quickcommands22.storage.StorageOperation

/**
 * 时间条件存储适配器
 *
 * 负责TimeBasedCondition的原生数据类型存储和重建。
 * 将复杂的时间条件对象拆分为独立的原生字段进行存储，
 * 完全避免JSON序列化问题。
 *
 * 支持的字段类型：
 * - 基础字段：id、timeConditionType
 * - 秒表参数：stopwatchHours、stopwatchMinutes、stopwatchSeconds
 * - 日出日落参数：sunEventType、latitude、longitude
 * - 日程时间参数：year、month、day、hour、minute、timeRepeatMode
 * - 周期时间参数：scheduledRepeatMode、selectedDays
 * - 延迟/周期触发参数：interval、unit、startTime
 *
 * 存储格式示例：
 * condition_{id}_type = "time_based"
 * condition_{id}_time_condition_type = "STOPWATCH"
 * condition_{id}_stopwatch_hours = 0
 * condition_{id}_stopwatch_minutes = 5
 * condition_{id}_stopwatch_seconds = 30
 * condition_{id}_selected_days_count = 2
 * condition_{id}_selected_days_0 = "MONDAY"
 * condition_{id}_selected_days_1 = "FRIDAY"
 *
 * @param storageManager 原生类型存储管理器
 * @param appListEngine 应用列表存储引擎
 */
class TimeBasedConditionAdapter(
    storageManager: NativeTypeStorageManager,
    appListEngine: AppListStorageEngine
) : BaseConditionAdapter<TimeBasedCondition>(storageManager, appListEngine) {

    companion object {
        private const val TAG = "TimeBasedConditionAdapter"

        // 字段名常量
        private const val FIELD_TIME_CONDITION_TYPE = "time_condition_type"

        // 秒表参数字段
        private const val FIELD_STOPWATCH_HOURS = "stopwatch_hours"
        private const val FIELD_STOPWATCH_MINUTES = "stopwatch_minutes"
        private const val FIELD_STOPWATCH_SECONDS = "stopwatch_seconds"

        // 日出日落参数字段
        private const val FIELD_SUN_EVENT_TYPE = "sun_event_type"
        private const val FIELD_LATITUDE = "latitude"
        private const val FIELD_LONGITUDE = "longitude"

        // 日程时间参数字段
        private const val FIELD_YEAR = "year"
        private const val FIELD_MONTH = "month"
        private const val FIELD_DAY = "day"
        private const val FIELD_HOUR = "hour"
        private const val FIELD_MINUTE = "minute"
        private const val FIELD_TIME_REPEAT_MODE = "time_repeat_mode"

        // 周期时间参数字段
        private const val FIELD_SCHEDULED_REPEAT_MODE = "scheduled_repeat_mode"
        private const val FIELD_SELECTED_DAYS = "selected_days"

        // 延迟触发、周期触发参数字段
        private const val FIELD_INTERVAL = "interval"
        private const val FIELD_UNIT = "unit"
        private const val FIELD_START_TIME = "start_time"
    }

    override fun getConditionType(): String = "time_based"

    /**
     * 保存时间条件
     * 将TimeBasedCondition的所有字段拆分为原生数据类型存储
     *
     * @param condition 要保存的时间条件
     * @return 操作是否成功
     */
    override fun save(condition: TimeBasedCondition): Boolean {
        if (!isValidConditionId(condition.id)) {
            logSaveError(condition.id, "Invalid condition ID")
            return false
        }

        return try {
            val operations = mutableListOf<StorageOperation>()

            // 保存基础字段
            operations.addAll(saveBaseFields(condition.id, condition))

            // 保存时间条件特有字段
            operations.addAll(listOf(
                saveEnum(generateKey(condition.id, FIELD_TIME_CONDITION_TYPE), condition.timeConditionType),

                // 秒表参数
                StorageOperation.createIntOperation(
                    StorageDomain.CONDITIONS,
                    generateKey(condition.id, FIELD_STOPWATCH_HOURS),
                    condition.stopwatchHours
                ),
                StorageOperation.createIntOperation(
                    StorageDomain.CONDITIONS,
                    generateKey(condition.id, FIELD_STOPWATCH_MINUTES),
                    condition.stopwatchMinutes
                ),
                StorageOperation.createIntOperation(
                    StorageDomain.CONDITIONS,
                    generateKey(condition.id, FIELD_STOPWATCH_SECONDS),
                    condition.stopwatchSeconds
                ),

                // 日出日落参数
                saveEnum(generateKey(condition.id, FIELD_SUN_EVENT_TYPE), condition.sunEventType),
                StorageOperation.createFloatOperation(
                    StorageDomain.CONDITIONS,
                    generateKey(condition.id, FIELD_LATITUDE),
                    condition.latitude?.toFloat() ?: 0f
                ),
                StorageOperation.createFloatOperation(
                    StorageDomain.CONDITIONS,
                    generateKey(condition.id, FIELD_LONGITUDE),
                    condition.longitude?.toFloat() ?: 0f
                ),

                // 日程时间参数
                StorageOperation.createIntOperation(
                    StorageDomain.CONDITIONS,
                    generateKey(condition.id, FIELD_YEAR),
                    condition.year
                ),
                StorageOperation.createIntOperation(
                    StorageDomain.CONDITIONS,
                    generateKey(condition.id, FIELD_MONTH),
                    condition.month
                ),
                StorageOperation.createIntOperation(
                    StorageDomain.CONDITIONS,
                    generateKey(condition.id, FIELD_DAY),
                    condition.day
                ),
                StorageOperation.createIntOperation(
                    StorageDomain.CONDITIONS,
                    generateKey(condition.id, FIELD_HOUR),
                    condition.hour
                ),
                StorageOperation.createIntOperation(
                    StorageDomain.CONDITIONS,
                    generateKey(condition.id, FIELD_MINUTE),
                    condition.minute
                ),
                saveEnum(generateKey(condition.id, FIELD_TIME_REPEAT_MODE), condition.timeRepeatMode),

                // 周期时间参数
                saveEnum(generateKey(condition.id, FIELD_SCHEDULED_REPEAT_MODE), condition.scheduledRepeatMode),

                // 延迟触发、周期触发参数
                StorageOperation.createIntOperation(
                    StorageDomain.CONDITIONS,
                    generateKey(condition.id, FIELD_INTERVAL),
                    condition.interval
                ),
                saveEnum(generateKey(condition.id, FIELD_UNIT), condition.unit),
                StorageOperation.createLongOperation(
                    StorageDomain.CONDITIONS,
                    generateKey(condition.id, FIELD_START_TIME),
                    condition.startTime
                )
            ))

            // 保存选择的星期几集合
            operations.addAll(saveDayOfWeekSet(condition.id, condition.selectedDays))

            // 执行批量存储操作
            val success = storageManager.executeBatch(operations)

            if (success) {
                logSaveSuccess(condition.id)
            } else {
                logSaveError(condition.id, "Failed to save basic fields")
            }

            success

        } catch (e: Exception) {
            logSaveError(condition.id, "Exception: ${e.message}")
            Log.e(TAG, "Exception while saving TimeBasedCondition: ${condition.id}", e)
            false
        }
    }

    /**
     * 保存星期几集合
     * 将Set<DayOfWeek>拆分为独立的键值对存储
     *
     * @param conditionId 条件ID
     * @param days 星期几集合
     * @return 存储操作列表
     */
    private fun saveDayOfWeekSet(conditionId: String, days: Set<DayOfWeek>): List<StorageOperation> {
        val baseKey = generateKey(conditionId, FIELD_SELECTED_DAYS)
        val operations = mutableListOf<StorageOperation>()

        // 保存集合大小
        operations.add(StorageOperation.createIntOperation(
            StorageDomain.CONDITIONS,
            "${baseKey}_count",
            days.size
        ))

        // 保存每个星期几的值
        days.forEachIndexed { index, day ->
            operations.add(StorageOperation.createStringOperation(
                StorageDomain.CONDITIONS,
                "${baseKey}_${index}",
                day.value
            ))
        }

        return operations
    }

    /**
     * 加载时间条件
     * 从拆分的原生数据类型字段重建TimeBasedCondition对象
     *
     * @param conditionId 条件ID
     * @return 重建的时间条件，失败时返回null
     */
    override fun load(conditionId: String): TimeBasedCondition? {
        if (!isValidConditionId(conditionId)) {
            logLoadError(conditionId, "Invalid condition ID")
            return null
        }

        if (!exists(conditionId)) {
            logLoadError(conditionId, "Condition does not exist")
            return null
        }

        return try {
            // 加载基础字段
            val timeConditionType = loadEnum(
                generateKey(conditionId, FIELD_TIME_CONDITION_TYPE),
                TimeConditionType::class.java,
                TimeConditionType.STOPWATCH
            )

            // 加载秒表参数
            val stopwatchHours = storageManager.loadInt(
                StorageDomain.CONDITIONS,
                generateKey(conditionId, FIELD_STOPWATCH_HOURS),
                0
            )

            val stopwatchMinutes = storageManager.loadInt(
                StorageDomain.CONDITIONS,
                generateKey(conditionId, FIELD_STOPWATCH_MINUTES),
                0
            )

            val stopwatchSeconds = storageManager.loadInt(
                StorageDomain.CONDITIONS,
                generateKey(conditionId, FIELD_STOPWATCH_SECONDS),
                30
            )

            // 加载日出日落参数
            val sunEventType = loadEnum(
                generateKey(conditionId, FIELD_SUN_EVENT_TYPE),
                SunEventType::class.java,
                SunEventType.SUNRISE
            )

            val latitudeFloat = storageManager.loadFloat(
                StorageDomain.CONDITIONS,
                generateKey(conditionId, FIELD_LATITUDE),
                0f
            )
            val latitude = if (latitudeFloat == 0f) null else latitudeFloat.toDouble()

            val longitudeFloat = storageManager.loadFloat(
                StorageDomain.CONDITIONS,
                generateKey(conditionId, FIELD_LONGITUDE),
                0f
            )
            val longitude = if (longitudeFloat == 0f) null else longitudeFloat.toDouble()

            // 加载日程时间参数
            val year = storageManager.loadInt(
                StorageDomain.CONDITIONS,
                generateKey(conditionId, FIELD_YEAR),
                java.util.Calendar.getInstance().get(java.util.Calendar.YEAR)
            )

            val month = storageManager.loadInt(
                StorageDomain.CONDITIONS,
                generateKey(conditionId, FIELD_MONTH),
                java.util.Calendar.getInstance().get(java.util.Calendar.MONTH) + 1
            )

            val day = storageManager.loadInt(
                StorageDomain.CONDITIONS,
                generateKey(conditionId, FIELD_DAY),
                java.util.Calendar.getInstance().get(java.util.Calendar.DAY_OF_MONTH)
            )

            val hour = storageManager.loadInt(
                StorageDomain.CONDITIONS,
                generateKey(conditionId, FIELD_HOUR),
                8
            )

            val minute = storageManager.loadInt(
                StorageDomain.CONDITIONS,
                generateKey(conditionId, FIELD_MINUTE),
                0
            )

            val timeRepeatMode = loadEnum(
                generateKey(conditionId, FIELD_TIME_REPEAT_MODE),
                TimeRepeatMode::class.java,
                TimeRepeatMode.ONCE
            )

            // 加载周期时间参数
            val scheduledRepeatMode = loadEnum(
                generateKey(conditionId, FIELD_SCHEDULED_REPEAT_MODE),
                ScheduledRepeatMode::class.java,
                ScheduledRepeatMode.ONCE
            )

            val selectedDays = loadDayOfWeekSet(conditionId)

            // 加载延迟触发、周期触发参数
            val interval = storageManager.loadInt(
                StorageDomain.CONDITIONS,
                generateKey(conditionId, FIELD_INTERVAL),
                30
            )

            val unit = loadEnum(
                generateKey(conditionId, FIELD_UNIT),
                TimeIntervalUnit::class.java,
                TimeIntervalUnit.SECONDS
            )

            val startTime = storageManager.loadLong(
                StorageDomain.CONDITIONS,
                generateKey(conditionId, FIELD_START_TIME),
                System.currentTimeMillis()
            )

            // 重建TimeBasedCondition对象
            val condition = TimeBasedCondition(
                id = conditionId,
                timeConditionType = timeConditionType,
                stopwatchHours = stopwatchHours,
                stopwatchMinutes = stopwatchMinutes,
                stopwatchSeconds = stopwatchSeconds,
                sunEventType = sunEventType,
                latitude = latitude,
                longitude = longitude,
                year = year,
                month = month,
                day = day,
                hour = hour,
                minute = minute,
                timeRepeatMode = timeRepeatMode,
                scheduledRepeatMode = scheduledRepeatMode,
                selectedDays = selectedDays,
                interval = interval,
                unit = unit,
                startTime = startTime
            )

            Log.d(TAG, "Successfully loaded TimeBasedCondition: $conditionId")
            condition

        } catch (e: Exception) {
            logLoadError(conditionId, "Exception: ${e.message}")
            Log.e(TAG, "Exception while loading TimeBasedCondition: $conditionId", e)
            null
        }
    }

    /**
     * 加载星期几集合
     * 从拆分的键值对重建Set<DayOfWeek>
     *
     * @param conditionId 条件ID
     * @return 星期几集合
     */
    private fun loadDayOfWeekSet(conditionId: String): Set<DayOfWeek> {
        val baseKey = generateKey(conditionId, FIELD_SELECTED_DAYS)
        val count = storageManager.loadInt(StorageDomain.CONDITIONS, "${baseKey}_count", 0)

        if (count == 0) {
            return emptySet()
        }

        val days = mutableSetOf<DayOfWeek>()
        for (index in 0 until count) {
            val dayValue = storageManager.loadString(
                StorageDomain.CONDITIONS,
                "${baseKey}_${index}",
                ""
            )
            if (dayValue.isNotEmpty()) {
                try {
                    DayOfWeek.fromValue(dayValue)?.let { days.add(it) }
                } catch (e: Exception) {
                    Log.w(TAG, "Invalid DayOfWeek value: $dayValue")
                }
            }
        }

        return days
    }
}
