package com.weinuo.quickcommands22.widget

import android.appwidget.AppWidgetManager
import android.content.ComponentName
import android.content.Context
import android.util.Log

/**
 * 小组件更新管理器
 * 
 * 负责管理小组件的更新策略，根据用户设置决定是否执行更新，
 * 提供手动触发更新的机制。
 */
object WidgetUpdateManager {
    
    private const val TAG = "WidgetUpdateManager"
    private const val PREFS_NAME = "background_manager_settings"
    private const val KEY_WIDGET_UPDATE_ENABLED = "global_widget_update_enabled"
    private const val KEY_WIDGET_UPDATE_INTERVAL = "global_widget_update_interval"
    
    /**
     * 检查是否启用了小组件更新
     * 
     * @param context 上下文
     * @return 是否启用更新
     */
    fun isWidgetUpdateEnabled(context: Context): Boolean {
        val sharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        return sharedPreferences.getBoolean(KEY_WIDGET_UPDATE_ENABLED, false)
    }
    
    /**
     * 获取小组件更新间隔（小时）
     * 
     * @param context 上下文
     * @return 更新间隔（小时）
     */
    fun getWidgetUpdateInterval(context: Context): Int {
        val sharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        return sharedPreferences.getInt(KEY_WIDGET_UPDATE_INTERVAL, 24)
    }
    
    /**
     * 手动触发所有小组件更新
     * 
     * 无论用户设置如何，都会强制更新所有小组件。
     * 用于用户主动触发更新或设置变更后的立即更新。
     * 
     * @param context 上下文
     */
    fun forceUpdateAllWidgets(context: Context) {
        try {
            val appWidgetManager = AppWidgetManager.getInstance(context)
            
            // 更新小组件1
            val widget1Ids = appWidgetManager.getAppWidgetIds(
                ComponentName(context, OneClickCommandWidget1::class.java)
            )
            widget1Ids.forEach { widgetId ->
                OneClickCommandWidget1.updateAppWidget(context, appWidgetManager, widgetId)
            }
            
            // 更新小组件2
            val widget2Ids = appWidgetManager.getAppWidgetIds(
                ComponentName(context, OneClickCommandWidget2::class.java)
            )
            widget2Ids.forEach { widgetId ->
                OneClickCommandWidget2.updateAppWidget(context, appWidgetManager, widgetId)
            }
            
            // 更新小组件3
            val widget3Ids = appWidgetManager.getAppWidgetIds(
                ComponentName(context, OneClickCommandWidget3::class.java)
            )
            widget3Ids.forEach { widgetId ->
                OneClickCommandWidget3.updateAppWidget(context, appWidgetManager, widgetId)
            }
            
            // 更新小组件4
            val widget4Ids = appWidgetManager.getAppWidgetIds(
                ComponentName(context, OneClickCommandWidget4::class.java)
            )
            widget4Ids.forEach { widgetId ->
                OneClickCommandWidget4.updateAppWidget(context, appWidgetManager, widgetId)
            }
            
            Log.d(TAG, "手动触发所有小组件更新完成")
            
        } catch (e: Exception) {
            Log.e(TAG, "手动更新小组件时发生错误", e)
        }
    }
    
    /**
     * 根据用户设置触发小组件更新
     * 
     * 只有在用户启用小组件更新时才会执行更新。
     * 
     * @param context 上下文
     */
    fun updateWidgetsIfEnabled(context: Context) {
        if (isWidgetUpdateEnabled(context)) {
            forceUpdateAllWidgets(context)
            Log.d(TAG, "根据用户设置执行小组件更新")
        } else {
            Log.d(TAG, "用户未启用小组件更新，跳过更新")
        }
    }
    
    /**
     * 获取小组件更新设置的详细信息
     * 
     * @param context 上下文
     * @return 设置信息字符串
     */
    fun getWidgetUpdateSettingsInfo(context: Context): String {
        val enabled = isWidgetUpdateEnabled(context)
        val interval = getWidgetUpdateInterval(context)
        
        return if (enabled) {
            "小组件更新已启用，更新间隔：${interval}小时"
        } else {
            "小组件更新已禁用，节省系统资源"
        }
    }
}
