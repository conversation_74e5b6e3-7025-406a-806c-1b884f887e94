package com.weinuo.quickcommands22.service

import android.content.Context
import android.util.Log
import com.weinuo.quickcommands22.data.QuickCommandRepository
import com.weinuo.quickcommands22.execution.SharedExecutionHandler
import com.weinuo.quickcommands22.model.ManualTriggerCondition
import com.weinuo.quickcommands22.model.ManualTriggerType
import com.weinuo.quickcommands22.model.QuickCommand
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * 手动触发管理器
 *
 * 负责管理手动触发条件与快捷指令的关联，当检测到手动触发事件时，
 * 查找并执行相应的快捷指令。
 *
 * 支持的手动触发类型：
 * - 主屏幕按钮长按
 * - 媒体键按下
 * - 动态快捷方式
 * - 静态快捷方式
 * - 桌面小组件
 * - 悬浮按钮
 * - 指纹手势
 *
 * @param context Android上下文
 */
class ManualTriggerManager(private val context: Context) {

    companion object {
        private const val TAG = "ManualTriggerManager"
    }

    // 快捷指令仓库
    private val quickCommandRepository = QuickCommandRepository.getInstance(context)

    // 执行处理器
    private val executionHandler = SharedExecutionHandler(context)

    // 协程作用域
    private val coroutineScope = CoroutineScope(Dispatchers.Main)

    /**
     * 处理手动触发事件
     *
     * @param triggerCondition 触发的手动触发条件
     */
    fun handleManualTrigger(triggerCondition: ManualTriggerCondition) {
        Log.d(TAG, "处理手动触发事件: ${triggerCondition.triggerType.displayName} (${triggerCondition.id})")

        coroutineScope.launch {
            try {
                // 查找包含此触发条件的快捷指令
                val matchingCommands = findMatchingCommands(triggerCondition)

                if (matchingCommands.isEmpty()) {
                    Log.d(TAG, "未找到匹配的快捷指令")
                    return@launch
                }

                Log.d(TAG, "找到 ${matchingCommands.size} 个匹配的快捷指令")

                // 执行所有匹配的快捷指令
                matchingCommands.forEach { command ->
                    executeQuickCommand(command, triggerCondition)
                }

            } catch (e: Exception) {
                Log.e(TAG, "处理手动触发事件失败", e)
            }
        }
    }

    /**
     * 查找包含指定触发条件的快捷指令
     */
    private suspend fun findMatchingCommands(triggerCondition: ManualTriggerCondition): List<QuickCommand> {
        val allCommands = quickCommandRepository.quickCommands.value

        return allCommands.filter { command ->
            // 检查指令是否启用
            if (!command.isEnabled) {
                return@filter false
            }

            // 检查触发条件中是否包含匹配的手动触发条件
            command.triggerConditions.any { condition ->
                condition is ManualTriggerCondition && condition.matches(triggerCondition)
            }
        }
    }

    /**
     * 执行快捷指令
     */
    private fun executeQuickCommand(command: QuickCommand, triggerCondition: ManualTriggerCondition) {
        Log.d(TAG, "执行快捷指令: ${command.name} (触发条件: ${triggerCondition.triggerType.displayName})")

        // 使用手动执行模式，跳过触发条件检查
        executionHandler.executeQuickCommandManually(
            command = command,
            onTaskStarted = { task ->
                Log.d(TAG, "任务开始执行: ${task.displayName}")
            },
            onTaskCompleted = { task, success ->
                Log.d(TAG, "任务执行完成: ${task.displayName}, 成功: $success")
            },
            onExecutionCompleted = {
                Log.d(TAG, "快捷指令执行完成: ${command.name}")
            },
            onExecutionAborted = { abortConditions ->
                val conditionNames = abortConditions.joinToString(", ") { it.displayName }
                Log.w(TAG, "快捷指令执行被中止: ${command.name}, 中止条件: $conditionNames")
            }
        )
    }

    /**
     * 处理主屏幕按钮长按事件
     */
    fun handleHomeButtonLongPress(condition: ManualTriggerCondition) {
        Log.d(TAG, "处理主屏幕按钮长按事件")
        handleManualTrigger(condition)
    }

    /**
     * 处理媒体键按下事件
     */
    fun handleMediaKeyPress(condition: ManualTriggerCondition) {
        Log.d(TAG, "处理媒体键按下事件")
        handleManualTrigger(condition)
    }

    /**
     * 处理动态快捷方式触发事件
     */
    fun handleDynamicShortcut(shortcutName: String) {
        Log.d(TAG, "处理动态快捷方式触发事件: $shortcutName")

        val condition = ManualTriggerCondition(
            triggerType = ManualTriggerType.DYNAMIC_SHORTCUT,
            shortcutName = shortcutName
        )
        handleManualTrigger(condition)
    }

    /**
     * 处理静态快捷方式触发事件
     */
    fun handleStaticShortcut(slotIndex: Int) {
        Log.d(TAG, "处理静态快捷方式触发事件: 槽位 $slotIndex")

        val condition = ManualTriggerCondition(
            triggerType = ManualTriggerType.STATIC_SHORTCUT,
            slotIndex = slotIndex
        )
        handleManualTrigger(condition)
    }

    /**
     * 处理桌面小组件触发事件
     */
    fun handleDesktopWidget(slotIndex: Int) {
        Log.d(TAG, "处理桌面小组件触发事件: 槽位 $slotIndex")

        val condition = ManualTriggerCondition(
            triggerType = ManualTriggerType.DESKTOP_WIDGET,
            slotIndex = slotIndex
        )
        handleManualTrigger(condition)
    }

    /**
     * 处理悬浮按钮触发事件
     */
    fun handleFloatingButton(buttonText: String) {
        Log.d(TAG, "处理悬浮按钮触发事件: $buttonText")

        val condition = ManualTriggerCondition(
            triggerType = ManualTriggerType.FLOATING_BUTTON,
            buttonText = buttonText
        )
        handleManualTrigger(condition)
    }

    /**
     * 处理指纹手势触发事件
     */
    fun handleFingerprintGesture(gestureType: com.weinuo.quickcommands22.model.FingerprintGestureType) {
        Log.d(TAG, "处理指纹手势触发事件: ${gestureType.displayName}")

        val condition = ManualTriggerCondition(
            triggerType = ManualTriggerType.FINGERPRINT_GESTURE,
            fingerprintGestureType = gestureType
        )
        handleManualTrigger(condition)
    }

    /**
     * 处理音量键按下事件
     */
    fun handleVolumeKeyPress(condition: ManualTriggerCondition) {
        Log.d(TAG, "处理音量键按下事件: ${condition.volumeKeyType.displayName}, 保留音量: ${condition.volumeKeyPreserveVolume}")
        handleManualTrigger(condition)
    }

    /**
     * 清理资源
     */
    fun cleanup() {
        Log.d(TAG, "手动触发管理器已清理")
    }
}
