package com.weinuo.quickcommands22.permission

/**
 * Shell脚本Shizuku操作类型枚举
 * 
 * 专门用于Shell脚本选择Shizuku执行模式时的权限检查。
 * 这是一个特殊的操作类型，只在用户选择Shizuku模式时触发权限检查，
 * 符合统一权限检查策略的要求。
 * 
 * 使用场景：
 * - 用户在Shell脚本配置中选择Shizuku执行模式时
 * - 通过PermissionAwareOperationSelector进行权限检查
 * - 权限通过后才允许设置为Shizuku模式
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
enum class ShellScriptShizukuOperation {
    /**
     * Shizuku Shell脚本执行
     * 需要Shizuku权限 - 通过Shizuku执行Shell脚本
     */
    SHIZUKU_SHELL
}
