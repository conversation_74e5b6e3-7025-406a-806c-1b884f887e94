package com.weinuo.quickcommands22.ui.theme.oceanblue

import androidx.compose.material3.Typography
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import com.weinuo.quickcommands22.ui.theme.system.StyleConfiguration
import com.weinuo.quickcommands22.ui.theme.config.*

/**
 * 海洋蓝主题样式配置
 *
 * 实现分层设计风格的样式配置
 * 特点：明显的阴影效果，清晰的层次结构，适中的圆角
 */
class OceanBlueStyleConfiguration : StyleConfiguration {

    /**
     * 圆角配置 - 分层设计使用适中的圆角
     */
    override val cornerRadius = CornerRadiusConfig(
        extraSmall = 4.dp,
        small = 8.dp,
        medium = 12.dp,
        large = 16.dp,
        extraLarge = 20.dp
    )

    /**
     * 阴影配置 - 分层设计的核心特征
     * 使用明显的阴影来表现层次深度
     */
    override val elevation = ElevationConfig(
        none = 0.dp,
        low = 1.dp,
        medium = 6.dp,
        high = 8.dp,
        extraHigh = 12.dp
    )

    /**
     * 间距配置 - 清晰的空间层次
     */
    override val spacing = SpacingConfig(
        extraSmall = 4.dp,
        small = 8.dp,
        medium = 16.dp,
        large = 24.dp,
        extraLarge = 32.dp
    )

    /**
     * 字体配置
     */
    override val typography = TypographyConfig(
        typography = Typography(),
        lineHeightMultiplier = 1.0f,
        letterSpacingMultiplier = 1.0f
    )

    /**
     * 边框配置 - 分层设计中的边界定义
     */
    override val borders = BorderConfig(
        width = 1.dp,
        color = Color.Gray,
        enabled = true
    )

    /**
     * 阴影配置 - 分层设计的视觉深度
     */
    override val shadows = ShadowConfig(
        enabled = true,
        color = Color.Black.copy(alpha = 0.1f),
        offsetX = 0.dp,
        offsetY = 2.dp,
        blurRadius = 4.dp,
        spreadRadius = 0.dp
    )

    /**
     * 效果配置 - 分层设计的视觉效果
     */
    override val effects = EffectsConfig(
        blurEnabled = false,
        blurRadius = 0.dp,
        transparencyEnabled = true,
        shadowEnabled = true,
        gradientEnabled = true,
        animationsEnabled = true
    )

    /**
     * 动画配置
     */
    override val animations = AnimationConfig(
        enabled = true,
        duration = 300,
        easing = "ease_in_out"
    )

    /**
     * 透明度配置
     */
    override val opacity = OpacityConfig(
        disabled = 0.38f,
        inactive = 0.6f,
        active = 1.0f,
        hover = 0.08f,
        pressed = 0.12f,
        focused = 0.12f,
        selected = 0.16f,
        dragging = 0.16f
    )

    /**
     * 尺寸配置
     */
    override val dimensions = DimensionConfig(
        minTouchTarget = 48.dp,
        iconSize = 24.dp,
        smallIconSize = 16.dp,
        largeIconSize = 32.dp,
        avatarSize = 40.dp,
        buttonHeight = 40.dp,
        textFieldHeight = 56.dp,
        appBarHeight = 64.dp,
        bottomNavHeight = 80.dp,
        fabSize = 56.dp,
        smallFabSize = 40.dp,
        largeFabSize = 96.dp
    )

    /**
     * 卡片样式配置 - 海洋蓝主题（分层设计）
     *
     * 特点：
     * - 使用标准12.dp圆角，保持与old项目一致
     * - 统一16.dp内边距
     * - 无阴影设计，使用扁平化风格
     */
    override val cardStyle = CardStyleConfig(
        // 基础卡片样式
        defaultCornerRadius = 12.dp,
        defaultElevation = 0.dp,  // 无阴影设计
        defaultHorizontalPadding = 13.dp,
        defaultVerticalPadding = 16.dp,
        settingsVerticalPadding = 16.dp,  // 海洋蓝主题设置界面卡片垂直边距

        // 间距配置
        itemSpacing = 8.dp,
        sectionSpacing = 16.dp,

        // 内容间距配置
        contentVerticalSpacing = 4.dp,
        contentHorizontalSpacing = 8.dp,

        // 选择状态样式
        selectedElevation = 0.dp,  // 选中时也不使用阴影
        selectedBorderWidth = 2.dp
    )
}
