package com.weinuo.quickcommands22.ui.theme.system

import com.weinuo.quickcommands22.ui.theme.config.*

/**
 * 样式配置接口
 *
 * 定义主题的视觉样式配置，包括圆角、间距、高度、字体等
 * 不同的设计方法会有不同的样式配置实现
 */
interface StyleConfiguration {
    /**
     * 圆角配置
     */
    val cornerRadius: CornerRadiusConfig

    /**
     * 高度配置
     */
    val elevation: ElevationConfig

    /**
     * 间距配置
     */
    val spacing: SpacingConfig

    /**
     * 字体配置
     */
    val typography: TypographyConfig

    /**
     * 视觉效果配置
     */
    val effects: EffectsConfig

    /**
     * 边框配置
     */
    val borders: BorderConfig

    /**
     * 阴影配置
     */
    val shadows: ShadowConfig

    /**
     * 动画配置
     */
    val animations: AnimationConfig

    /**
     * 颜色透明度配置
     */
    val opacity: OpacityConfig

    /**
     * 尺寸配置
     */
    val dimensions: DimensionConfig

    /**
     * 卡片样式配置
     *
     * 统一管理所有卡片组件的样式属性，解决硬编码样式值导致的主题切换不一致问题
     */
    val cardStyle: CardStyleConfig

    /**
     * 获取配置版本
     */
    fun getVersion(): String {
        return "1.0.0"
    }

    /**
     * 验证配置的有效性
     */
    fun validate(): Boolean {
        return true // 默认实现总是返回true
    }
}

/**
 * 交互配置接口
 *
 * 定义用户交互状态的视觉反馈配置
 */
interface InteractionConfiguration {
    /**
     * 悬停状态配置
     */
    val hover: InteractionStateConfig

    /**
     * 按压状态配置
     */
    val pressed: InteractionStateConfig

    /**
     * 焦点状态配置
     */
    val focused: InteractionStateConfig

    /**
     * 激活状态配置
     */
    val active: InteractionStateConfig

    /**
     * 选中状态配置
     */
    val selected: InteractionStateConfig

    /**
     * 禁用状态配置
     */
    val disabled: InteractionStateConfig

    /**
     * 拖拽状态配置
     */
    val dragging: InteractionStateConfig

    /**
     * 获取特定交互状态的配置
     */
    fun getStateConfig(state: InteractionState): InteractionStateConfig {
        return when (state) {
            InteractionState.HOVER -> hover
            InteractionState.PRESSED -> pressed
            InteractionState.FOCUSED -> focused
            InteractionState.ACTIVE -> active
            InteractionState.SELECTED -> selected
            InteractionState.DISABLED -> disabled
            InteractionState.DRAGGING -> dragging
        }
    }
}

/**
 * 动画配置接口
 *
 * 定义主题中使用的动画效果配置
 */
interface AnimationConfiguration {
    /**
     * 快速动画持续时间（毫秒）
     */
    val fastDuration: Int

    /**
     * 中等动画持续时间（毫秒）
     */
    val mediumDuration: Int

    /**
     * 慢速动画持续时间（毫秒）
     */
    val slowDuration: Int

    /**
     * 进入动画配置
     */
    val enter: AnimationSpec

    /**
     * 退出动画配置
     */
    val exit: AnimationSpec

    /**
     * 过渡动画配置
     */
    val transition: AnimationSpec

    /**
     * 弹性动画配置
     */
    val spring: SpringAnimationSpec

    /**
     * 缓动动画配置
     */
    val easing: EasingAnimationSpec

    /**
     * 是否启用动画
     */
    val animationsEnabled: Boolean

    /**
     * 获取指定类型的动画持续时间
     */
    fun getDuration(type: AnimationType): Int {
        return when (type) {
            AnimationType.FAST -> fastDuration
            AnimationType.MEDIUM -> mediumDuration
            AnimationType.SLOW -> slowDuration
        }
    }
}

/**
 * 交互状态枚举
 */
enum class InteractionState {
    HOVER,
    PRESSED,
    FOCUSED,
    ACTIVE,
    SELECTED,
    DISABLED,
    DRAGGING
}

/**
 * 动画类型枚举
 */
enum class AnimationType {
    FAST,
    MEDIUM,
    SLOW
}
