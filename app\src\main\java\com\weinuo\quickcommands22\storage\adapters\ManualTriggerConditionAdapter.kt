package com.weinuo.quickcommands22.storage.adapters

import android.util.Log
import com.weinuo.quickcommands22.model.*
import com.weinuo.quickcommands22.storage.NativeTypeStorageManager
import com.weinuo.quickcommands22.storage.AppListStorageEngine
import com.weinuo.quickcommands22.storage.StorageDomain
import com.weinuo.quickcommands22.storage.StorageOperation
import com.weinuo.quickcommands22.storage.StorageType

/**
 * 手动触发条件存储适配器
 *
 * 负责ManualTriggerCondition的原生数据类型存储和重建。
 * 将复杂的手动触发条件对象拆分为独立的原生字段进行存储，
 * 完全避免JSON序列化问题。
 *
 * 支持的字段类型：
 * - 基础字段：id、triggerType
 * - 槽位参数：slotIndex（静态快捷方式和桌面小组件使用）
 * - 悬浮按钮参数：buttonText、buttonSize、buttonAlpha
 * - 动态快捷方式参数：shortcutName
 *
 * 存储格式示例：
 * condition_{id}_type = "manual_trigger"
 * condition_{id}_trigger_type = "DYNAMIC_SHORTCUT"
 * condition_{id}_slot_index = 0
 * condition_{id}_button_text = "执行任务"
 * condition_{id}_shortcut_name = "快速启动"
 * condition_{id}_button_size = 60
 * condition_{id}_button_alpha = 0.8
 *
 * @param storageManager 原生类型存储管理器
 * @param appListEngine 应用列表存储引擎
 */
class ManualTriggerConditionAdapter(
    storageManager: NativeTypeStorageManager,
    appListEngine: AppListStorageEngine
) : BaseConditionAdapter<ManualTriggerCondition>(storageManager, appListEngine) {

    companion object {
        private const val TAG = "ManualTriggerConditionAdapter"

        // 字段名常量
        private const val FIELD_TRIGGER_TYPE = "trigger_type"
        private const val FIELD_SLOT_INDEX = "slot_index"
        private const val FIELD_BUTTON_TEXT = "button_text"
        private const val FIELD_SHORTCUT_NAME = "shortcut_name"
        private const val FIELD_BUTTON_SIZE = "button_size"
        private const val FIELD_BUTTON_ALPHA = "button_alpha"
        private const val FIELD_FINGERPRINT_GESTURE_TYPE = "fingerprint_gesture_type"
        private const val FIELD_HOME_BUTTON_LONG_PRESS_THRESHOLD = "home_button_long_press_threshold"
        private const val FIELD_MEDIA_KEY_TYPE = "media_key_type"
        private const val FIELD_MEDIA_KEY_PRESS_COUNT = "media_key_press_count"
        private const val FIELD_MEDIA_KEY_PRESS_TIMEOUT = "media_key_press_timeout"
        private const val FIELD_SHORTCUT_OPENING_NAME = "shortcut_opening_name"
        private const val FIELD_SWIPE_START_CORNER = "swipe_start_corner"
        private const val FIELD_SWIPE_DIRECTION = "swipe_direction"
        private const val FIELD_VOLUME_BUTTON_TYPE = "volume_button_type"
        private const val FIELD_VOLUME_BUTTON_LONG_PRESS_THRESHOLD = "volume_button_long_press_threshold"
        private const val FIELD_VOLUME_KEY_TYPE = "volume_key_type"
        private const val FIELD_VOLUME_KEY_PRESERVE_VOLUME = "volume_key_preserve_volume"
        private const val FIELD_POWER_BUTTON_LONG_PRESS_THRESHOLD = "power_button_long_press_threshold"
    }

    override fun getConditionType() = "manual_trigger"

    override fun save(condition: ManualTriggerCondition): Boolean {
        Log.d(TAG, "开始保存手动触发条件: ${condition.id}")

        return try {
            val prefix = getPrefix(condition.id)
            val operations = mutableListOf<StorageOperation>()

            // 基础字段
            operations.addAll(saveBaseFields(condition.id, condition))

            // 手动触发条件特有字段
            operations.addAll(listOf(
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_TRIGGER_TYPE}", condition.triggerType.value, StorageType.STRING),
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_SLOT_INDEX}", condition.slotIndex, StorageType.INT),
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_BUTTON_TEXT}", condition.buttonText, StorageType.STRING),
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_SHORTCUT_NAME}", condition.shortcutName, StorageType.STRING),
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_BUTTON_SIZE}", condition.buttonSize, StorageType.INT),
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_BUTTON_ALPHA}", condition.buttonAlpha, StorageType.FLOAT),
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_FINGERPRINT_GESTURE_TYPE}", condition.fingerprintGestureType.value, StorageType.STRING),
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_HOME_BUTTON_LONG_PRESS_THRESHOLD}", condition.homeButtonLongPressThreshold, StorageType.LONG),
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_MEDIA_KEY_TYPE}", condition.mediaKeyType.value, StorageType.STRING),
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_MEDIA_KEY_PRESS_COUNT}", condition.mediaKeyPressCount, StorageType.INT),
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_MEDIA_KEY_PRESS_TIMEOUT}", condition.mediaKeyPressTimeout, StorageType.LONG),
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_SHORTCUT_OPENING_NAME}", condition.shortcutOpeningName, StorageType.STRING),
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_SWIPE_START_CORNER}", condition.swipeStartCorner.value, StorageType.STRING),
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_SWIPE_DIRECTION}", condition.swipeDirection.value, StorageType.STRING),
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_VOLUME_BUTTON_TYPE}", condition.volumeButtonType.value, StorageType.STRING),
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_VOLUME_BUTTON_LONG_PRESS_THRESHOLD}", condition.volumeButtonLongPressThreshold, StorageType.LONG),
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_VOLUME_KEY_TYPE}", condition.volumeKeyType.value, StorageType.STRING),
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_VOLUME_KEY_PRESERVE_VOLUME}", condition.volumeKeyPreserveVolume, StorageType.BOOLEAN),
                StorageOperation(StorageDomain.CONDITIONS, "${prefix}${FIELD_POWER_BUTTON_LONG_PRESS_THRESHOLD}", condition.powerButtonLongPressThreshold, StorageType.LONG)
            ))

            val success = storageManager.executeBatch(operations)
            if (success) {
                Log.d(TAG, "手动触发条件保存成功: ${condition.id}")
            } else {
                Log.e(TAG, "手动触发条件保存失败: ${condition.id}")
            }
            success

        } catch (e: Exception) {
            Log.e(TAG, "保存手动触发条件时发生异常: ${condition.id}", e)
            false
        }
    }

    override fun load(conditionId: String): ManualTriggerCondition? {
        Log.d(TAG, "开始加载手动触发条件: $conditionId")

        return try {
            val prefix = getPrefix(conditionId)

            // 检查条件是否存在
            val conditionType = storageManager.loadString(StorageDomain.CONDITIONS, "${prefix}type")
            if (conditionType != getConditionType()) {
                Log.w(TAG, "条件类型不匹配或条件不存在: $conditionId, 期望: ${getConditionType()}, 实际: $conditionType")
                return null
            }

            // 加载触发类型
            val triggerType = storageManager.loadString(StorageDomain.CONDITIONS, "${prefix}${FIELD_TRIGGER_TYPE}").let {
                try {
                    ManualTriggerType.fromValue(it)
                } catch (e: Exception) {
                    Log.w(TAG, "无效的手动触发类型: $it, 使用默认值")
                    ManualTriggerType.DYNAMIC_SHORTCUT
                }
            }

            // 加载其他字段
            val slotIndex = storageManager.loadInt(StorageDomain.CONDITIONS, "${prefix}${FIELD_SLOT_INDEX}", 0)
            val buttonText = storageManager.loadString(StorageDomain.CONDITIONS, "${prefix}${FIELD_BUTTON_TEXT}")
            val shortcutName = storageManager.loadString(StorageDomain.CONDITIONS, "${prefix}${FIELD_SHORTCUT_NAME}")
            val buttonSize = storageManager.loadInt(StorageDomain.CONDITIONS, "${prefix}${FIELD_BUTTON_SIZE}", 60)
            val buttonAlpha = storageManager.loadFloat(StorageDomain.CONDITIONS, "${prefix}${FIELD_BUTTON_ALPHA}", 0.8f)

            // 加载指纹手势类型
            val fingerprintGestureType = storageManager.loadString(StorageDomain.CONDITIONS, "${prefix}${FIELD_FINGERPRINT_GESTURE_TYPE}").let {
                try {
                    FingerprintGestureType.fromValue(it)
                } catch (e: Exception) {
                    Log.w(TAG, "无效的指纹手势类型: $it, 使用默认值")
                    FingerprintGestureType.TAP
                }
            }

            // 加载新增字段
            val homeButtonLongPressThreshold = storageManager.loadLong(StorageDomain.CONDITIONS, "${prefix}${FIELD_HOME_BUTTON_LONG_PRESS_THRESHOLD}", 1000L)

            val mediaKeyType = storageManager.loadString(StorageDomain.CONDITIONS, "${prefix}${FIELD_MEDIA_KEY_TYPE}").let {
                try {
                    MediaKeyType.fromValue(it)
                } catch (e: Exception) {
                    Log.w(TAG, "无效的媒体键类型: $it, 使用默认值")
                    MediaKeyType.PLAY_PAUSE
                }
            }

            val mediaKeyPressCount = storageManager.loadInt(StorageDomain.CONDITIONS, "${prefix}${FIELD_MEDIA_KEY_PRESS_COUNT}", 1)
            val mediaKeyPressTimeout = storageManager.loadLong(StorageDomain.CONDITIONS, "${prefix}${FIELD_MEDIA_KEY_PRESS_TIMEOUT}", 1000L)

            // 加载新增的触发条件字段
            val shortcutOpeningName = storageManager.loadString(StorageDomain.CONDITIONS, "${prefix}${FIELD_SHORTCUT_OPENING_NAME}", "")

            val swipeStartCorner = storageManager.loadString(StorageDomain.CONDITIONS, "${prefix}${FIELD_SWIPE_START_CORNER}").let {
                try {
                    SwipeStartCorner.fromValue(it)
                } catch (e: Exception) {
                    Log.w(TAG, "无效的滑动开始区域: $it, 使用默认值")
                    SwipeStartCorner.TOP_LEFT
                }
            }

            val swipeDirection = storageManager.loadString(StorageDomain.CONDITIONS, "${prefix}${FIELD_SWIPE_DIRECTION}").let {
                try {
                    SwipeDirection.fromValue(it)
                } catch (e: Exception) {
                    Log.w(TAG, "无效的滑动方向: $it, 使用默认值")
                    SwipeDirection.HORIZONTAL
                }
            }

            val volumeButtonType = storageManager.loadString(StorageDomain.CONDITIONS, "${prefix}${FIELD_VOLUME_BUTTON_TYPE}").let {
                try {
                    VolumeButtonType.fromValue(it)
                } catch (e: Exception) {
                    Log.w(TAG, "无效的音量按钮类型: $it, 使用默认值")
                    VolumeButtonType.VOLUME_UP
                }
            }

            val volumeButtonLongPressThreshold = storageManager.loadLong(StorageDomain.CONDITIONS, "${prefix}${FIELD_VOLUME_BUTTON_LONG_PRESS_THRESHOLD}", 1000L)

            val volumeKeyType = storageManager.loadString(StorageDomain.CONDITIONS, "${prefix}${FIELD_VOLUME_KEY_TYPE}").let {
                try {
                    VolumeButtonType.fromValue(it)
                } catch (e: Exception) {
                    Log.w(TAG, "无效的音量键类型: $it, 使用默认值")
                    VolumeButtonType.VOLUME_UP
                }
            }

            val volumeKeyPreserveVolume = storageManager.loadBoolean(StorageDomain.CONDITIONS, "${prefix}${FIELD_VOLUME_KEY_PRESERVE_VOLUME}", false)
            val powerButtonLongPressThreshold = storageManager.loadLong(StorageDomain.CONDITIONS, "${prefix}${FIELD_POWER_BUTTON_LONG_PRESS_THRESHOLD}", 1000L)

            // 重建ManualTriggerCondition对象
            val condition = ManualTriggerCondition(
                id = conditionId,
                triggerType = triggerType,
                slotIndex = slotIndex,
                buttonText = buttonText,
                shortcutName = shortcutName,
                buttonSize = buttonSize,
                buttonAlpha = buttonAlpha,
                fingerprintGestureType = fingerprintGestureType,
                homeButtonLongPressThreshold = homeButtonLongPressThreshold,
                mediaKeyType = mediaKeyType,
                mediaKeyPressCount = mediaKeyPressCount,
                mediaKeyPressTimeout = mediaKeyPressTimeout,
                shortcutOpeningName = shortcutOpeningName,
                swipeStartCorner = swipeStartCorner,
                swipeDirection = swipeDirection,
                volumeButtonType = volumeButtonType,
                volumeButtonLongPressThreshold = volumeButtonLongPressThreshold,
                volumeKeyType = volumeKeyType,
                volumeKeyPreserveVolume = volumeKeyPreserveVolume,
                powerButtonLongPressThreshold = powerButtonLongPressThreshold
            )

            Log.d(TAG, "手动触发条件加载成功: $conditionId")
            condition

        } catch (e: Exception) {
            Log.e(TAG, "加载手动触发条件时发生异常: $conditionId", e)
            null
        }
    }
}
