package com.weinuo.quickcommands22.permission

import android.content.Context
import androidx.activity.result.ActivityResultLauncher
import androidx.core.app.ActivityCompat
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import com.weinuo.quickcommands22.utils.*

/**
 * 全局权限管理器
 * 统一管理所有权限申请和状态
 */
class GlobalPermissionManager private constructor(private val context: Context) {

    companion object {
        @Volatile
        private var INSTANCE: GlobalPermissionManager? = null

        fun getInstance(context: Context): GlobalPermissionManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: GlobalPermissionManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }

    /**
     * 权限类型枚举
     */
    enum class PermissionType {
        COMMUNICATION,  // 通信权限（通话记录、短信、联系人）
        LOCATION,       // 位置权限（精确位置、粗略位置）
        BLUETOOTH,      // 蓝牙权限（连接、扫描，支持Android 12+新权限模型）
        SENSOR,         // 传感器权限（运动识别）
        DEVICE_ADMIN,   // 设备管理器权限（设备管理员）
        NOTIFICATION_LISTENER,  // 通知使用权限（通知监听）
        NOTIFICATION,   // 通知权限（发送通知，Android 13+）
        BUBBLE,         // 气泡通知权限（显示气泡通知）
        OVERLAY,        // 悬浮窗权限（系统悬浮窗）
        MEDIA,          // 媒体权限（录音）
        STORAGE,        // 存储权限（文件访问、所有文件权限）
        CAMERA,         // 相机权限（拍照、录像、访问照片和视频）
        NETWORK,        // 网络权限（网络访问、网络状态、WiFi状态）
        ACCESSIBILITY,  // 无障碍服务权限（状态栏控制等）
        MICROPHONE,     // 麦克风权限（语音搜索、录音）
        SHIZUKU,        // Shizuku权限（Shell命令执行）
        USAGE_STATS,    // 使用情况访问权限（应用状态监控）
        DO_NOT_DISTURB  // 勿扰模式权限（通知策略访问权限）
    }

    /**
     * 权限状态数据类
     */
    data class PermissionState(
        val type: PermissionType,
        val isGranted: Boolean,
        val isRequesting: Boolean = false,
        val isPermanentlyDenied: Boolean = false,
        val shouldShowRationale: Boolean = false
    )

    // 权限状态管理
    private val _permissionStates = MutableStateFlow<Map<PermissionType, PermissionState>>(
        PermissionType.values().associateWith { type ->
            PermissionState(type, checkPermissionGranted(type))
        }
    )
    val permissionStates: StateFlow<Map<PermissionType, PermissionState>> = _permissionStates.asStateFlow()

    // 权限申请启动器存储
    private val permissionLaunchers = mutableMapOf<PermissionType, ActivityResultLauncher<Array<String>>>()

    // 权限申请确认状态管理
    private val _permissionConfirmationStates = MutableStateFlow<Map<PermissionType, Boolean>>(
        PermissionType.values().associateWith { false }
    )
    val permissionConfirmationStates: StateFlow<Map<PermissionType, Boolean>> = _permissionConfirmationStates.asStateFlow()

    /**
     * 注册权限申请启动器
     */
    fun registerPermissionLauncher(
        type: PermissionType,
        launcher: ActivityResultLauncher<Array<String>>
    ) {
        permissionLaunchers[type] = launcher
    }

    /**
     * 显示权限申请确认对话框
     * 这是权限申请的第一步，用户确认后才会实际申请权限
     */
    fun showPermissionConfirmationDialog(type: PermissionType) {
        // 如果权限已经授予，直接返回
        if (checkPermissionGranted(type)) {
            return
        }

        // 显示确认对话框
        val currentStates = _permissionConfirmationStates.value.toMutableMap()
        currentStates[type] = true
        _permissionConfirmationStates.value = currentStates
    }

    /**
     * 隐藏权限申请确认对话框
     */
    fun hidePermissionConfirmationDialog(type: PermissionType) {
        val currentStates = _permissionConfirmationStates.value.toMutableMap()
        currentStates[type] = false
        _permissionConfirmationStates.value = currentStates
    }

    /**
     * 用户确认申请权限后调用此方法
     * 这是权限申请的第二步，实际执行权限申请
     */
    fun requestPermissionAfterConfirmation(type: PermissionType) {
        // 隐藏确认对话框
        hidePermissionConfirmationDialog(type)

        // 更新状态为正在申请
        updatePermissionState(type, isRequesting = true)

        when (type) {
            PermissionType.COMMUNICATION -> {
                permissionLaunchers[type]?.let { launcher ->
                    CommunicationPermissionUtil.requestCommunicationPermissions(launcher)
                }
            }
            PermissionType.LOCATION -> {
                permissionLaunchers[type]?.let { launcher ->
                    LocationPermissionUtil.requestLocationPermissions(launcher)
                }
            }
            PermissionType.BLUETOOTH -> {
                permissionLaunchers[type]?.let { launcher ->
                    BluetoothPermissionUtil.requestBluetoothPermissions(launcher)
                }
            }
            PermissionType.SENSOR -> {
                permissionLaunchers[type]?.let { launcher ->
                    SensorPermissionUtil.requestActivityRecognitionPermission(launcher)
                }
            }
            PermissionType.DEVICE_ADMIN -> {
                DeviceEventPermissionUtil.requestDeviceAdminPermission(context)
                // 设备管理器权限申请后立即更新状态
                updatePermissionState(type, isRequesting = false)
            }
            PermissionType.NOTIFICATION_LISTENER -> {
                DeviceEventPermissionUtil.requestNotificationListenerPermission(context)
                // 通知使用权限申请后立即更新状态
                updatePermissionState(type, isRequesting = false)
            }
            PermissionType.NOTIFICATION -> {
                permissionLaunchers[type]?.let { launcher ->
                    com.weinuo.quickcommands22.utils.NotificationPermissionUtil.requestNotificationPermissions(launcher)
                }
            }
            PermissionType.BUBBLE -> {
                com.weinuo.quickcommands22.utils.NotificationPermissionUtil.requestBubblePermission(context)
                // 气泡通知权限申请后立即更新状态
                updatePermissionState(type, isRequesting = false)
            }
            PermissionType.OVERLAY -> {
                OverlayPermissionUtil.requestOverlayPermission(context)
                // 悬浮窗权限申请后立即更新状态
                updatePermissionState(type, isRequesting = false)
            }
            PermissionType.MEDIA -> {
                permissionLaunchers[type]?.let { launcher ->
                    MediaPermissionUtil.requestMediaPermissions(launcher)
                }
            }
            PermissionType.STORAGE -> {
                // 存储权限需要特殊处理，跳转到设置页面
                StoragePermissionUtil.openStoragePermissionSettings(context)
                // 存储权限申请后立即更新状态
                updatePermissionState(type, isRequesting = false)
            }
            PermissionType.CAMERA -> {
                permissionLaunchers[type]?.let { launcher ->
                    // 申请相机权限（只申请相机权限，存储权限单独处理）
                    launcher.launch(arrayOf(android.Manifest.permission.CAMERA))
                }
            }
            PermissionType.NETWORK -> {
                permissionLaunchers[type]?.let { launcher ->
                    NetworkPermissionUtil.requestNetworkPermissions(launcher)
                }
            }
            PermissionType.ACCESSIBILITY -> {
                DeviceEventPermissionUtil.requestAccessibilityPermission(context)
                // 无障碍服务权限申请后立即更新状态
                updatePermissionState(type, isRequesting = false)
            }
            PermissionType.MICROPHONE -> {
                permissionLaunchers[type]?.let { launcher ->
                    MediaPermissionUtil.requestMicrophonePermission(launcher)
                }
            }
            PermissionType.SHIZUKU -> {
                // Shizuku权限通过ShizukuManager处理，使用与MainActivity一致的requestCode
                com.weinuo.quickcommands22.shizuku.ShizukuManager.requestShizukuPermission(1000)
                // Shizuku权限申请后立即更新状态
                updatePermissionState(type, isRequesting = false)
            }
            PermissionType.USAGE_STATS -> {
                // 使用情况访问权限需要跳转到设置页面
                UsageStatsPermissionUtil.openUsageStatsSettings(context)
                // 使用情况访问权限申请后立即更新状态
                updatePermissionState(type, isRequesting = false)
            }
            PermissionType.DO_NOT_DISTURB -> {
                // 勿扰模式权限需要跳转到设置页面
                openDoNotDisturbSettings()
                // 勿扰模式权限申请后立即更新状态
                updatePermissionState(type, isRequesting = false)
            }
        }
    }



    /**
     * 处理权限申请结果
     */
    fun handlePermissionResult(type: PermissionType, permissions: Map<String, Boolean>) {
        val isGranted = permissions.values.all { it }

        // 检查是否有权限被永久拒绝
        val isPermanentlyDenied = if (!isGranted) {
            checkIfPermanentlyDenied(type, permissions)
        } else {
            false
        }

        // 检查是否应该显示权限说明
        val shouldShowRationale = if (!isGranted && !isPermanentlyDenied) {
            checkShouldShowRationale(type)
        } else {
            false
        }

        updatePermissionState(
            type = type,
            isGranted = isGranted,
            isRequesting = false,
            isPermanentlyDenied = isPermanentlyDenied,
            shouldShowRationale = shouldShowRationale
        )
    }

    /**
     * 检查权限是否已授予
     */
    private fun checkPermissionGranted(type: PermissionType): Boolean {
        return when (type) {
            PermissionType.COMMUNICATION -> CommunicationPermissionUtil.hasAllCommunicationPermissions(context)
            PermissionType.LOCATION -> LocationPermissionUtil.hasLocationPermission(context)
            PermissionType.BLUETOOTH -> BluetoothPermissionUtil.hasBluetoothPermission(context)
            PermissionType.SENSOR -> SensorPermissionUtil.hasActivityRecognitionPermission(context)
            PermissionType.DEVICE_ADMIN -> DeviceEventPermissionUtil.hasDeviceAdminPermission(context)
            PermissionType.NOTIFICATION_LISTENER -> DeviceEventPermissionUtil.hasNotificationListenerPermission(context)
            PermissionType.NOTIFICATION -> com.weinuo.quickcommands22.utils.NotificationPermissionUtil.hasPostNotificationPermission(context)
            PermissionType.BUBBLE -> com.weinuo.quickcommands22.utils.NotificationPermissionUtil.areBubblesAllowed(context)
            PermissionType.OVERLAY -> OverlayPermissionUtil.hasOverlayPermission(context)
            PermissionType.MEDIA -> MediaPermissionUtil.hasAllMediaPermissions(context)
            PermissionType.STORAGE -> StoragePermissionUtil.hasStoragePermission(context)
            PermissionType.CAMERA -> com.weinuo.quickcommands22.utils.CameraPermissionUtil.hasCameraPermission(context)
            PermissionType.NETWORK -> NetworkPermissionUtil.hasNetworkPermissions(context)
            PermissionType.ACCESSIBILITY -> DeviceEventPermissionUtil.hasAccessibilityPermission(context)
            PermissionType.MICROPHONE -> MediaPermissionUtil.hasMicrophonePermission(context)
            PermissionType.SHIZUKU -> com.weinuo.quickcommands22.shizuku.ShizukuManager.checkShizukuPermission()
            PermissionType.USAGE_STATS -> UsageStatsPermissionUtil.hasUsageStatsPermission(context)
            PermissionType.DO_NOT_DISTURB -> {
                // 勿扰模式权限检查
                if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
                    val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as android.app.NotificationManager
                    notificationManager.isNotificationPolicyAccessGranted
                } else {
                    true // Android 6.0以下版本不需要此权限
                }
            }
        }
    }

    /**
     * 检查权限是否被永久拒绝
     */
    private fun checkIfPermanentlyDenied(type: PermissionType, permissions: Map<String, Boolean>): Boolean {
        // 对于特殊权限（设备管理器、通知使用权限、气泡通知、悬浮窗权限、无障碍服务、存储权限、Shizuku、使用情况访问、勿扰模式），不适用永久拒绝概念
        if (type in listOf(PermissionType.DEVICE_ADMIN, PermissionType.NOTIFICATION_LISTENER, PermissionType.BUBBLE, PermissionType.OVERLAY, PermissionType.ACCESSIBILITY, PermissionType.STORAGE, PermissionType.SHIZUKU, PermissionType.USAGE_STATS, PermissionType.DO_NOT_DISTURB)) {
            return false
        }

        // 检查是否有权限被拒绝且不再显示权限说明
        return try {
            permissions.any { (permission, granted) ->
                !granted && !ActivityCompat.shouldShowRequestPermissionRationale(
                    context as androidx.activity.ComponentActivity, permission
                )
            }
        } catch (e: Exception) {
            // 如果无法转换为ComponentActivity，返回false
            false
        }
    }

    /**
     * 检查是否应该显示权限说明
     */
    private fun checkShouldShowRationale(type: PermissionType): Boolean {
        // 对于特殊权限，不适用权限说明概念
        if (type in listOf(PermissionType.DEVICE_ADMIN, PermissionType.NOTIFICATION_LISTENER, PermissionType.BUBBLE, PermissionType.OVERLAY, PermissionType.ACCESSIBILITY, PermissionType.STORAGE, PermissionType.SHIZUKU, PermissionType.USAGE_STATS, PermissionType.DO_NOT_DISTURB)) {
            return false
        }

        val permissions = getPermissionsForType(type)
        return try {
            permissions.any { permission ->
                ActivityCompat.shouldShowRequestPermissionRationale(
                    context as androidx.activity.ComponentActivity, permission
                )
            }
        } catch (e: Exception) {
            // 如果无法转换为ComponentActivity，返回false
            false
        }
    }

    /**
     * 获取权限类型对应的权限列表
     */
    private fun getPermissionsForType(type: PermissionType): Array<String> {
        return when (type) {
            PermissionType.COMMUNICATION -> CommunicationPermissionUtil.getRequiredCommunicationPermissions()
            PermissionType.LOCATION -> LocationPermissionUtil.getRequiredLocationPermissions()
            PermissionType.BLUETOOTH -> BluetoothPermissionUtil.getRequiredBluetoothPermissions()
            PermissionType.SENSOR -> SensorPermissionUtil.getRequiredSensorPermissions()
            PermissionType.NOTIFICATION -> com.weinuo.quickcommands22.utils.NotificationPermissionUtil.getRequiredNotificationPermissions()
            PermissionType.MEDIA -> MediaPermissionUtil.getRequiredMediaPermissions()
            PermissionType.CAMERA -> arrayOf(android.Manifest.permission.CAMERA)
            PermissionType.NETWORK -> NetworkPermissionUtil.getRequiredNetworkPermissions()
            PermissionType.MICROPHONE -> MediaPermissionUtil.getRequiredMicrophonePermissions()
            else -> emptyArray()
        }
    }

    /**
     * 更新权限状态
     */
    private fun updatePermissionState(
        type: PermissionType,
        isGranted: Boolean? = null,
        isRequesting: Boolean? = null,
        isPermanentlyDenied: Boolean? = null,
        shouldShowRationale: Boolean? = null
    ) {
        val currentStates = _permissionStates.value.toMutableMap()
        val currentState = currentStates[type] ?: PermissionState(type, false)

        currentStates[type] = currentState.copy(
            isGranted = isGranted ?: checkPermissionGranted(type),
            isRequesting = isRequesting ?: currentState.isRequesting,
            isPermanentlyDenied = isPermanentlyDenied ?: currentState.isPermanentlyDenied,
            shouldShowRationale = shouldShowRationale ?: currentState.shouldShowRationale
        )

        _permissionStates.value = currentStates
    }

    /**
     * 刷新所有权限状态
     */
    fun refreshPermissionStates() {
        val currentStates = _permissionStates.value.toMutableMap()
        PermissionType.values().forEach { type ->
            val currentState = currentStates[type] ?: PermissionState(type, false)
            currentStates[type] = currentState.copy(
                isGranted = checkPermissionGranted(type),
                isRequesting = false
            )
        }
        _permissionStates.value = currentStates
    }

    /**
     * 获取特定权限的状态
     */
    fun getPermissionState(type: PermissionType): PermissionState {
        return _permissionStates.value[type] ?: PermissionState(type, false)
    }

    /**
     * 检查权限是否已授予
     */
    fun isPermissionGranted(type: PermissionType): Boolean {
        return getPermissionState(type).isGranted
    }

    /**
     * 检查权限是否被永久拒绝
     */
    fun isPermissionPermanentlyDenied(type: PermissionType): Boolean {
        return getPermissionState(type).isPermanentlyDenied
    }

    /**
     * 检查是否应该显示权限说明
     */
    fun shouldShowPermissionRationale(type: PermissionType): Boolean {
        return getPermissionState(type).shouldShowRationale
    }

    /**
     * 打开应用设置页面（用于永久拒绝的权限）
     */
    fun openAppSettings() {
        try {
            val intent = android.content.Intent(android.provider.Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                data = android.net.Uri.fromParts("package", context.packageName, null)
                addFlags(android.content.Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            context.startActivity(intent)
        } catch (e: Exception) {
            // 忽略异常，用户可以手动到设置中授权
        }
    }

    /**
     * 打开勿扰模式权限设置页面
     */
    private fun openDoNotDisturbSettings() {
        try {
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
                val intent = android.content.Intent(android.provider.Settings.ACTION_NOTIFICATION_POLICY_ACCESS_SETTINGS).apply {
                    addFlags(android.content.Intent.FLAG_ACTIVITY_NEW_TASK)
                }
                context.startActivity(intent)
            }
        } catch (e: Exception) {
            // 如果无法打开勿扰模式设置，回退到应用设置
            try {
                val intent = android.content.Intent(android.provider.Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                    data = android.net.Uri.fromParts("package", context.packageName, null)
                    addFlags(android.content.Intent.FLAG_ACTIVITY_NEW_TASK)
                }
                context.startActivity(intent)
            } catch (e2: Exception) {
                // 忽略异常，用户可以手动到设置中授权
            }
        }
    }
}
