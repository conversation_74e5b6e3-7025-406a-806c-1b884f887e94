package com.weinuo.quickcommands22.ui.components.layered

import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import com.weinuo.quickcommands22.ui.theme.config.CardConfig

/**
 * 分层设计风格的卡片
 *
 * 特点：
 * - 使用标准Material 3 Card设计
 * - 简洁清晰的视觉分离
 * - 遵循Material Design 3规范
 * - 保持与old项目一致的简洁风格
 */
@Composable
fun LayeredCard(
    config: CardConfig,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceContainerLow
        )
    ) {
        config.content()
    }
}
