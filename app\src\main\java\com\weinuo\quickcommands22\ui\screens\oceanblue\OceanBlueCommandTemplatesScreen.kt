package com.weinuo.quickcommands22.ui.screens.oceanblue

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.*
import androidx.compose.material3.rememberTopAppBarState
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.navigation.NavController
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import com.weinuo.quickcommands22.R
import com.weinuo.quickcommands22.model.CommandTemplate
import com.weinuo.quickcommands22.model.CommandTemplateProvider

import com.weinuo.quickcommands22.ui.components.themed.ThemedCommandTemplateCardWithImage
import com.weinuo.quickcommands22.ui.components.layered.LayeredTopAppBar
import com.weinuo.quickcommands22.ui.components.themed.ThemedSearchTextField
import com.weinuo.quickcommands22.ui.theme.config.TopAppBarConfig
import com.weinuo.quickcommands22.ui.theme.config.TopAppBarStyle
import com.weinuo.quickcommands22.model.DeviceEventCondition
import com.weinuo.quickcommands22.model.DeviceActionTask
import com.weinuo.quickcommands22.model.ConnectivityTask
import com.weinuo.quickcommands22.model.MediaTask
import com.weinuo.quickcommands22.model.VolumeTask
import com.weinuo.quickcommands22.model.SensorStateCondition
import com.weinuo.quickcommands22.data.QuickCommandRepository
import java.util.UUID
import androidx.compose.ui.platform.LocalContext
import com.weinuo.quickcommands22.ui.activities.QuickCommandFormActivity

/**
 * 海洋蓝主题专用 - 指令模板界面
 *
 * 特点：
 * - 分层设计风格
 * - 使用标准Material 3组件
 * - 清晰的视觉层次
 *
 * 展示预设的快捷指令模板，用户可以选择模板快速创建快捷指令
 *
 * @param navController 导航控制器
 * @param quickCommandRepository 快捷指令仓库
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun OceanBlueCommandTemplatesScreen(
    navController: NavController,
    quickCommandRepository: QuickCommandRepository
) {
    val scrollBehavior = TopAppBarDefaults.exitUntilCollapsedScrollBehavior(
        rememberTopAppBarState()
    )
    val focusManager = LocalFocusManager.current
    val context = LocalContext.current

    // 搜索状态
    var searchQuery by remember { mutableStateOf("") }

    // 获取所有模板
    val allTemplates = remember { CommandTemplateProvider.getAllTemplates() }

    // 根据搜索查询过滤模板
    val filteredTemplates = remember(allTemplates, searchQuery, context) {
        if (searchQuery.isEmpty()) {
            allTemplates
        } else {
            allTemplates.filter { template ->
                template.getLocalizedTitle(context).contains(searchQuery, ignoreCase = true) ||
                template.getLocalizedDescription(context).contains(searchQuery, ignoreCase = true) ||
                template.category.getLocalizedDisplayName(context).contains(searchQuery, ignoreCase = true)
            }
        }
    }

    // 按分类分组模板
    val templatesByCategory = remember(filteredTemplates) {
        filteredTemplates.groupBy { it.category }
    }

    Scaffold(
        topBar = {
            LayeredTopAppBar(
                config = TopAppBarConfig(
                    title = stringResource(R.string.nav_command_templates),
                    style = TopAppBarStyle.STANDARD,
                    collapsible = true,
                    scrollBehavior = scrollBehavior,
                    windowInsets = WindowInsets.statusBars
                )
            )
        },
        modifier = Modifier.nestedScroll(scrollBehavior.nestedScrollConnection)
    ) { innerPadding ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(innerPadding)
        ) {
            // 搜索框
            ThemedSearchTextField(
                searchQuery = searchQuery,
                onSearchQueryChange = { searchQuery = it },
                onClearSearch = {
                    searchQuery = ""
                    focusManager.clearFocus()
                },
                placeholder = stringResource(R.string.search_templates),
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp)
            )

            if (allTemplates.isEmpty()) {
                // 显示空状态
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "暂无可用模板",
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        modifier = Modifier.padding(16.dp)
                    )
                }
            } else if (filteredTemplates.isEmpty()) {
                // 显示搜索无结果状态
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "未找到匹配的模板",
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        modifier = Modifier.padding(16.dp)
                    )
                }
            } else {
                // 显示模板列表
                LazyColumn(
                    modifier = Modifier
                        .weight(1f)
                        .padding(horizontal = 16.dp),
                    contentPadding = PaddingValues(bottom = 80.dp), // 为底部导航栏留出空间
                    verticalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    item {
                        Spacer(modifier = Modifier.height(8.dp))
                    }

                    // 直接显示所有模板，不按分类
                    items(filteredTemplates) { template ->
                        ThemedCommandTemplateCardWithImage(
                            template = template,
                            onClick = { selectedTemplate ->
                                // 基于模板创建新的快捷指令
                                createCommandFromTemplate(
                                    template = selectedTemplate,
                                    navController = navController,
                                    quickCommandRepository = quickCommandRepository
                                )
                            }
                        )
                    }

                    item {
                        Spacer(modifier = Modifier.height(16.dp))
                    }
                }
            }
        }
    }
}

/**
 * 基于模板创建快捷指令
 *
 * @param template 选中的模板
 * @param navController 导航控制器
 * @param quickCommandRepository 快捷指令仓库
 */
private fun createCommandFromTemplate(
    template: CommandTemplate,
    navController: NavController,
    quickCommandRepository: QuickCommandRepository
) {
    // 为模板中的指令和所有子项生成新的ID
    val newCommand = template.command.copy(
        id = UUID.randomUUID().toString(),
        triggerConditions = template.command.triggerConditions.map { condition ->
            when (condition) {
                is DeviceEventCondition -> condition.copy(id = UUID.randomUUID().toString())
                is SensorStateCondition -> condition.copy(id = UUID.randomUUID().toString())
                else -> condition // 其他类型条件保持原样，实际使用时需要根据具体类型处理
            }
        },
        tasks = template.command.tasks.map { task ->
            when (task) {
                is DeviceActionTask -> task.copy(id = UUID.randomUUID().toString())
                is ConnectivityTask -> task.copy(id = UUID.randomUUID().toString())
                is MediaTask -> task.copy(id = UUID.randomUUID().toString())
                is VolumeTask -> task.copy(id = UUID.randomUUID().toString())
                else -> task // 其他类型任务保持原样，实际使用时需要根据具体类型处理
            }
        },
        abortConditions = template.command.abortConditions.map { condition ->
            when (condition) {
                is DeviceEventCondition -> condition.copy(id = UUID.randomUUID().toString())
                is SensorStateCondition -> condition.copy(id = UUID.randomUUID().toString())
                else -> condition // 其他类型条件保持原样，实际使用时需要根据具体类型处理
            }
        }
    )

    // 保存新创建的快捷指令（使用协程）
    CoroutineScope(Dispatchers.IO).launch {
        quickCommandRepository.saveCommand(newCommand)
    }

    // 启动快捷指令表单Activity（编辑模式），让用户可以进一步自定义
    QuickCommandFormActivity.startForEdit(navController.context, newCommand.id)
}
