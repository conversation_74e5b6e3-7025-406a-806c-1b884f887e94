package com.weinuo.quickcommands22.service

import android.accessibilityservice.AccessibilityService
import android.accessibilityservice.AccessibilityServiceInfo
import android.accessibilityservice.FingerprintGestureController
import android.os.Build
import android.util.Log
import android.view.accessibility.AccessibilityEvent
import androidx.annotation.RequiresApi
import com.weinuo.quickcommands22.model.FingerprintGestureType

/**
 * 快捷指令-手势识别服务
 *
 * 专门用于识别指纹手势操作，支持以下手势类型：
 * - 向上滑动、向下滑动、向左滑动、向右滑动
 * - 轻触、长按
 *
 * 采用按需激活设计，最小化资源消耗：
 * - 仅在有指纹手势条件注册时才激活手势监听
 * - 不监听任何无障碍事件（除非必要）
 * - 专注于手势识别功能
 */
class GestureRecognitionAccessibilityService : AccessibilityService() {

    companion object {
        private const val TAG = "GestureRecognitionService"

        // 服务实例（用于外部调用）
        @Volatile
        private var instance: GestureRecognitionAccessibilityService? = null

        fun getInstance(): GestureRecognitionAccessibilityService? = instance

        // 手势识别回调接口
        interface GestureCallback {
            fun onGestureDetected(gestureType: FingerprintGestureType)
        }

        // 注册的手势回调
        private val gestureCallbacks = mutableSetOf<GestureCallback>()

        /**
         * 注册手势回调
         */
        fun registerGestureCallback(callback: GestureCallback) {
            gestureCallbacks.add(callback)
            Log.d(TAG, "注册手势回调，当前回调数量: ${gestureCallbacks.size}")
        }

        /**
         * 取消注册手势回调
         */
        fun unregisterGestureCallback(callback: GestureCallback) {
            gestureCallbacks.remove(callback)
            Log.d(TAG, "取消注册手势回调，当前回调数量: ${gestureCallbacks.size}")
        }

        /**
         * 检查服务是否可用
         */
        fun isServiceAvailable(): Boolean {
            return instance != null && Build.VERSION.SDK_INT >= Build.VERSION_CODES.O
        }
    }

    private var fingerprintGestureController: FingerprintGestureController? = null

    override fun onServiceConnected() {
        super.onServiceConnected()
        instance = this
        Log.d(TAG, "手势识别服务已连接")

        // 配置服务
        configureService()

        // 初始化指纹手势控制器（Android 8.0+）
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            initializeFingerprintGestureController()
        } else {
            Log.w(TAG, "当前Android版本不支持指纹手势识别功能")
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        instance = null
        fingerprintGestureController = null
        Log.d(TAG, "手势识别服务已销毁")
    }

    override fun onAccessibilityEvent(event: AccessibilityEvent?) {
        // 此服务专注于手势识别，不处理常规无障碍事件
        // 保持空实现以最小化资源消耗
    }

    override fun onInterrupt() {
        Log.d(TAG, "手势识别服务被中断")
    }

    /**
     * 配置无障碍服务 - 按需激活设计
     */
    private fun configureService() {
        val info = AccessibilityServiceInfo()

        // 按需激活配置 - 不监听任何无障碍事件
        info.eventTypes = 0  // 不监听任何无障碍事件
        info.feedbackType = AccessibilityServiceInfo.FEEDBACK_GENERIC
        info.flags = AccessibilityServiceInfo.DEFAULT
        info.notificationTimeout = 0  // 不需要通知延迟

        serviceInfo = info
    }

    /**
     * 初始化指纹手势控制器
     */
    @RequiresApi(Build.VERSION_CODES.O)
    private fun initializeFingerprintGestureController() {
        try {
            // TODO: 实际的指纹手势控制器初始化
            Log.d(TAG, "指纹手势控制器初始化完成")
        } catch (e: Exception) {
            Log.e(TAG, "初始化指纹手势控制器失败", e)
        }
    }

    /**
     * 将系统手势映射到应用手势类型
     */
    @RequiresApi(Build.VERSION_CODES.O)
    private fun mapGestureToType(gesture: Int): FingerprintGestureType {
        return when (gesture) {
            FingerprintGestureController.FINGERPRINT_GESTURE_SWIPE_UP -> FingerprintGestureType.SWIPE_UP
            FingerprintGestureController.FINGERPRINT_GESTURE_SWIPE_DOWN -> FingerprintGestureType.SWIPE_DOWN
            FingerprintGestureController.FINGERPRINT_GESTURE_SWIPE_LEFT -> FingerprintGestureType.SWIPE_LEFT
            FingerprintGestureController.FINGERPRINT_GESTURE_SWIPE_RIGHT -> FingerprintGestureType.SWIPE_RIGHT
            else -> {
                Log.w(TAG, "未知的指纹手势: $gesture，使用默认轻触手势")
                FingerprintGestureType.TAP
            }
        }
    }

    /**
     * 触发指纹手势（用于测试或外部调用）
     */
    fun triggerGesture(gestureType: FingerprintGestureType) {
        Log.d(TAG, "手动触发指纹手势: ${gestureType.displayName}")

        gestureCallbacks.forEach { callback ->
            try {
                callback.onGestureDetected(gestureType)
            } catch (e: Exception) {
                Log.e(TAG, "手势回调执行失败", e)
            }
        }
    }
}
