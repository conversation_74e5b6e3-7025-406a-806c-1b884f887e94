package com.weinuo.quickcommands22.ui.activities

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.provider.MediaStore
import android.util.Log
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.Check
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.lifecycle.lifecycleScope
import com.weinuo.quickcommands22.ui.components.CircleCropImageView
import com.weinuo.quickcommands22.ui.theme.QuickCommandsTheme
import com.weinuo.quickcommands22.utils.IconFileManager
import com.weinuo.quickcommands22.data.SettingsRepository
import com.weinuo.quickcommands22.ui.theme.manager.ThemeManager
import com.weinuo.quickcommands22.ui.components.skyblue.SkyBlueBackButton
import com.weinuo.quickcommands22.ui.components.skyblue.SkyBlueSaveButton
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * 图标选择Activity
 * 
 * 功能：
 * - 启动系统文件选择器选择图片
 * - 显示圆形裁剪界面
 * - 保存裁剪后的图标
 * - 返回图标路径给调用方
 */
class IconSelectionActivity : ComponentActivity() {

    companion object {
        private const val TAG = "IconSelectionActivity"
        const val EXTRA_COMMAND_ID = "command_id"
        const val EXTRA_IMAGE_URI = "image_uri"
        const val EXTRA_ICON_PATH = "icon_path"

        /**
         * 启动图标选择Activity
         *
         * @param context 上下文
         * @param commandId 快捷指令ID
         * @param imageUri 要裁剪的图片URI
         */
        fun startForResult(context: Context, commandId: String, imageUri: String, requestCode: Int) {
            val intent = Intent(context, IconSelectionActivity::class.java).apply {
                putExtra(EXTRA_COMMAND_ID, commandId)
                putExtra(EXTRA_IMAGE_URI, imageUri)
            }
            if (context is Activity) {
                context.startActivityForResult(intent, requestCode)
            }
        }
    }

    private var commandId: String = ""
    private var imageUri: Uri? = null
    private var cropImageView: CircleCropImageView? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 获取传入的参数
        commandId = intent.getStringExtra(EXTRA_COMMAND_ID) ?: ""
        val imageUriString = intent.getStringExtra(EXTRA_IMAGE_URI)

        if (commandId.isEmpty() || imageUriString.isNullOrEmpty()) {
            Log.e(TAG, "参数不完整，关闭Activity")
            finish()
            return
        }

        imageUri = Uri.parse(imageUriString)

        setContent {
            QuickCommandsTheme {
                IconSelectionScreen(
                    onBackClick = { finish() },
                    onSaveClick = { saveCroppedIcon() },
                    onCropViewCreated = { view ->
                        cropImageView = view
                        // 直接加载图片
                        imageUri?.let { loadImageToCropView(it) }
                    }
                )
            }
        }
    }



    /**
     * 加载图片到裁剪视图
     */
    private fun loadImageToCropView(uri: Uri) {
        try {
            val bitmap = MediaStore.Images.Media.getBitmap(contentResolver, uri)
            cropImageView?.setImageBitmap(bitmap)
        } catch (e: Exception) {
            Log.e(TAG, "加载图片失败", e)
            Toast.makeText(this, "加载图片失败", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 保存裁剪后的图标
     */
    private fun saveCroppedIcon() {
        val cropView = cropImageView
        if (cropView == null) {
            Toast.makeText(this, "请先选择图片", Toast.LENGTH_SHORT).show()
            return
        }

        // 在协程中执行保存操作
        lifecycleScope.launch {
            try {
                val croppedBitmap = withContext(Dispatchers.Default) {
                    cropView.getCroppedBitmap(200) // 输出200x200的圆形图标
                }

                if (croppedBitmap != null) {
                    val iconPath = withContext(Dispatchers.IO) {
                        IconFileManager.getInstance(this@IconSelectionActivity)
                            .saveIcon(commandId, croppedBitmap)
                    }

                    if (iconPath != null) {
                        // 返回结果
                        val resultIntent = Intent().apply {
                            putExtra(EXTRA_ICON_PATH, iconPath)
                        }
                        setResult(Activity.RESULT_OK, resultIntent)
                        finish()
                    } else {
                        Toast.makeText(this@IconSelectionActivity, "保存图标失败", Toast.LENGTH_SHORT).show()
                    }
                } else {
                    Toast.makeText(this@IconSelectionActivity, "裁剪图片失败", Toast.LENGTH_SHORT).show()
                }
            } catch (e: Exception) {
                Log.e(TAG, "保存图标时发生异常", e)
                Toast.makeText(this@IconSelectionActivity, "保存图标失败", Toast.LENGTH_SHORT).show()
            }
        }
    }
}

/**
 * 图标选择界面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun IconSelectionScreen(
    onBackClick: () -> Unit,
    onSaveClick: () -> Unit,
    onCropViewCreated: (CircleCropImageView) -> Unit
) {
    val context = LocalContext.current

    // 获取主题管理器和设置仓库
    val themeManager = remember { ThemeManager.getInstance(context) }
    val settingsRepository = remember { SettingsRepository(context) }
    val globalSettings by settingsRepository.globalSettings.collectAsState()

    // 跟踪裁剪视图是否已创建
    var isCropViewReady by remember { mutableStateOf(false) }

    // 主题感知的标题样式
    val themeAwareTitleStyle = if (themeManager.getCurrentThemeId() == "sky_blue") {
        // 天空蓝主题：使用全局设置的标题字重和字体大小
        MaterialTheme.typography.titleLarge.copy(
            fontWeight = when (globalSettings.topAppBarTitleFontWeight) {
                "normal" -> FontWeight.Normal
                "medium" -> FontWeight.Medium
                "bold" -> FontWeight.Bold
                else -> FontWeight.Medium
            },
            fontSize = globalSettings.screenTitleFontSize.sp
        )
    } else {
        // 海洋蓝主题：保持原有样式
        MaterialTheme.typography.titleLarge
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = "选择图标",
                        style = themeAwareTitleStyle
                    )
                },
                navigationIcon = {
                    if (themeManager.getCurrentThemeId() == "sky_blue") {
                        // 天空蓝主题：使用专用的返回按钮
                        SkyBlueBackButton(onClick = onBackClick)
                    } else {
                        // 其他主题：使用原有的箭头图标
                        IconButton(onClick = onBackClick) {
                            Icon(
                                imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                                contentDescription = "返回"
                            )
                        }
                    }
                },
                actions = {
                    // 保存按钮 - 主题感知，始终显示但根据裁剪视图状态控制可用性
                    if (themeManager.getCurrentThemeId() == "sky_blue") {
                        // 天空蓝主题：使用专用的保存按钮（勾号图标）
                        SkyBlueSaveButton(
                            onClick = onSaveClick,
                            enabled = isCropViewReady
                        )
                    } else {
                        // 其他主题：使用原有的勾号图标
                        IconButton(
                            onClick = onSaveClick,
                            enabled = isCropViewReady
                        ) {
                            Icon(
                                imageVector = Icons.Filled.Check,
                                contentDescription = "保存"
                            )
                        }
                    }
                }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // 裁剪视图容器
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f),
                contentAlignment = Alignment.Center
            ) {
                AndroidView<CircleCropImageView>(
                    factory = { context ->
                        CircleCropImageView(context).also { view ->
                            onCropViewCreated(view)
                            isCropViewReady = true // 标记裁剪视图已准备就绪
                        }
                    },
                    modifier = Modifier.fillMaxSize()
                )
            }

            Spacer(modifier = Modifier.height(16.dp))

            // 使用说明
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surfaceVariant
                )
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "使用说明",
                        style = MaterialTheme.typography.titleSmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "• 可以拖拽和缩放图片调整位置\n• 双击可以重置图片位置\n• 只有圆形区域内的内容会被保存",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }
    }
}
