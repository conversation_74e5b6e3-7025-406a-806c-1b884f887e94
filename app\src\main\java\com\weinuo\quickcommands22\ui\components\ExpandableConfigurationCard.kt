package com.weinuo.quickcommands22.ui.components

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.expandVertically
import androidx.compose.animation.shrinkVertically
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ExpandLess
import androidx.compose.material.icons.filled.ExpandMore
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import com.weinuo.quickcommands22.permission.PermissionAwareOperationSelector
import com.weinuo.quickcommands22.permission.GlobalPermissionManager
import com.weinuo.quickcommands22.permission.PermissionRegistry
import androidx.compose.runtime.collectAsState

/**
 * 通用可展开配置卡片组件
 *
 * 高度可复用的配置卡片组件，支持泛型操作类型，内置权限检查逻辑，
 * 提供平滑的展开/折叠动画效果，遵循Material Design 3设计规范。
 *
 * 统一权限检查策略：
 * - 权限检查时机：只在卡片展开前进行权限检查，作为展开的前置条件
 * - 权限检查触发：用户点击需要权限的卡片时，先检查权限，通过后才展开卡片
 * - 权限检查位置：在点击处理逻辑中进行，使用LaunchedEffect(selectedOperation)
 * - 权限检查组件：使用PermissionAwareOperationSelector统一权限检查组件
 * - 权限检查条件：通过permissionRequired参数控制，只有permissionRequired=true的配置项才需要权限检查
 * - 权限未授予处理：权限被拒绝时，卡片保持收起状态，不展开配置界面
 * - 权限处理自动化：PermissionAwareOperationSelector会自动处理所有权限相关逻辑
 *
 * @param T 操作类型的泛型参数，支持任意枚举类型
 * @param title 卡片标题
 * @param description 卡片描述
 * @param operationType 操作类型，用于权限检查
 * @param isExpanded 是否展开状态
 * @param onExpandToggle 展开/折叠切换回调
 * @param permissionRequired 是否需要权限检查，默认为true
 * @param content 卡片内容区域的Composable内容
 */
@Composable
fun <T : Any> ExpandableConfigurationCard(
    title: String,
    description: String,
    operationType: T,
    isExpanded: Boolean,
    onExpandToggle: () -> Unit,
    permissionRequired: Boolean = true,
    content: @Composable () -> Unit
) {
    val context = LocalContext.current

    // 权限检查状态 - 用于控制卡片展开
    var selectedOperation by remember { mutableStateOf<T?>(null) }

    // 权限检查 - 只在用户选择需要权限的操作时进行
    if (permissionRequired && selectedOperation != null) {
        val globalPermissionManager = remember { GlobalPermissionManager.getInstance(context) }
        val permissionStates by globalPermissionManager.permissionStates.collectAsState()

        PermissionAwareOperationSelector(
            selectedOperation = selectedOperation!!,
            onPermissionDialogDismissed = {
                // 权限对话框关闭时重置selectedOperation，确保下次点击能重新触发权限检查
                selectedOperation = null
            },
            context = context
        )

        // 权限检查完成后的处理逻辑 - 监听权限状态变化
        LaunchedEffect(selectedOperation, permissionStates) {
            if (selectedOperation != null) {
                // 检查当前操作是否需要权限以及权限是否已授予
                val hasRequiredPermissions = PermissionRegistry.hasPermissionForOperation(
                    operation = selectedOperation!!,
                    globalPermissionManager = globalPermissionManager,
                    context = context
                )

                if (hasRequiredPermissions && !isExpanded) {
                    // 权限已授予且卡片未展开，展开卡片并重置权限检查状态
                    onExpandToggle()
                    selectedOperation = null
                }
                // 如果权限未授予，保持selectedOperation状态，继续显示权限检查组件
                // 如果卡片已展开，也重置权限检查状态
                if (isExpanded) {
                    selectedOperation = null
                }
            }
        }
    }

    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 4.dp)
            .clickable(
                interactionSource = remember { MutableInteractionSource() },
                indication = null // 移除涟漪效果
            ) {
                // 统一权限检查策略：权限检查作为卡片展开的前置条件
                if (permissionRequired && !isExpanded) {
                    // 需要权限且卡片未展开时，先检查权限是否已授予
                    val globalPermissionManager = GlobalPermissionManager.getInstance(context)
                    val hasRequiredPermissions = PermissionRegistry.hasPermissionForOperation(
                        operation = operationType,
                        globalPermissionManager = globalPermissionManager,
                        context = context
                    )

                    if (hasRequiredPermissions) {
                        // 权限已授予，直接展开卡片
                        onExpandToggle()
                    } else {
                        // 权限未授予，触发权限检查
                        selectedOperation = operationType
                    }
                } else {
                    // 不需要权限或卡片已展开时，直接切换展开状态
                    onExpandToggle()
                }
            },
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = if (isExpanded) {
                MaterialTheme.colorScheme.primaryContainer
            } else {
                MaterialTheme.colorScheme.surfaceContainerLow
            }
        ),

    ) {
        Column(
            modifier = Modifier.fillMaxWidth()
        ) {
            // 卡片头部 - 标题、描述和展开按钮
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column(
                    modifier = Modifier.weight(1f)
                ) {
                    Text(
                        text = title,
                        style = MaterialTheme.typography.titleMedium,
                        color = if (isExpanded) {
                            MaterialTheme.colorScheme.onPrimaryContainer
                        } else {
                            MaterialTheme.colorScheme.onSurface
                        }
                    )
                    Spacer(modifier = Modifier.height(4.dp))
                    Text(
                        text = description,
                        style = MaterialTheme.typography.bodyMedium,
                        color = if (isExpanded) {
                            MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.7f)
                        } else {
                            MaterialTheme.colorScheme.onSurfaceVariant
                        }
                    )
                }

                IconButton(
                    onClick = {
                        // 统一权限检查策略：权限检查作为卡片展开的前置条件
                        if (permissionRequired && !isExpanded) {
                            // 需要权限且卡片未展开时，先检查权限是否已授予
                            val globalPermissionManager = GlobalPermissionManager.getInstance(context)
                            val hasRequiredPermissions = PermissionRegistry.hasPermissionForOperation(
                                operation = operationType,
                                globalPermissionManager = globalPermissionManager,
                                context = context
                            )

                            if (hasRequiredPermissions) {
                                // 权限已授予，直接展开卡片
                                onExpandToggle()
                            } else {
                                // 权限未授予，触发权限检查
                                selectedOperation = operationType
                            }
                        } else {
                            // 不需要权限或卡片已展开时，直接切换展开状态
                            onExpandToggle()
                        }
                    },
                    interactionSource = remember { MutableInteractionSource() },
                    colors = IconButtonDefaults.iconButtonColors(
                        contentColor = if (isExpanded) {
                            MaterialTheme.colorScheme.onPrimaryContainer
                        } else {
                            MaterialTheme.colorScheme.onSurfaceVariant
                        }
                    )
                ) {
                    Icon(
                        imageVector = if (isExpanded) Icons.Default.ExpandLess else Icons.Default.ExpandMore,
                        contentDescription = if (isExpanded) "收起" else "展开"
                    )
                }
            }

            // 可展开的内容区域
            AnimatedVisibility(
                visible = isExpanded,
                enter = expandVertically(),
                exit = shrinkVertically()
            ) {
                // 让卡片根据内容自然扩展，由LazyColumn处理整体滚动
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(start = 16.dp, end = 16.dp, bottom = 16.dp)
                ) {
                    // 使用 Spacer 分隔头部和内容区域，与全局设置界面设计保持一致
                    Spacer(modifier = Modifier.height(16.dp))

                    // 自定义内容区域 - 让内容自然展开，无滚动限制
                    content()
                }
            }
        }
    }
}

/**
 * 配置项数据结构
 *
 * 定义通用的配置项数据结构，支持泛型操作类型，
 * 包含权限配置选项和自定义配置内容组件。
 *
 * @param T 操作类型的泛型参数
 * @param id 配置项唯一标识符
 * @param title 配置项标题
 * @param description 配置项描述
 * @param operationType 操作类型，用于权限检查
 * @param permissionRequired 是否需要权限检查，默认为true
 * @param isExperimental 是否为实验性功能，默认为false
 * @param content 配置内容组件，接收操作类型和完成回调
 * @param editableContent 支持编辑模式的配置内容组件，接收操作类型、初始配置对象和完成回调
 */
data class ConfigurationCardItem<T>(
    val id: String,
    val title: String,
    val description: String,
    val operationType: T,
    val permissionRequired: Boolean = true,
    val isExperimental: Boolean = false,
    val content: @Composable (T, (Any) -> Unit) -> Unit,
    val editableContent: (@Composable (T, Any?, (Any) -> Unit) -> Unit)? = null
)
