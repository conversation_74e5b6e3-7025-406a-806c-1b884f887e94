package com.weinuo.quickcommands22.monitoring

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.BatteryManager
import android.os.Build
import android.os.PowerManager
import android.util.Log
import com.weinuo.quickcommands22.model.*
import kotlinx.coroutines.*

/**
 * 电池状态监听器
 * 监听电池电量、充电状态、温度、电源键、省电模式等状态变化，触发相应的条件
 *
 * 支持的监听功能：
 * - 电池电量变化（低于/高于阈值、电量变化超过阈值、电量过低警告）
 * - 充电状态变化（开始充电、停止充电、充电完成）
 * - 电池温度变化（温度变化超过阈值）
 * - 电源键事件（按下、长按、多次按下）
 * - 省电模式变化（启用/禁用）
 */
class BatteryStateMonitor(
    private val context: Context,
    private val onConditionTriggered: (BatteryStateCondition, Map<String, Any>) -> Unit
) {

    companion object {
        private const val TAG = "BatteryStateMonitor"
    }

    // 协程作用域
    private val monitorScope = CoroutineScope(Dispatchers.Default + SupervisorJob())

    // 监听器状态
    private var isMonitoring = false

    // 注册的条件列表
    private val registeredConditions = mutableListOf<BatteryStateCondition>()

    // 系统服务
    private val powerManager: PowerManager by lazy {
        context.getSystemService(Context.POWER_SERVICE) as PowerManager
    }

    // 广播接收器
    private var batteryReceiver: BroadcastReceiver? = null
    private var powerSaveReceiver: BroadcastReceiver? = null
    private var powerButtonReceiver: BroadcastReceiver? = null

    // 状态缓存
    private var lastBatteryLevel: Int = -1
    private var lastChargingState: Boolean = false
    private var lastBatteryTemperature: Float = -1f
    private var lastPowerSaveMode: Boolean = false

    // 电源键按下计数
    private var powerButtonPressCount: Int = 0
    private var lastPowerButtonPressTime: Long = 0
    private val powerButtonResetDelay = 2000L // 2秒内的按键计为连续按下

    // 电池电量变化缓存（按条件ID分组）
    private val lastBatteryLevels = mutableMapOf<String, Int>()
    private val lastBatteryTemperatures = mutableMapOf<String, Float>()

    /**
     * 注册电池状态条件
     */
    fun registerCondition(condition: BatteryStateCondition) {
        synchronized(registeredConditions) {
            if (!registeredConditions.any { it.id == condition.id }) {
                registeredConditions.add(condition)
                Log.d(TAG, "Registered battery condition: ${condition.getDescription()}")

                // 预初始化条件状态
                preInitializeCondition(condition)
            }
        }
    }

    /**
     * 取消注册电池状态条件
     */
    fun unregisterCondition(conditionId: String) {
        synchronized(registeredConditions) {
            registeredConditions.removeAll { it.id == conditionId }
            // 清理相关缓存
            lastBatteryLevels.remove("battery_level_$conditionId")
            lastBatteryTemperatures.remove("battery_temperature_$conditionId")
            Log.d(TAG, "Unregistered battery condition: $conditionId")
        }
    }

    /**
     * 清除所有注册的条件
     */
    fun clearAllConditions() {
        synchronized(registeredConditions) {
            registeredConditions.clear()
            lastBatteryLevels.clear()
            lastBatteryTemperatures.clear()
            Log.d(TAG, "Cleared all battery conditions")
        }
    }

    /**
     * 获取已注册条件数量
     */
    fun getRegisteredConditionCount(): Int {
        return synchronized(registeredConditions) {
            registeredConditions.size
        }
    }

    /**
     * 开始监听电池状态
     */
    fun startMonitoring() {
        if (isMonitoring) {
            Log.d(TAG, "Battery monitoring already started")
            return
        }

        try {
            setupBatteryMonitoring()
            setupPowerSaveMonitoring()
            setupPowerButtonMonitoring()

            // 初始化状态缓存
            initializeStateCache()

            isMonitoring = true
            Log.d(TAG, "Battery monitoring started successfully")

        } catch (e: Exception) {
            Log.e(TAG, "Error starting battery monitoring", e)
            stopMonitoring()
        }
    }

    /**
     * 停止监听电池状态
     */
    fun stopMonitoring() {
        if (!isMonitoring) {
            Log.d(TAG, "Battery monitoring already stopped")
            return
        }

        try {
            // 取消注册广播接收器
            batteryReceiver?.let { context.unregisterReceiver(it) }
            powerSaveReceiver?.let { context.unregisterReceiver(it) }
            powerButtonReceiver?.let { context.unregisterReceiver(it) }

            batteryReceiver = null
            powerSaveReceiver = null
            powerButtonReceiver = null

            isMonitoring = false
            Log.d(TAG, "Battery monitoring stopped")

        } catch (e: Exception) {
            Log.e(TAG, "Error stopping battery monitoring", e)
        }
    }

    /**
     * 预初始化条件状态
     */
    private fun preInitializeCondition(condition: BatteryStateCondition) {
        when (condition.conditionType) {
            BatteryConditionType.BATTERY_LEVEL -> {
                if (condition.levelSubType == BatteryLevelSubType.CHANGED) {
                    val conditionKey = "battery_level_${condition.id}"
                    val currentBatteryLevel = getBatteryLevel()
                    if (currentBatteryLevel != -1) {
                        lastBatteryLevels[conditionKey] = currentBatteryLevel
                        Log.d(TAG, "Pre-initialized battery level for condition ${condition.id}: $currentBatteryLevel%")
                    }
                }
            }
            BatteryConditionType.BATTERY_TEMPERATURE -> {
                val conditionKey = "battery_temperature_${condition.id}"
                try {
                    val batteryIntent = context.registerReceiver(null, IntentFilter(Intent.ACTION_BATTERY_CHANGED))
                    val temperature = batteryIntent?.getIntExtra(BatteryManager.EXTRA_TEMPERATURE, -1) ?: -1
                    if (temperature != -1) {
                        val temperatureCelsius = temperature / 10.0f
                        lastBatteryTemperatures[conditionKey] = temperatureCelsius
                        Log.d(TAG, "Pre-initialized battery temperature for condition ${condition.id}: ${temperatureCelsius}°C")
                    }
                } catch (e: Exception) {
                    Log.w(TAG, "Failed to pre-initialize battery temperature for condition ${condition.id}", e)
                }
            }
            else -> { /* 其他电池条件不需要预初始化 */ }
        }
    }

    /**
     * 初始化状态缓存
     */
    private fun initializeStateCache() {
        try {
            // 初始化电池状态
            val batteryIntent = context.registerReceiver(null, IntentFilter(Intent.ACTION_BATTERY_CHANGED))
            if (batteryIntent != null) {
                lastBatteryLevel = batteryIntent.getIntExtra(BatteryManager.EXTRA_LEVEL, -1)
                val status = batteryIntent.getIntExtra(BatteryManager.EXTRA_STATUS, -1)
                lastChargingState = status == BatteryManager.BATTERY_STATUS_CHARGING ||
                                   status == BatteryManager.BATTERY_STATUS_FULL
                val temperature = batteryIntent.getIntExtra(BatteryManager.EXTRA_TEMPERATURE, -1)
                lastBatteryTemperature = if (temperature != -1) temperature / 10.0f else -1f
            }

            // 初始化省电模式状态
            lastPowerSaveMode = powerManager.isPowerSaveMode

            Log.d(TAG, "State cache initialized - Level: $lastBatteryLevel%, Charging: $lastChargingState, Temperature: ${lastBatteryTemperature}°C, PowerSave: $lastPowerSaveMode")

        } catch (e: Exception) {
            Log.e(TAG, "Error initializing state cache", e)
        }
    }

    /**
     * 获取当前电池电量
     */
    private fun getBatteryLevel(): Int {
        return try {
            val batteryIntent = context.registerReceiver(null, IntentFilter(Intent.ACTION_BATTERY_CHANGED))
            batteryIntent?.getIntExtra(BatteryManager.EXTRA_LEVEL, -1) ?: -1
        } catch (e: Exception) {
            Log.e(TAG, "Error getting battery level", e)
            -1
        }
    }

    /**
     * 设置电池状态监听
     */
    private fun setupBatteryMonitoring() {
        batteryReceiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context, intent: Intent) {
                monitorScope.launch {
                    handleBatteryStateChange(intent)
                }
            }
        }

        val batteryFilter = IntentFilter().apply {
            addAction(Intent.ACTION_BATTERY_CHANGED)
            addAction(Intent.ACTION_BATTERY_LOW)
            addAction(Intent.ACTION_BATTERY_OKAY)
            addAction(Intent.ACTION_POWER_CONNECTED)
            addAction(Intent.ACTION_POWER_DISCONNECTED)
        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            context.registerReceiver(batteryReceiver, batteryFilter, Context.RECEIVER_EXPORTED)
        } else {
            context.registerReceiver(batteryReceiver, batteryFilter)
        }

        Log.d(TAG, "Battery monitoring setup completed")
    }

    /**
     * 设置省电模式监听
     */
    private fun setupPowerSaveMonitoring() {
        powerSaveReceiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context, intent: Intent) {
                monitorScope.launch {
                    handlePowerSaveModeChange(intent)
                }
            }
        }

        val powerSaveFilter = IntentFilter().apply {
            addAction(PowerManager.ACTION_POWER_SAVE_MODE_CHANGED)
        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            context.registerReceiver(powerSaveReceiver, powerSaveFilter, Context.RECEIVER_EXPORTED)
        } else {
            context.registerReceiver(powerSaveReceiver, powerSaveFilter)
        }

        Log.d(TAG, "Power save monitoring setup completed")
    }

    /**
     * 设置电源键监听
     */
    private fun setupPowerButtonMonitoring() {
        powerButtonReceiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context, intent: Intent) {
                monitorScope.launch {
                    handlePowerButtonPress(intent)
                }
            }
        }

        val powerButtonFilter = IntentFilter().apply {
            addAction(Intent.ACTION_SCREEN_OFF)
            addAction(Intent.ACTION_SCREEN_ON)
        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            context.registerReceiver(powerButtonReceiver, powerButtonFilter, Context.RECEIVER_EXPORTED)
        } else {
            context.registerReceiver(powerButtonReceiver, powerButtonFilter)
        }

        Log.d(TAG, "Power button monitoring setup completed")
    }

    /**
     * 处理电池状态变化
     */
    private suspend fun handleBatteryStateChange(intent: Intent) {
        try {
            val action = intent.action
            Log.d(TAG, "Battery state changed: $action")

            when (action) {
                Intent.ACTION_BATTERY_CHANGED -> {
                    handleBatteryChanged(intent)
                }
                Intent.ACTION_BATTERY_LOW -> {
                    handleBatteryLow()
                }
                Intent.ACTION_BATTERY_OKAY -> {
                    handleBatteryOkay()
                }
                Intent.ACTION_POWER_CONNECTED -> {
                    handlePowerConnected()
                }
                Intent.ACTION_POWER_DISCONNECTED -> {
                    handlePowerDisconnected()
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error handling battery state change", e)
        }
    }

    /**
     * 处理电池状态详细变化
     */
    private suspend fun handleBatteryChanged(intent: Intent) {
        val currentLevel = intent.getIntExtra(BatteryManager.EXTRA_LEVEL, -1)
        val status = intent.getIntExtra(BatteryManager.EXTRA_STATUS, -1)
        val currentChargingState = status == BatteryManager.BATTERY_STATUS_CHARGING ||
                                  status == BatteryManager.BATTERY_STATUS_FULL
        val temperature = intent.getIntExtra(BatteryManager.EXTRA_TEMPERATURE, -1)
        val currentTemperature = if (temperature != -1) temperature / 10.0f else -1f

        // 检查电池电量条件
        if (currentLevel != -1 && currentLevel != lastBatteryLevel) {
            checkBatteryLevelConditions(currentLevel, lastBatteryLevel)
            lastBatteryLevel = currentLevel
        }

        // 检查充电状态条件
        if (currentChargingState != lastChargingState) {
            checkChargingStateConditions(currentChargingState, currentLevel)
            lastChargingState = currentChargingState
        }

        // 检查电池温度条件
        if (currentTemperature != -1f && currentTemperature != lastBatteryTemperature) {
            checkBatteryTemperatureConditions(currentTemperature, lastBatteryTemperature)
            lastBatteryTemperature = currentTemperature
        }
    }

    /**
     * 检查电池电量条件
     */
    private suspend fun checkBatteryLevelConditions(currentLevel: Int, previousLevel: Int) {
        val matchingConditions = registeredConditions.filter {
            it.conditionType == BatteryConditionType.BATTERY_LEVEL
        }

        for (condition in matchingConditions) {
            val isTriggered = when (condition.levelSubType) {
                BatteryLevelSubType.BELOW -> {
                    currentLevel < condition.levelThreshold && previousLevel >= condition.levelThreshold
                }
                BatteryLevelSubType.ABOVE -> {
                    currentLevel > condition.levelThreshold && previousLevel <= condition.levelThreshold
                }
                BatteryLevelSubType.CHANGED -> {
                    val conditionKey = "battery_level_${condition.id}"
                    val lastLevel = lastBatteryLevels[conditionKey]
                    lastBatteryLevels[conditionKey] = currentLevel

                    if (lastLevel != null) {
                        val levelChange = kotlin.math.abs(currentLevel - lastLevel)
                        levelChange >= condition.levelChangeThreshold
                    } else {
                        false // 第一次检查不触发
                    }
                }
                BatteryLevelSubType.LOW_WARNING -> {
                    currentLevel <= 15 && previousLevel > 15 // 系统低电量警告阈值
                }
            }

            if (isTriggered) {
                val eventData = mapOf(
                    "currentLevel" to currentLevel,
                    "previousLevel" to previousLevel,
                    "threshold" to condition.levelThreshold,
                    "timestamp" to System.currentTimeMillis()
                )
                onConditionTriggered(condition, eventData)
                Log.d(TAG, "Battery level condition triggered: ${condition.getDescription()}")
            }
        }
    }

    /**
     * 检查充电状态条件
     */
    private suspend fun checkChargingStateConditions(currentChargingState: Boolean, currentLevel: Int) {
        val matchingConditions = registeredConditions.filter {
            it.conditionType == BatteryConditionType.CHARGING_STATE
        }

        for (condition in matchingConditions) {
            val isTriggered = when (condition.chargingSubType) {
                ChargingSubType.STARTED -> {
                    currentChargingState && !lastChargingState
                }
                ChargingSubType.STOPPED -> {
                    !currentChargingState && lastChargingState
                }
                ChargingSubType.FULLY_CHARGED -> {
                    currentLevel >= 100 && currentChargingState
                }
            }

            if (isTriggered) {
                val eventData = mapOf(
                    "chargingState" to currentChargingState,
                    "previousChargingState" to lastChargingState,
                    "batteryLevel" to currentLevel,
                    "timestamp" to System.currentTimeMillis()
                )
                onConditionTriggered(condition, eventData)
                Log.d(TAG, "Charging state condition triggered: ${condition.getDescription()}")
            }
        }
    }

    /**
     * 检查电池温度条件
     */
    private suspend fun checkBatteryTemperatureConditions(currentTemperature: Float, previousTemperature: Float) {
        val matchingConditions = registeredConditions.filter {
            it.conditionType == BatteryConditionType.BATTERY_TEMPERATURE
        }

        for (condition in matchingConditions) {
            val isTriggered = when (condition.temperatureSubType) {
                TemperatureSubType.INCREASED -> {
                    val conditionKey = "battery_temperature_${condition.id}"
                    val lastTemperature = lastBatteryTemperatures[conditionKey]
                    lastBatteryTemperatures[conditionKey] = currentTemperature

                    if (lastTemperature != null) {
                        val temperatureChange = currentTemperature - lastTemperature
                        temperatureChange >= condition.temperatureThreshold
                    } else {
                        false // 第一次检查不触发
                    }
                }
                TemperatureSubType.DECREASED -> {
                    val conditionKey = "battery_temperature_${condition.id}"
                    val lastTemperature = lastBatteryTemperatures[conditionKey]
                    lastBatteryTemperatures[conditionKey] = currentTemperature

                    if (lastTemperature != null) {
                        val temperatureChange = lastTemperature - currentTemperature
                        temperatureChange >= condition.temperatureThreshold
                    } else {
                        false // 第一次检查不触发
                    }
                }
                TemperatureSubType.CHANGED -> {
                    val conditionKey = "battery_temperature_${condition.id}"
                    val lastTemperature = lastBatteryTemperatures[conditionKey]
                    lastBatteryTemperatures[conditionKey] = currentTemperature

                    if (lastTemperature != null) {
                        val temperatureChange = kotlin.math.abs(currentTemperature - lastTemperature)
                        temperatureChange >= condition.temperatureThreshold
                    } else {
                        false // 第一次检查不触发
                    }
                }
            }

            if (isTriggered) {
                val eventData = mapOf(
                    "currentTemperature" to currentTemperature,
                    "previousTemperature" to previousTemperature,
                    "threshold" to condition.temperatureThreshold,
                    "timestamp" to System.currentTimeMillis()
                )
                onConditionTriggered(condition, eventData)
                Log.d(TAG, "Battery temperature condition triggered: ${condition.getDescription()}")
            }
        }
    }

    /**
     * 处理电池电量过低事件
     */
    private suspend fun handleBatteryLow() {
        val matchingConditions = registeredConditions.filter {
            it.conditionType == BatteryConditionType.BATTERY_LEVEL &&
            it.levelSubType == BatteryLevelSubType.LOW_WARNING
        }

        for (condition in matchingConditions) {
            val eventData = mapOf(
                "batteryLow" to true,
                "timestamp" to System.currentTimeMillis()
            )
            onConditionTriggered(condition, eventData)
            Log.d(TAG, "Battery low condition triggered: ${condition.getDescription()}")
        }
    }

    /**
     * 处理电池电量恢复正常事件
     */
    private suspend fun handleBatteryOkay() {
        // 电池电量恢复正常，可以用于某些条件的重置
        Log.d(TAG, "Battery level okay")
    }

    /**
     * 处理电源连接事件
     */
    private suspend fun handlePowerConnected() {
        val matchingConditions = registeredConditions.filter {
            it.conditionType == BatteryConditionType.CHARGING_STATE &&
            it.chargingSubType == ChargingSubType.STARTED
        }

        for (condition in matchingConditions) {
            val eventData = mapOf(
                "powerConnected" to true,
                "timestamp" to System.currentTimeMillis()
            )
            onConditionTriggered(condition, eventData)
            Log.d(TAG, "Power connected condition triggered: ${condition.getDescription()}")
        }
    }

    /**
     * 处理电源断开事件
     */
    private suspend fun handlePowerDisconnected() {
        val matchingConditions = registeredConditions.filter {
            it.conditionType == BatteryConditionType.CHARGING_STATE &&
            it.chargingSubType == ChargingSubType.STOPPED
        }

        for (condition in matchingConditions) {
            val eventData = mapOf(
                "powerConnected" to false,
                "timestamp" to System.currentTimeMillis()
            )
            onConditionTriggered(condition, eventData)
            Log.d(TAG, "Power disconnected condition triggered: ${condition.getDescription()}")
        }
    }

    /**
     * 处理省电模式变化
     */
    private suspend fun handlePowerSaveModeChange(intent: Intent) {
        try {
            val currentPowerSaveMode = powerManager.isPowerSaveMode

            if (currentPowerSaveMode != lastPowerSaveMode) {
                val matchingConditions = registeredConditions.filter {
                    it.conditionType == BatteryConditionType.POWER_SAVE_MODE &&
                    when (it.powerSaveModeSubType) {
                        PowerSaveModeSubType.ENABLED -> currentPowerSaveMode
                        PowerSaveModeSubType.DISABLED -> !currentPowerSaveMode
                    }
                }

                for (condition in matchingConditions) {
                    val eventData = mapOf(
                        "powerSaveMode" to currentPowerSaveMode,
                        "previousPowerSaveMode" to lastPowerSaveMode,
                        "timestamp" to System.currentTimeMillis()
                    )
                    onConditionTriggered(condition, eventData)
                    Log.d(TAG, "Power save mode condition triggered: ${condition.getDescription()}")
                }

                lastPowerSaveMode = currentPowerSaveMode
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error handling power save mode change", e)
        }
    }

    /**
     * 处理电源键按下事件
     */
    private suspend fun handlePowerButtonPress(intent: Intent) {
        try {
            val action = intent.action
            val currentTime = System.currentTimeMillis()

            // 简单的电源键检测逻辑（基于屏幕开关事件）
            if (action == Intent.ACTION_SCREEN_OFF || action == Intent.ACTION_SCREEN_ON) {
                // 检查是否在重置时间内
                if (currentTime - lastPowerButtonPressTime > powerButtonResetDelay) {
                    powerButtonPressCount = 0
                }

                powerButtonPressCount++
                lastPowerButtonPressTime = currentTime

                val matchingConditions = registeredConditions.filter {
                    it.conditionType == BatteryConditionType.POWER_BUTTON &&
                    when (it.powerButtonSubType) {
                        PowerButtonSubType.PRESSED -> powerButtonPressCount >= it.powerButtonPressCount
                    }
                }

                for (condition in matchingConditions) {
                    val eventData = mapOf(
                        "pressCount" to powerButtonPressCount,
                        "action" to action,
                        "timestamp" to currentTime
                    )
                    onConditionTriggered(condition, eventData)
                    Log.d(TAG, "Power button condition triggered: ${condition.getDescription()}")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error handling power button press", e)
        }
    }
}
