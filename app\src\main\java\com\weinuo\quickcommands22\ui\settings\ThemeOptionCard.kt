package com.weinuo.quickcommands22.ui.settings

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.CheckCircle
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.unit.dp
import com.weinuo.quickcommands22.ui.theme.system.AppTheme

/**
 * 主题选项卡片组件
 *
 * 显示单个主题的选择卡片，包括：
 * - 主题预览色块
 * - 主题名称和描述
 * - 特性标签
 * - 选中状态指示
 */
@Composable
fun ThemeOptionCard(
    theme: AppTheme,
    isSelected: Boolean,
    onSelect: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .clickable { onSelect() },
        colors = CardDefaults.cardColors(
            containerColor = if (isSelected) {
                MaterialTheme.colorScheme.primaryContainer
            } else {
                MaterialTheme.colorScheme.surfaceContainerLow
            }
        ),
        border = if (isSelected) {
            BorderStroke(2.dp, MaterialTheme.colorScheme.primary)
        } else null,
        elevation = CardDefaults.cardElevation(
            defaultElevation = if (isSelected) 4.dp else 1.dp
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 主题预览色块
            ThemePreviewColorBlock(
                theme = theme,
                modifier = Modifier.size(56.dp)
            )
            
            Spacer(modifier = Modifier.width(16.dp))
            
            Column(modifier = Modifier.weight(1f)) {
                // 主题名称
                Text(
                    text = theme.displayName,
                    style = MaterialTheme.typography.titleMedium,
                    color = if (isSelected) {
                        MaterialTheme.colorScheme.onPrimaryContainer
                    } else {
                        MaterialTheme.colorScheme.onSurface
                    }
                )
                
                Spacer(modifier = Modifier.height(4.dp))
                
                // 主题描述
                Text(
                    text = getThemeDescription(theme),
                    style = MaterialTheme.typography.bodySmall,
                    color = if (isSelected) {
                        MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.7f)
                    } else {
                        MaterialTheme.colorScheme.onSurfaceVariant
                    }
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // 特性标签
                Row(
                    horizontalArrangement = Arrangement.spacedBy(6.dp)
                ) {
                    getThemeFeatures(theme).forEach { feature ->
                        ThemeFeatureChip(
                            text = feature,
                            isSelected = isSelected
                        )
                    }
                }
            }
            
            // 选中状态指示
            if (isSelected) {
                Icon(
                    imageVector = Icons.Default.CheckCircle,
                    contentDescription = "已选择",
                    tint = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.size(24.dp)
                )
            }
        }
    }
}

/**
 * 主题预览色块
 *
 * 显示主题的主要颜色组合
 */
@Composable
fun ThemePreviewColorBlock(
    theme: AppTheme,
    modifier: Modifier = Modifier
) {
    val themeCache = remember { com.weinuo.quickcommands22.ui.theme.manager.ThemeCache() }
    val colorScheme = remember(theme) {
        themeCache.getColorScheme(theme)
    }

    Box(
        modifier = modifier
            .background(
                brush = Brush.linearGradient(
                    colors = listOf(
                        colorScheme.primary,
                        colorScheme.primaryContainer,
                        colorScheme.surface
                    )
                ),
                shape = RoundedCornerShape(12.dp)
            )
    )
}

/**
 * 主题特性标签
 */
@Composable
fun ThemeFeatureChip(
    text: String,
    isSelected: Boolean
) {
    Surface(
        shape = RoundedCornerShape(12.dp),
        color = if (isSelected) {
            MaterialTheme.colorScheme.primary.copy(alpha = 0.2f)
        } else {
            MaterialTheme.colorScheme.surfaceVariant
        }
    ) {
        Text(
            text = text,
            style = MaterialTheme.typography.labelSmall,
            color = if (isSelected) {
                MaterialTheme.colorScheme.primary
            } else {
                MaterialTheme.colorScheme.onSurfaceVariant
            },
            modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
        )
    }
}

/**
 * 获取主题描述
 */
private fun getThemeDescription(theme: AppTheme): String = when (theme) {
    AppTheme.OCEAN_BLUE -> "分层设计风格，清晰的层次结构和阴影效果"
    AppTheme.SKY_BLUE -> "整合设计风格，支持模糊效果和现代化视觉体验"
}

/**
 * 获取主题特性列表
 */
private fun getThemeFeatures(theme: AppTheme): List<String> = when (theme) {
    AppTheme.OCEAN_BLUE -> listOf("分层设计", "阴影效果", "经典风格")
    AppTheme.SKY_BLUE -> listOf("整合设计", "模糊效果", "现代风格")
}
