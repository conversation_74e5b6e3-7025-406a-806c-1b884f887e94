package com.weinuo.quickcommands22.service

import android.accessibilityservice.AccessibilityService
import android.accessibilityservice.AccessibilityServiceInfo
import android.content.Intent
import android.view.accessibility.AccessibilityEvent
import android.view.KeyEvent
import android.util.Log
import com.weinuo.quickcommands22.repository.ConditionRepository

/**
 * 后台管理-系统操作服务
 *
 * 此服务专门用于执行系统级操作，包括：
 * - 打开快速设置面板
 * - 显示电源菜单
 * - 打开最近任务
 * - 打开应用抽屉
 * - 切换无障碍对讲功能
 * - 按返回键
 * - 屏幕开关控制
 * - 主屏幕按钮长按检测
 * - 媒体键按下检测
 *
 * 采用按需激活策略，减少电量消耗：
 * - 只监听必要的无障碍事件
 * - 执行完操作后立即释放资源
 * - 不进行持续的后台监控
 *
 * 注意：此服务专门用于系统操作，与存活率提升服务完全独立
 */
class SystemOperationAccessibilityService : AccessibilityService() {

    companion object {
        private const val TAG = "SystemOperationService"

        // 服务实例（用于外部调用）
        @Volatile
        private var instance: SystemOperationAccessibilityService? = null

        fun getInstance(): SystemOperationAccessibilityService? = instance

        // 系统操作类型常量
        const val ACTION_QUICK_SETTINGS = "action_quick_settings"
        const val ACTION_POWER_MENU = "action_power_menu"
        const val ACTION_RECENT_TASKS = "action_recent_tasks"
        const val ACTION_APP_DRAWER = "action_app_drawer"
        const val ACTION_ACCESSIBILITY_TOGGLE = "action_accessibility_toggle"
        const val ACTION_BACK_KEY = "action_back_key"
        const val ACTION_SCREEN_ON = "action_screen_on"
        const val ACTION_SCREEN_OFF = "action_screen_off"
    }

    // 按键检测器
    private var homeButtonLongPressDetector: HomeButtonLongPressDetector? = null
    private var mediaKeyPressDetector: MediaKeyPressDetector? = null
    private var volumeKeyPressDetector: VolumeKeyPressDetector? = null

    // 手动触发管理器
    private var manualTriggerManager: ManualTriggerManager? = null

    override fun onServiceConnected() {
        super.onServiceConnected()
        instance = this
        Log.d(TAG, "系统操作无障碍服务已连接")

        // 配置服务 - 最小化资源消耗
        configureService()

        // 初始化按键检测器
        initializeKeyDetectors()
    }

    override fun onAccessibilityEvent(event: AccessibilityEvent?) {
        // 最小化事件处理 - 只在需要时处理特定事件
        // 大部分时间保持空实现以节省CPU和电量
    }

    override fun onInterrupt() {
        Log.d(TAG, "系统操作无障碍服务被中断")
    }

    override fun onDestroy() {
        super.onDestroy()

        // 清理按键检测器
        homeButtonLongPressDetector?.cleanup()
        mediaKeyPressDetector?.cleanup()
        volumeKeyPressDetector?.cleanup()
        homeButtonLongPressDetector = null
        mediaKeyPressDetector = null
        volumeKeyPressDetector = null

        // 清理手动触发管理器
        manualTriggerManager?.cleanup()
        manualTriggerManager = null

        instance = null
        Log.d(TAG, "系统操作无障碍服务已销毁")
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        intent?.let { handleSystemOperation(it) }
        return START_NOT_STICKY // 不需要自动重启
    }

    /**
     * 配置无障碍服务 - 最小化资源消耗设计
     */
    private fun configureService() {
        val info = AccessibilityServiceInfo()

        // 最小化资源消耗配置
        info.eventTypes = 0  // 不监听任何无障碍事件（按需激活）
        info.feedbackType = AccessibilityServiceInfo.FEEDBACK_GENERIC
        info.flags = AccessibilityServiceInfo.FLAG_REQUEST_FILTER_KEY_EVENTS  // 启用按键事件过滤
        info.notificationTimeout = 0  // 不需要通知

        serviceInfo = info
    }

    /**
     * 处理系统操作请求
     */
    private fun handleSystemOperation(intent: Intent) {
        val action = intent.getStringExtra("action") ?: return

        Log.d(TAG, "执行系统操作: $action")

        when (action) {
            ACTION_QUICK_SETTINGS -> performQuickSettings()
            ACTION_POWER_MENU -> performPowerMenu()
            ACTION_RECENT_TASKS -> performRecentTasks()
            ACTION_APP_DRAWER -> performAppDrawer()
            ACTION_ACCESSIBILITY_TOGGLE -> performAccessibilityToggle()
            ACTION_BACK_KEY -> performBackKey()
            ACTION_SCREEN_ON -> performScreenOn()
            ACTION_SCREEN_OFF -> performScreenOff()
            else -> Log.w(TAG, "未知的系统操作: $action")
        }
    }

    /**
     * 打开快速设置面板
     */
    private fun performQuickSettings(): Boolean {
        return try {
            val success = performGlobalAction(GLOBAL_ACTION_QUICK_SETTINGS)
            Log.d(TAG, "快速设置面板操作结果: $success")
            success
        } catch (e: Exception) {
            Log.e(TAG, "执行快速设置操作失败", e)
            false
        }
    }

    /**
     * 显示电源菜单
     */
    private fun performPowerMenu(): Boolean {
        return try {
            val success = performGlobalAction(GLOBAL_ACTION_POWER_DIALOG)
            Log.d(TAG, "电源菜单操作结果: $success")
            success
        } catch (e: Exception) {
            Log.e(TAG, "执行电源菜单操作失败", e)
            false
        }
    }

    /**
     * 打开最近任务
     */
    private fun performRecentTasks(): Boolean {
        return try {
            val success = performGlobalAction(GLOBAL_ACTION_RECENTS)
            Log.d(TAG, "最近任务操作结果: $success")
            success
        } catch (e: Exception) {
            Log.e(TAG, "执行最近任务操作失败", e)
            false
        }
    }

    /**
     * 打开应用抽屉（通过Home键模拟）
     */
    private fun performAppDrawer(): Boolean {
        return try {
            // 先回到主屏幕
            val homeSuccess = performGlobalAction(GLOBAL_ACTION_HOME)
            if (homeSuccess) {
                // 等待一小段时间后再次按Home键打开应用抽屉
                // 注意：这个行为取决于启动器的实现
                Thread.sleep(200)
                val drawerSuccess = performGlobalAction(GLOBAL_ACTION_HOME)
                Log.d(TAG, "应用抽屉操作结果: $drawerSuccess")
                drawerSuccess
            } else {
                Log.w(TAG, "无法返回主屏幕，应用抽屉操作失败")
                false
            }
        } catch (e: Exception) {
            Log.e(TAG, "执行应用抽屉操作失败", e)
            false
        }
    }

    /**
     * 切换无障碍对讲功能
     */
    private fun performAccessibilityToggle(): Boolean {
        return try {
            // 使用无障碍快捷方式切换对讲功能
            val success = performGlobalAction(GLOBAL_ACTION_ACCESSIBILITY_SHORTCUT)
            Log.d(TAG, "无障碍对讲切换操作结果: $success")
            success
        } catch (e: Exception) {
            Log.e(TAG, "执行无障碍对讲切换操作失败", e)
            false
        }
    }

    /**
     * 按返回键
     */
    private fun performBackKey(): Boolean {
        return try {
            val success = performGlobalAction(GLOBAL_ACTION_BACK)
            Log.d(TAG, "按返回键操作结果: $success")
            success
        } catch (e: Exception) {
            Log.e(TAG, "执行按返回键操作失败", e)
            false
        }
    }

    /**
     * 开启屏幕
     */
    private fun performScreenOn(): Boolean {
        return try {
            // 通过无障碍服务开启屏幕，使用GLOBAL_ACTION_POWER模拟电源键
            // 注意：这个方法在某些设备上可能不起作用，因为无障碍服务的限制
            val success = performGlobalAction(AccessibilityService.GLOBAL_ACTION_POWER_DIALOG)
            Log.d(TAG, "开启屏幕操作结果: $success")
            success
        } catch (e: Exception) {
            Log.e(TAG, "执行开启屏幕操作失败", e)
            false
        }
    }

    /**
     * 关闭屏幕
     */
    private fun performScreenOff(): Boolean {
        return try {
            // 通过无障碍服务关闭屏幕，使用GLOBAL_ACTION_POWER模拟电源键
            // 注意：这个方法在某些设备上可能不起作用，因为无障碍服务的限制
            val success = performGlobalAction(AccessibilityService.GLOBAL_ACTION_POWER_DIALOG)
            Log.d(TAG, "关闭屏幕操作结果: $success")
            success
        } catch (e: Exception) {
            Log.e(TAG, "执行关闭屏幕操作失败", e)
            false
        }
    }

    /**
     * 执行系统操作的公共接口
     */
    fun executeSystemOperation(operationType: String): Boolean {
        return when (operationType) {
            ACTION_QUICK_SETTINGS -> performQuickSettings()
            ACTION_POWER_MENU -> performPowerMenu()
            ACTION_RECENT_TASKS -> performRecentTasks()
            ACTION_APP_DRAWER -> performAppDrawer()
            ACTION_ACCESSIBILITY_TOGGLE -> performAccessibilityToggle()
            ACTION_BACK_KEY -> performBackKey()
            ACTION_SCREEN_ON -> performScreenOn()
            ACTION_SCREEN_OFF -> performScreenOff()
            else -> {
                Log.w(TAG, "不支持的系统操作类型: $operationType")
                false
            }
        }
    }

    /**
     * 初始化按键检测器
     */
    private fun initializeKeyDetectors() {
        try {
            // 初始化手动触发管理器
            manualTriggerManager = ManualTriggerManager(this)

            // 获取条件仓库实例
            val conditionRepository = ConditionRepository(this)

            // 初始化主屏幕按钮长按检测器
            homeButtonLongPressDetector = HomeButtonLongPressDetector(this, conditionRepository).apply {
                registerCallback(object : HomeButtonLongPressDetector.HomeButtonLongPressCallback {
                    override fun onHomeButtonLongPress(condition: com.weinuo.quickcommands22.model.ManualTriggerCondition) {
                        manualTriggerManager?.handleHomeButtonLongPress(condition)
                    }
                })
            }

            // 初始化媒体键按下检测器
            mediaKeyPressDetector = MediaKeyPressDetector(this, conditionRepository).apply {
                registerCallback(object : MediaKeyPressDetector.MediaKeyPressCallback {
                    override fun onMediaKeyPress(condition: com.weinuo.quickcommands22.model.ManualTriggerCondition) {
                        manualTriggerManager?.handleMediaKeyPress(condition)
                    }
                })
            }

            // 初始化音量键按下检测器
            volumeKeyPressDetector = VolumeKeyPressDetector(this, conditionRepository).apply {
                registerCallback(object : VolumeKeyPressDetector.VolumeKeyPressCallback {
                    override fun onVolumeKeyPress(condition: com.weinuo.quickcommands22.model.ManualTriggerCondition) {
                        manualTriggerManager?.handleVolumeKeyPress(condition)
                    }
                })
            }

            Log.d(TAG, "按键检测器初始化完成")
        } catch (e: Exception) {
            Log.e(TAG, "初始化按键检测器失败", e)
        }
    }

    /**
     * 处理按键事件
     */
    override fun onKeyEvent(event: KeyEvent): Boolean {
        var handled = false

        // 主屏幕按钮长按检测
        homeButtonLongPressDetector?.let { detector ->
            if (detector.onKeyEvent(event.keyCode, event)) {
                handled = true
            }
        }

        // 媒体键按下检测
        mediaKeyPressDetector?.let { detector ->
            if (detector.onMediaKeyEvent(event.keyCode, event)) {
                handled = true
            }
        }

        // 音量键按下检测
        volumeKeyPressDetector?.let { detector ->
            if (detector.onVolumeKeyEvent(event.keyCode, event)) {
                handled = true
            }
        }

        return handled || super.onKeyEvent(event)
    }


}
