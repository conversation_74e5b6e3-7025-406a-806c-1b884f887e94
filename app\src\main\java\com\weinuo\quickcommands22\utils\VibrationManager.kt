package com.weinuo.quickcommands22.utils

import android.content.Context
import android.os.Build
import android.os.VibrationEffect
import android.os.Vibrator
import android.os.VibratorManager
import android.util.Log
import kotlinx.coroutines.*

/**
 * 震动管理器
 *
 * 提供统一的震动功能管理，支持多种震动模式和强度控制
 */
object VibrationManager {
    private const val TAG = "VibrationManager"

    /**
     * 震动模式枚举
     */
    enum class VibrationMode {
        NONE,           // 无震动
        SINGLE,         // 单次震动
        CONTINUOUS,     // 持续震动
        INTERMITTENT,   // 间歇震动
        ESCALATING      // 渐强震动
    }

    /**
     * 震动强度枚举
     */
    enum class VibrationIntensity {
        LIGHT,      // 轻微
        MEDIUM,     // 中等
        STRONG      // 强烈
    }

    /**
     * 震动配置数据类
     */
    data class VibrationConfig(
        val mode: VibrationMode = VibrationMode.NONE,
        val intensity: VibrationIntensity = VibrationIntensity.MEDIUM,
        val duration: Long = 1000L,        // 持续时间（毫秒）
        val interval: Long = 500L,         // 间歇模式的间隔时间（毫秒）
        val repeatCount: Int = 3           // 间歇模式的重复次数
    )

    private var currentVibrationJob: Job? = null

    /**
     * 获取震动器实例（现代Android API）
     */
    private fun getVibrator(context: Context): Vibrator? {
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                val vibratorManager = context.getSystemService(Context.VIBRATOR_MANAGER_SERVICE) as VibratorManager
                vibratorManager.defaultVibrator
            } else {
                // 对于Android O到Android R，直接使用VibratorManager的前身
                @Suppress("DEPRECATION")
                context.getSystemService(Context.VIBRATOR_SERVICE) as Vibrator
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting vibrator", e)
            null
        }
    }

    /**
     * 检查设备是否支持震动
     */
    fun hasVibrator(context: Context): Boolean {
        val vibrator = getVibrator(context)
        return vibrator?.hasVibrator() == true
    }

    /**
     * 根据强度获取震动幅度
     */
    private fun getAmplitude(intensity: VibrationIntensity): Int {
        return when (intensity) {
            VibrationIntensity.LIGHT -> 85    // 轻微震动
            VibrationIntensity.MEDIUM -> 170  // 中等震动
            VibrationIntensity.STRONG -> 255  // 强烈震动
        }
    }

    /**
     * 创建震动效果（现代Android API）
     */
    private fun createVibrationEffect(config: VibrationConfig): VibrationEffect? {
        val amplitude = getAmplitude(config.intensity)

        return when (config.mode) {
            VibrationMode.NONE -> null
            VibrationMode.SINGLE -> {
                VibrationEffect.createOneShot(config.duration, amplitude)
            }
            VibrationMode.CONTINUOUS -> {
                VibrationEffect.createOneShot(Long.MAX_VALUE, amplitude)
            }
            VibrationMode.INTERMITTENT -> {
                val pattern = longArrayOf(0, config.duration, config.interval)
                VibrationEffect.createWaveform(pattern, intArrayOf(0, amplitude, 0), 0)
            }
            VibrationMode.ESCALATING -> {
                val steps = 5
                val stepDuration = config.duration / steps
                val timings = mutableListOf<Long>()
                val amplitudes = mutableListOf<Int>()

                for (i in 0 until steps) {
                    timings.add(stepDuration)
                    val stepAmplitude = (amplitude * (i + 1) / steps).coerceIn(1, 255)
                    amplitudes.add(stepAmplitude)
                }

                VibrationEffect.createWaveform(timings.toLongArray(), amplitudes.toIntArray(), -1)
            }
        }
    }

    /**
     * 开始震动
     */
    fun startVibration(context: Context, config: VibrationConfig) {
        if (config.mode == VibrationMode.NONE) {
            return
        }

        val vibrator = getVibrator(context) ?: return

        if (!vibrator.hasVibrator()) {
            Log.w(TAG, "Device does not support vibration")
            return
        }

        // 停止当前震动
        stopVibration(context)

        try {
            val effect = createVibrationEffect(config)
            if (effect != null) {
                vibrator.vibrate(effect)
                Log.d(TAG, "Started vibration with mode: ${config.mode}, intensity: ${config.intensity}")
            }

            // 对于间歇震动，启动协程管理重复次数（如果不是无限重复）
            if (config.mode == VibrationMode.INTERMITTENT && config.repeatCount > 0) {
                currentVibrationJob = CoroutineScope(Dispatchers.Main).launch {
                    delay((config.duration + config.interval) * config.repeatCount.toLong())
                    stopVibration(context)
                }
            }

        } catch (e: Exception) {
            Log.e(TAG, "Error starting vibration", e)
        }
    }

    /**
     * 停止震动
     */
    fun stopVibration(context: Context) {
        try {
            currentVibrationJob?.cancel()
            currentVibrationJob = null

            val vibrator = getVibrator(context)
            vibrator?.cancel()
            Log.d(TAG, "Stopped vibration")
        } catch (e: Exception) {
            Log.e(TAG, "Error stopping vibration", e)
        }
    }

    /**
     * 检查是否正在震动
     */
    fun isVibrating(): Boolean {
        return currentVibrationJob?.isActive == true
    }

    /**
     * 预设震动配置
     */
    object Presets {
        val LIGHT_SINGLE = VibrationConfig(
            mode = VibrationMode.SINGLE,
            intensity = VibrationIntensity.LIGHT,
            duration = 200L
        )

        val MEDIUM_SINGLE = VibrationConfig(
            mode = VibrationMode.SINGLE,
            intensity = VibrationIntensity.MEDIUM,
            duration = 500L
        )

        val STRONG_SINGLE = VibrationConfig(
            mode = VibrationMode.SINGLE,
            intensity = VibrationIntensity.STRONG,
            duration = 800L
        )

        val CONTINUOUS_MEDIUM = VibrationConfig(
            mode = VibrationMode.CONTINUOUS,
            intensity = VibrationIntensity.MEDIUM
        )

        val INTERMITTENT_MEDIUM = VibrationConfig(
            mode = VibrationMode.INTERMITTENT,
            intensity = VibrationIntensity.MEDIUM,
            duration = 300L,
            interval = 200L,
            repeatCount = 5
        )

        val ESCALATING_STRONG = VibrationConfig(
            mode = VibrationMode.ESCALATING,
            intensity = VibrationIntensity.STRONG,
            duration = 2000L
        )
    }
}
