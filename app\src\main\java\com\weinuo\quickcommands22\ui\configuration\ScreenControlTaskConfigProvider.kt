package com.weinuo.quickcommands22.ui.configuration

import android.app.Activity
import android.content.Context
import android.content.Intent
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.selection.selectableGroup
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import android.widget.Toast
import com.weinuo.quickcommands22.R
import com.weinuo.quickcommands22.model.*
import com.weinuo.quickcommands22.ui.components.ConfigurationCardItem
import com.weinuo.quickcommands22.ui.components.themed.ThemedRadioButton
import com.weinuo.quickcommands22.ui.theme.config.RadioButtonConfig
import com.weinuo.quickcommands22.ui.recording.GestureRecordingActivity
import com.weinuo.quickcommands22.ui.recording.RecordingMode
import com.weinuo.quickcommands22.ui.recording.CompactRecordingModeSelector
import com.weinuo.quickcommands22.floating.FloatingRecordingManager
import com.weinuo.quickcommands22.floating.FloatingRecordingRequirement
import com.weinuo.quickcommands22.floating.AdvancedFloatingRecordingManager
import com.weinuo.quickcommands22.floating.FloatingRecordingResultManager

/**
 * 屏幕控制任务配置数据提供器
 *
 * 提供屏幕控制任务的配置项列表，支持亮度控制、屏幕开关、旋转控制等操作，
 * 使用通用组件复用架构，提供高度模块化的配置管理。
 */
object ScreenControlTaskConfigProvider {

    /**
     * 获取屏幕控制任务配置项列表
     *
     * @param context 上下文，用于获取字符串资源
     * @return 屏幕控制任务配置项列表
     */
    fun getConfigurationItems(context: Context): List<ConfigurationCardItem<ScreenControlOperation>> {
        return listOf(
            // 亮度控制配置项
            ConfigurationCardItem(
                id = "brightness_control",
                title = context.getString(R.string.screen_brightness_control),
                description = context.getString(R.string.screen_brightness_control_description),
                operationType = ScreenControlOperation.BRIGHTNESS_CONTROL,
                permissionRequired = true, // 需要系统设置权限
                content = { operation, onComplete ->
                    BrightnessControlConfigContent(operation, onComplete)
                }
            ),

            // 保持设备唤醒配置项
            ConfigurationCardItem(
                id = "keep_device_awake",
                title = context.getString(R.string.screen_keep_device_awake),
                description = context.getString(R.string.screen_keep_device_awake_description),
                operationType = ScreenControlOperation.KEEP_DEVICE_AWAKE,
                permissionRequired = true, // 需要Shizuku权限
                content = { operation, onComplete ->
                    KeepDeviceAwakeConfigContent(operation, onComplete)
                }
            ),

            // 屏幕开关配置项
            ConfigurationCardItem(
                id = "screen_on_off",
                title = "屏幕开关",
                description = "开启或关闭屏幕，支持Shizuku和无障碍服务两种实现方式",
                operationType = ScreenControlOperation.SCREEN_ON_OFF,
                permissionRequired = false, // 条件性权限需求：根据实现方式决定是否需要权限
                content = { operation, onComplete ->
                    ScreenOnOffConfigContent(operation, onComplete)
                }
            ),

            // 屏幕暗度配置项
            ConfigurationCardItem(
                id = "screen_dimness",
                title = "屏幕暗度",
                description = "控制屏幕暗度和光传感器",
                operationType = ScreenControlOperation.SCREEN_DIMNESS,
                permissionRequired = true, // 需要系统设置权限
                content = { operation, onComplete ->
                    ScreenDimnessConfigContent(operation, onComplete)
                }
            ),

            // 屏蔽屏幕触摸配置项
            ConfigurationCardItem(
                id = "block_screen_touch",
                title = "屏蔽屏幕触摸",
                description = "使用透明悬浮窗屏蔽屏幕触摸",
                operationType = ScreenControlOperation.BLOCK_SCREEN_TOUCH,
                permissionRequired = true, // 需要悬浮窗权限
                content = { operation, onComplete ->
                    BlockScreenTouchConfigContent(operation, onComplete)
                }
            ),

            // 强制屏幕旋转配置项
            ConfigurationCardItem(
                id = "force_screen_rotation",
                title = "强制屏幕旋转",
                description = "强制设置屏幕方向",
                operationType = ScreenControlOperation.FORCE_SCREEN_ROTATION,
                permissionRequired = true, // 需要系统设置权限
                content = { operation, onComplete ->
                    ForceScreenRotationConfigContent(operation, onComplete)
                }
            ),

            // 检查屏幕上的文字配置项
            ConfigurationCardItem(
                id = "check_screen_text",
                title = "检查屏幕上的文字",
                description = "检查特定文本字符串当前是否显示在屏幕上，检查结果将保存到文本文件中",
                operationType = ScreenControlOperation.CHECK_SCREEN_TEXT,
                permissionRequired = true, // 需要无障碍服务权限
                content = { operation, onComplete ->
                    CheckScreenTextConfigContent(operation, onComplete)
                }
            ),

            // 读取屏幕内容配置项
            ConfigurationCardItem(
                id = "read_screen_content",
                title = "读取屏幕内容",
                description = "将当前屏幕的内容捕获到文本文件中",
                operationType = ScreenControlOperation.READ_SCREEN_CONTENT,
                permissionRequired = true, // 需要无障碍服务权限
                content = { operation, onComplete ->
                    ReadScreenContentConfigContent(operation, onComplete)
                }
            ),

            // 检查界面元素颜色配置项
            ConfigurationCardItem(
                id = "check_pixel_color",
                title = "检查界面元素颜色",
                description = "分析屏幕指定位置的界面元素颜色特征，将颜色信息保存到文本文件中",
                operationType = ScreenControlOperation.CHECK_PIXEL_COLOR,
                permissionRequired = true, // 需要无障碍服务权限
                content = { operation, onComplete ->
                    CheckPixelColorConfigContent(operation, onComplete)
                }
            ),

            // 设置屏幕超时配置项
            ConfigurationCardItem(
                id = "set_screen_timeout",
                title = "设置屏幕超时",
                description = "设置屏幕自动关闭时间",
                operationType = ScreenControlOperation.SET_SCREEN_TIMEOUT,
                permissionRequired = true, // 需要系统设置权限
                content = { operation, onComplete ->
                    SetScreenTimeoutConfigContent(operation, onComplete)
                }
            ),

            // 自动点击回放配置项
            ConfigurationCardItem(
                id = "auto_clicker_playback",
                title = "自动点击回放",
                description = "通过即时录制或快速操作实现自动点击功能",
                operationType = ScreenControlOperation.AUTO_CLICKER_PLAYBACK,
                permissionRequired = true, // 需要自动点击器无障碍服务权限
                content = { operation, onComplete ->
                    AutoClickerPlaybackConfigContent(operation, onComplete)
                }
            )
        )
    }
}

/**
 * 亮度控制配置内容组件
 *
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun BrightnessControlConfigContent(
    operation: ScreenControlOperation,
    onComplete: (Any) -> Unit
) {

    var brightnessControlType by rememberSaveable { mutableStateOf(BrightnessControlType.PERCENTAGE) }
    var brightnessValue by rememberSaveable { mutableStateOf(50) }
    var enableAutoBrightness by rememberSaveable { mutableStateOf(false) }

    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "配置亮度控制",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        // 亮度控制类型选择
        Text(
            text = "亮度控制类型",
            style = MaterialTheme.typography.bodyMedium
        )
        Column(modifier = Modifier.selectableGroup()) {
            BrightnessControlType.values().forEach { type ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .selectable(
                            selected = (brightnessControlType == type),
                            onClick = { brightnessControlType = type },
                            role = Role.RadioButton
                        )
                        .padding(horizontal = 16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    ThemedRadioButton(
                        config = RadioButtonConfig(
                            selected = (brightnessControlType == type),
                            onClick = null,
                            enabled = true
                        )
                    )
                    Text(
                        text = when (type) {
                            BrightnessControlType.PERCENTAGE -> "百分比 (0-100%)"
                            BrightnessControlType.ABSOLUTE_VALUE -> "绝对值 (0-255)"
                        },
                        style = MaterialTheme.typography.bodyMedium,
                        modifier = Modifier.padding(start = 16.dp)
                    )
                }
            }
        }

        // 亮度值输入
        OutlinedTextField(
            value = brightnessValue.toString(),
            onValueChange = { value ->
                brightnessValue = value.toIntOrNull()?.coerceIn(
                    0,
                    if (brightnessControlType == BrightnessControlType.PERCENTAGE) 100 else 255
                ) ?: brightnessValue
            },
            label = {
                Text(
                    if (brightnessControlType == BrightnessControlType.PERCENTAGE)
                        "亮度百分比 (0-100)"
                    else
                        "亮度绝对值 (0-255)"
                )
            },
            modifier = Modifier.fillMaxWidth()
        )

        // 自动亮度开关
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Switch(
                checked = enableAutoBrightness,
                onCheckedChange = { enableAutoBrightness = it }
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = "启用自动亮度",
                style = MaterialTheme.typography.bodyMedium
            )
        }

        // 确认按钮
        Button(
            onClick = {
                val task = ScreenControlTask(
                    operation = operation,
                    brightnessControlType = brightnessControlType,
                    brightnessValue = brightnessValue,
                    enableAutoBrightness = enableAutoBrightness
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 保持设备唤醒配置内容组件
 *
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun KeepDeviceAwakeConfigContent(
    operation: ScreenControlOperation,
    onComplete: (Any) -> Unit
) {
    var keepAwakeOperation by rememberSaveable { mutableStateOf(KeepAwakeOperation.ENABLE) }

    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "配置保持设备唤醒",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        // 保持唤醒操作选择
        Text(
            text = "保持唤醒操作",
            style = MaterialTheme.typography.bodyMedium
        )
        Column(modifier = Modifier.selectableGroup()) {
            KeepAwakeOperation.values().forEach { awakeOp ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .selectable(
                            selected = (keepAwakeOperation == awakeOp),
                            onClick = { keepAwakeOperation = awakeOp },
                            role = Role.RadioButton
                        )
                        .padding(horizontal = 16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    ThemedRadioButton(
                        config = RadioButtonConfig(
                            selected = (keepAwakeOperation == awakeOp),
                            onClick = null,
                            enabled = true
                        )
                    )
                    Text(
                        text = when (awakeOp) {
                            KeepAwakeOperation.ENABLE -> "启用保持唤醒"
                            KeepAwakeOperation.DISABLE -> "禁用保持唤醒"
                        },
                        style = MaterialTheme.typography.bodyMedium,
                        modifier = Modifier.padding(start = 16.dp)
                    )
                }
            }
        }

        Text(
            text = "启用后，设备将不会自动进入休眠状态",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        // 确认按钮
        Button(
            onClick = {
                val task = ScreenControlTask(
                    operation = operation,
                    keepAwakeOperation = keepAwakeOperation
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 屏幕开关配置内容组件
 *
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 *
 * 条件性权限需求的特殊处理：屏幕开关支持Shizuku和无障碍服务两种实现方式，
 * 只有选择需要权限的实现方式时才进行权限检查，不适用标准统一权限检查策略
 */
@Composable
private fun ScreenOnOffConfigContent(
    operation: ScreenControlOperation,
    onComplete: (Any) -> Unit
) {
    val context = LocalContext.current

    var screenOnOffOperation by rememberSaveable { mutableStateOf(ScreenOnOffOperation.TURN_ON) }
    var screenOnOffImplementation by rememberSaveable { mutableStateOf<ScreenOnOffImplementation?>(null) }

    // 屏幕开关特殊权限检查状态：等待Shizuku权限授予
    var isWaitingForShizukuPermission by rememberSaveable { mutableStateOf(false) }
    // 屏幕开关特殊权限检查状态：等待无障碍服务权限授予
    var isWaitingForAccessibilityPermission by rememberSaveable { mutableStateOf(false) }

    // 观察权限状态变化，用于处理权限授予后的逻辑
    val globalPermissionManager = rememberSaveable { com.weinuo.quickcommands22.permission.GlobalPermissionManager.getInstance(context) }
    val permissionStates by globalPermissionManager.permissionStates.collectAsState()

    // 观察Shizuku权限状态变化，权限授予后自动设置为Shizuku模式
    LaunchedEffect(permissionStates, isWaitingForShizukuPermission) {
        if (isWaitingForShizukuPermission &&
            com.weinuo.quickcommands22.shizuku.ShizukuManager.checkShizukuPermission()) {
            // 权限已授予，设置为Shizuku模式
            screenOnOffImplementation = ScreenOnOffImplementation.SHIZUKU
            isWaitingForShizukuPermission = false
        }
    }

    // 观察无障碍服务权限状态变化，权限授予后自动设置为无障碍服务模式
    LaunchedEffect(permissionStates, isWaitingForAccessibilityPermission) {
        if (isWaitingForAccessibilityPermission &&
            com.weinuo.quickcommands22.service.SystemOperationAccessibilityService.getInstance() != null) {
            // 权限已授予，设置为无障碍服务模式
            screenOnOffImplementation = ScreenOnOffImplementation.ACCESSIBILITY_SERVICE
            isWaitingForAccessibilityPermission = false
        }
    }

    // 添加取消等待权限的机制：当用户选择其他模式时取消等待
    LaunchedEffect(screenOnOffImplementation) {
        when (screenOnOffImplementation) {
            ScreenOnOffImplementation.ACCESSIBILITY_SERVICE -> {
                if (isWaitingForShizukuPermission) {
                    // 用户选择了无障碍服务模式，取消等待Shizuku权限
                    isWaitingForShizukuPermission = false
                }
            }
            ScreenOnOffImplementation.SHIZUKU -> {
                if (isWaitingForAccessibilityPermission) {
                    // 用户选择了Shizuku模式，取消等待无障碍服务权限
                    isWaitingForAccessibilityPermission = false
                }
            }
            null -> {
                // 用户未选择任何模式，清除所有等待状态
                isWaitingForShizukuPermission = false
                isWaitingForAccessibilityPermission = false
            }
        }
    }

    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "配置屏幕开关",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        // 屏幕开关操作选择
        Text(
            text = "屏幕操作",
            style = MaterialTheme.typography.bodyMedium
        )
        Column(modifier = Modifier.selectableGroup()) {
            ScreenOnOffOperation.values().forEach { screenOp ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .selectable(
                            selected = (screenOnOffOperation == screenOp),
                            onClick = { screenOnOffOperation = screenOp },
                            role = Role.RadioButton
                        )
                        .padding(horizontal = 16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    ThemedRadioButton(
                        config = RadioButtonConfig(
                            selected = (screenOnOffOperation == screenOp),
                            onClick = null,
                            enabled = true
                        )
                    )
                    Text(
                        text = when (screenOp) {
                            ScreenOnOffOperation.TURN_ON -> "开启屏幕"
                            ScreenOnOffOperation.TURN_OFF -> "关闭屏幕"
                        },
                        style = MaterialTheme.typography.bodyMedium,
                        modifier = Modifier.padding(start = 16.dp)
                    )
                }
            }
        }

        // 实现方式选择
        Text(
            text = "实现方式",
            style = MaterialTheme.typography.bodyMedium
        )

        Column(modifier = Modifier.selectableGroup()) {
            ScreenOnOffImplementation.values().forEach { impl ->
                val onImplSelect: () -> Unit = {
                    when (impl) {
                        ScreenOnOffImplementation.SHIZUKU -> {
                            // 选择Shizuku模式时先检查权限
                            if (com.weinuo.quickcommands22.shizuku.ShizukuManager.checkShizukuPermission()) {
                                // 已有权限，直接设置
                                screenOnOffImplementation = impl
                            } else {
                                // 没有权限，触发权限检查
                                isWaitingForShizukuPermission = true
                            }
                        }
                        ScreenOnOffImplementation.ACCESSIBILITY_SERVICE -> {
                            // 选择无障碍服务模式时先检查权限
                            if (com.weinuo.quickcommands22.service.SystemOperationAccessibilityService.getInstance() != null) {
                                // 已有权限，直接设置
                                screenOnOffImplementation = impl
                            } else {
                                // 没有权限，触发权限检查
                                isWaitingForAccessibilityPermission = true
                            }
                        }
                    }
                }

                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .selectable(
                            selected = (screenOnOffImplementation == impl),
                            onClick = onImplSelect,
                            role = Role.RadioButton
                        )
                        .padding(horizontal = 16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    ThemedRadioButton(
                        config = RadioButtonConfig(
                            selected = (screenOnOffImplementation == impl),
                            onClick = onImplSelect,
                            enabled = true
                        )
                    )
                    Text(
                        text = impl.displayName,
                        style = MaterialTheme.typography.bodyMedium,
                        modifier = Modifier.padding(start = 16.dp)
                    )
                }
            }
        }

        // 实现方式说明
        Text(
            text = when (screenOnOffImplementation) {
                ScreenOnOffImplementation.SHIZUKU -> "通过Shizuku执行系统命令控制屏幕开关，需要Shizuku权限"
                ScreenOnOffImplementation.ACCESSIBILITY_SERVICE -> "通过无障碍服务控制屏幕开关，需要启用'后台管理-系统操作服务'"
                null -> "请选择一种实现方式来控制屏幕开关"
            },
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        // 确认按钮
        Button(
            onClick = {
                screenOnOffImplementation?.let { implementation ->
                    val task = ScreenControlTask(
                        operation = operation,
                        screenOnOffOperation = screenOnOffOperation,
                        screenOnOffImplementation = implementation
                    )
                    onComplete(task)
                }
            },
            enabled = screenOnOffImplementation != null,
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }

    // 屏幕开关特殊权限检查：只有等待Shizuku权限时才检查权限
    if (isWaitingForShizukuPermission) {
        com.weinuo.quickcommands22.permission.PermissionAwareOperationSelector(
            selectedOperation = com.weinuo.quickcommands22.permission.ShellScriptShizukuOperation.SHIZUKU_SHELL,
            onPermissionDialogDismissed = {
                // 权限对话框关闭时不清除等待状态，让LaunchedEffect处理权限授予后的逻辑
                // isWaitingForShizukuPermission 保持 true，直到权限授予或用户取消
            },
            context = context
        )
    }

    // 屏幕开关特殊权限检查：只有等待无障碍服务权限时才检查权限
    if (isWaitingForAccessibilityPermission) {
        com.weinuo.quickcommands22.permission.PermissionAwareOperationSelector(
            selectedOperation = com.weinuo.quickcommands22.model.DeviceActionOperation.BACK_KEY,
            onPermissionDialogDismissed = {
                // 权限对话框关闭时不清除等待状态，让LaunchedEffect处理权限授予后的逻辑
                // isWaitingForAccessibilityPermission 保持 true，直到权限授予或用户取消
            },
            context = context
        )
    }
}

/**
 * 屏幕暗度配置内容组件
 *
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun ScreenDimnessConfigContent(
    operation: ScreenControlOperation,
    onComplete: (Any) -> Unit
) {
    var screenDimnessSensorMode by rememberSaveable { mutableStateOf(ScreenDimnessSensorMode.SENSOR_OFF) }
    var screenDimnessPercentage by rememberSaveable { mutableStateOf(50) }

    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "配置屏幕暗度",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        // 传感器模式选择
        Text(
            text = "光传感器模式",
            style = MaterialTheme.typography.bodyMedium
        )
        Column(modifier = Modifier.selectableGroup()) {
            ScreenDimnessSensorMode.values().forEach { mode ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .selectable(
                            selected = (screenDimnessSensorMode == mode),
                            onClick = { screenDimnessSensorMode = mode },
                            role = Role.RadioButton
                        )
                        .padding(horizontal = 16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    ThemedRadioButton(
                        config = RadioButtonConfig(
                            selected = (screenDimnessSensorMode == mode),
                            onClick = null,
                            enabled = true
                        )
                    )
                    Text(
                        text = when (mode) {
                            ScreenDimnessSensorMode.SENSOR_ON -> "开启光传感器"
                            ScreenDimnessSensorMode.SENSOR_OFF -> "关闭光传感器"
                        },
                        style = MaterialTheme.typography.bodyMedium,
                        modifier = Modifier.padding(start = 16.dp)
                    )
                }
            }
        }

        // 暗度百分比输入（仅在传感器开启时显示）
        if (screenDimnessSensorMode == ScreenDimnessSensorMode.SENSOR_ON) {
            OutlinedTextField(
                value = screenDimnessPercentage.toString(),
                onValueChange = { value ->
                    screenDimnessPercentage = value.toIntOrNull()?.coerceIn(0, 100) ?: screenDimnessPercentage
                },
                label = { Text("暗度百分比 (0-100)") },
                modifier = Modifier.fillMaxWidth()
            )

            Text(
                text = "请注意，更高的值意味着屏幕更暗",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }

        // 确认按钮
        Button(
            onClick = {
                val task = ScreenControlTask(
                    operation = operation,
                    screenDimnessSensorMode = screenDimnessSensorMode,
                    screenDimnessPercentage = screenDimnessPercentage
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 屏蔽屏幕触摸配置内容组件
 *
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun BlockScreenTouchConfigContent(
    operation: ScreenControlOperation,
    onComplete: (Any) -> Unit
) {

    var touchBlockOperation by rememberSaveable { mutableStateOf(TouchBlockOperation.ENABLE) }
    var emergencyCloseEnabled by rememberSaveable { mutableStateOf(true) }
    var emergencyClickCount by rememberSaveable { mutableStateOf(5) }
    var emergencyTimeWindowSeconds by rememberSaveable { mutableStateOf(3) }

    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "配置屏幕触摸屏蔽",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        // 触摸屏蔽操作选择
        Text(
            text = "触摸屏蔽操作",
            style = MaterialTheme.typography.bodyMedium
        )
        Column(modifier = Modifier.selectableGroup()) {
            TouchBlockOperation.values().forEach { touchOp ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .selectable(
                            selected = (touchBlockOperation == touchOp),
                            onClick = { touchBlockOperation = touchOp },
                            role = Role.RadioButton
                        )
                        .padding(horizontal = 16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    ThemedRadioButton(
                        config = RadioButtonConfig(
                            selected = (touchBlockOperation == touchOp),
                            onClick = null,
                            enabled = true
                        )
                    )
                    Text(
                        text = when (touchOp) {
                            TouchBlockOperation.ENABLE -> "启用触摸屏蔽"
                            TouchBlockOperation.DISABLE -> "禁用触摸屏蔽"
                            TouchBlockOperation.TOGGLE -> "切换触摸屏蔽"
                        },
                        style = MaterialTheme.typography.bodyMedium,
                        modifier = Modifier.padding(start = 16.dp)
                    )
                }
            }
        }

        // 应急关闭设置
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Checkbox(
                checked = emergencyCloseEnabled,
                onCheckedChange = { emergencyCloseEnabled = it }
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = "启用应急关闭（左上角连续点击）",
                style = MaterialTheme.typography.bodyMedium
            )
        }

        if (emergencyCloseEnabled) {
            // 点击次数设置
            OutlinedTextField(
                value = emergencyClickCount.toString(),
                onValueChange = { value ->
                    emergencyClickCount = value.toIntOrNull()?.coerceIn(3, 10) ?: emergencyClickCount
                },
                label = { Text("连续点击次数 (3-10)") },
                modifier = Modifier.fillMaxWidth(),
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                singleLine = true
            )

            // 时间窗口设置
            OutlinedTextField(
                value = emergencyTimeWindowSeconds.toString(),
                onValueChange = { value ->
                    emergencyTimeWindowSeconds = value.toIntOrNull()?.coerceIn(1, 10) ?: emergencyTimeWindowSeconds
                },
                label = { Text("时间窗口 (1-10秒)") },
                modifier = Modifier.fillMaxWidth(),
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                singleLine = true
            )
        }

        // 确认按钮
        Button(
            onClick = {
                val task = ScreenControlTask(
                    operation = operation,
                    touchBlockOperation = touchBlockOperation,
                    emergencyCloseEnabled = emergencyCloseEnabled,
                    emergencyClickCount = emergencyClickCount,
                    emergencyTimeWindowSeconds = emergencyTimeWindowSeconds
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 强制屏幕旋转配置内容组件
 *
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun ForceScreenRotationConfigContent(
    operation: ScreenControlOperation,
    onComplete: (Any) -> Unit
) {

    var forceRotationMode by rememberSaveable { mutableStateOf(ForceRotationMode.FORCE_PORTRAIT) }

    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "配置强制屏幕旋转",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        // 旋转模式选择
        Text(
            text = "屏幕旋转模式",
            style = MaterialTheme.typography.bodyMedium
        )
        Column(modifier = Modifier.selectableGroup()) {
            ForceRotationMode.values().forEach { mode ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .selectable(
                            selected = (forceRotationMode == mode),
                            onClick = { forceRotationMode = mode },
                            role = Role.RadioButton
                        )
                        .padding(horizontal = 16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    ThemedRadioButton(
                        config = RadioButtonConfig(
                            selected = (forceRotationMode == mode),
                            onClick = null,
                            enabled = true
                        )
                    )
                    Text(
                        text = when (mode) {
                            ForceRotationMode.FORCE_PORTRAIT -> "强制竖屏"
                            ForceRotationMode.FORCE_LANDSCAPE -> "强制横屏"
                            ForceRotationMode.STOP_FORCE_ROTATION -> "停止强制旋转"
                            ForceRotationMode.FORCE_KEEP_CURRENT -> "强制保持当前方向"
                            ForceRotationMode.TOGGLE_KEEP_CURRENT -> "切换是否强制保持当前方向"
                            ForceRotationMode.FORCE_REVERSE_PORTRAIT -> "强制反向竖屏"
                            ForceRotationMode.FORCE_REVERSE_LANDSCAPE -> "强制反向横屏"
                            ForceRotationMode.FORCE_SENSOR_LANDSCAPE -> "强制传感器横屏"
                            ForceRotationMode.FORCE_SENSOR_PORTRAIT -> "强制传感器竖屏"
                            ForceRotationMode.FORCE_SENSOR_ROTATION -> "强制按传感器方向旋转"
                        },
                        style = MaterialTheme.typography.bodyMedium,
                        modifier = Modifier.padding(start = 16.dp)
                    )
                }
            }
        }

        // 确认按钮
        Button(
            onClick = {
                val task = ScreenControlTask(
                    operation = operation,
                    forceRotationMode = forceRotationMode
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 设置屏幕超时配置内容组件
 *
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun SetScreenTimeoutConfigContent(
    operation: ScreenControlOperation,
    onComplete: (Any) -> Unit
) {
    var screenTimeoutValue by rememberSaveable { mutableStateOf(30) }
    var screenTimeoutUnit by rememberSaveable { mutableStateOf(ScreenTimeoutUnit.SECONDS) }

    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "配置屏幕超时",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        // 超时时间输入
        OutlinedTextField(
            value = screenTimeoutValue.toString(),
            onValueChange = { value ->
                screenTimeoutValue = value.toIntOrNull()?.coerceAtLeast(1) ?: screenTimeoutValue
            },
            label = { Text("超时时间") },
            modifier = Modifier.fillMaxWidth()
        )

        // 时间单位选择
        Text(
            text = "时间单位",
            style = MaterialTheme.typography.bodyMedium
        )
        Column(modifier = Modifier.selectableGroup()) {
            ScreenTimeoutUnit.values().forEach { unit ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .selectable(
                            selected = (screenTimeoutUnit == unit),
                            onClick = { screenTimeoutUnit = unit },
                            role = Role.RadioButton
                        )
                        .padding(horizontal = 16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    ThemedRadioButton(
                        config = RadioButtonConfig(
                            selected = (screenTimeoutUnit == unit),
                            onClick = null,
                            enabled = true
                        )
                    )
                    Text(
                        text = when (unit) {
                            ScreenTimeoutUnit.SECONDS -> "秒"
                            ScreenTimeoutUnit.MINUTES -> "分钟"
                        },
                        style = MaterialTheme.typography.bodyMedium,
                        modifier = Modifier.padding(start = 16.dp)
                    )
                }
            }
        }

        // 确认按钮
        Button(
            onClick = {
                val task = ScreenControlTask(
                    operation = operation,
                    screenTimeoutValue = screenTimeoutValue,
                    screenTimeoutUnit = screenTimeoutUnit
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 检查屏幕文字配置内容组件
 *
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun CheckScreenTextConfigContent(
    operation: ScreenControlOperation,
    onComplete: (Any) -> Unit
) {
    var checkTextContent by rememberSaveable { mutableStateOf("") }
    var checkTextCaseSensitive by rememberSaveable { mutableStateOf(false) }
    var checkTextUseRegex by rememberSaveable { mutableStateOf(false) }
    var checkTextMatchMode by rememberSaveable { mutableStateOf(TextMatchMode.CONTAINS) }
    var checkTextIncludeOverlay by rememberSaveable { mutableStateOf(false) }
    var checkTextIgnoreHidden by rememberSaveable { mutableStateOf(true) }
    var checkTextOutputFile by rememberSaveable { mutableStateOf("") }
    var checkTextViewIdOutputFile by rememberSaveable { mutableStateOf("") }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 要检查的文本内容
        Text(
            text = stringResource(R.string.check_text_content),
            style = MaterialTheme.typography.titleMedium
        )

        OutlinedTextField(
            value = checkTextContent,
            onValueChange = { checkTextContent = it },
            label = { Text(stringResource(R.string.text_content_label)) },
            placeholder = { Text(stringResource(R.string.text_content_placeholder)) },
            modifier = Modifier.fillMaxWidth(),
            singleLine = false,
            maxLines = 3
        )

        // 匹配选项
        Text(
            text = stringResource(R.string.match_options),
            style = MaterialTheme.typography.titleMedium
        )

        // 匹配模式选择
        Text(
            text = "匹配模式",
            style = MaterialTheme.typography.bodyMedium
        )

        Column(modifier = Modifier.selectableGroup()) {
            TextMatchMode.values().forEach { mode ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .selectable(
                            selected = (checkTextMatchMode == mode),
                            onClick = { checkTextMatchMode = mode },
                            role = Role.RadioButton
                        )
                        .padding(horizontal = 16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    ThemedRadioButton(
                        config = RadioButtonConfig(
                            selected = (checkTextMatchMode == mode),
                            onClick = null,
                            enabled = true
                        )
                    )
                    Text(
                        text = when (mode) {
                            TextMatchMode.CONTAINS -> "包含"
                            TextMatchMode.EXACT_MATCH -> "精确匹配"
                        },
                        style = MaterialTheme.typography.bodyMedium,
                        modifier = Modifier.padding(start = 16.dp)
                    )
                }
            }
        }

        // 区分大小写
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Checkbox(
                checked = checkTextCaseSensitive,
                onCheckedChange = { checkTextCaseSensitive = it }
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = "区分大小写",
                style = MaterialTheme.typography.bodyMedium
            )
        }

        // 使用正则表达式
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Checkbox(
                checked = checkTextUseRegex,
                onCheckedChange = { checkTextUseRegex = it }
            )
            Spacer(modifier = Modifier.width(8.dp))
            Column {
                Text(
                    text = "使用正则表达式",
                    style = MaterialTheme.typography.bodyMedium
                )
                Text(
                    text = "支持更复杂的文本匹配模式",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }

        // 包括叠加层
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Checkbox(
                checked = checkTextIncludeOverlay,
                onCheckedChange = { checkTextIncludeOverlay = it }
            )
            Spacer(modifier = Modifier.width(8.dp))
            Column {
                Text(
                    text = "包括叠加层",
                    style = MaterialTheme.typography.bodyMedium
                )
                Text(
                    text = "检查时包括悬浮窗等叠加层内容",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }

        // 忽略隐藏文本
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Checkbox(
                checked = checkTextIgnoreHidden,
                onCheckedChange = { checkTextIgnoreHidden = it }
            )
            Spacer(modifier = Modifier.width(8.dp))
            Column {
                Text(
                    text = "忽略隐藏文本",
                    style = MaterialTheme.typography.bodyMedium
                )
                Text(
                    text = "不检查不可见或隐藏的文本元素",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }

        // 输出文件路径
        Text(
            text = "输出文件路径",
            style = MaterialTheme.typography.titleMedium
        )

        OutlinedTextField(
            value = checkTextOutputFile,
            onValueChange = { checkTextOutputFile = it },
            label = { Text("文件路径") },
            placeholder = { Text("例如：/sdcard/screen_check_result.txt（留空使用默认路径）") },
            modifier = Modifier.fillMaxWidth(),
            singleLine = true
        )

        Text(
            text = "检查结果将以 true/false 的形式保存到指定文件中",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        // 视图ID输出文件路径
        OutlinedTextField(
            value = checkTextViewIdOutputFile,
            onValueChange = { checkTextViewIdOutputFile = it },
            label = { Text("视图ID输出文件路径") },
            placeholder = { Text("例如：/sdcard/view_ids.txt（留空则不保存视图ID）") },
            modifier = Modifier.fillMaxWidth(),
            singleLine = true
        )

        Text(
            text = "如果找到匹配文本，将把对应的视图ID保存到此文件中",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        // 确认按钮
        Button(
            onClick = {
                val task = ScreenControlTask(
                    operation = operation,
                    checkTextContent = checkTextContent,
                    checkTextCaseSensitive = checkTextCaseSensitive,
                    checkTextUseRegex = checkTextUseRegex,
                    checkTextMatchMode = checkTextMatchMode,
                    checkTextIncludeOverlay = checkTextIncludeOverlay,
                    checkTextIgnoreHidden = checkTextIgnoreHidden,
                    checkTextOutputFile = checkTextOutputFile,
                    checkTextViewIdOutputFile = checkTextViewIdOutputFile
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth(),
            enabled = checkTextContent.isNotEmpty()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 读取屏幕内容配置内容组件
 *
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun ReadScreenContentConfigContent(
    operation: ScreenControlOperation,
    onComplete: (Any) -> Unit
) {
    var readContentOutputFile by rememberSaveable { mutableStateOf("") }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 功能说明
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceVariant
            )
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "功能说明",
                    style = MaterialTheme.typography.titleSmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "此任务将捕获当前屏幕上的所有可见文本内容，并保存到指定的文本文件中。可用于屏幕内容分析、自动化测试等场景。",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }

        // 输出文件路径
        Text(
            text = "输出文件路径",
            style = MaterialTheme.typography.titleMedium
        )

        OutlinedTextField(
            value = readContentOutputFile,
            onValueChange = { readContentOutputFile = it },
            label = { Text("文件路径") },
            placeholder = { Text("例如：/sdcard/screen_content.txt（留空使用默认路径）") },
            modifier = Modifier.fillMaxWidth(),
            singleLine = true
        )

        Text(
            text = "屏幕内容将以纯文本形式保存到指定文件中，如果文件已存在将被覆盖",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        // 确认按钮
        Button(
            onClick = {
                val task = ScreenControlTask(
                    operation = operation,
                    readContentOutputFile = readContentOutputFile
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}



/**
 * 自动点击回放配置内容组件
 *
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun AutoClickerPlaybackConfigContent(
    operation: ScreenControlOperation,
    onComplete: (Any) -> Unit
) {
    var sourceType by rememberSaveable { mutableStateOf(AutoClickerSourceType.INSTANT_RECORDING) }
    var quickOperationType by rememberSaveable { mutableStateOf(QuickOperationType.SINGLE_CLICK) }
    var recordedGesture by rememberSaveable { mutableStateOf("") }
    var playbackLoopCount by rememberSaveable { mutableStateOf(1) }
    var playbackSpeed by rememberSaveable { mutableStateOf(1.0f) }
    var delayBetweenLoops by rememberSaveable { mutableStateOf(1000L) }

    // 快速操作参数
    var clickX by rememberSaveable { mutableStateOf(0.5f) }
    var clickY by rememberSaveable { mutableStateOf(0.5f) }
    var clickCount by rememberSaveable { mutableStateOf(1) }
    var clickInterval by rememberSaveable { mutableStateOf(500L) }
    var longPressDuration by rememberSaveable { mutableStateOf(1000L) }
    var swipeStartX by rememberSaveable { mutableStateOf(0.5f) }
    var swipeStartY by rememberSaveable { mutableStateOf(0.8f) }
    var swipeEndX by rememberSaveable { mutableStateOf(0.5f) }
    var swipeEndY by rememberSaveable { mutableStateOf(0.2f) }
    var swipeDuration by rememberSaveable { mutableStateOf(300L) }

    val context = LocalContext.current
    val recordingLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartActivityForResult()
    ) { result ->
        when (result.resultCode) {
            Activity.RESULT_OK -> {
                result.data?.getStringExtra(GestureRecordingActivity.EXTRA_RECORDED_GESTURE)?.let { gesture ->
                    recordedGesture = gesture
                }
            }
            Activity.RESULT_FIRST_USER -> {
                // 重新录制请求：清空当前录制数据，回到初始状态
                result.data?.getStringExtra("action")?.let { action ->
                    if (action == "re_record") {
                        recordedGesture = ""
                    }
                }
            }
        }
    }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "自动点击回放设置",
            style = MaterialTheme.typography.titleMedium
        )

        // 操作来源选择
        Text(
            text = "操作来源",
            style = MaterialTheme.typography.titleSmall
        )

        AutoClickerSourceType.values().forEach { type ->
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable { sourceType = type },
                verticalAlignment = Alignment.CenterVertically
            ) {
                ThemedRadioButton(
                    config = RadioButtonConfig(
                        selected = sourceType == type,
                        onClick = { sourceType = type },
                        enabled = true
                    )
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = type.displayName,
                    style = MaterialTheme.typography.bodyMedium
                )
            }
        }

        // 根据选择的来源类型显示不同的配置
        when (sourceType) {
            AutoClickerSourceType.INSTANT_RECORDING -> {
                InstantRecordingConfig(
                    recordedGesture = recordedGesture,
                    onStartRecording = {
                        recordingLauncher.launch(
                            Intent(context, GestureRecordingActivity::class.java)
                        )
                    },
                    onGestureRecorded = { gestureData ->
                        recordedGesture = gestureData
                    }
                )
            }
            AutoClickerSourceType.QUICK_OPERATION -> {
                QuickOperationConfig(
                    quickOperationType = quickOperationType,
                    onQuickOperationTypeChange = { quickOperationType = it },
                    clickX = clickX,
                    clickY = clickY,
                    clickCount = clickCount,
                    clickInterval = clickInterval,
                    longPressDuration = longPressDuration,
                    swipeStartX = swipeStartX,
                    swipeStartY = swipeStartY,
                    swipeEndX = swipeEndX,
                    swipeEndY = swipeEndY,
                    swipeDuration = swipeDuration,
                    onClickXChange = { clickX = it },
                    onClickYChange = { clickY = it },
                    onClickCountChange = { clickCount = it },
                    onClickIntervalChange = { clickInterval = it },
                    onLongPressDurationChange = { longPressDuration = it },
                    onSwipeStartXChange = { swipeStartX = it },
                    onSwipeStartYChange = { swipeStartY = it },
                    onSwipeEndXChange = { swipeEndX = it },
                    onSwipeEndYChange = { swipeEndY = it },
                    onSwipeDurationChange = { swipeDuration = it }
                )
            }
        }

        Divider()

        // 回放配置
        PlaybackConfig(
            playbackLoopCount = playbackLoopCount,
            playbackSpeed = playbackSpeed,
            delayBetweenLoops = delayBetweenLoops,
            onPlaybackLoopCountChange = { playbackLoopCount = it },
            onPlaybackSpeedChange = { playbackSpeed = it },
            onDelayBetweenLoopsChange = { delayBetweenLoops = it }
        )

        // 确认按钮
        Button(
            onClick = {
                val task = ScreenControlTask(
                    operation = operation,
                    autoClickerSourceType = sourceType,
                    quickOperationType = quickOperationType,
                    recordedGesture = recordedGesture,
                    playbackLoopCount = playbackLoopCount,
                    playbackSpeed = playbackSpeed,
                    delayBetweenLoops = delayBetweenLoops,
                    clickX = clickX,
                    clickY = clickY,
                    clickCount = clickCount,
                    clickInterval = clickInterval,
                    longPressDuration = longPressDuration,
                    swipeStartX = swipeStartX,
                    swipeStartY = swipeStartY,
                    swipeEndX = swipeEndX,
                    swipeEndY = swipeEndY,
                    swipeDuration = swipeDuration
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth(),
            enabled = when (sourceType) {
                AutoClickerSourceType.INSTANT_RECORDING -> recordedGesture.isNotEmpty()
                AutoClickerSourceType.QUICK_OPERATION -> true
            }
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 即时录制配置组件
 */
@Composable
private fun InstantRecordingConfig(
    recordedGesture: String,
    onStartRecording: () -> Unit,
    onGestureRecorded: (String) -> Unit = {}
) {
    val context = LocalContext.current
    var selectedRecordingMode by remember { mutableStateOf(RecordingMode.FULLSCREEN) }
    var isDraggableMarkersEnabled by remember { mutableStateOf(false) }

    // 使用悬浮窗录制结果管理器来轮询检查录制结果
    val resultManager = remember { FloatingRecordingResultManager(context) }

    LaunchedEffect(Unit) {
        resultManager.pollForRecordingResult().collect { recordingId ->
            when {
                recordingId == "" -> {
                    // 重新录制请求：清空当前录制数据
                    onGestureRecorded("")
                }
                !recordingId.isNullOrEmpty() -> {
                    // 正常录制结果
                    onGestureRecorded(recordingId)
                }
            }
        }
    }

    /**
     * 启动录制的统一处理函数
     */
    fun startRecordingWithMode(mode: RecordingMode) {
        when (mode) {
            RecordingMode.FULLSCREEN -> {
                // 启动全屏录制Activity
                onStartRecording()
            }
            RecordingMode.FLOATING -> {
                // 启动悬浮窗录制（传统）
                startFloatingRecording(context)
            }
            RecordingMode.FLOATING_ADVANCED -> {
                // 启动悬浮窗录制（高级）
                startAdvancedFloatingRecording(context)
            }
        }
    }

    Column(
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        Text(
            text = "即时录制",
            style = MaterialTheme.typography.titleSmall
        )

        if (recordedGesture.isEmpty()) {
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surfaceVariant
                )
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "尚未录制手势操作",
                        style = MaterialTheme.typography.bodyMedium,
                        modifier = Modifier.align(Alignment.CenterHorizontally)
                    )

                    Spacer(modifier = Modifier.height(12.dp))

                    // 录制模式选择
                    CompactRecordingModeSelector(
                        selectedMode = selectedRecordingMode,
                        onModeSelected = { selectedRecordingMode = it }
                    )

                    // 可拖拽数字标记复选框（仅在高级悬浮窗模式下显示）
                    if (selectedRecordingMode == RecordingMode.FLOATING_ADVANCED) {
                        Spacer(modifier = Modifier.height(8.dp))

                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Checkbox(
                                checked = isDraggableMarkersEnabled,
                                onCheckedChange = { isDraggableMarkersEnabled = it }
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(
                                text = "可拖拽坐标数字标记",
                                style = MaterialTheme.typography.bodyMedium
                            )
                        }

                        if (isDraggableMarkersEnabled) {
                            Text(
                                text = "启用后可以直接拖拽坐标标记移动，精确调整手势位置",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant,
                                modifier = Modifier.padding(start = 40.dp)
                            )
                        }
                    }

                    Spacer(modifier = Modifier.height(12.dp))

                    // 开始录制按钮
                    Button(
                        onClick = {
                            startRecordingWithMode(selectedRecordingMode)
                        },
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Text(
                            when (selectedRecordingMode) {
                                RecordingMode.FULLSCREEN -> "开始全屏录制"
                                RecordingMode.FLOATING -> "开始悬浮窗录制（传统）"
                                RecordingMode.FLOATING_ADVANCED -> "开始悬浮窗录制（高级）"
                            }
                        )
                    }

                    Spacer(modifier = Modifier.height(8.dp))

                    Text(
                        text = when (selectedRecordingMode) {
                            RecordingMode.FULLSCREEN -> "在专用界面中录制手势，适合复杂操作"
                            RecordingMode.FLOATING -> "通过悬浮窗录制，可在任意应用中操作"
                            RecordingMode.FLOATING_ADVANCED -> "可视化添加手势坐标，支持点击、长按、滑动等"
                        },
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        modifier = Modifier.align(Alignment.CenterHorizontally)
                    )
                }
            }
        } else {
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.primaryContainer
                )
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "✓ 已录制手势操作",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onPrimaryContainer
                    )

                    Spacer(modifier = Modifier.height(12.dp))

                    // 录制模式选择（重新录制时显示）
                    CompactRecordingModeSelector(
                        selectedMode = selectedRecordingMode,
                        onModeSelected = { selectedRecordingMode = it }
                    )

                    // 可拖拽数字标记复选框（仅在高级悬浮窗模式下显示）
                    if (selectedRecordingMode == RecordingMode.FLOATING_ADVANCED) {
                        Spacer(modifier = Modifier.height(8.dp))

                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Checkbox(
                                checked = isDraggableMarkersEnabled,
                                onCheckedChange = { isDraggableMarkersEnabled = it }
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(
                                text = "可拖拽坐标数字标记",
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onPrimaryContainer
                            )
                        }

                        if (isDraggableMarkersEnabled) {
                            Text(
                                text = "启用后可以直接拖拽坐标标记移动，精确调整手势位置",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.7f),
                                modifier = Modifier.padding(start = 40.dp)
                            )
                        }
                    }

                    Spacer(modifier = Modifier.height(12.dp))

                    Button(
                        onClick = {
                            startRecordingWithMode(selectedRecordingMode)
                        },
                        colors = ButtonDefaults.buttonColors(
                            containerColor = MaterialTheme.colorScheme.primary
                        ),
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Text("重新录制")
                    }
                }
            }
        }

        Text(
            text = "点击开始录制后，将进入专门的录制界面，您可以在其中演示要自动执行的手势操作。",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

/**
 * 快速操作配置组件
 */
@Composable
private fun QuickOperationConfig(
    quickOperationType: QuickOperationType,
    onQuickOperationTypeChange: (QuickOperationType) -> Unit,
    clickX: Float,
    clickY: Float,
    clickCount: Int,
    clickInterval: Long,
    longPressDuration: Long,
    swipeStartX: Float,
    swipeStartY: Float,
    swipeEndX: Float,
    swipeEndY: Float,
    swipeDuration: Long,
    onClickXChange: (Float) -> Unit,
    onClickYChange: (Float) -> Unit,
    onClickCountChange: (Int) -> Unit,
    onClickIntervalChange: (Long) -> Unit,
    onLongPressDurationChange: (Long) -> Unit,
    onSwipeStartXChange: (Float) -> Unit,
    onSwipeStartYChange: (Float) -> Unit,
    onSwipeEndXChange: (Float) -> Unit,
    onSwipeEndYChange: (Float) -> Unit,
    onSwipeDurationChange: (Long) -> Unit
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        Text(
            text = "快速操作",
            style = MaterialTheme.typography.titleSmall
        )

        // 操作类型选择
        QuickOperationType.values().forEach { type ->
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable { onQuickOperationTypeChange(type) },
                verticalAlignment = Alignment.CenterVertically
            ) {
                ThemedRadioButton(
                    config = RadioButtonConfig(
                        selected = quickOperationType == type,
                        onClick = { onQuickOperationTypeChange(type) },
                        enabled = true
                    )
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = type.displayName,
                    style = MaterialTheme.typography.bodyMedium
                )
            }
        }

        // 根据操作类型显示相应的参数配置
        when (quickOperationType) {
            QuickOperationType.SINGLE_CLICK -> {
                SingleClickConfig(
                    clickX = clickX,
                    clickY = clickY,
                    onClickXChange = onClickXChange,
                    onClickYChange = onClickYChange
                )
            }
            QuickOperationType.CONTINUOUS_CLICK -> {
                ContinuousClickConfig(
                    clickX = clickX,
                    clickY = clickY,
                    clickCount = clickCount,
                    clickInterval = clickInterval,
                    onClickXChange = onClickXChange,
                    onClickYChange = onClickYChange,
                    onClickCountChange = onClickCountChange,
                    onClickIntervalChange = onClickIntervalChange
                )
            }
            QuickOperationType.SWIPE_OPERATION -> {
                SwipeOperationConfig(
                    swipeStartX = swipeStartX,
                    swipeStartY = swipeStartY,
                    swipeEndX = swipeEndX,
                    swipeEndY = swipeEndY,
                    swipeDuration = swipeDuration,
                    onSwipeStartXChange = onSwipeStartXChange,
                    onSwipeStartYChange = onSwipeStartYChange,
                    onSwipeEndXChange = onSwipeEndXChange,
                    onSwipeEndYChange = onSwipeEndYChange,
                    onSwipeDurationChange = onSwipeDurationChange
                )
            }
            QuickOperationType.LONG_PRESS -> {
                LongPressConfig(
                    clickX = clickX,
                    clickY = clickY,
                    longPressDuration = longPressDuration,
                    onClickXChange = onClickXChange,
                    onClickYChange = onClickYChange,
                    onLongPressDurationChange = onLongPressDurationChange
                )
            }
        }
    }
}

/**
 * 启动悬浮窗录制（传统）
 */
private fun startFloatingRecording(context: Context) {
    // 检查悬浮窗录制的前置条件
    val requirement = FloatingRecordingManager.checkFloatingRecordingRequirements(context)

    when (requirement) {
        FloatingRecordingRequirement.READY -> {
            // 条件满足，启动悬浮窗录制
            // 注意：这里会自动处理已存在的悬浮窗（先停止再启动）
            val success = FloatingRecordingManager.startFloatingRecording(context)
            if (success) {
                Toast.makeText(context, "悬浮窗录制（传统）已启动，点击悬浮按钮开始录制", Toast.LENGTH_LONG).show()
            } else {
                Toast.makeText(context, "启动悬浮窗录制失败", Toast.LENGTH_SHORT).show()
            }
        }

        FloatingRecordingRequirement.OVERLAY_PERMISSION_REQUIRED -> {
            // 需要悬浮窗权限
            Toast.makeText(context, "需要悬浮窗权限才能使用此功能", Toast.LENGTH_SHORT).show()
            FloatingRecordingManager.requestRequiredPermission(context, requirement)
        }
    }
}

/**
 * 启动高级悬浮窗录制
 */
private fun startAdvancedFloatingRecording(context: Context) {
    // 检查悬浮窗权限
    if (!AdvancedFloatingRecordingManager.isAdvancedFloatingRecordingSupported(context)) {
        Toast.makeText(context, "需要悬浮窗权限才能使用此功能", Toast.LENGTH_SHORT).show()
        com.weinuo.quickcommands22.utils.OverlayPermissionUtil.requestOverlayPermission(context)
        return
    }

    // 启动高级悬浮窗录制
    val success = AdvancedFloatingRecordingManager.startAdvancedFloatingRecording(context)
    if (success) {
        Toast.makeText(context, "高级悬浮窗录制已启动，点击加号按钮添加手势", Toast.LENGTH_LONG).show()
    } else {
        Toast.makeText(context, "启动高级悬浮窗录制失败", Toast.LENGTH_SHORT).show()
    }
}

/**
 * 检查界面元素颜色配置内容组件
 *
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun CheckPixelColorConfigContent(
    operation: ScreenControlOperation,
    onComplete: (Any) -> Unit
) {
    var coordinateType by rememberSaveable { mutableStateOf(CoordinateType.PIXEL) }
    var pixelX by rememberSaveable { mutableStateOf(0.0f) }
    var pixelY by rememberSaveable { mutableStateOf(0.0f) }
    var outputFile by rememberSaveable { mutableStateOf("") }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 功能说明
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceVariant
            )
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "功能说明",
                    style = MaterialTheme.typography.titleSmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "此任务将分析屏幕指定位置的界面元素，推断其颜色特征，并将详细的颜色信息保存到文本文件中，包括RGB值、整数表示法和十六进制表示法。颜色分析基于界面元素的类型、状态、文本内容等特征。",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }

        // 坐标类型选择
        Text(
            text = "坐标类型",
            style = MaterialTheme.typography.titleMedium
        )

        Column(modifier = Modifier.selectableGroup()) {
            CoordinateType.values().forEach { type ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .selectable(
                            selected = (coordinateType == type),
                            onClick = { coordinateType = type },
                            role = Role.RadioButton
                        )
                        .padding(horizontal = 16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    ThemedRadioButton(
                        config = RadioButtonConfig(
                            selected = (coordinateType == type),
                            onClick = null,
                            enabled = true
                        )
                    )
                    Text(
                        text = when (type) {
                            CoordinateType.PIXEL -> "像素坐标"
                            CoordinateType.PERCENTAGE -> "百分比坐标 (0.0-1.0)"
                        },
                        style = MaterialTheme.typography.bodyMedium,
                        modifier = Modifier.padding(start = 16.dp)
                    )
                }
            }
        }

        // X坐标输入
        OutlinedTextField(
            value = pixelX.toString(),
            onValueChange = { value ->
                pixelX = value.toFloatOrNull()?.let { newValue ->
                    when (coordinateType) {
                        CoordinateType.PIXEL -> newValue.coerceAtLeast(0.0f)
                        CoordinateType.PERCENTAGE -> newValue.coerceIn(0.0f, 1.0f)
                    }
                } ?: pixelX
            },
            label = {
                Text(
                    when (coordinateType) {
                        CoordinateType.PIXEL -> "X坐标 (像素)"
                        CoordinateType.PERCENTAGE -> "X坐标 (0.0-1.0)"
                    }
                )
            },
            modifier = Modifier.fillMaxWidth(),
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
            singleLine = true
        )

        // Y坐标输入
        OutlinedTextField(
            value = pixelY.toString(),
            onValueChange = { value ->
                pixelY = value.toFloatOrNull()?.let { newValue ->
                    when (coordinateType) {
                        CoordinateType.PIXEL -> newValue.coerceAtLeast(0.0f)
                        CoordinateType.PERCENTAGE -> newValue.coerceIn(0.0f, 1.0f)
                    }
                } ?: pixelY
            },
            label = {
                Text(
                    when (coordinateType) {
                        CoordinateType.PIXEL -> "Y坐标 (像素)"
                        CoordinateType.PERCENTAGE -> "Y坐标 (0.0-1.0)"
                    }
                )
            },
            modifier = Modifier.fillMaxWidth(),
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
            singleLine = true
        )

        // 坐标说明
        Text(
            text = when (coordinateType) {
                CoordinateType.PIXEL -> "使用屏幕的绝对像素坐标，左上角为 (0, 0)"
                CoordinateType.PERCENTAGE -> "使用相对坐标，左上角为 (0.0, 0.0)，右下角为 (1.0, 1.0)"
            },
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        // 输出文件路径
        Text(
            text = "输出文件路径",
            style = MaterialTheme.typography.titleMedium
        )

        OutlinedTextField(
            value = outputFile,
            onValueChange = { outputFile = it },
            label = { Text("文件路径") },
            placeholder = { Text("例如：/sdcard/pixel_color.txt（留空使用默认路径）") },
            modifier = Modifier.fillMaxWidth(),
            singleLine = true
        )

        Text(
            text = "颜色信息将包含：位置坐标、[R]-红色值、[G]-绿色值、[B]-蓝色值、[A]-透明度、[colorInt]-颜色整数表示、[colorHex]-颜色十六进制表示等详细信息",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        // 确认按钮
        Button(
            onClick = {
                val task = ScreenControlTask(
                    operation = operation,
                    checkPixelCoordinateType = coordinateType,
                    checkPixelX = pixelX,
                    checkPixelY = pixelY,
                    checkPixelColorOutputFile = outputFile
                )
                onComplete(task)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}