package com.weinuo.quickcommands22.ui.components.integrated

import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.collectIsFocusedAsState
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.text.selection.TextSelectionColors
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import com.weinuo.quickcommands22.ui.theme.config.TextFieldConfig
import com.weinuo.quickcommands22.ui.theme.manager.ThemeManager
import com.weinuo.quickcommands22.ui.theme.skyblue.SkyBlueColorScheme

/**
 * 整合设计风格的文本输入框组件
 *
 * 特点：
 * - 无阴影设计
 * - 大圆角
 * - 流畅的焦点动画
 * - 统一的视觉体验
 * - 支持多种样式变体
 */
@Composable
fun IntegratedTextField(
    config: TextFieldConfig,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    val themeManager = remember { ThemeManager.getInstance(context) }
    val styleConfig = themeManager.currentThemeProvider.value.getStyleConfiguration()
    val extendedColors = SkyBlueColorScheme.ExtendedColors
    
    // 交互状态
    val interactionSource = remember { MutableInteractionSource() }
    val isFocused by interactionSource.collectIsFocusedAsState()
    
    // 动画状态
    val animatedBorderColor by animateColorAsState(
        targetValue = when {
            config.isError -> MaterialTheme.colorScheme.error
            isFocused -> extendedColors.interactiveFocus
            else -> MaterialTheme.colorScheme.outline
        },
        animationSpec = tween(durationMillis = 150),
        label = "textfield_border_color"
    )
    
    val animatedLabelColor by animateColorAsState(
        targetValue = when {
            config.isError -> MaterialTheme.colorScheme.error
            isFocused -> extendedColors.interactiveFocus
            else -> MaterialTheme.colorScheme.onSurfaceVariant
        },
        animationSpec = tween(durationMillis = 150),
        label = "textfield_label_color"
    )

    OutlinedTextField(
        value = config.value,
        onValueChange = { newValue: String -> config.onValueChange(newValue) },
        modifier = modifier.fillMaxWidth(),
        enabled = config.enabled,
        readOnly = config.readOnly,
        textStyle = MaterialTheme.typography.bodyLarge,
        label = if (config.label != null) {
            @Composable {
                Text(
                    text = config.label!!,
                    color = animatedLabelColor
                )
            }
        } else null,
        placeholder = if (config.placeholder != null) {
            @Composable {
                Text(
                    text = config.placeholder!!,
                    color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.6f)
                )
            }
        } else null,
        leadingIcon = if (config.leadingIcon != null) {
            @Composable {
                Icon(
                    imageVector = config.leadingIcon!!,
                    contentDescription = null,
                    tint = if (isFocused) {
                        extendedColors.interactiveFocus
                    } else {
                        MaterialTheme.colorScheme.onSurfaceVariant
                    }
                )
            }
        } else null,
        trailingIcon = if (config.trailingIcon != null) {
            @Composable {
                IconButton(
                    onClick = config.onTrailingIconClick ?: {}
                ) {
                    Icon(
                        imageVector = config.trailingIcon!!,
                        contentDescription = null,
                        tint = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        } else null,
        prefix = if (config.prefix != null) {
            @Composable {
                Text(
                    text = config.prefix!!,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        } else null,
        suffix = if (config.suffix != null) {
            @Composable {
                Text(
                    text = config.suffix!!,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        } else null,
        supportingText = if (config.supportingText != null) {
            @Composable {
                Text(
                    text = config.supportingText!!,
                    color = if (config.isError) {
                        MaterialTheme.colorScheme.error
                    } else {
                        MaterialTheme.colorScheme.onSurfaceVariant
                    }
                )
            }
        } else null,
        isError = config.isError,
        visualTransformation = (config.visualTransformation as? VisualTransformation) ?: VisualTransformation.None,
        keyboardOptions = (config.keyboardOptions as? KeyboardOptions) ?: KeyboardOptions.Default,
        keyboardActions = (config.keyboardActions as? KeyboardActions) ?: KeyboardActions.Default,
        singleLine = config.singleLine,
        maxLines = if (config.singleLine) 1 else config.maxLines,
        minLines = config.minLines,
        interactionSource = interactionSource,
        shape = RoundedCornerShape(styleConfig.cornerRadius.medium),
        colors = OutlinedTextFieldDefaults.colors(
            focusedBorderColor = animatedBorderColor,
            unfocusedBorderColor = MaterialTheme.colorScheme.outline,
            disabledBorderColor = MaterialTheme.colorScheme.outline.copy(alpha = 0.38f),
            errorBorderColor = MaterialTheme.colorScheme.error,
            focusedContainerColor = extendedColors.backgroundFocus,
            unfocusedContainerColor = MaterialTheme.colorScheme.surface,
            disabledContainerColor = MaterialTheme.colorScheme.surface.copy(alpha = 0.38f),
            errorContainerColor = MaterialTheme.colorScheme.errorContainer.copy(alpha = 0.1f),
            cursorColor = extendedColors.interactiveFocus,
            errorCursorColor = MaterialTheme.colorScheme.error,
            selectionColors = TextSelectionColors(
                handleColor = extendedColors.interactiveFocus,
                backgroundColor = extendedColors.interactiveFocus.copy(alpha = 0.4f)
            )
        )
    )
}

/**
 * 整合设计风格的填充文本输入框
 */
@Composable
fun IntegratedFilledTextField(
    config: TextFieldConfig,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    val themeManager = remember { ThemeManager.getInstance(context) }
    val styleConfig = themeManager.currentThemeProvider.value.getStyleConfiguration()
    val extendedColors = SkyBlueColorScheme.ExtendedColors
    
    // 交互状态
    val interactionSource = remember { MutableInteractionSource() }
    val isFocused by interactionSource.collectIsFocusedAsState()
    
    // 动画状态
    val animatedLabelColor by animateColorAsState(
        targetValue = when {
            config.isError -> MaterialTheme.colorScheme.error
            isFocused -> extendedColors.interactiveFocus
            else -> MaterialTheme.colorScheme.onSurfaceVariant
        },
        animationSpec = tween(durationMillis = 150),
        label = "filled_textfield_label_color"
    )

    TextField(
        value = config.value,
        onValueChange = { newValue: String -> config.onValueChange(newValue) },
        modifier = modifier.fillMaxWidth(),
        enabled = config.enabled,
        readOnly = config.readOnly,
        textStyle = MaterialTheme.typography.bodyLarge,
        label = if (config.label != null) {
            @Composable {
                Text(
                    text = config.label!!,
                    color = animatedLabelColor
                )
            }
        } else null,
        placeholder = if (config.placeholder != null) {
            @Composable {
                Text(
                    text = config.placeholder!!,
                    color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.6f)
                )
            }
        } else null,
        leadingIcon = if (config.leadingIcon != null) {
            @Composable {
                Icon(
                    imageVector = config.leadingIcon!!,
                    contentDescription = null,
                    tint = if (isFocused) {
                        extendedColors.interactiveFocus
                    } else {
                        MaterialTheme.colorScheme.onSurfaceVariant
                    }
                )
            }
        } else null,
        trailingIcon = if (config.trailingIcon != null) {
            @Composable {
                IconButton(
                    onClick = config.onTrailingIconClick ?: {}
                ) {
                    Icon(
                        imageVector = config.trailingIcon!!,
                        contentDescription = null,
                        tint = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        } else null,
        prefix = if (config.prefix != null) {
            @Composable {
                Text(
                    text = config.prefix!!,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        } else null,
        suffix = if (config.suffix != null) {
            @Composable {
                Text(
                    text = config.suffix!!,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        } else null,
        supportingText = if (config.supportingText != null) {
            @Composable {
                Text(
                    text = config.supportingText!!,
                    color = if (config.isError) {
                        MaterialTheme.colorScheme.error
                    } else {
                        MaterialTheme.colorScheme.onSurfaceVariant
                    }
                )
            }
        } else null,
        isError = config.isError,
        visualTransformation = (config.visualTransformation as? VisualTransformation) ?: VisualTransformation.None,
        keyboardOptions = (config.keyboardOptions as? KeyboardOptions) ?: KeyboardOptions.Default,
        keyboardActions = (config.keyboardActions as? KeyboardActions) ?: KeyboardActions.Default,
        singleLine = config.singleLine,
        maxLines = if (config.singleLine) 1 else config.maxLines,
        minLines = config.minLines,
        interactionSource = interactionSource,
        shape = RoundedCornerShape(
            topStart = styleConfig.cornerRadius.medium,
            topEnd = styleConfig.cornerRadius.medium,
            bottomStart = 0.dp,
            bottomEnd = 0.dp
        ),
        colors = TextFieldDefaults.colors(
            focusedIndicatorColor = extendedColors.interactiveFocus,
            unfocusedIndicatorColor = MaterialTheme.colorScheme.onSurfaceVariant,
            disabledIndicatorColor = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.38f),
            errorIndicatorColor = MaterialTheme.colorScheme.error,
            focusedContainerColor = extendedColors.backgroundFocus,
            unfocusedContainerColor = MaterialTheme.colorScheme.surfaceContainerHighest,
            disabledContainerColor = MaterialTheme.colorScheme.surfaceContainerHighest.copy(alpha = 0.38f),
            errorContainerColor = MaterialTheme.colorScheme.errorContainer.copy(alpha = 0.1f),
            cursorColor = extendedColors.interactiveFocus,
            errorCursorColor = MaterialTheme.colorScheme.error,
            selectionColors = TextSelectionColors(
                handleColor = extendedColors.interactiveFocus,
                backgroundColor = extendedColors.interactiveFocus.copy(alpha = 0.4f)
            )
        )
    )
}
