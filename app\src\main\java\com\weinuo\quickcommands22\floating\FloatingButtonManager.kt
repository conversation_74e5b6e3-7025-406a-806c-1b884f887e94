package com.weinuo.quickcommands22.floating

import android.content.Context
import android.util.Log
import com.weinuo.quickcommands22.data.QuickCommandRepository
import com.weinuo.quickcommands22.service.FloatingButtonService
import com.weinuo.quickcommands22.utils.OverlayPermissionUtil

/**
 * 悬浮按钮管理器
 * 统一管理悬浮按钮的创建、删除、更新
 */
object FloatingButtonManager {

    private const val TAG = "FloatingButtonManager"

    /**
     * 悬浮按钮状态数据类
     */
    data class FloatingButtonState(
        val commandId: String,
        val buttonSize: Int,
        val buttonAlpha: Float,
        val showText: Boolean,
        val buttonText: String,
        val isActive: Boolean
    )

    // 悬浮按钮状态映射表
    private val floatingButtonStates = mutableMapOf<String, FloatingButtonState>()

    /**
     * 创建悬浮按钮
     * 注意：调用此方法前应确保已有悬浮窗权限
     */
    fun createFloatingButton(
        context: Context,
        commandId: String,
        buttonSize: Int,
        buttonAlpha: Float,
        showText: Boolean = false,
        buttonText: String = ""
    ): Boolean {
        return try {
            // 验证参数范围
            val validSize = buttonSize.coerceIn(20, 200)
            val validAlpha = buttonAlpha.coerceIn(0.1f, 1.0f)

            Log.d(TAG, "Creating floating button for command: $commandId")

            // 启动悬浮按钮服务
            FloatingButtonService.startService(context, commandId, validSize, validAlpha, showText, buttonText)

            // 更新状态映射表
            floatingButtonStates[commandId] = FloatingButtonState(
                commandId = commandId,
                buttonSize = validSize,
                buttonAlpha = validAlpha,
                showText = showText,
                buttonText = buttonText,
                isActive = true
            )

            Log.d(TAG, "Floating button created successfully for command: $commandId")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error creating floating button for command: $commandId", e)
            false
        }
    }

    /**
     * 删除悬浮按钮
     */
    fun removeFloatingButton(context: Context, commandId: String): Boolean {
        return try {
            Log.d(TAG, "Removing floating button for command: $commandId")

            // 停止悬浮按钮服务
            FloatingButtonService.stopService(context, commandId)

            // 从状态映射表中移除
            floatingButtonStates.remove(commandId)

            Log.d(TAG, "Floating button removed successfully for command: $commandId")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error removing floating button for command: $commandId", e)
            false
        }
    }

    /**
     * 更新悬浮按钮
     */
    fun updateFloatingButton(
        context: Context,
        commandId: String,
        buttonSize: Int,
        buttonAlpha: Float,
        showText: Boolean = false,
        buttonText: String = ""
    ): Boolean {
        return try {
            Log.d(TAG, "Updating floating button for command: $commandId")

            // 先删除现有的悬浮按钮
            removeFloatingButton(context, commandId)

            // 创建新的悬浮按钮
            createFloatingButton(context, commandId, buttonSize, buttonAlpha, showText, buttonText)
        } catch (e: Exception) {
            Log.e(TAG, "Error updating floating button for command: $commandId", e)
            false
        }
    }

    /**
     * 删除所有悬浮按钮
     */
    fun removeAllFloatingButtons(context: Context) {
        try {
            Log.d(TAG, "Removing all floating buttons")

            val commandIds = floatingButtonStates.keys.toList()
            for (commandId in commandIds) {
                removeFloatingButton(context, commandId)
            }

            Log.d(TAG, "All floating buttons removed successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Error removing all floating buttons", e)
        }
    }

    /**
     * 检查悬浮按钮是否激活
     */
    fun isFloatingButtonActive(commandId: String): Boolean {
        return floatingButtonStates[commandId]?.isActive ?: false
    }

    /**
     * 获取悬浮按钮状态
     */
    fun getFloatingButtonState(commandId: String): FloatingButtonState? {
        return floatingButtonStates[commandId]
    }

    /**
     * 获取所有激活的悬浮按钮
     */
    fun getAllActiveFloatingButtons(): List<FloatingButtonState> {
        return floatingButtonStates.values.filter { it.isActive }
    }

    /**
     * 初始化悬浮按钮管理器
     * 从数据库中恢复悬浮按钮状态
     * 支持快捷指令
     */
    suspend fun initializeFromRepository(context: Context) {
        try {
            Log.d(TAG, "Initializing floating button manager")

            // 检查悬浮窗权限
            if (!OverlayPermissionUtil.hasOverlayPermission(context)) {
                Log.w(TAG, "No overlay permission, skipping floating button initialization")
                return
            }

            val repository = QuickCommandRepository.getInstance(context)

            // 为所有启用悬浮按钮的快捷指令创建悬浮按钮
            val quickCommands = repository.quickCommands.value
            for (command in quickCommands) {
                // 只处理启用的快捷指令
                if (!command.isEnabled) {
                    Log.d(TAG, "Skipping disabled command: ${command.name}")
                    continue
                }

                // 检查快捷指令是否有手动触发条件配置了悬浮按钮
                val hasFloatingButtonTrigger = command.triggerConditions.any { condition ->
                    condition is com.weinuo.quickcommands22.model.ManualTriggerCondition &&
                    condition.triggerType == com.weinuo.quickcommands22.model.ManualTriggerType.FLOATING_BUTTON
                }

                if (hasFloatingButtonTrigger) {
                    val floatingButtonCondition = command.triggerConditions
                        .filterIsInstance<com.weinuo.quickcommands22.model.ManualTriggerCondition>()
                        .firstOrNull { it.triggerType == com.weinuo.quickcommands22.model.ManualTriggerType.FLOATING_BUTTON }

                    if (floatingButtonCondition != null) {
                        Log.d(TAG, "Creating floating button for command: ${command.name}")
                        createFloatingButton(
                            context = context,
                            commandId = command.id,
                            buttonSize = floatingButtonCondition.buttonSize,
                            buttonAlpha = floatingButtonCondition.buttonAlpha,
                            showText = floatingButtonCondition.buttonText.isNotEmpty(),
                            buttonText = floatingButtonCondition.buttonText
                        )
                    }
                }
            }

            Log.d(TAG, "Floating button manager initialized successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Error initializing floating button manager", e)
        }
    }

    /**
     * 清理所有状态
     */
    fun clear() {
        floatingButtonStates.clear()
        Log.d(TAG, "Floating button manager cleared")
    }
}
