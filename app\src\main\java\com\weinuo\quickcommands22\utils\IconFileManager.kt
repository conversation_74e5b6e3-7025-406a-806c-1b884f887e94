package com.weinuo.quickcommands22.utils

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import android.util.Log
import java.io.File
import java.io.FileOutputStream
import java.io.IOException

/**
 * 图标文件管理器
 * 
 * 负责管理快捷指令的自定义图标文件，包括：
 * - 图标文件的保存和删除
 * - 图标文件路径的生成和验证
 * - 图标文件的清理和维护
 * 
 * 设计原则：
 * - 所有图标文件统一存储在应用私有目录的icons文件夹中
 * - 使用快捷指令ID作为文件名，确保唯一性
 * - 统一保存为PNG格式，支持透明度
 * - 提供完整的文件生命周期管理
 */
class IconFileManager private constructor(private val context: Context) {
    
    companion object {
        private const val TAG = "IconFileManager"
        private const val ICONS_DIR_NAME = "icons"
        private const val ICON_FILE_EXTENSION = ".png"
        private const val ICON_QUALITY = 90 // PNG压缩质量
        
        @Volatile
        private var INSTANCE: IconFileManager? = null
        
        /**
         * 获取IconFileManager单例实例
         */
        fun getInstance(context: Context): IconFileManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: IconFileManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    // 图标存储目录
    private val iconsDir: File by lazy {
        File(context.filesDir, ICONS_DIR_NAME).apply {
            if (!exists()) {
                mkdirs()
                Log.d(TAG, "创建图标存储目录: $absolutePath")
            }
        }
    }
    
    /**
     * 保存图标文件
     * 
     * @param commandId 快捷指令ID
     * @param iconBitmap 图标Bitmap
     * @return 保存成功返回文件路径，失败返回null
     */
    fun saveIcon(commandId: String, iconBitmap: Bitmap): String? {
        return try {
            val iconFile = getIconFile(commandId)
            
            // 如果文件已存在，先删除
            if (iconFile.exists()) {
                iconFile.delete()
                Log.d(TAG, "删除已存在的图标文件: ${iconFile.absolutePath}")
            }
            
            // 保存新的图标文件
            FileOutputStream(iconFile).use { outputStream ->
                iconBitmap.compress(Bitmap.CompressFormat.PNG, ICON_QUALITY, outputStream)
                outputStream.flush()
            }
            
            Log.d(TAG, "图标保存成功: ${iconFile.absolutePath}")
            iconFile.absolutePath
            
        } catch (e: IOException) {
            Log.e(TAG, "保存图标文件失败: commandId=$commandId", e)
            null
        } catch (e: Exception) {
            Log.e(TAG, "保存图标文件时发生未知异常: commandId=$commandId", e)
            null
        }
    }
    
    /**
     * 从URI保存图标文件
     * 
     * @param commandId 快捷指令ID
     * @param iconUri 图标URI
     * @return 保存成功返回文件路径，失败返回null
     */
    fun saveIconFromUri(commandId: String, iconUri: Uri): String? {
        return try {
            val inputStream = context.contentResolver.openInputStream(iconUri)
            val bitmap = BitmapFactory.decodeStream(inputStream)
            inputStream?.close()
            
            if (bitmap != null) {
                saveIcon(commandId, bitmap)
            } else {
                Log.e(TAG, "无法从URI解码图片: $iconUri")
                null
            }
        } catch (e: Exception) {
            Log.e(TAG, "从URI保存图标失败: commandId=$commandId, uri=$iconUri", e)
            null
        }
    }
    
    /**
     * 删除图标文件
     * 
     * @param commandId 快捷指令ID
     * @return 删除是否成功
     */
    fun deleteIcon(commandId: String): Boolean {
        return try {
            val iconFile = getIconFile(commandId)
            if (iconFile.exists()) {
                val deleted = iconFile.delete()
                if (deleted) {
                    Log.d(TAG, "图标删除成功: ${iconFile.absolutePath}")
                } else {
                    Log.w(TAG, "图标删除失败: ${iconFile.absolutePath}")
                }
                deleted
            } else {
                Log.d(TAG, "图标文件不存在，无需删除: commandId=$commandId")
                true
            }
        } catch (e: Exception) {
            Log.e(TAG, "删除图标文件时发生异常: commandId=$commandId", e)
            false
        }
    }
    
    /**
     * 检查图标文件是否存在
     * 
     * @param commandId 快捷指令ID
     * @return 文件是否存在
     */
    fun iconExists(commandId: String): Boolean {
        return getIconFile(commandId).exists()
    }
    
    /**
     * 获取图标文件路径
     * 
     * @param commandId 快捷指令ID
     * @return 图标文件路径，如果文件不存在返回null
     */
    fun getIconPath(commandId: String): String? {
        val iconFile = getIconFile(commandId)
        return if (iconFile.exists()) {
            iconFile.absolutePath
        } else {
            null
        }
    }
    
    /**
     * 加载图标Bitmap
     * 
     * @param commandId 快捷指令ID
     * @return 图标Bitmap，如果文件不存在或加载失败返回null
     */
    fun loadIcon(commandId: String): Bitmap? {
        return try {
            val iconFile = getIconFile(commandId)
            if (iconFile.exists()) {
                BitmapFactory.decodeFile(iconFile.absolutePath)
            } else {
                null
            }
        } catch (e: Exception) {
            Log.e(TAG, "加载图标失败: commandId=$commandId", e)
            null
        }
    }
    
    /**
     * 清理无用的图标文件
     * 
     * @param validCommandIds 有效的快捷指令ID列表
     * @return 清理的文件数量
     */
    fun cleanupUnusedIcons(validCommandIds: Set<String>): Int {
        var cleanedCount = 0
        
        try {
            if (!iconsDir.exists()) {
                Log.d(TAG, "图标目录不存在，无需清理")
                return 0
            }
            
            val iconFiles = iconsDir.listFiles() ?: return 0
            
            for (file in iconFiles) {
                if (file.isFile && file.name.endsWith(ICON_FILE_EXTENSION)) {
                    val commandId = file.nameWithoutExtension
                    if (!validCommandIds.contains(commandId)) {
                        if (file.delete()) {
                            cleanedCount++
                            Log.d(TAG, "清理无用图标文件: ${file.absolutePath}")
                        } else {
                            Log.w(TAG, "清理图标文件失败: ${file.absolutePath}")
                        }
                    }
                }
            }
            
            Log.d(TAG, "图标清理完成，清理文件数量: $cleanedCount")
            
        } catch (e: Exception) {
            Log.e(TAG, "清理图标文件时发生异常", e)
        }
        
        return cleanedCount
    }
    
    /**
     * 获取图标存储目录的大小
     * 
     * @return 目录大小（字节）
     */
    fun getIconsDirSize(): Long {
        return try {
            if (!iconsDir.exists()) {
                0L
            } else {
                iconsDir.walkTopDown()
                    .filter { it.isFile }
                    .map { it.length() }
                    .sum()
            }
        } catch (e: Exception) {
            Log.e(TAG, "计算图标目录大小时发生异常", e)
            0L
        }
    }
    
    /**
     * 获取图标文件对象
     * 
     * @param commandId 快捷指令ID
     * @return 图标文件对象
     */
    private fun getIconFile(commandId: String): File {
        return File(iconsDir, "$commandId$ICON_FILE_EXTENSION")
    }
}
