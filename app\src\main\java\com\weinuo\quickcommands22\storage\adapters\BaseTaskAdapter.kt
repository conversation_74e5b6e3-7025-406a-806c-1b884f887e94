package com.weinuo.quickcommands22.storage.adapters

import android.util.Log
import com.weinuo.quickcommands22.model.SharedTask
import com.weinuo.quickcommands22.storage.NativeTypeStorageManager
import com.weinuo.quickcommands22.storage.StorageDomain
import com.weinuo.quickcommands22.storage.StorageKeyGenerator
import com.weinuo.quickcommands22.storage.StorageOperation

/**
 * 任务存储适配器接口
 *
 * 定义了所有任务存储适配器必须实现的基本操作。
 * 每种SharedTask子类都需要对应的适配器实现。
 *
 * @param T 具体的任务类型
 */
interface TaskStorageAdapter<T : SharedTask> {

    /**
     * 保存任务到存储
     *
     * @param task 要保存的任务
     * @return 操作是否成功
     */
    fun save(task: T): Boolean

    /**
     * 从存储加载任务
     *
     * @param taskId 任务ID
     * @return 加载的任务，失败时返回null
     */
    fun load(taskId: String): T?

    /**
     * 从存储删除任务
     *
     * @param taskId 任务ID
     * @return 操作是否成功
     */
    fun delete(taskId: String): Boolean

    /**
     * 获取任务类型标识
     *
     * @return 任务类型字符串
     */
    fun getTaskType(): String

    /**
     * 检查任务是否存在
     *
     * @param taskId 任务ID
     * @return 任务是否存在
     */
    fun exists(taskId: String): Boolean
}

/**
 * 任务存储适配器基类
 *
 * 提供所有任务适配器的通用功能实现，包括：
 * - 基础字段的存储和加载
 * - 键名生成规则
 * - 通用的删除和检查操作
 * - 错误处理和日志记录
 *
 * @param T 具体的任务类型
 * @param storageManager 原生类型存储管理器
 */
abstract class BaseTaskAdapter<T : SharedTask>(
    protected val storageManager: NativeTypeStorageManager
) : TaskStorageAdapter<T> {

    companion object {
        private const val TAG = "BaseTaskAdapter"

        // 基础字段名常量
        protected const val FIELD_TYPE = "type"
        protected const val FIELD_ID = "id"
    }

    /**
     * 获取任务存储键前缀
     *
     * @param taskId 任务ID
     * @return 键前缀
     */
    protected fun getPrefix(taskId: String): String {
        return StorageKeyGenerator.getTaskPrefix(taskId)
    }

    /**
     * 生成任务字段键名
     *
     * @param taskId 任务ID
     * @param fieldName 字段名
     * @return 完整键名
     */
    protected fun generateKey(taskId: String, fieldName: String): String {
        return StorageKeyGenerator.generateTaskKey(taskId, fieldName)
    }

    /**
     * 保存基础字段
     * 所有任务都有的通用字段
     *
     * @param taskId 任务ID
     * @param task 任务实例
     * @return 存储操作列表
     */
    protected fun saveBaseFields(taskId: String, task: SharedTask): List<StorageOperation> {
        return listOf(
            StorageOperation.createStringOperation(
                StorageDomain.TASKS,
                generateKey(taskId, FIELD_TYPE),
                getTaskType()
            ),
            StorageOperation.createStringOperation(
                StorageDomain.TASKS,
                generateKey(taskId, FIELD_ID),
                task.id
            )
        )
    }

    /**
     * 保存字符串列表
     * 将字符串列表拆分为独立的键值对存储
     *
     * @param baseKey 基础键名
     * @param list 字符串列表
     * @return 存储操作列表
     */
    protected fun saveStringList(baseKey: String, list: List<String>): List<StorageOperation> {
        val operations = mutableListOf<StorageOperation>()

        // 保存列表大小
        operations.add(StorageOperation.createIntOperation(
            StorageDomain.TASKS,
            StorageKeyGenerator.generateCollectionKey(baseKey, "count"),
            list.size
        ))

        // 保存每个元素
        list.forEachIndexed { index, item ->
            operations.add(StorageOperation.createStringOperation(
                StorageDomain.TASKS,
                StorageKeyGenerator.generateCollectionKey(baseKey, index.toString()),
                item
            ))
        }

        return operations
    }

    /**
     * 加载字符串列表
     * 从拆分的键值对重建字符串列表
     *
     * @param baseKey 基础键名
     * @return 字符串列表
     */
    protected fun loadStringList(baseKey: String): List<String> {
        val countKey = StorageKeyGenerator.generateCollectionKey(baseKey, "count")
        val count = storageManager.loadInt(StorageDomain.TASKS, countKey, 0)

        if (count == 0) {
            return emptyList()
        }

        val list = mutableListOf<String>()
        for (index in 0 until count) {
            val itemKey = StorageKeyGenerator.generateCollectionKey(baseKey, index.toString())
            val item = storageManager.loadString(StorageDomain.TASKS, itemKey)
            if (item.isNotEmpty()) {
                list.add(item)
            }
        }

        return list
    }

    /**
     * 保存枚举值
     * 将枚举转换为字符串存储
     *
     * @param key 键名
     * @param enumValue 枚举值
     * @return 存储操作
     */
    protected fun <E : Enum<E>> saveEnum(key: String, enumValue: E): StorageOperation {
        return StorageOperation.createStringOperation(
            StorageDomain.TASKS,
            key,
            enumValue.name
        )
    }

    /**
     * 保存可空枚举值
     * 将可空枚举转换为字符串存储
     *
     * @param key 键名
     * @param enumValue 可空枚举值
     * @return 存储操作
     */
    protected fun <E : Enum<E>> saveEnumNullable(key: String, enumValue: E?): StorageOperation {
        return StorageOperation.createStringOperation(
            StorageDomain.TASKS,
            key,
            enumValue?.name ?: ""
        )
    }

    /**
     * 加载枚举值
     * 从字符串重建枚举值
     *
     * @param key 键名
     * @param enumClass 枚举类
     * @param defaultValue 默认值
     * @return 枚举值
     */
    protected fun <E : Enum<E>> loadEnum(
        key: String,
        enumClass: Class<E>,
        defaultValue: E
    ): E {
        val enumName = storageManager.loadString(StorageDomain.TASKS, key)
        return if (enumName.isNotEmpty()) {
            try {
                java.lang.Enum.valueOf(enumClass, enumName)
            } catch (e: IllegalArgumentException) {
                Log.w(TAG, "Invalid enum value: $enumName for ${enumClass.simpleName}, using default")
                defaultValue
            }
        } else {
            defaultValue
        }
    }

    /**
     * 加载可空枚举值
     * 从字符串重建可空枚举值
     *
     * @param key 键名
     * @param enumClass 枚举类
     * @return 可空枚举值
     */
    protected fun <E : Enum<E>> loadEnumNullable(
        key: String,
        enumClass: Class<E>
    ): E? {
        val enumName = storageManager.loadString(StorageDomain.TASKS, key)
        return if (enumName.isNotEmpty()) {
            try {
                java.lang.Enum.valueOf(enumClass, enumName)
            } catch (e: IllegalArgumentException) {
                Log.w(TAG, "Invalid enum value: $enumName for ${enumClass.simpleName}, returning null")
                null
            }
        } else {
            null
        }
    }

    /**
     * 加载枚举值（使用lambda表达式）
     * 从字符串重建枚举值
     *
     * @param key 键名
     * @param converter 字符串到枚举的转换函数
     * @return 可空枚举值
     */
    protected fun <E : Enum<E>> loadEnum(key: String, converter: (String) -> E): E? {
        val enumName = storageManager.loadString(StorageDomain.TASKS, key)
        return if (enumName.isNotEmpty()) {
            try {
                converter(enumName)
            } catch (e: Exception) {
                Log.w(TAG, "Invalid enum value: $enumName, returning null")
                null
            }
        } else {
            null
        }
    }

    /**
     * 加载可空枚举值（使用lambda表达式）
     * 从字符串重建可空枚举值
     *
     * @param key 键名
     * @param converter 字符串到枚举的转换函数
     * @return 可空枚举值
     */
    protected fun <E : Enum<E>> loadEnumNullable(key: String, converter: (String) -> E): E? {
        val enumName = storageManager.loadString(StorageDomain.TASKS, key)
        return if (enumName.isNotEmpty()) {
            try {
                converter(enumName)
            } catch (e: Exception) {
                Log.w(TAG, "Invalid enum value: $enumName, returning null")
                null
            }
        } else {
            null
        }
    }

    /**
     * 删除任务的所有相关数据
     *
     * @param taskId 任务ID
     * @return 操作是否成功
     */
    override fun delete(taskId: String): Boolean {
        return try {
            val prefix = getPrefix(taskId)
            val success = storageManager.deleteByPrefix(StorageDomain.TASKS, prefix)
            if (success) {
                Log.d(TAG, "Successfully deleted task: $taskId")
            } else {
                Log.e(TAG, "Failed to delete task: $taskId")
            }
            success
        } catch (e: Exception) {
            Log.e(TAG, "Exception while deleting task: $taskId", e)
            false
        }
    }

    /**
     * 检查任务是否存在
     *
     * @param taskId 任务ID
     * @return 任务是否存在
     */
    override fun exists(taskId: String): Boolean {
        val typeKey = generateKey(taskId, FIELD_TYPE)
        return storageManager.containsKey(StorageDomain.TASKS, typeKey)
    }

    /**
     * 验证任务ID是否有效
     *
     * @param taskId 任务ID
     * @return 是否有效
     */
    protected fun isValidTaskId(taskId: String): Boolean {
        return taskId.isNotBlank()
    }

    /**
     * 记录保存成功日志
     *
     * @param taskId 任务ID
     */
    protected fun logSaveSuccess(taskId: String) {
        Log.d(TAG, "Successfully saved ${getTaskType()} task: $taskId")
    }

    /**
     * 记录保存失败日志
     *
     * @param taskId 任务ID
     * @param error 错误信息
     */
    protected fun logSaveError(taskId: String, error: String) {
        Log.e(TAG, "Failed to save ${getTaskType()} task: $taskId, error: $error")
    }

    /**
     * 记录加载失败日志
     *
     * @param taskId 任务ID
     * @param error 错误信息
     */
    protected fun logLoadError(taskId: String, error: String) {
        Log.e(TAG, "Failed to load ${getTaskType()} task: $taskId, error: $error")
    }
}
