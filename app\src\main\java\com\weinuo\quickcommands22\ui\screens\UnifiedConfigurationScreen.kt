package com.weinuo.quickcommands22.ui.screens


import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import kotlinx.coroutines.delay
import com.weinuo.quickcommands22.ui.components.CategoryDisplayGrid
import com.weinuo.quickcommands22.ui.components.themed.ThemedSearchTextField
import androidx.navigation.NavController

import com.weinuo.quickcommands22.ui.configuration.*
import com.weinuo.quickcommands22.storage.NavigationDataStorageManager
import com.weinuo.quickcommands22.data.SettingsRepository
import com.weinuo.quickcommands22.ui.theme.manager.ThemeManager
import com.weinuo.quickcommands22.ui.components.skyblue.SkyBlueBackButton

/**
 * 统一配置界面
 *
 * 统一的配置界面，支持三种模式：添加触发条件、添加中止条件、添加任务。
 * 支持编辑模式，可以预填充现有配置数据进行修改。
 * 集成权限检查、网格展示、配置面板，完全复用现有组件和逻辑。
 *
 * @param configurationMode 配置模式
 * @param initialConfigData 初始配置数据（导航键名，编辑模式使用）
 * @param editIndex 编辑项索引（编辑模式使用）
 * @param onItemConfigured 配置完成回调，包含配置项和编辑索引
 * @param onNavigateToDetailedConfig 导航到详细配置界面的回调
 * @param onDismiss 取消回调
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun UnifiedConfigurationScreen(
    configurationMode: ConfigurationMode,
    navController: NavController,
    initialConfigData: String? = null,
    editIndex: Int? = null,
    onItemConfigured: (Any, Int?) -> Unit,
    onNavigateToDetailedConfig: (ConfigurationItem, String?, Int?) -> Unit,
    onDismiss: () -> Unit
) {
    val context = LocalContext.current
    val focusManager = LocalFocusManager.current

    // 获取主题管理器和设置仓库
    val themeManager = remember { ThemeManager.getInstance(context) }
    val settingsRepository = remember { SettingsRepository(context) }
    val globalSettings by settingsRepository.globalSettings.collectAsState()

    // 主题感知的标题样式
    val themeAwareTitleStyle = if (themeManager.getCurrentThemeId() == "sky_blue") {
        // 天空蓝主题：使用全局设置的标题字重和字体大小
        MaterialTheme.typography.titleLarge.copy(
            fontWeight = when (globalSettings.topAppBarTitleFontWeight) {
                "normal" -> FontWeight.Normal
                "medium" -> FontWeight.Medium
                "bold" -> FontWeight.Bold
                else -> FontWeight.Medium
            },
            fontSize = globalSettings.screenTitleFontSize.sp
        )
    } else {
        // 海洋蓝主题：保持原有样式
        MaterialTheme.typography.titleLarge
    }

    // 创建导航数据存储管理器
    val navigationDataManager = remember { NavigationDataStorageManager(context) }

    // 编辑模式检测
    val isEditMode = initialConfigData != null && editIndex != null

    // 状态管理（移除对话框相关状态）

    // 搜索状态管理
    var searchQuery by remember { mutableStateOf("") }
    var debouncedSearchQuery by remember { mutableStateOf("") }

    // 防抖动搜索
    LaunchedEffect(searchQuery) {
        delay(300) // 300ms防抖动
        debouncedSearchQuery = searchQuery
    }

    // 获取配置项列表
    val allItems = remember(configurationMode) {
        ConfigurationDataProvider.getItemsForMode(context, configurationMode)
    }

    // 获取扩展搜索数据（包含子配置项信息）
    val searchableItems = remember(configurationMode) {
        ConfigurationDataProvider.getSearchableItemsForMode(context, configurationMode)
    }

    // 过滤后的配置项列表（使用防抖动的搜索查询和扩展搜索）
    val filteredItems = remember(searchableItems, debouncedSearchQuery) {
        if (debouncedSearchQuery.isBlank()) {
            allItems
        } else {
            searchableItems.filter { searchableItem ->
                searchableItem.searchableText.contains(debouncedSearchQuery, ignoreCase = true)
            }.map { it.mainItem }
        }
    }

    // 获取显示信息，编辑模式显示不同标题
    val title = if (isEditMode) {
        when (configurationMode) {
            ConfigurationMode.TRIGGER_CONDITION -> "编辑条件"
            ConfigurationMode.ABORT_CONDITION -> "编辑中止条件"
            ConfigurationMode.TASK -> "编辑任务"
        }
    } else {
        configurationMode.getDisplayInfo()
    }

    // 编辑模式初始化
    LaunchedEffect(initialConfigData) {
        if (initialConfigData != null) {
            try {
                // 从导航数据存储加载配置数据
                val configObject = when (configurationMode) {
                    ConfigurationMode.TRIGGER_CONDITION,
                    ConfigurationMode.ABORT_CONDITION -> {
                        navigationDataManager.loadConditionEditData(initialConfigData)?.condition
                    }
                    ConfigurationMode.TASK -> {
                        navigationDataManager.loadTaskEditData(initialConfigData)?.task
                    }
                }

                configObject?.let { config ->
                    // TODO: 实现基于原生数据类型的配置项查找逻辑
                    // 暂时跳过配置项匹配，直接使用第一个配置项
                    if (allItems.isNotEmpty()) {
                        onNavigateToDetailedConfig(allItems.first(), initialConfigData, editIndex)
                    }
                }
            } catch (e: Exception) {
                // 加载失败，保持默认状态
            }
        }
    }

    // 移除复杂的数据传递逻辑，因为DetailedConfigurationScreen现在直接跳过UnifiedConfigurationScreen



    // 权限检查已移至详细配置界面

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = title,
                        style = themeAwareTitleStyle
                    )
                },
                navigationIcon = {
                    if (themeManager.getCurrentThemeId() == "sky_blue") {
                        // 天空蓝主题：使用专用的返回按钮
                        SkyBlueBackButton(onClick = onDismiss)
                    } else {
                        // 其他主题：使用原有的箭头图标
                        IconButton(onClick = onDismiss) {
                            Icon(
                                imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                                contentDescription = "返回"
                            )
                        }
                    }
                }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            // 搜索框
            ThemedSearchTextField(
                searchQuery = searchQuery,
                onSearchQueryChange = { searchQuery = it },
                onClearSearch = {
                    searchQuery = ""
                    focusManager.clearFocus()
                },
                placeholder = configurationMode.getSearchPlaceholder(),
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp, vertical = 8.dp)
            )

            // 网格展示区域
            if (filteredItems.isEmpty() && debouncedSearchQuery.isNotEmpty()) {
                // 搜索结果为空时的提示
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .weight(1f)
                        .padding(32.dp),
                    horizontalAlignment = androidx.compose.ui.Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center
                ) {
                    Text(
                        text = "未找到匹配的选项",
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "尝试使用不同的关键词搜索",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            } else {
                CategoryDisplayGrid(
                    items = filteredItems,
                    onItemSelected = { item ->
                        // 导航到详细配置界面而不是显示对话框
                        // 对于新建模式，不传递初始数据和编辑索引
                        onNavigateToDetailedConfig(item, null, null)
                    },
                    modifier = Modifier.weight(1f)
                )
            }
        }
    }

    // 配置对话框已替换为全屏详细配置界面
}

/**
 * 统一配置内容组件（不依赖NavController）
 *
 * 这是一个纯内容组件，可以在Activity或Fragment中使用，
 * 通过回调函数处理导航逻辑，实现更好的解耦。
 *
 * @param configurationMode 配置模式
 * @param initialConfigData 初始配置数据（导航键名，编辑模式使用）
 * @param editIndex 编辑项索引（编辑模式使用）
 * @param onNavigateBack 返回导航回调
 * @param onNavigateToDetailedConfiguration 导航到详细配置界面回调
 * @param onItemConfigured 配置完成回调，包含配置项和编辑索引
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun UnifiedConfigurationContent(
    configurationMode: ConfigurationMode,
    initialConfigData: String? = null,
    editIndex: Int? = null,
    onNavigateBack: () -> Unit,
    onNavigateToDetailedConfiguration: (ConfigurationItem, String?, Int?) -> Unit,
    onItemConfigured: (Any, Int?) -> Unit
) {
    val context = LocalContext.current
    val focusManager = LocalFocusManager.current

    // 获取主题管理器和设置仓库
    val themeManager = remember { ThemeManager.getInstance(context) }
    val settingsRepository = remember { SettingsRepository(context) }
    val globalSettings by settingsRepository.globalSettings.collectAsState()

    // 主题感知的标题样式
    val themeAwareTitleStyle = if (themeManager.getCurrentThemeId() == "sky_blue") {
        // 天空蓝主题：使用全局设置的标题字重和字体大小
        MaterialTheme.typography.titleLarge.copy(
            fontWeight = when (globalSettings.topAppBarTitleFontWeight) {
                "normal" -> FontWeight.Normal
                "medium" -> FontWeight.Medium
                "bold" -> FontWeight.Bold
                else -> FontWeight.Medium
            },
            fontSize = globalSettings.screenTitleFontSize.sp
        )
    } else {
        // 海洋蓝主题：保持原有样式
        MaterialTheme.typography.titleLarge
    }

    // 创建导航数据存储管理器
    val navigationDataManager = remember { NavigationDataStorageManager(context) }

    // 编辑模式检测
    val isEditMode = initialConfigData != null && editIndex != null

    // 搜索状态管理
    var searchQuery by remember { mutableStateOf("") }
    var debouncedSearchQuery by remember { mutableStateOf("") }

    // 防抖动搜索
    LaunchedEffect(searchQuery) {
        delay(300) // 300ms防抖动
        debouncedSearchQuery = searchQuery
    }

    // 获取所有配置项并过滤实验性功能
    val allConfigItems = remember(configurationMode) {
        ConfigurationDataProvider.getItemsForMode(context, configurationMode)
    }

    // 实验性功能过滤已在子配置项级别实现，主配置项无需过滤
    val allItems = allConfigItems

    // 获取扩展搜索数据（包含子配置项信息）
    // 实验性功能过滤已在子配置项级别实现，搜索项无需过滤
    val searchableItems = remember(configurationMode) {
        ConfigurationDataProvider.getSearchableItemsForMode(context, configurationMode)
    }

    // 过滤后的配置项列表（使用防抖动的搜索查询和扩展搜索）
    val filteredItems = remember(searchableItems, debouncedSearchQuery) {
        if (debouncedSearchQuery.isBlank()) {
            allItems
        } else {
            searchableItems.filter { searchableItem ->
                searchableItem.searchableText.contains(debouncedSearchQuery, ignoreCase = true)
            }.map { it.mainItem }
        }
    }

    // 获取显示信息，编辑模式显示不同标题
    val title = if (isEditMode) {
        when (configurationMode) {
            ConfigurationMode.TRIGGER_CONDITION -> "编辑条件"
            ConfigurationMode.ABORT_CONDITION -> "编辑中止条件"
            ConfigurationMode.TASK -> "编辑任务"
        }
    } else {
        configurationMode.getDisplayInfo()
    }

    // 编辑模式初始化
    LaunchedEffect(initialConfigData) {
        if (initialConfigData != null) {
            try {
                // 从导航数据存储加载配置数据
                val configObject = when (configurationMode) {
                    ConfigurationMode.TRIGGER_CONDITION,
                    ConfigurationMode.ABORT_CONDITION -> {
                        navigationDataManager.loadConditionEditData(initialConfigData)?.condition
                    }
                    ConfigurationMode.TASK -> {
                        navigationDataManager.loadTaskEditData(initialConfigData)?.task
                    }
                }

                configObject?.let { config ->
                    // TODO: 实现基于原生数据类型的配置项查找逻辑
                    // 暂时跳过配置项匹配，直接使用第一个配置项
                    if (allItems.isNotEmpty()) {
                        onNavigateToDetailedConfiguration(allItems.first(), initialConfigData, editIndex)
                    }
                }
            } catch (e: Exception) {
                // 加载失败，保持默认状态
            }
        }
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = title,
                        style = themeAwareTitleStyle
                    )
                },
                navigationIcon = {
                    if (themeManager.getCurrentThemeId() == "sky_blue") {
                        // 天空蓝主题：使用专用的返回按钮
                        SkyBlueBackButton(onClick = onNavigateBack)
                    } else {
                        // 其他主题：使用原有的箭头图标
                        IconButton(onClick = onNavigateBack) {
                            Icon(
                                imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                                contentDescription = "返回"
                            )
                        }
                    }
                }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            // 搜索框
            ThemedSearchTextField(
                searchQuery = searchQuery,
                onSearchQueryChange = { searchQuery = it },
                onClearSearch = {
                    searchQuery = ""
                    focusManager.clearFocus()
                },
                placeholder = configurationMode.getSearchPlaceholder(),
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp, vertical = 8.dp)
            )

            // 网格展示区域
            if (filteredItems.isEmpty() && debouncedSearchQuery.isNotEmpty()) {
                // 搜索结果为空时的提示
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .weight(1f)
                        .padding(32.dp),
                    horizontalAlignment = androidx.compose.ui.Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center
                ) {
                    Text(
                        text = "未找到匹配的选项",
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "尝试使用不同的关键词搜索",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            } else {
                CategoryDisplayGrid(
                    items = filteredItems,
                    onItemSelected = { item ->
                        // 导航到详细配置界面而不是显示对话框
                        // 对于新建模式，不传递初始数据和编辑索引
                        onNavigateToDetailedConfiguration(item, null, null)
                    },
                    modifier = Modifier.weight(1f)
                )
            }
        }
    }
}


