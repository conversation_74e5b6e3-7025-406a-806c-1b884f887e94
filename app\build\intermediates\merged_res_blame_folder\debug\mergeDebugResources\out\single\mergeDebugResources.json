[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-debug-48:\\drawable_menu_item_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-main-50:\\drawable\\menu_item_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-debug-48:\\xml_device_admin.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-main-50:\\xml\\device_admin.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-debug-48:\\xml_shortcuts.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-main-50:\\xml\\shortcuts.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-debug-48:\\xml_system_operation_accessibility_service_config.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-main-50:\\xml\\system_operation_accessibility_service_config.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-debug-48:\\drawable_ic_screen_rotation_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-main-50:\\drawable\\ic_screen_rotation_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-debug-48:\\drawable_ic_music_note_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-main-50:\\drawable\\ic_music_note_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-debug-48:\\menu_advanced_recording_menu.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-main-50:\\menu\\advanced_recording_menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-debug-48:\\drawable_ic_launcher_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-main-50:\\drawable\\ic_launcher_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-debug-48:\\drawable_ic_shortcut_command.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-main-50:\\drawable\\ic_shortcut_command.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-debug-48:\\drawable_ic_circle_white.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-main-50:\\drawable\\ic_circle_white.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-debug-48:\\drawable_ic_notification.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-main-50:\\drawable\\ic_notification.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-debug-48:\\drawable_ic_close.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-main-50:\\drawable\\ic_close.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-debug-48:\\drawable_ic_lightbulb_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-main-50:\\drawable\\ic_lightbulb_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-debug-48:\\xml_data_extraction_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-main-50:\\xml\\data_extraction_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-debug-48:\\drawable_template_screen_on_network.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-main-50:\\drawable\\template_screen_on_network.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-debug-48:\\xml_auto_clicker_accessibility_service_config.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-main-50:\\xml\\auto_clicker_accessibility_service_config.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-debug-48:\\drawable_ic_settings.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-main-50:\\drawable\\ic_settings.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-debug-48:\\mipmap-mdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-main-50:\\mipmap-mdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-debug-48:\\drawable_ic_share_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-main-50:\\drawable\\ic_share_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-debug-48:\\drawable_ic_stop.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-main-50:\\drawable\\ic_stop.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-debug-48:\\drawable_ic_sky_blue_check_enabled.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-main-50:\\drawable\\ic_sky_blue_check_enabled.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-debug-48:\\drawable_ic_launcher_foreground.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-main-50:\\drawable\\ic_launcher_foreground.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-debug-48:\\xml_accessibility_service_config.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-main-50:\\xml\\accessibility_service_config.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-debug-48:\\drawable_circular_progress_drawable.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-main-50:\\drawable\\circular_progress_drawable.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-debug-48:\\xml_file_provider_paths.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-main-50:\\xml\\file_provider_paths.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-debug-48:\\drawable_ic_radio_button_checked_sky_blue.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-main-50:\\drawable\\ic_radio_button_checked_sky_blue.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-debug-48:\\drawable_ic_sky_blue_back_arrow.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-main-50:\\drawable\\ic_sky_blue_back_arrow.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-debug-48:\\xml_one_click_command_widget_4_info.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-main-50:\\xml\\one_click_command_widget_4_info.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-debug-48:\\xml_gesture_recognition_accessibility_service_config.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-main-50:\\xml\\gesture_recognition_accessibility_service_config.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-debug-48:\\drawable_overlay_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-main-50:\\drawable\\overlay_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-debug-48:\\drawable_ic_sky_blue_more_vert.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-main-50:\\drawable\\ic_sky_blue_more_vert.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-debug-48:\\drawable_ic_swipe.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-main-50:\\drawable\\ic_swipe.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-debug-48:\\drawable_ic_radio_button_unchecked_sky_blue.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-main-50:\\drawable\\ic_radio_button_unchecked_sky_blue.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-debug-48:\\drawable_ic_add_skyblue.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-main-50:\\drawable\\ic_add_skyblue.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-debug-48:\\drawable_ic_map_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-main-50:\\drawable\\ic_map_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-debug-48:\\drawable_ic_long_press.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-main-50:\\drawable\\ic_long_press.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-debug-48:\\xml_interface_interaction_accessibility_service_config.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-main-50:\\xml\\interface_interaction_accessibility_service_config.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-debug-48:\\layout_overlay_debug_log.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-main-50:\\layout\\overlay_debug_log.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-debug-48:\\drawable_floating_button_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-main-50:\\drawable\\floating_button_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-debug-48:\\mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-main-50:\\mipmap-xxxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-debug-48:\\mipmap-anydpi_ic_launcher_round.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-main-50:\\mipmap-anydpi\\ic_launcher_round.xml"}, {"merged": "com.weinuo.quickcommands.app-debug-48:/layout_overlay_debug_log.xml.flat", "source": "com.weinuo.quickcommands.app-main-50:/layout/overlay_debug_log.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-debug-48:\\drawable_ic_search_sky_blue_bold.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-main-50:\\drawable\\ic_search_sky_blue_bold.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-debug-48:\\drawable_ic_sky_blue_check_disabled.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-main-50:\\drawable\\ic_sky_blue_check_disabled.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-debug-48:\\mipmap-hdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-main-50:\\mipmap-hdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-debug-48:\\mipmap-mdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-main-50:\\mipmap-mdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-debug-48:\\drawable_ic_shopping_cart_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-main-50:\\drawable\\ic_shopping_cart_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-debug-48:\\mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-main-50:\\mipmap-xxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-debug-48:\\mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-main-50:\\mipmap-xhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-debug-48:\\xml_one_click_command_widget_3_info.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-main-50:\\xml\\one_click_command_widget_3_info.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-debug-48:\\xml_backup_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-main-50:\\xml\\backup_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-debug-48:\\drawable_ic_search_sky_blue_regular.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-main-50:\\drawable\\ic_search_sky_blue_regular.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-debug-48:\\drawable_overlay_background_md3.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-main-50:\\drawable\\overlay_background_md3.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-debug-48:\\mipmap-anydpi_ic_launcher.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-main-50:\\mipmap-anydpi\\ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-debug-48:\\drawable_ic_pause.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-main-50:\\drawable\\ic_pause.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-debug-48:\\xml_one_click_command_widget_2_info.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-main-50:\\xml\\one_click_command_widget_2_info.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-debug-48:\\drawable_ic_play_arrow.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-main-50:\\drawable\\ic_play_arrow.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-debug-48:\\drawable_menu_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-main-50:\\drawable\\menu_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-debug-48:\\drawable_circular_reminder_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-main-50:\\drawable\\circular_reminder_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-debug-48:\\drawable_template_screen_off_network.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-main-50:\\drawable\\template_screen_off_network.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-debug-48:\\drawable_template_anti_embarrassment_mode.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-main-50:\\drawable\\template_anti_embarrassment_mode.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-debug-48:\\drawable_ic_save.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-main-50:\\drawable\\ic_save.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-debug-48:\\drawable_ic_close_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-main-50:\\drawable\\ic_close_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-debug-48:\\drawable_ic_apps_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-main-50:\\drawable\\ic_apps_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-debug-48:\\drawable_ic_touch_app.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-main-50:\\drawable\\ic_touch_app.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-debug-48:\\drawable_ic_record_voice_over.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-main-50:\\drawable\\ic_record_voice_over.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-debug-48:\\drawable_ic_search_sky_blue_medium.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-main-50:\\drawable\\ic_search_sky_blue_medium.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-debug-48:\\layout_widget_one_click_command.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-main-50:\\layout\\widget_one_click_command.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-debug-48:\\mipmap-hdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-main-50:\\mipmap-hdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-debug-48:\\drawable_ic_sky_blue_arrow_right.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-main-50:\\drawable\\ic_sky_blue_arrow_right.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-debug-48:\\xml_one_click_command_widget_1_info.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-main-50:\\xml\\one_click_command_widget_1_info.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-debug-48:\\drawable_ic_clear.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-main-50:\\drawable\\ic_clear.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-debug-48:\\mipmap-xxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-main-50:\\mipmap-xxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-debug-48:\\mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-main-50:\\mipmap-xxxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-debug-48:\\layout_overlay_smart_reminder.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-main-50:\\layout\\overlay_smart_reminder.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-debug-48:\\drawable_widget_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-main-50:\\drawable\\widget_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-debug-48:\\mipmap-xhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.weinuo.quickcommands.app-main-50:\\mipmap-xhdpi\\ic_launcher.webp"}]