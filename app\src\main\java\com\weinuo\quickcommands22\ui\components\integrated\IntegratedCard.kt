package com.weinuo.quickcommands22.ui.components.integrated

import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.collectIsHoveredAsState
import androidx.compose.foundation.interaction.collectIsPressedAsState
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.weinuo.quickcommands22.ui.theme.config.CardConfig
import com.weinuo.quickcommands22.ui.theme.manager.ThemeManager
import com.weinuo.quickcommands22.ui.theme.skyblue.SkyBlueColorScheme
import com.weinuo.quickcommands22.ui.theme.skyblue.SkyBlueStyleConfiguration
import com.weinuo.quickcommands22.data.SettingsRepository

/**
 * 整合设计风格的卡片组件
 *
 * 特点：
 * - 无阴影设计
 * - 大圆角
 * - 流畅的交互动画
 * - 统一的视觉体验
 * - 支持多种样式变体
 */
@Composable
fun IntegratedCard(
    config: CardConfig,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    val themeManager = remember { ThemeManager.getInstance(context) }
    val styleConfig = themeManager.currentThemeProvider.value.getStyleConfiguration()
    val interactionConfig = themeManager.currentThemeProvider.value.getInteractionConfiguration()

    // 获取动态卡片样式配置（仅在天空蓝主题下使用）
    val dynamicCardStyle = if (themeManager.getCurrentThemeId() == "sky_blue") {
        val settingsRepository = remember { SettingsRepository(context) }
        SkyBlueStyleConfiguration.getDynamicCardStyleConfig(settingsRepository)
    } else {
        styleConfig.cardStyle
    }
    
    // 交互状态
    val interactionSource = remember { MutableInteractionSource() }
    val isPressed by interactionSource.collectIsPressedAsState()
    val isHovered by interactionSource.collectIsHoveredAsState()
    
    // 动画状态
    val animatedScale by animateFloatAsState(
        targetValue = if (isPressed) 0.99f else 1f,
        animationSpec = tween(durationMillis = 150),
        label = "card_scale"
    )
    
    val animatedBackgroundColor by animateColorAsState(
        targetValue = when {
            isPressed -> SkyBlueColorScheme.ExtendedColors.interactivePressed
            isHovered -> SkyBlueColorScheme.ExtendedColors.interactiveHover
            else -> Color.Transparent
        },
        animationSpec = tween(durationMillis = 150),
        label = "card_background"
    )

    // 根据是否有onClick决定使用哪种Card
    if (config.onClick != null) {
        Card(
            onClick = config.onClick,
            modifier = modifier
                .scale(animatedScale)
                .fillMaxWidth(),
            enabled = config.enabled,
            shape = RoundedCornerShape(dynamicCardStyle.defaultCornerRadius),
            colors = CardDefaults.cardColors(
                containerColor = config.backgroundColor ?: MaterialTheme.colorScheme.surfaceContainerLow,
                contentColor = config.contentColor ?: MaterialTheme.colorScheme.onSurface,
                disabledContainerColor = MaterialTheme.colorScheme.surfaceContainerLow.copy(alpha = 0.38f),
                disabledContentColor = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.38f)
            ),
            elevation = CardDefaults.cardElevation(
                defaultElevation = 0.dp, // 整合设计不使用阴影
                pressedElevation = 0.dp,
                focusedElevation = 0.dp,
                hoveredElevation = 0.dp,
                draggedElevation = 0.dp,
                disabledElevation = 0.dp
            ),
            border = config.border as? BorderStroke,
            interactionSource = interactionSource
        ) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(dynamicCardStyle.getSettingsPaddingValues())
            ) {
                config.content()
            }

            // 交互覆盖层
            if (animatedBackgroundColor != Color.Transparent) {
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(dynamicCardStyle.getSettingsPaddingValues())
                ) {
                    Surface(
                        modifier = Modifier.fillMaxSize(),
                        color = animatedBackgroundColor,
                        shape = RoundedCornerShape(dynamicCardStyle.defaultCornerRadius)
                    ) {}
                }
            }
        }
    } else {
        // 不可点击的Card
        Card(
            modifier = modifier.fillMaxWidth(),
            shape = RoundedCornerShape(dynamicCardStyle.defaultCornerRadius),
            colors = CardDefaults.cardColors(
                containerColor = config.backgroundColor ?: MaterialTheme.colorScheme.surfaceContainerLow,
                contentColor = config.contentColor ?: MaterialTheme.colorScheme.onSurface
            ),
            elevation = CardDefaults.cardElevation(
                defaultElevation = 0.dp // 整合设计不使用阴影
            ),
            border = config.border as? BorderStroke
        ) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(dynamicCardStyle.getSettingsPaddingValues())
            ) {
                config.content()
            }
        }
    }
}

/**
 * 整合设计风格的轮廓卡片
 * 
 * 带有边框的卡片变体
 */
@Composable
fun IntegratedOutlinedCard(
    config: CardConfig,
    modifier: Modifier = Modifier,
    borderColor: Color = MaterialTheme.colorScheme.outline,
    borderWidth: Dp = 1.dp
) {
    IntegratedCard(
        config = config.copy(
            border = BorderStroke(
                width = borderWidth,
                color = borderColor
            )
        ),
        modifier = modifier
    )
}

/**
 * 整合设计风格的填充卡片
 * 
 * 带有填充背景的卡片变体
 */
@Composable
fun IntegratedFilledCard(
    config: CardConfig,
    modifier: Modifier = Modifier,
    fillColor: Color = MaterialTheme.colorScheme.surfaceContainerHighest
) {
    IntegratedCard(
        config = config.copy(
            backgroundColor = fillColor
        ),
        modifier = modifier
    )
}

/**
 * 整合设计风格的强调卡片
 * 
 * 使用品牌色的卡片变体
 */
@Composable
fun IntegratedEmphasizedCard(
    config: CardConfig,
    modifier: Modifier = Modifier
) {
    IntegratedCard(
        config = config.copy(
            backgroundColor = MaterialTheme.colorScheme.primaryContainer,
            contentColor = MaterialTheme.colorScheme.onPrimaryContainer
        ),
        modifier = modifier
    )
}



/**
 * 整合设计风格的可选择卡片
 * 
 * 支持选择状态的卡片变体
 */
@Composable
fun IntegratedSelectableCard(
    config: CardConfig,
    selected: Boolean,
    onSelectionChange: (Boolean) -> Unit,
    modifier: Modifier = Modifier
) {
    val extendedColors = SkyBlueColorScheme.ExtendedColors
    
    // 动画状态
    val animatedBorderColor by animateColorAsState(
        targetValue = if (selected) {
            extendedColors.iconEmphasize
        } else {
            MaterialTheme.colorScheme.outline
        },
        animationSpec = tween(durationMillis = 200),
        label = "selectable_border_color"
    )
    
    val animatedBackgroundColor by animateColorAsState(
        targetValue = if (selected) {
            MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.3f)
        } else {
            config.backgroundColor ?: MaterialTheme.colorScheme.surfaceContainerLow
        },
        animationSpec = tween(durationMillis = 200),
        label = "selectable_background_color"
    )

    IntegratedCard(
        config = config.copy(
            onClick = { onSelectionChange(!selected) },
            backgroundColor = animatedBackgroundColor,
            border = BorderStroke(
                width = if (selected) 2.dp else 1.dp,
                color = animatedBorderColor
            )
        ),
        modifier = modifier
    )
}
