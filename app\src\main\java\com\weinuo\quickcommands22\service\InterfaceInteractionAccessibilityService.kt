package com.weinuo.quickcommands22.service

import android.accessibilityservice.AccessibilityService
import android.accessibilityservice.AccessibilityServiceInfo
import android.util.Log
import android.view.accessibility.AccessibilityEvent
import android.view.accessibility.AccessibilityNodeInfo
import com.weinuo.quickcommands22.R
import com.weinuo.quickcommands22.model.TextMatchMode
import com.weinuo.quickcommands22.model.PixelColorInfo
import android.graphics.Color
import android.graphics.Rect
import java.io.File
import java.io.FileWriter
import java.io.IOException

/**
 * 屏幕文字检查结果
 */
data class ScreenTextCheckResult(
    val found: Boolean,
    val viewId: String? = null
)

/**
 * 快捷指令-界面交互服务
 *
 * 用于界面自动化操作，包括：
 * - 界面点击条件：检测到对特定文本内容的点击时触发条件
 * - 屏幕内容条件：当屏幕出现或移出某些文本内容时触发条件
 * - 检查屏幕文字任务：检查特定文本字符串当前是否显示在屏幕上
 *   支持精确匹配/包含匹配、叠加层检测、隐藏文本过滤、视图ID提取等高级选项
 * - 读取屏幕内容任务：将当前屏幕的内容捕获到文本文件中
 * - 检查界面元素颜色任务：分析指定位置的界面元素颜色特征
 *   基于元素类型、状态、文本内容等推断颜色信息，无需截图权限
 *
 * 如需使用界面相关的触发条件或任务，请启用此服务。
 *
 * 采用按需激活设计：
 * - 默认不监听任何无障碍事件（eventTypes = 0）
 * - 只在需要时动态配置事件监听
 * - 最小化资源消耗和电量使用
 */
class InterfaceInteractionAccessibilityService : AccessibilityService() {

    companion object {
        private const val TAG = "InterfaceInteractionService"

        // 服务实例（用于外部调用）
        @Volatile
        private var instance: InterfaceInteractionAccessibilityService? = null

        fun getInstance(): InterfaceInteractionAccessibilityService? = instance

        /**
         * 检查服务是否可用
         */
        fun isServiceAvailable(): Boolean = instance != null
    }

    // 注册的界面交互条件
    private val registeredConditions = mutableListOf<com.weinuo.quickcommands22.model.AppStateCondition>()

    // 条件触发回调
    private var onConditionTriggered: ((com.weinuo.quickcommands22.model.AppStateCondition, Map<String, Any>) -> Unit)? = null

    // 上次屏幕内容缓存（用于检测内容变化）
    private val lastScreenContentCache = mutableMapOf<String, String>()

    override fun onServiceConnected() {
        super.onServiceConnected()
        instance = this
        Log.i(TAG, "界面交互服务已连接")

        // 配置服务 - 按需激活设计
        configureService()
    }

    override fun onDestroy() {
        super.onDestroy()
        instance = null
        Log.i(TAG, "界面交互服务已断开")
    }

    override fun onAccessibilityEvent(event: AccessibilityEvent?) {
        // 按需处理无障碍事件
        event?.let {
            try {
                when (it.eventType) {
                    AccessibilityEvent.TYPE_VIEW_CLICKED -> {
                        handleInteractionEvent(it, com.weinuo.quickcommands22.model.InterfaceInteractionType.CLICK)
                    }
                    AccessibilityEvent.TYPE_VIEW_LONG_CLICKED -> {
                        handleInteractionEvent(it, com.weinuo.quickcommands22.model.InterfaceInteractionType.LONG_CLICK)
                    }
                    AccessibilityEvent.TYPE_WINDOW_CONTENT_CHANGED -> {
                        handleContentChangedEvent(it)
                    }
                    else -> {
                        // 其他事件类型暂不处理
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error handling accessibility event", e)
            }
        }
    }

    override fun onInterrupt() {
        Log.w(TAG, "界面交互服务被中断")
    }

    /**
     * 配置无障碍服务 - 按需激活设计
     */
    private fun configureService() {
        val info = AccessibilityServiceInfo()

        // 按需激活配置 - 默认不监听任何事件
        info.eventTypes = 0  // 不监听任何无障碍事件（按需激活）
        info.feedbackType = AccessibilityServiceInfo.FEEDBACK_GENERIC
        info.flags = AccessibilityServiceInfo.FLAG_INCLUDE_NOT_IMPORTANT_VIEWS or
                    AccessibilityServiceInfo.FLAG_REPORT_VIEW_IDS
        info.notificationTimeout = 0  // 不需要通知延迟

        serviceInfo = info
    }

    /**
     * 注册界面交互条件
     */
    fun registerCondition(condition: com.weinuo.quickcommands22.model.AppStateCondition) {
        synchronized(registeredConditions) {
            // 避免重复注册
            if (registeredConditions.none { it.id == condition.id }) {
                registeredConditions.add(condition)
                Log.d(TAG, "Registered interface interaction condition: ${condition.getDescription()}")

                // 更新事件监听
                updateEventListening()
            }
        }
    }

    /**
     * 取消注册界面交互条件
     */
    fun unregisterCondition(conditionId: String) {
        synchronized(registeredConditions) {
            val removed = registeredConditions.removeAll { it.id == conditionId }
            if (removed) {
                Log.d(TAG, "Unregistered interface interaction condition: $conditionId")

                // 更新事件监听
                updateEventListening()
            }
        }
    }

    /**
     * 设置条件触发回调
     */
    fun setConditionTriggeredCallback(callback: (com.weinuo.quickcommands22.model.AppStateCondition, Map<String, Any>) -> Unit) {
        onConditionTriggered = callback
    }

    /**
     * 更新事件监听
     */
    private fun updateEventListening() {
        val hasClickConditions = registeredConditions.any {
            it.stateType == com.weinuo.quickcommands22.model.AppStateType.INTERFACE_CLICK &&
            it.interfaceInteractionType == com.weinuo.quickcommands22.model.InterfaceInteractionType.CLICK
        }
        val hasLongClickConditions = registeredConditions.any {
            it.stateType == com.weinuo.quickcommands22.model.AppStateType.INTERFACE_CLICK &&
            it.interfaceInteractionType == com.weinuo.quickcommands22.model.InterfaceInteractionType.LONG_CLICK
        }
        val hasContentConditions = registeredConditions.any {
            it.stateType == com.weinuo.quickcommands22.model.AppStateType.SCREEN_CONTENT
        }

        var eventTypes = 0
        if (hasClickConditions) {
            eventTypes = eventTypes or AccessibilityEvent.TYPE_VIEW_CLICKED
        }
        if (hasLongClickConditions) {
            eventTypes = eventTypes or AccessibilityEvent.TYPE_VIEW_LONG_CLICKED
        }
        if (hasContentConditions) {
            eventTypes = eventTypes or AccessibilityEvent.TYPE_WINDOW_CONTENT_CHANGED
        }

        enableEventListening(eventTypes)
        Log.d(TAG, "Updated event listening: click=$hasClickConditions, longClick=$hasLongClickConditions, content=$hasContentConditions, eventTypes=$eventTypes")
    }

    /**
     * 动态启用事件监听
     *
     * @param eventTypes 要监听的事件类型
     */
    fun enableEventListening(eventTypes: Int) {
        val info = serviceInfo ?: AccessibilityServiceInfo()
        info.eventTypes = eventTypes
        serviceInfo = info
        Log.d(TAG, "已启用事件监听: $eventTypes")
    }

    /**
     * 禁用事件监听
     */
    fun disableEventListening() {
        val info = serviceInfo ?: AccessibilityServiceInfo()
        info.eventTypes = 0
        serviceInfo = info
        Log.d(TAG, "已禁用事件监听")
    }

    /**
     * 处理界面交互事件（点击或长按）
     */
    private fun handleInteractionEvent(event: AccessibilityEvent, interactionType: com.weinuo.quickcommands22.model.InterfaceInteractionType) {
        try {
            val source = event.source
            if (source != null) {
                val packageName = event.packageName?.toString() ?: ""

                // 检查是否匹配注册的界面交互条件
                synchronized(registeredConditions) {
                    for (condition in registeredConditions) {
                        if (condition.stateType == com.weinuo.quickcommands22.model.AppStateType.INTERFACE_CLICK) {
                            // 根据条件的叠加层设置获取交互文本
                            val interactionText = getNodeText(source, condition.includeOverlayLayers)

                            Log.d(TAG, "检测到${interactionType.displayName}事件: $interactionText, 包名: $packageName")

                            if (isInteractionConditionMatched(condition, interactionText, packageName, interactionType)) {
                                triggerCondition(condition, mapOf(
                                    "interactionText" to interactionText,
                                    "interactionType" to interactionType.name,
                                    "packageName" to packageName,
                                    "timestamp" to System.currentTimeMillis()
                                ))
                            }
                        }
                    }
                }

                source.recycle()
            }
        } catch (e: Exception) {
            Log.e(TAG, "处理界面交互事件失败", e)
        }
    }

    /**
     * 处理内容变化事件
     */
    private fun handleContentChangedEvent(event: AccessibilityEvent) {
        try {
            val packageName = event.packageName?.toString() ?: ""

            // 检查是否匹配注册的屏幕内容条件
            synchronized(registeredConditions) {
                for (condition in registeredConditions) {
                    if (condition.stateType == com.weinuo.quickcommands22.model.AppStateType.SCREEN_CONTENT) {
                        // 根据条件的叠加层设置读取屏幕内容
                        val currentContent = readScreenContent(condition.includeOverlayLayers)

                        if (isScreenContentConditionMatched(condition, currentContent, packageName)) {
                            triggerCondition(condition, mapOf(
                                "screenContent" to currentContent,
                                "packageName" to packageName,
                                "timestamp" to System.currentTimeMillis()
                            ))
                        }
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "处理内容变化事件失败", e)
        }
    }

    /**
     * 获取节点文本内容
     */
    private fun getNodeText(node: AccessibilityNodeInfo, includeOverlayLayers: Boolean = false): String {
        val text = StringBuilder()

        // 获取节点自身的文本
        node.text?.let { text.append(it) }
        node.contentDescription?.let {
            if (text.isNotEmpty()) text.append(" ")
            text.append(it)
        }

        // 如果包括叠加层，还需要获取视图ID信息
        if (includeOverlayLayers) {
            node.viewIdResourceName?.let { viewId ->
                if (text.isNotEmpty()) text.append(" ")
                text.append("[ID:$viewId]")
            }
        }

        return text.toString()
    }

    /**
     * 检查屏幕上是否包含指定文本
     *
     * @param targetText 目标文本
     * @param caseSensitive 是否区分大小写
     * @param useRegex 是否使用正则表达式
     * @return 是否找到目标文本
     */
    fun checkScreenText(targetText: String, caseSensitive: Boolean = false, useRegex: Boolean = false): Boolean {
        return try {
            val rootNode = rootInActiveWindow
            if (rootNode != null) {
                val result = searchTextInNode(rootNode, targetText, caseSensitive, useRegex)
                rootNode.recycle()
                result
            } else {
                false
            }
        } catch (e: Exception) {
            Log.e(TAG, getString(R.string.screen_text_check_failed), e)
            false
        }
    }

    /**
     * 检查屏幕上是否包含指定文本（增强版）
     *
     * @param targetText 目标文本
     * @param caseSensitive 是否区分大小写
     * @param useRegex 是否使用正则表达式
     * @param matchMode 匹配模式
     * @param includeOverlay 是否包括叠加层
     * @param ignoreHidden 是否忽略隐藏文本
     * @return 检查结果，包含是否找到和视图ID
     */
    fun checkScreenTextAdvanced(
        targetText: String,
        caseSensitive: Boolean = false,
        useRegex: Boolean = false,
        matchMode: TextMatchMode = TextMatchMode.CONTAINS,
        includeOverlay: Boolean = false,
        ignoreHidden: Boolean = true
    ): ScreenTextCheckResult {
        return try {
            val rootNode = rootInActiveWindow
            if (rootNode != null) {
                val result = searchTextInNodeAdvanced(
                    rootNode, targetText, caseSensitive, useRegex, matchMode, includeOverlay, ignoreHidden
                )
                rootNode.recycle()
                result
            } else {
                ScreenTextCheckResult(false)
            }
        } catch (e: Exception) {
            Log.e(TAG, getString(R.string.screen_text_check_failed), e)
            ScreenTextCheckResult(false)
        }
    }

    /**
     * 读取屏幕内容
     *
     * @param includeOverlayLayers 是否包括叠加层
     * @return 屏幕上的所有文本内容
     */
    fun readScreenContent(includeOverlayLayers: Boolean = false): String {
        return try {
            val rootNode = rootInActiveWindow
            if (rootNode != null) {
                val content = extractTextFromNode(rootNode, includeOverlayLayers)
                rootNode.recycle()
                content
            } else {
                ""
            }
        } catch (e: Exception) {
            Log.e(TAG, "读取屏幕内容失败", e)
            ""
        }
    }

    /**
     * 在节点中搜索文本
     */
    private fun searchTextInNode(node: AccessibilityNodeInfo, targetText: String, caseSensitive: Boolean, useRegex: Boolean): Boolean {
        // 检查当前节点的文本
        val nodeText = getNodeText(node)
        if (nodeText.isNotEmpty()) {
            val found = if (useRegex) {
                try {
                    val pattern = if (caseSensitive) {
                        targetText.toRegex()
                    } else {
                        targetText.toRegex(RegexOption.IGNORE_CASE)
                    }
                    pattern.containsMatchIn(nodeText)
                } catch (e: Exception) {
                    Log.w(TAG, "正则表达式匹配失败: $targetText", e)
                    false
                }
            } else {
                if (caseSensitive) {
                    nodeText.contains(targetText)
                } else {
                    nodeText.contains(targetText, ignoreCase = true)
                }
            }

            if (found) return true
        }

        // 递归检查子节点
        for (i in 0 until node.childCount) {
            val child = node.getChild(i)
            if (child != null) {
                val found = searchTextInNode(child, targetText, caseSensitive, useRegex)
                child.recycle()
                if (found) return true
            }
        }

        return false
    }

    /**
     * 在节点中搜索文本（增强版）
     */
    private fun searchTextInNodeAdvanced(
        node: AccessibilityNodeInfo,
        targetText: String,
        caseSensitive: Boolean,
        useRegex: Boolean,
        matchMode: TextMatchMode,
        includeOverlay: Boolean,
        ignoreHidden: Boolean
    ): ScreenTextCheckResult {
        // 如果忽略隐藏文本且当前节点不可见，跳过
        if (ignoreHidden && !node.isVisibleToUser) {
            return ScreenTextCheckResult(false)
        }

        // 检查当前节点的文本
        val nodeText = getNodeText(node, includeOverlay)
        if (nodeText.isNotEmpty()) {
            val found = when {
                useRegex -> {
                    try {
                        val pattern = if (caseSensitive) {
                            targetText.toRegex()
                        } else {
                            targetText.toRegex(RegexOption.IGNORE_CASE)
                        }
                        pattern.containsMatchIn(nodeText)
                    } catch (e: Exception) {
                        Log.w(TAG, "正则表达式匹配失败: $targetText", e)
                        false
                    }
                }
                matchMode == TextMatchMode.EXACT_MATCH -> {
                    if (caseSensitive) {
                        nodeText == targetText
                    } else {
                        nodeText.equals(targetText, ignoreCase = true)
                    }
                }
                else -> { // TextMatchMode.CONTAINS
                    if (caseSensitive) {
                        nodeText.contains(targetText)
                    } else {
                        nodeText.contains(targetText, ignoreCase = true)
                    }
                }
            }

            if (found) {
                // 尝试获取视图ID
                val viewId = node.viewIdResourceName
                return ScreenTextCheckResult(true, viewId)
            }
        }

        // 递归检查子节点
        for (i in 0 until node.childCount) {
            val child = node.getChild(i)
            if (child != null) {
                val result = searchTextInNodeAdvanced(
                    child, targetText, caseSensitive, useRegex, matchMode, includeOverlay, ignoreHidden
                )
                child.recycle()
                if (result.found) return result
            }
        }

        return ScreenTextCheckResult(false)
    }

    /**
     * 从节点提取文本内容
     */
    private fun extractTextFromNode(node: AccessibilityNodeInfo, includeOverlayLayers: Boolean = false): String {
        val text = StringBuilder()

        // 获取当前节点的文本
        val nodeText = getNodeText(node, includeOverlayLayers)
        if (nodeText.isNotEmpty()) {
            if (text.isNotEmpty()) text.append("\n")
            text.append(nodeText)
        }

        // 递归提取子节点的文本
        for (i in 0 until node.childCount) {
            val child = node.getChild(i)
            if (child != null) {
                val childText = extractTextFromNode(child, includeOverlayLayers)
                if (childText.isNotEmpty()) {
                    if (text.isNotEmpty()) text.append("\n")
                    text.append(childText)
                }
                child.recycle()
            }
        }

        return text.toString()
    }

    /**
     * 将检查结果保存到文件
     */
    fun saveCheckResultToFile(result: Boolean, filePath: String): Boolean {
        return try {
            val file = File(filePath)
            file.parentFile?.mkdirs()

            FileWriter(file).use { writer ->
                writer.write(result.toString())
            }

            Log.d(TAG, "检查结果已保存到: $filePath, 结果: $result")
            true
        } catch (e: IOException) {
            Log.e(TAG, "保存检查结果失败: $filePath", e)
            false
        }
    }

    /**
     * 将屏幕内容保存到文件
     */
    fun saveContentToFile(content: String, filePath: String): Boolean {
        return try {
            val file = File(filePath)
            file.parentFile?.mkdirs()

            FileWriter(file).use { writer ->
                writer.write(content)
            }

            Log.d(TAG, "屏幕内容已保存到: $filePath, 内容长度: ${content.length}")
            true
        } catch (e: IOException) {
            Log.e(TAG, "保存屏幕内容失败: $filePath", e)
            false
        }
    }

    /**
     * 检查界面交互条件是否匹配
     */
    private fun isInteractionConditionMatched(
        condition: com.weinuo.quickcommands22.model.AppStateCondition,
        interactionText: String,
        packageName: String,
        interactionType: com.weinuo.quickcommands22.model.InterfaceInteractionType
    ): Boolean {
        // 检查交互类型是否匹配
        if (condition.interfaceInteractionType != interactionType) {
            return false
        }

        // 检查应用包名是否匹配
        if (condition.detectionMode == com.weinuo.quickcommands22.model.AppDetectionMode.SELECTED_APPS) {
            if (condition.targetPackageName != packageName) {
                return false
            }
        }

        // 检查交互文本是否匹配
        return isTextMatched(interactionText, condition.clickTargetText, condition.caseSensitive, condition.useRegex)
    }

    /**
     * 检查屏幕内容条件是否匹配
     */
    private fun isScreenContentConditionMatched(
        condition: com.weinuo.quickcommands22.model.AppStateCondition,
        currentContent: String,
        packageName: String
    ): Boolean {
        // 检查应用包名是否匹配
        if (condition.detectionMode == com.weinuo.quickcommands22.model.AppDetectionMode.SELECTED_APPS) {
            if (condition.targetPackageName != packageName) {
                return false
            }
        }

        // 获取上次的屏幕内容
        val conditionKey = "${condition.id}_$packageName"
        val lastContent = lastScreenContentCache[conditionKey] ?: ""

        // 更新缓存
        lastScreenContentCache[conditionKey] = currentContent

        // 根据匹配类型检查内容是否匹配
        val currentMatched = when (condition.screenContentMatchType) {
            com.weinuo.quickcommands22.model.ScreenContentMatchType.TEXT_CONTENT -> {
                isTextMatched(currentContent, condition.screenContentText, condition.caseSensitive, condition.useRegex)
            }
            com.weinuo.quickcommands22.model.ScreenContentMatchType.VIEW_ID -> {
                isViewIdMatched(currentContent, condition.screenContentText)
            }
        }

        val lastMatched = when (condition.screenContentMatchType) {
            com.weinuo.quickcommands22.model.ScreenContentMatchType.TEXT_CONTENT -> {
                isTextMatched(lastContent, condition.screenContentText, condition.caseSensitive, condition.useRegex)
            }
            com.weinuo.quickcommands22.model.ScreenContentMatchType.VIEW_ID -> {
                isViewIdMatched(lastContent, condition.screenContentText)
            }
        }

        // 根据触发模式判断
        return when (condition.screenContentTriggerMode) {
            com.weinuo.quickcommands22.model.ScreenContentTriggerMode.APPEAR -> {
                // 出现时触发：之前没有，现在有
                !lastMatched && currentMatched
            }
            com.weinuo.quickcommands22.model.ScreenContentTriggerMode.DISAPPEAR -> {
                // 消失时触发：之前有，现在没有
                lastMatched && !currentMatched
            }
        }
    }

    /**
     * 检查文本是否匹配
     */
    private fun isTextMatched(text: String, targetText: String, caseSensitive: Boolean, useRegex: Boolean): Boolean {
        if (targetText.isEmpty()) return false

        return if (useRegex) {
            try {
                val pattern = if (caseSensitive) {
                    targetText.toRegex()
                } else {
                    targetText.toRegex(RegexOption.IGNORE_CASE)
                }
                pattern.containsMatchIn(text)
            } catch (e: Exception) {
                Log.w(TAG, "正则表达式匹配失败: $targetText", e)
                false
            }
        } else {
            if (caseSensitive) {
                text.contains(targetText)
            } else {
                text.contains(targetText, ignoreCase = true)
            }
        }
    }

    /**
     * 检查视图ID是否匹配
     */
    private fun isViewIdMatched(content: String, targetViewId: String): Boolean {
        if (targetViewId.isEmpty()) return false

        // 在内容中查找ID标记，格式为 [ID:viewId]
        val idPattern = "\\[ID:([^\\]]+)\\]".toRegex()
        val matches = idPattern.findAll(content)

        return matches.any { match ->
            val viewId = match.groupValues[1]
            viewId == targetViewId || viewId.endsWith(":$targetViewId")
        }
    }

    /**
     * 获取指定位置的界面元素颜色信息
     *
     * 通过无障碍服务分析指定位置的界面元素，获取其颜色特征
     * 这种方法基于界面元素的类型、文本内容等信息来推断颜色
     */
    fun getPixelColorAtPosition(x: Int, y: Int): PixelColorInfo {
        return try {
            val rootNode = rootInActiveWindow
            if (rootNode != null) {
                val colorInfo = findColorAtPosition(rootNode, x, y)
                rootNode.recycle()
                colorInfo ?: PixelColorInfo(x, y, Color.parseColor("#F5F5F5")) // 默认浅灰色
            } else {
                Log.w(TAG, "无法获取根节点，返回默认颜色信息")
                PixelColorInfo(x, y, Color.parseColor("#F5F5F5")) // 默认浅灰色
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取界面元素颜色失败: ($x, $y)", e)
            PixelColorInfo(x, y, Color.parseColor("#F5F5F5")) // 默认浅灰色
        }
    }

    /**
     * 在节点树中查找指定位置的界面元素颜色
     */
    private fun findColorAtPosition(node: AccessibilityNodeInfo, x: Int, y: Int): PixelColorInfo? {
        try {
            // 获取节点的屏幕位置
            val rect = Rect()
            node.getBoundsInScreen(rect)

            // 检查点是否在当前节点范围内
            if (rect.contains(x, y)) {
                // 递归检查子节点，优先获取最具体的元素
                for (i in 0 until node.childCount) {
                    val child = node.getChild(i)
                    if (child != null) {
                        val childColor = findColorAtPosition(child, x, y)
                        child.recycle()
                        if (childColor != null) {
                            return childColor
                        }
                    }
                }

                // 如果没有子节点或子节点没有匹配，分析当前节点
                val estimatedColor = analyzeNodeColor(node, x, y)
                return PixelColorInfo(x, y, estimatedColor)
            }

            // 如果点不在当前节点范围内，检查子节点
            for (i in 0 until node.childCount) {
                val child = node.getChild(i)
                if (child != null) {
                    val childColor = findColorAtPosition(child, x, y)
                    child.recycle()
                    if (childColor != null) {
                        return childColor
                    }
                }
            }

            return null
        } catch (e: Exception) {
            Log.e(TAG, "查找界面元素颜色时发生错误: ($x, $y)", e)
            return null
        }
    }

    /**
     * 分析节点的颜色特征
     *
     * 基于界面元素的类型、文本内容、状态等信息来推断其颜色特征
     */
    private fun analyzeNodeColor(node: AccessibilityNodeInfo, x: Int, y: Int): Int {
        return try {
            // 获取节点的基本信息
            val className = node.className?.toString() ?: ""
            val text = node.text?.toString() ?: ""
            val contentDescription = node.contentDescription?.toString() ?: ""
            val isClickable = node.isClickable
            val isSelected = node.isSelected
            val isEnabled = node.isEnabled
            val isFocused = node.isFocused

            // 记录分析信息用于调试
            Log.d(TAG, "分析位置($x, $y)的界面元素: 类型=$className, 文本=$text, 描述=$contentDescription, 可点击=$isClickable, 已选中=$isSelected, 已启用=$isEnabled, 已聚焦=$isFocused")

            // 基于界面元素特征分析颜色
            when {
                // 优先检查文本或描述中的颜色关键词
                text.contains("红色", ignoreCase = true) || contentDescription.contains("red", ignoreCase = true) -> Color.RED
                text.contains("绿色", ignoreCase = true) || contentDescription.contains("green", ignoreCase = true) -> Color.GREEN
                text.contains("蓝色", ignoreCase = true) || contentDescription.contains("blue", ignoreCase = true) -> Color.BLUE
                text.contains("黄色", ignoreCase = true) || contentDescription.contains("yellow", ignoreCase = true) -> Color.YELLOW
                text.contains("紫色", ignoreCase = true) || contentDescription.contains("purple", ignoreCase = true) -> Color.parseColor("#9C27B0")
                text.contains("橙色", ignoreCase = true) || contentDescription.contains("orange", ignoreCase = true) -> Color.parseColor("#FF9800")
                text.contains("白色", ignoreCase = true) || contentDescription.contains("white", ignoreCase = true) -> Color.WHITE
                text.contains("黑色", ignoreCase = true) || contentDescription.contains("black", ignoreCase = true) -> Color.BLACK
                text.contains("灰色", ignoreCase = true) || contentDescription.contains("gray", ignoreCase = true) -> Color.GRAY

                // 基于界面元素状态分析
                isFocused -> Color.parseColor("#2196F3") // 聚焦状态通常是蓝色
                isSelected -> Color.parseColor("#4CAF50") // 选中状态通常是绿色
                !isEnabled -> Color.parseColor("#BDBDBD") // 禁用状态通常是灰色

                // 基于界面元素类型分析
                className.contains("Button", ignoreCase = true) -> {
                    if (isClickable) Color.parseColor("#2196F3") else Color.parseColor("#E0E0E0")
                }
                className.contains("TextView", ignoreCase = true) || className.contains("Text", ignoreCase = true) -> {
                    Color.parseColor("#212121") // 文本通常是深色
                }
                className.contains("EditText", ignoreCase = true) || className.contains("Edit", ignoreCase = true) -> {
                    Color.WHITE // 编辑框通常是白色背景
                }
                className.contains("ImageView", ignoreCase = true) || className.contains("Image", ignoreCase = true) -> {
                    Color.parseColor("#757575") // 图像占位符通常是中性灰色
                }
                className.contains("Switch", ignoreCase = true) -> {
                    if (isSelected) Color.parseColor("#4CAF50") else Color.parseColor("#BDBDBD")
                }
                className.contains("CheckBox", ignoreCase = true) -> {
                    if (isSelected) Color.parseColor("#2196F3") else Color.parseColor("#757575")
                }
                className.contains("RadioButton", ignoreCase = true) -> {
                    if (isSelected) Color.parseColor("#2196F3") else Color.parseColor("#757575")
                }
                className.contains("ProgressBar", ignoreCase = true) -> {
                    Color.parseColor("#2196F3") // 进度条通常是蓝色
                }
                className.contains("SeekBar", ignoreCase = true) -> {
                    Color.parseColor("#2196F3") // 滑动条通常是蓝色
                }

                // 基于可点击性分析
                isClickable -> Color.parseColor("#2196F3") // 可点击元素通常有蓝色调

                // 默认颜色：根据位置特征
                else -> {
                    // 可以根据屏幕位置推断一些常见的颜色模式
                    val displayMetrics = node.extras?.let {
                        try {
                            // 尝试获取屏幕信息，但这里可能无法直接获取
                            null
                        } catch (e: Exception) {
                            null
                        }
                    }
                    Color.parseColor("#F5F5F5") // 默认浅灰色
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "分析界面元素颜色失败", e)
            Color.parseColor("#F5F5F5") // 默认浅灰色
        }
    }

    /**
     * 将像素颜色信息保存到文件
     */
    fun savePixelColorToFile(colorInfo: PixelColorInfo, filePath: String): Boolean {
        return try {
            val file = File(filePath)
            file.parentFile?.mkdirs()

            FileWriter(file).use { writer ->
                writer.write(colorInfo.getFormattedInfo())
            }

            Log.d(TAG, "像素颜色信息已保存到: $filePath")
            true
        } catch (e: IOException) {
            Log.e(TAG, "保存像素颜色信息失败: $filePath", e)
            false
        }
    }

    /**
     * 将视图ID保存到文件
     */
    fun saveViewIdToFile(viewId: String, filePath: String): Boolean {
        return try {
            val file = File(filePath)
            file.parentFile?.mkdirs()

            FileWriter(file).use { writer ->
                writer.write(viewId)
            }

            Log.d(TAG, "视图ID已保存到: $filePath, ID: $viewId")
            true
        } catch (e: IOException) {
            Log.e(TAG, "保存视图ID失败: $filePath", e)
            false
        }
    }

    /**
     * 触发条件
     */
    private fun triggerCondition(condition: com.weinuo.quickcommands22.model.AppStateCondition, eventData: Map<String, Any>) {
        try {
            Log.d(TAG, "Interface interaction condition triggered: ${condition.getDescription()}")
            onConditionTriggered?.invoke(condition, eventData)
        } catch (e: Exception) {
            Log.e(TAG, "Error triggering condition: ${condition.getDescription()}", e)
        }
    }
}
