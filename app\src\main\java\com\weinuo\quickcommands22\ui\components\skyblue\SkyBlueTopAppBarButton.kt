package com.weinuo.quickcommands22.ui.components.skyblue

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.weinuo.quickcommands22.data.SettingsRepository
import com.weinuo.quickcommands22.R

/**
 * 天空蓝主题专用顶部应用栏按钮组件
 *
 * 特点：
 * - 支持半透明圆形背景（可在设置中控制显示与隐藏）
 * - 主题感知的颜色配置
 * - 支持图标和文本显示
 * - 符合天空蓝主题的整合设计风格
 */
@Composable
fun SkyBlueTopAppBarButton(
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    icon: ImageVector? = null,
    text: String? = null,
    enabled: Boolean = true,
    showCircleBackground: Boolean? = null, // null表示使用全局设置
    iconSize: androidx.compose.ui.unit.Dp = 24.dp // 图标尺寸
) {
    val context = LocalContext.current
    val settingsRepository = remember { SettingsRepository(context) }
    val globalSettings by settingsRepository.globalSettings.collectAsState()
    
    // 确定是否显示圆形背景
    val shouldShowCircleBackground = showCircleBackground ?: globalSettings.topAppBarButtonCircleBackgroundEnabled

    // 获取动态大小设置
    val circleBackgroundSize = globalSettings.topAppBarButtonCircleBackgroundSize.dp

    // 颜色配置
    val contentColor = if (enabled) {
        MaterialTheme.colorScheme.onSurface
    } else {
        MaterialTheme.colorScheme.onSurface.copy(alpha = 0.38f)
    }

    val circleBackgroundColor = if (shouldShowCircleBackground) {
        if (enabled) {
            Color.Gray.copy(alpha = 0.15f) // 半透明灰色背景，更淡的效果
        } else {
            Color.Gray.copy(alpha = 0.05f) // 按钮不可用时使用更高的透明度
        }
    } else {
        Color.Transparent
    }

    // 获取缩进设置
    val horizontalMargin = globalSettings.topAppBarButtonCircleBackgroundHorizontalMargin.dp
    val rightMargin = globalSettings.topAppBarButtonCircleBackgroundRightMargin.dp

    Box(
        modifier = modifier
            .size(circleBackgroundSize)
            .clip(CircleShape)
            .background(circleBackgroundColor)
            .clickable(enabled = enabled) { onClick() },
        contentAlignment = Alignment.Center
    ) {
        when {
            icon != null -> {
                Icon(
                    imageVector = icon,
                    contentDescription = text,
                    tint = contentColor,
                    modifier = Modifier.size(iconSize)
                )
            }
            text != null -> {
                Text(
                    text = text,
                    color = contentColor,
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Medium
                )
            }
        }
    }
}

/**
 * 天空蓝主题专用返回按钮
 * 使用自定义的返回箭头图标
 * 由于返回箭头图标的宽高比是1:2，使用更大的尺寸以确保与其他图标和谐
 */
@Composable
fun SkyBlueBackButton(
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true
) {
    val context = LocalContext.current
    val settingsRepository = remember { SettingsRepository(context) }
    val globalSettings by settingsRepository.globalSettings.collectAsState()

    // 获取左右边缘缩进设置和右边缘距离设置
    val horizontalMargin = globalSettings.topAppBarButtonCircleBackgroundHorizontalMargin.dp
    val rightMargin = globalSettings.topAppBarButtonCircleBackgroundRightMargin.dp

    SkyBlueTopAppBarButton(
        onClick = onClick,
        modifier = modifier
            .padding(start = (horizontalMargin - 16.dp).coerceAtLeast(0.dp)) // 左边缘缩进：减去TopAppBar默认的16dp padding
            .padding(end = rightMargin), // 右边缘距离：控制返回按钮右边缘到标题左边缘的距离
        icon = ImageVector.vectorResource(R.drawable.ic_sky_blue_back_arrow),
        enabled = enabled,
        iconSize = 28.dp // 使用更大的尺寸以补偿12x24的宽高比
    )
}

/**
 * 天空蓝主题专用保存按钮
 * 使用状态感知的自定义勾图标
 */
@Composable
fun SkyBlueSaveButton(
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true
) {
    val context = LocalContext.current
    val settingsRepository = remember { SettingsRepository(context) }
    val globalSettings by settingsRepository.globalSettings.collectAsState()

    // 获取左右边缘缩进设置
    val horizontalMargin = globalSettings.topAppBarButtonCircleBackgroundHorizontalMargin.dp

    val iconRes = if (enabled) {
        R.drawable.ic_sky_blue_check_enabled
    } else {
        R.drawable.ic_sky_blue_check_disabled
    }

    SkyBlueTopAppBarButton(
        onClick = onClick,
        modifier = modifier.padding(end = (horizontalMargin - 16.dp).coerceAtLeast(0.dp)), // 减去TopAppBar默认的16dp padding
        icon = ImageVector.vectorResource(iconRes),
        enabled = enabled,
        iconSize = 24.dp // 标准尺寸，适合24x24图标
    )
}
