package com.weinuo.quickcommands22.ui.utils

import androidx.compose.runtime.saveable.Saver
import androidx.compose.runtime.snapshots.SnapshotStateList
import androidx.compose.runtime.toMutableStateList
import com.weinuo.quickcommands22.model.SharedTriggerCondition
import com.weinuo.quickcommands22.model.SharedTask
import com.weinuo.quickcommands22.storage.UIStateStorageManager

/**
 * 原生状态保存器
 *
 * 提供基于原生数据类型存储的Compose状态保存功能，替代JSON序列化。
 * 使用UIStateStorageManager进行状态的持久化存储和恢复。
 *
 * 核心功能：
 * - 条件列表状态保存器
 * - 任务列表状态保存器
 * - 简单数据类型状态保存器
 * - 自动状态键管理
 */
object NativeStateSaver {
    
    /**
     * 创建条件列表状态保存器
     *
     * @param uiStateManager UI状态存储管理器
     * @param stateKey 状态键名
     * @return 条件列表的Saver
     */
    fun createConditionListSaver(
        uiStateManager: UIStateStorageManager,
        stateKey: String
    ): Saver<SnapshotStateList<SharedTriggerCondition>, Any> {
        return Saver(
            save = { list ->
                // 保存条件列表到持久化存储
                val success = uiStateManager.saveConditionListState(stateKey, list.toList())
                if (success) {
                    // 返回一个标记，表示数据已保存到持久化存储
                    "saved_to_storage"
                } else {
                    // 保存失败，返回null
                    null
                }
            },
            restore = { savedValue ->
                if (savedValue == "saved_to_storage") {
                    // 从持久化存储恢复条件列表
                    val conditions = uiStateManager.loadConditionListState(stateKey)
                    conditions.toMutableStateList()
                } else {
                    // 没有保存的数据，返回空列表
                    mutableListOf<SharedTriggerCondition>().toMutableStateList()
                }
            }
        )
    }
    
    /**
     * 创建任务列表状态保存器
     *
     * @param uiStateManager UI状态存储管理器
     * @param stateKey 状态键名
     * @return 任务列表的Saver
     */
    fun createTaskListSaver(
        uiStateManager: UIStateStorageManager,
        stateKey: String
    ): Saver<SnapshotStateList<SharedTask>, Any> {
        return Saver(
            save = { list ->
                // 保存任务列表到持久化存储
                val success = uiStateManager.saveTaskListState(stateKey, list.toList())
                if (success) {
                    // 返回一个标记，表示数据已保存到持久化存储
                    "saved_to_storage"
                } else {
                    // 保存失败，返回null
                    null
                }
            },
            restore = { savedValue ->
                if (savedValue == "saved_to_storage") {
                    // 从持久化存储恢复任务列表
                    val tasks = uiStateManager.loadTaskListState(stateKey)
                    tasks.toMutableStateList()
                } else {
                    // 没有保存的数据，返回空列表
                    mutableListOf<SharedTask>().toMutableStateList()
                }
            }
        )
    }
    
    /**
     * 创建字符串状态保存器
     *
     * @param uiStateManager UI状态存储管理器
     * @param stateKey 状态键名
     * @param key 字段键名
     * @param defaultValue 默认值
     * @return 字符串的Saver
     */
    fun createStringSaver(
        uiStateManager: UIStateStorageManager,
        stateKey: String,
        key: String,
        defaultValue: String = ""
    ): Saver<String, Any> {
        return Saver(
            save = { value ->
                val success = uiStateManager.saveSimpleState(stateKey, key, value)
                if (success) value else null
            },
            restore = { savedValue ->
                if (savedValue is String) {
                    savedValue
                } else {
                    uiStateManager.loadStringState(stateKey, key, defaultValue)
                }
            }
        )
    }
    
    /**
     * 创建整数状态保存器
     *
     * @param uiStateManager UI状态存储管理器
     * @param stateKey 状态键名
     * @param key 字段键名
     * @param defaultValue 默认值
     * @return 整数的Saver
     */
    fun createIntSaver(
        uiStateManager: UIStateStorageManager,
        stateKey: String,
        key: String,
        defaultValue: Int = 0
    ): Saver<Int, Any> {
        return Saver(
            save = { value ->
                val success = uiStateManager.saveSimpleState(stateKey, key, value)
                if (success) value else null
            },
            restore = { savedValue ->
                if (savedValue is Int) {
                    savedValue
                } else {
                    uiStateManager.loadIntState(stateKey, key, defaultValue)
                }
            }
        )
    }
    
    /**
     * 创建布尔状态保存器
     *
     * @param uiStateManager UI状态存储管理器
     * @param stateKey 状态键名
     * @param key 字段键名
     * @param defaultValue 默认值
     * @return 布尔的Saver
     */
    fun createBooleanSaver(
        uiStateManager: UIStateStorageManager,
        stateKey: String,
        key: String,
        defaultValue: Boolean = false
    ): Saver<Boolean, Any> {
        return Saver(
            save = { value ->
                val success = uiStateManager.saveSimpleState(stateKey, key, value)
                if (success) value else null
            },
            restore = { savedValue ->
                if (savedValue is Boolean) {
                    savedValue
                } else {
                    uiStateManager.loadBooleanState(stateKey, key, defaultValue)
                }
            }
        )
    }
    
    /**
     * 创建浮点数状态保存器
     *
     * @param uiStateManager UI状态存储管理器
     * @param stateKey 状态键名
     * @param key 字段键名
     * @param defaultValue 默认值
     * @return 浮点数的Saver
     */
    fun createFloatSaver(
        uiStateManager: UIStateStorageManager,
        stateKey: String,
        key: String,
        defaultValue: Float = 0f
    ): Saver<Float, Any> {
        return Saver(
            save = { value ->
                val success = uiStateManager.saveSimpleState(stateKey, key, value)
                if (success) value else null
            },
            restore = { savedValue ->
                if (savedValue is Float) {
                    savedValue
                } else {
                    uiStateManager.loadFloatState(stateKey, key, defaultValue)
                }
            }
        )
    }
    
    /**
     * 创建长整数状态保存器
     *
     * @param uiStateManager UI状态存储管理器
     * @param stateKey 状态键名
     * @param key 字段键名
     * @param defaultValue 默认值
     * @return 长整数的Saver
     */
    fun createLongSaver(
        uiStateManager: UIStateStorageManager,
        stateKey: String,
        key: String,
        defaultValue: Long = 0L
    ): Saver<Long, Any> {
        return Saver(
            save = { value ->
                val success = uiStateManager.saveSimpleState(stateKey, key, value)
                if (success) value else null
            },
            restore = { savedValue ->
                if (savedValue is Long) {
                    savedValue
                } else {
                    uiStateManager.loadLongState(stateKey, key, defaultValue)
                }
            }
        )
    }
    
    /**
     * 生成状态键名
     *
     * @param baseKey 基础键名
     * @param suffix 后缀
     * @return 完整的状态键名
     */
    fun generateStateKey(baseKey: String, suffix: String): String {
        return "${baseKey}_${suffix}"
    }
    
    /**
     * 生成带重置键的状态键名
     *
     * @param baseKey 基础键名
     * @param suffix 后缀
     * @param resetKey 重置键
     * @return 完整的状态键名
     */
    fun generateStateKeyWithReset(baseKey: String, suffix: String, resetKey: Int): String {
        return "${baseKey}_${suffix}_reset_${resetKey}"
    }
}
