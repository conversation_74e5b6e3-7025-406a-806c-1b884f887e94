package com.weinuo.quickcommands22.ui.screens.skyblue

import androidx.compose.runtime.Composable
import androidx.navigation.NavController
import com.weinuo.quickcommands22.ui.screens.ScreenFactory
import com.weinuo.quickcommands22.ui.screens.ScreenType
import com.weinuo.quickcommands22.data.SettingsRepository
import com.weinuo.quickcommands22.data.QuickCommandRepository
import com.weinuo.quickcommands22.shortcut.ShortcutManager
import com.weinuo.quickcommands22.utils.ExperimentalFeatureDetector

/**
 * 天空蓝主题界面工厂
 *
 * 创建整合设计风格的界面实现
 * 特点：统一的视觉体验，支持模糊效果，可以添加天空蓝主题特有功能
 */
class SkyBlueScreenFactory : ScreenFactory {

    /**
     * 创建天空蓝主题的快捷指令界面
     * 使用天空蓝专用版本
     */
    @Composable
    override fun createQuickCommandsScreen(
        navController: NavController,
        quickCommandRepository: QuickCommandRepository,
        shortcutManager: ShortcutManager
    ) {
        SkyBlueQuickCommandsScreen(navController, quickCommandRepository, shortcutManager)
    }

    /**
     * 创建天空蓝主题的全局设置界面
     * 使用天空蓝专用版本，包含模糊效果设置等特有功能
     */
    @Composable
    override fun createGlobalSettingsScreen(
        settingsRepository: SettingsRepository,
        experimentalFeatureDetector: ExperimentalFeatureDetector?
    ) {
        SkyBlueGlobalSettingsScreen(settingsRepository, experimentalFeatureDetector)
    }

    /**
     * 创建天空蓝主题的命令模板界面
     * 使用天空蓝专用版本
     */
    @Composable
    override fun createCommandTemplatesScreen(
        navController: NavController,
        quickCommandRepository: QuickCommandRepository
    ) {
        SkyBlueCommandTemplatesScreen(navController, quickCommandRepository)
    }

    /**
     * 创建天空蓝主题的智能提醒界面
     * 使用天空蓝专用版本
     */
    @Composable
    override fun createSmartRemindersScreen(navController: NavController) {
        SkyBlueSmartRemindersScreen(navController)
    }

    /**
     * 检查是否支持特定界面类型
     * 天空蓝主题支持所有标准界面，并且支持一些特有功能
     */
    override fun supportsScreen(screenType: ScreenType): Boolean {
        return when (screenType) {
            ScreenType.QUICK_COMMANDS -> true
            ScreenType.GLOBAL_SETTINGS -> true
            ScreenType.COMMAND_TEMPLATES -> true
            ScreenType.SMART_REMINDERS -> true
            // 其他界面类型暂时使用默认支持
            else -> true
        }
    }

    /**
     * 获取天空蓝界面工厂的版本
     */
    override fun getVersion(): String {
        return "1.0.0"
    }
}
