package com.weinuo.quickcommands22.repository

import android.content.Context
import android.util.Log
import com.weinuo.quickcommands22.model.SharedTriggerCondition
import com.weinuo.quickcommands22.model.AppStateCondition
import com.weinuo.quickcommands22.model.TimeBasedCondition
import com.weinuo.quickcommands22.model.CommunicationStateCondition
import com.weinuo.quickcommands22.model.ConnectionStateCondition
import com.weinuo.quickcommands22.model.DeviceEventCondition
import com.weinuo.quickcommands22.model.SensorStateCondition
import com.weinuo.quickcommands22.model.ManualTriggerCondition
import com.weinuo.quickcommands22.storage.NativeTypeStorageManager
import com.weinuo.quickcommands22.storage.AppListStorageEngine
import com.weinuo.quickcommands22.storage.adapters.ConditionAdapterManager
import com.weinuo.quickcommands22.storage.StorageDomain
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * 条件仓库类
 *
 * 提供统一的条件CRUD接口，支持所有类型的触发条件管理。
 * 使用原生数据类型存储架构，确保数据持久性和可靠性。
 *
 * 核心功能：
 * - 统一的条件CRUD操作
 * - 按类型查询条件
 * - 条件验证功能
 * - 响应式数据流支持
 * - 批量操作优化
 * - 完整的错误处理和日志记录
 *
 * 支持的条件类型：
 * - AppStateCondition: 应用状态条件
 * - TimeBasedCondition: 时间条件
 * - CommunicationStateCondition: 通信状态条件
 * - ConnectionStateCondition: 连接状态条件
 * - DeviceEventCondition: 设备事件条件
 * - SensorStateCondition: 传感器状态条件
 * - ManualTriggerCondition: 手动触发条件
 *
 * @param context Android上下文
 */
class ConditionRepository(private val context: Context) {

    companion object {
        private const val TAG = "ConditionRepository"

        // 条件类型常量
        const val TYPE_APP_STATE = "app_state"
        const val TYPE_TIME_BASED = "time_based"
        const val TYPE_COMMUNICATION_STATE = "communication_state"
        const val TYPE_CONNECTION_STATE = "connection_state"
        const val TYPE_DEVICE_EVENT = "device_event"
        const val TYPE_SENSOR_STATE = "sensor_state"
        const val TYPE_MANUAL_TRIGGER = "manual_trigger"
    }

    // 核心存储组件
    private val storageManager = NativeTypeStorageManager(context)
    private val appListEngine = AppListStorageEngine(storageManager)
    private val conditionAdapterManager = ConditionAdapterManager(storageManager, appListEngine)

    // 响应式数据流
    private val _allConditions = MutableStateFlow<List<SharedTriggerCondition>>(emptyList())
    val allConditions: StateFlow<List<SharedTriggerCondition>> = _allConditions.asStateFlow()

    // 按类型分组的数据流
    private val _appStateConditions = MutableStateFlow<List<AppStateCondition>>(emptyList())
    val appStateConditions: StateFlow<List<AppStateCondition>> = _appStateConditions.asStateFlow()

    private val _timeBasedConditions = MutableStateFlow<List<TimeBasedCondition>>(emptyList())
    val timeBasedConditions: StateFlow<List<TimeBasedCondition>> = _timeBasedConditions.asStateFlow()

    // 初始化时加载数据
    init {
        loadConditionsToFlow()
    }

    /**
     * 保存条件
     *
     * @param condition 要保存的条件
     * @return 操作是否成功
     */
    suspend fun saveCondition(condition: SharedTriggerCondition): Boolean = withContext(Dispatchers.IO) {
        try {
            val success = conditionAdapterManager.saveCondition(condition)
            if (success) {
                Log.d(TAG, "Successfully saved condition: ${condition.id} (type: ${getConditionType(condition)})")
                // 更新数据流
                updateConditionInFlow(condition)
            } else {
                Log.e(TAG, "Failed to save condition: ${condition.id}")
            }
            success
        } catch (e: Exception) {
            Log.e(TAG, "Error saving condition: ${condition.id}", e)
            false
        }
    }

    /**
     * 加载条件
     *
     * @param conditionId 条件ID
     * @return 条件对象，如果不存在则返回null
     */
    suspend fun loadCondition(conditionId: String): SharedTriggerCondition? = withContext(Dispatchers.IO) {
        try {
            val condition = conditionAdapterManager.loadConditionByStoredType(conditionId)
            if (condition != null) {
                Log.d(TAG, "Successfully loaded condition: ${condition.id} (type: ${getConditionType(condition)})")
            } else {
                Log.d(TAG, "Condition not found: $conditionId")
            }
            condition
        } catch (e: Exception) {
            Log.e(TAG, "Error loading condition: $conditionId", e)
            null
        }
    }

    /**
     * 删除条件
     *
     * @param conditionId 条件ID
     * @return 操作是否成功
     */
    suspend fun deleteCondition(conditionId: String): Boolean = withContext(Dispatchers.IO) {
        try {
            // 首先获取条件类型
            val conditionTypeKey = "condition_${conditionId}_type"
            val conditionType = storageManager.loadString(StorageDomain.CONDITIONS, conditionTypeKey)

            val success = if (conditionType.isNotEmpty()) {
                conditionAdapterManager.deleteCondition(conditionId, conditionType)
            } else {
                Log.w(TAG, "Condition type not found, attempting prefix deletion: $conditionId")
                val prefix = "condition_${conditionId}_"
                storageManager.deleteByPrefix(StorageDomain.CONDITIONS, prefix)
            }

            if (success) {
                Log.d(TAG, "Successfully deleted condition: $conditionId")
                // 更新数据流
                removeConditionFromFlow(conditionId)
            } else {
                Log.e(TAG, "Failed to delete condition: $conditionId")
            }
            success
        } catch (e: Exception) {
            Log.e(TAG, "Error deleting condition: $conditionId", e)
            false
        }
    }

    /**
     * 检查条件是否存在
     *
     * @param conditionId 条件ID
     * @return 条件是否存在
     */
    suspend fun conditionExists(conditionId: String): Boolean = withContext(Dispatchers.IO) {
        try {
            val conditionTypeKey = "condition_${conditionId}_type"
            val exists = storageManager.containsKey(StorageDomain.CONDITIONS, conditionTypeKey)
            Log.d(TAG, "Condition exists check: $conditionId -> $exists")
            exists
        } catch (e: Exception) {
            Log.e(TAG, "Error checking condition existence: $conditionId", e)
            false
        }
    }

    /**
     * 加载所有条件
     *
     * @return 条件列表
     */
    suspend fun loadAllConditions(): List<SharedTriggerCondition> = withContext(Dispatchers.IO) {
        try {
            val conditions = mutableListOf<SharedTriggerCondition>()
            val conditionIds = getAllConditionIds()

            conditionIds.forEach { conditionId ->
                try {
                    val condition = conditionAdapterManager.loadConditionByStoredType(conditionId)
                    if (condition != null) {
                        conditions.add(condition)
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Error loading condition: $conditionId", e)
                }
            }

            Log.d(TAG, "Successfully loaded ${conditions.size}/${conditionIds.size} conditions")
            _allConditions.value = conditions
            updateTypeSpecificFlows(conditions)
            conditions
        } catch (e: Exception) {
            Log.e(TAG, "Error loading all conditions", e)
            emptyList()
        }
    }

    /**
     * 按类型查询条件
     *
     * @param conditionType 条件类型
     * @return 指定类型的条件列表
     */
    suspend fun getConditionsByType(conditionType: String): List<SharedTriggerCondition> = withContext(Dispatchers.IO) {
        try {
            val allConditions = loadAllConditions()
            val filteredConditions = allConditions.filter { getConditionType(it) == conditionType }
            Log.d(TAG, "Found ${filteredConditions.size} conditions of type: $conditionType")
            filteredConditions
        } catch (e: Exception) {
            Log.e(TAG, "Error getting conditions by type: $conditionType", e)
            emptyList()
        }
    }

    /**
     * 批量保存条件
     *
     * @param conditions 条件列表
     * @return 成功保存的条件数量
     */
    suspend fun saveConditions(conditions: List<SharedTriggerCondition>): Int = withContext(Dispatchers.IO) {
        var successCount = 0
        conditions.forEach { condition ->
            if (saveCondition(condition)) {
                successCount++
            }
        }
        Log.d(TAG, "Batch save conditions completed: $successCount/${conditions.size}")
        return@withContext successCount
    }

    /**
     * 批量删除条件
     *
     * @param conditionIds 条件ID列表
     * @return 成功删除的条件数量
     */
    suspend fun deleteConditions(conditionIds: List<String>): Int = withContext(Dispatchers.IO) {
        var successCount = 0
        conditionIds.forEach { conditionId ->
            if (deleteCondition(conditionId)) {
                successCount++
            }
        }
        Log.d(TAG, "Batch delete conditions completed: $successCount/${conditionIds.size}")
        return@withContext successCount
    }

    /**
     * 获取条件数量
     *
     * @return 条件总数
     */
    suspend fun getConditionCount(): Int = withContext(Dispatchers.IO) {
        try {
            val count = getAllConditionIds().size
            Log.d(TAG, "Total condition count: $count")
            count
        } catch (e: Exception) {
            Log.e(TAG, "Error getting condition count", e)
            0
        }
    }

    /**
     * 按类型获取条件数量
     *
     * @param conditionType 条件类型
     * @return 指定类型的条件数量
     */
    suspend fun getConditionCountByType(conditionType: String): Int = withContext(Dispatchers.IO) {
        try {
            val conditions = getConditionsByType(conditionType)
            val count = conditions.size
            Log.d(TAG, "Condition count for type $conditionType: $count")
            count
        } catch (e: Exception) {
            Log.e(TAG, "Error getting condition count by type: $conditionType", e)
            0
        }
    }

    /**
     * 验证条件
     *
     * @param condition 要验证的条件
     * @return 验证结果
     */
    fun validateCondition(condition: SharedTriggerCondition): ConditionValidationResult {
        val issues = mutableListOf<String>()

        try {
            // 基本验证
            if (condition.id.isBlank()) {
                issues.add("条件ID不能为空")
            }

            // 按类型进行特定验证
            when (condition) {
                is AppStateCondition -> validateAppStateCondition(condition, issues)
                is TimeBasedCondition -> validateTimeBasedCondition(condition, issues)
                is CommunicationStateCondition -> validateCommunicationStateCondition(condition, issues)
                is ConnectionStateCondition -> validateConnectionStateCondition(condition, issues)
                is DeviceEventCondition -> validateDeviceEventCondition(condition, issues)
                is SensorStateCondition -> validateSensorStateCondition(condition, issues)
                is ManualTriggerCondition -> validateManualTriggerCondition(condition, issues)
                else -> issues.add("未知的条件类型: ${condition.javaClass.simpleName}")
            }

        } catch (e: Exception) {
            issues.add("验证过程中发生异常: ${e.message}")
            Log.e(TAG, "Error validating condition: ${condition.id}", e)
        }

        return ConditionValidationResult(issues.isEmpty(), issues)
    }

    /**
     * 清空所有条件数据
     * 注意：这是一个危险操作，会删除所有条件数据
     *
     * @return 操作是否成功
     */
    suspend fun clearAllConditions(): Boolean = withContext(Dispatchers.IO) {
        try {
            val prefs = storageManager.getPreferences(StorageDomain.CONDITIONS)
            val editor = prefs.edit()
            editor.clear()
            val success = editor.commit()

            if (success) {
                Log.d(TAG, "Successfully cleared all conditions")
                _allConditions.value = emptyList()
                updateTypeSpecificFlows(emptyList())
            } else {
                Log.e(TAG, "Failed to clear all conditions")
            }
            success
        } catch (e: Exception) {
            Log.e(TAG, "Error clearing all conditions", e)
            false
        }
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 加载条件到数据流
     */
    private fun loadConditionsToFlow() {
        try {
            kotlinx.coroutines.GlobalScope.launch {
                val conditions = loadAllConditions()
                _allConditions.value = conditions
                updateTypeSpecificFlows(conditions)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error loading conditions to flow", e)
        }
    }

    /**
     * 更新数据流中的条件
     */
    private fun updateConditionInFlow(condition: SharedTriggerCondition) {
        try {
            val currentConditions = _allConditions.value.toMutableList()
            val existingIndex = currentConditions.indexOfFirst { it.id == condition.id }

            if (existingIndex != -1) {
                currentConditions[existingIndex] = condition
            } else {
                currentConditions.add(condition)
            }

            _allConditions.value = currentConditions
            updateTypeSpecificFlows(currentConditions)
        } catch (e: Exception) {
            Log.e(TAG, "Error updating condition in flow: ${condition.id}", e)
        }
    }

    /**
     * 从数据流中移除条件
     */
    private fun removeConditionFromFlow(conditionId: String) {
        try {
            val currentConditions = _allConditions.value.toMutableList()
            currentConditions.removeAll { it.id == conditionId }

            _allConditions.value = currentConditions
            updateTypeSpecificFlows(currentConditions)
        } catch (e: Exception) {
            Log.e(TAG, "Error removing condition from flow: $conditionId", e)
        }
    }

    /**
     * 更新按类型分组的数据流
     */
    private fun updateTypeSpecificFlows(conditions: List<SharedTriggerCondition>) {
        try {
            _appStateConditions.value = conditions.filterIsInstance<AppStateCondition>()
            _timeBasedConditions.value = conditions.filterIsInstance<TimeBasedCondition>()
        } catch (e: Exception) {
            Log.e(TAG, "Error updating type-specific flows", e)
        }
    }

    /**
     * 获取所有条件ID
     */
    private fun getAllConditionIds(): List<String> {
        return try {
            val prefs = storageManager.getPreferences(StorageDomain.CONDITIONS)
            val allKeys = prefs.all.keys

            // 提取所有条件ID（从type字段的键中提取）
            val conditionIds = mutableSetOf<String>()
            allKeys.forEach { key ->
                if (key.startsWith("condition_") && key.endsWith("_type")) {
                    val conditionId = key.removePrefix("condition_").removeSuffix("_type")
                    conditionIds.add(conditionId)
                }
            }

            conditionIds.toList()
        } catch (e: Exception) {
            Log.e(TAG, "Error getting all condition IDs", e)
            emptyList()
        }
    }

    /**
     * 获取条件类型
     */
    private fun getConditionType(condition: SharedTriggerCondition): String {
        return when (condition) {
            is AppStateCondition -> TYPE_APP_STATE
            is TimeBasedCondition -> TYPE_TIME_BASED
            is CommunicationStateCondition -> TYPE_COMMUNICATION_STATE
            is ConnectionStateCondition -> TYPE_CONNECTION_STATE
            is DeviceEventCondition -> TYPE_DEVICE_EVENT
            is SensorStateCondition -> TYPE_SENSOR_STATE
            is ManualTriggerCondition -> TYPE_MANUAL_TRIGGER
            else -> "unknown"
        }
    }

    // ==================== 条件验证方法 ====================

    /**
     * 验证应用状态条件
     */
    private fun validateAppStateCondition(condition: AppStateCondition, issues: MutableList<String>) {
        // 检查应用检测模式相关字段
        if (condition.detectionMode == com.weinuo.quickcommands22.model.AppDetectionMode.SELECTED_APPS) {
            if (condition.targetPackageName.isBlank()) {
                issues.add("特定应用模式下目标应用包名不能为空")
            }
        }

        // 检查后台时间阈值
        if (condition.backgroundTimeThresholdMinutes < 0) {
            issues.add("后台时间阈值不能为负数")
        }

        // 检查超时时间
        if (condition.timeoutSeconds < 0) {
            issues.add("超时时间不能为负数")
        }
    }

    /**
     * 验证时间条件
     */
    private fun validateTimeBasedCondition(condition: TimeBasedCondition, issues: MutableList<String>) {
        when (condition.timeConditionType) {
            com.weinuo.quickcommands22.model.TimeConditionType.STOPWATCH -> {
                if (condition.stopwatchHours == 0 && condition.stopwatchMinutes == 0 && condition.stopwatchSeconds == 0) {
                    issues.add("秒表时间不能全为0")
                }
            }
            com.weinuo.quickcommands22.model.TimeConditionType.SUN_EVENT -> {
                if (condition.latitude == null || condition.longitude == null) {
                    issues.add("日出日落条件需要设置经纬度")
                }
            }
            com.weinuo.quickcommands22.model.TimeConditionType.SCHEDULED_TIME -> {
                if (condition.year < 1970 || condition.year > 2100) {
                    issues.add("年份设置不合理")
                }
                if (condition.month < 1 || condition.month > 12) {
                    issues.add("月份设置不合理")
                }
                if (condition.day < 1 || condition.day > 31) {
                    issues.add("日期设置不合理")
                }
                if (condition.hour < 0 || condition.hour > 23) {
                    issues.add("小时设置不合理")
                }
                if (condition.minute < 0 || condition.minute > 59) {
                    issues.add("分钟设置不合理")
                }
            }
            com.weinuo.quickcommands22.model.TimeConditionType.PERIODIC_TIME -> {
                if (condition.selectedDays.isEmpty()) {
                    issues.add("周期时间条件需要选择至少一天")
                }
            }
            com.weinuo.quickcommands22.model.TimeConditionType.DELAYED_TRIGGER,
            com.weinuo.quickcommands22.model.TimeConditionType.PERIODIC_TRIGGER -> {
                if (condition.interval <= 0) {
                    issues.add("时间间隔必须大于0")
                }
            }
        }
    }

    /**
     * 验证通信状态条件
     */
    private fun validateCommunicationStateCondition(condition: CommunicationStateCondition, issues: MutableList<String>) {
        // 根据通信状态条件的具体字段进行验证
        // 这里可以添加具体的验证逻辑
    }

    /**
     * 验证连接状态条件
     */
    private fun validateConnectionStateCondition(condition: ConnectionStateCondition, issues: MutableList<String>) {
        // 根据连接状态条件的具体字段进行验证
        // 这里可以添加具体的验证逻辑
    }

    /**
     * 验证设备事件条件
     */
    private fun validateDeviceEventCondition(condition: DeviceEventCondition, issues: MutableList<String>) {
        // 根据设备事件条件的具体字段进行验证
        // 这里可以添加具体的验证逻辑
    }

    /**
     * 验证传感器状态条件
     */
    private fun validateSensorStateCondition(condition: SensorStateCondition, issues: MutableList<String>) {
        // 根据传感器状态条件的具体字段进行验证
        // 这里可以添加具体的验证逻辑
    }

    /**
     * 验证手动触发条件
     */
    private fun validateManualTriggerCondition(condition: ManualTriggerCondition, issues: MutableList<String>) {
        // 手动触发条件通常不需要特殊验证
    }
}

/**
 * 条件验证结果数据类
 */
data class ConditionValidationResult(
    val isValid: Boolean,
    val issues: List<String>
)
