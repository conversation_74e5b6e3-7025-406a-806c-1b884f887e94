package com.weinuo.quickcommands22.utils

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.location.Location
import android.location.LocationManager
import android.util.Log
import androidx.core.app.ActivityCompat
import java.util.*
import kotlin.math.*

/**
 * 日出日落计算工具类
 * 基于地理位置计算精确的日出日落时间
 * 
 * 算法基于：Sunrise Equation (https://en.wikipedia.org/wiki/Sunrise_equation)
 * 精度：约±2分钟
 */
object SunriseSunsetCalculator {
    private const val TAG = "SunriseSunsetCalculator"
    
    // 太阳角度常量
    private const val CIVIL_TWILIGHT_ANGLE = -6.0  // 民用晨昏蒙影角度
    private const val SUNRISE_SUNSET_ANGLE = -0.833  // 日出日落角度（考虑大气折射和太阳半径）
    
    /**
     * 计算指定日期和位置的日出时间
     * 
     * @param latitude 纬度（-90到90度）
     * @param longitude 经度（-180到180度）
     * @param date 指定日期，默认为当天
     * @param timeZone 时区，默认为系统时区
     * @return 日出时间的Calendar对象，如果当天无日出则返回null
     */
    fun calculateSunrise(
        latitude: Double,
        longitude: Double,
        date: Calendar = Calendar.getInstance(),
        timeZone: TimeZone = TimeZone.getDefault()
    ): Calendar? {
        return calculateSunEvent(latitude, longitude, date, timeZone, true)
    }
    
    /**
     * 计算指定日期和位置的日落时间
     * 
     * @param latitude 纬度（-90到90度）
     * @param longitude 经度（-180到180度）
     * @param date 指定日期，默认为当天
     * @param timeZone 时区，默认为系统时区
     * @return 日落时间的Calendar对象，如果当天无日落则返回null
     */
    fun calculateSunset(
        latitude: Double,
        longitude: Double,
        date: Calendar = Calendar.getInstance(),
        timeZone: TimeZone = TimeZone.getDefault()
    ): Calendar? {
        return calculateSunEvent(latitude, longitude, date, timeZone, false)
    }
    
    /**
     * 尝试获取系统当前位置
     * 需要位置权限
     * 
     * @param context Android上下文
     * @return 位置对象，如果无法获取则返回null
     */
    fun getCurrentLocation(context: Context): Location? {
        if (ActivityCompat.checkSelfPermission(
                context,
                Manifest.permission.ACCESS_FINE_LOCATION
            ) != PackageManager.PERMISSION_GRANTED &&
            ActivityCompat.checkSelfPermission(
                context,
                Manifest.permission.ACCESS_COARSE_LOCATION
            ) != PackageManager.PERMISSION_GRANTED
        ) {
            Log.w(TAG, "Location permission not granted")
            return null
        }
        
        val locationManager = context.getSystemService(Context.LOCATION_SERVICE) as LocationManager
        
        // 尝试获取GPS位置
        val gpsLocation = try {
            locationManager.getLastKnownLocation(LocationManager.GPS_PROVIDER)
        } catch (e: Exception) {
            Log.w(TAG, "Failed to get GPS location", e)
            null
        }
        
        // 如果GPS不可用，尝试网络位置
        val networkLocation = try {
            locationManager.getLastKnownLocation(LocationManager.NETWORK_PROVIDER)
        } catch (e: Exception) {
            Log.w(TAG, "Failed to get network location", e)
            null
        }
        
        // 返回更精确的位置
        return when {
            gpsLocation != null && networkLocation != null -> {
                if (gpsLocation.accuracy <= networkLocation.accuracy) gpsLocation else networkLocation
            }
            gpsLocation != null -> gpsLocation
            networkLocation != null -> networkLocation
            else -> {
                Log.w(TAG, "No location available")
                null
            }
        }
    }
    
    /**
     * 计算日出或日落时间的核心算法
     */
    private fun calculateSunEvent(
        latitude: Double,
        longitude: Double,
        date: Calendar,
        timeZone: TimeZone,
        isSunrise: Boolean
    ): Calendar? {
        // 验证输入参数
        if (latitude < -90 || latitude > 90) {
            Log.e(TAG, "Invalid latitude: $latitude")
            return null
        }
        if (longitude < -180 || longitude > 180) {
            Log.e(TAG, "Invalid longitude: $longitude")
            return null
        }
        
        // 计算一年中的第几天
        val dayOfYear = date.get(Calendar.DAY_OF_YEAR)
        val year = date.get(Calendar.YEAR)
        
        // 计算太阳赤纬角
        val solarDeclination = calculateSolarDeclination(dayOfYear)
        
        // 计算时角
        val hourAngle = calculateHourAngle(latitude, solarDeclination, SUNRISE_SUNSET_ANGLE)
        
        // 检查极昼极夜情况
        if (hourAngle.isNaN()) {
            Log.d(TAG, "Polar day/night at latitude $latitude on day $dayOfYear")
            return null
        }
        
        // 计算太阳时
        val solarTime = if (isSunrise) {
            12.0 - hourAngle / 15.0
        } else {
            12.0 + hourAngle / 15.0
        }
        
        // 计算时差修正
        val equationOfTime = calculateEquationOfTime(dayOfYear)
        
        // 转换为当地时间
        val localTime = solarTime - longitude / 15.0 + equationOfTime / 60.0
        
        // 创建结果Calendar对象
        val result = Calendar.getInstance(timeZone).apply {
            set(Calendar.YEAR, year)
            set(Calendar.MONTH, date.get(Calendar.MONTH))
            set(Calendar.DAY_OF_MONTH, date.get(Calendar.DAY_OF_MONTH))
            
            val hours = localTime.toInt()
            val minutes = ((localTime - hours) * 60).toInt()
            val seconds = (((localTime - hours) * 60 - minutes) * 60).toInt()
            
            set(Calendar.HOUR_OF_DAY, hours)
            set(Calendar.MINUTE, minutes)
            set(Calendar.SECOND, seconds)
            set(Calendar.MILLISECOND, 0)
        }
        
        return result
    }
    
    /**
     * 计算太阳赤纬角
     */
    private fun calculateSolarDeclination(dayOfYear: Int): Double {
        val angle = 2.0 * PI * (dayOfYear - 81) / 365.0
        return asin(sin(toRadians(23.45)) * sin(angle))
    }
    
    /**
     * 计算时角
     */
    private fun calculateHourAngle(latitude: Double, solarDeclination: Double, sunAngle: Double): Double {
        val latRad = toRadians(latitude)
        val sunAngleRad = toRadians(sunAngle)
        
        val cosHourAngle = (sin(sunAngleRad) - sin(latRad) * sin(solarDeclination)) / 
                          (cos(latRad) * cos(solarDeclination))
        
        return if (cosHourAngle >= -1.0 && cosHourAngle <= 1.0) {
            toDegrees(acos(cosHourAngle))
        } else {
            Double.NaN
        }
    }
    
    /**
     * 计算时差（太阳时与平均太阳时的差值）
     */
    private fun calculateEquationOfTime(dayOfYear: Int): Double {
        val b = 2.0 * PI * (dayOfYear - 81) / 365.0
        return 9.87 * sin(2 * b) - 7.53 * cos(b) - 1.5 * sin(b)
    }
    
    /**
     * 角度转弧度
     */
    private fun toRadians(degrees: Double): Double = degrees * PI / 180.0
    
    /**
     * 弧度转角度
     */
    private fun toDegrees(radians: Double): Double = radians * 180.0 / PI
}
