package com.weinuo.quickcommands22.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.weinuo.quickcommands22.R
import com.weinuo.quickcommands22.storage.SmartReminderConfigAdapter
import kotlinx.coroutines.launch
import java.util.*

/**
 * 自定义购物平台配置全屏界面
 *
 * 用于添加或编辑自定义购物平台的配置，包括：
 * - 平台名称和应用包名
 * - URL匹配规则管理
 * - 高级选项配置
 *
 * 设计特点：
 * - 全屏界面，避免对话框嵌套问题
 * - 完整的表单验证
 * - 支持新建和编辑模式
 * - Material Design 3风格
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CustomShoppingPlatformConfigScreen(
    platformData: String? = null,
    onPlatformSaved: (SmartReminderConfigAdapter.CustomShoppingPlatform) -> Unit,
    onNavigateBack: () -> Unit
) {
    val context = LocalContext.current
    val scope = rememberCoroutineScope()

    // 解析传入的平台数据
    val initialPlatform: SmartReminderConfigAdapter.CustomShoppingPlatform? = remember(platformData) {
        platformData?.let {
            // 这里应该实现JSON解析，暂时使用null
            null
        }
    }

    // 表单状态
    var name by remember { mutableStateOf(initialPlatform?.name ?: "") }
    var packageName by remember { mutableStateOf(initialPlatform?.packageName ?: "") }
    var urlPatterns by remember { mutableStateOf<List<String>>(initialPlatform?.urlPatterns ?: emptyList()) }
    var newPattern by remember { mutableStateOf("") }
    var useRegex by remember { mutableStateOf(initialPlatform?.useRegex ?: false) }
    var caseSensitive by remember { mutableStateOf(initialPlatform?.caseSensitive ?: false) }
    var enabled by remember { mutableStateOf(initialPlatform?.enabled ?: true) }

    // 表单验证状态
    var nameError by remember { mutableStateOf(false) }
    var packageNameError by remember { mutableStateOf(false) }
    var urlPatternsError by remember { mutableStateOf(false) }

    // 保存函数
    val saveConfig = {
        scope.launch {
            // 验证表单
            nameError = name.isBlank()
            packageNameError = packageName.isBlank()
            urlPatternsError = urlPatterns.isEmpty()

            if (!nameError && !packageNameError && !urlPatternsError) {
                val platform = SmartReminderConfigAdapter.CustomShoppingPlatform(
                    id = initialPlatform?.id ?: UUID.randomUUID().toString(),
                    name = name.trim(),
                    packageName = packageName.trim(),
                    urlPatterns = urlPatterns,
                    useRegex = useRegex,
                    caseSensitive = caseSensitive,
                    enabled = enabled
                )
                onPlatformSaved(platform)
                onNavigateBack()
            }
        }
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = if (initialPlatform != null) "编辑购物平台" else "添加购物平台"
                    )
                },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                            contentDescription = stringResource(R.string.back)
                        )
                    }
                },
                actions = {
                    TextButton(
                        onClick = { saveConfig() }
                    ) {
                        Text("保存")
                    }
                }
            )
        }
    ) { innerPadding ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(innerPadding)
                .padding(16.dp)
                .verticalScroll(rememberScrollState()),
            verticalArrangement = Arrangement.spacedBy(24.dp)
        ) {
            // 基本信息配置
            ConfigSection(
                title = "基本信息",
                description = "设置购物平台的名称和对应的应用包名"
            ) {
                Column(verticalArrangement = Arrangement.spacedBy(16.dp)) {
                    // 平台名称
                    OutlinedTextField(
                        value = name,
                        onValueChange = {
                            name = it
                            nameError = false
                        },
                        label = { Text("平台名称") },
                        placeholder = { Text("例如：某某商城") },
                        modifier = Modifier.fillMaxWidth(),
                        singleLine = true,
                        isError = nameError,
                        supportingText = if (nameError) {
                            { Text("请输入平台名称", color = MaterialTheme.colorScheme.error) }
                        } else null
                    )

                    // 应用包名
                    OutlinedTextField(
                        value = packageName,
                        onValueChange = {
                            packageName = it
                            packageNameError = false
                        },
                        label = { Text("应用包名") },
                        placeholder = { Text("例如：com.example.shop") },
                        modifier = Modifier.fillMaxWidth(),
                        singleLine = true,
                        isError = packageNameError,
                        supportingText = if (packageNameError) {
                            { Text("请输入应用包名", color = MaterialTheme.colorScheme.error) }
                        } else null
                    )
                }
            }

            // URL匹配规则配置
            ConfigSection(
                title = "URL匹配规则",
                description = "设置用于识别该购物平台链接的URL匹配规则"
            ) {
                Column(verticalArrangement = Arrangement.spacedBy(16.dp)) {
                    // 添加新规则
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        OutlinedTextField(
                            value = newPattern,
                            onValueChange = { newPattern = it },
                            label = { Text("新规则") },
                            placeholder = { Text("例如：.*example\\.com.*") },
                            modifier = Modifier.weight(1f),
                            singleLine = true
                        )

                        Spacer(modifier = Modifier.width(8.dp))

                        IconButton(
                            onClick = {
                                if (newPattern.isNotBlank()) {
                                    urlPatterns = urlPatterns + listOf(newPattern.trim())
                                    newPattern = ""
                                    urlPatternsError = false
                                }
                            }
                        ) {
                            Icon(
                                imageVector = Icons.Default.Add,
                                contentDescription = "添加规则"
                            )
                        }
                    }

                    // 显示错误信息
                    if (urlPatternsError) {
                        Text(
                            text = "请至少添加一条URL匹配规则",
                            color = MaterialTheme.colorScheme.error,
                            style = MaterialTheme.typography.bodySmall
                        )
                    }

                    // 现有规则列表
                    urlPatterns.forEach { pattern ->
                        Card(
                            modifier = Modifier.fillMaxWidth()
                        ) {
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(12.dp),
                                horizontalArrangement = Arrangement.SpaceBetween,
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text(
                                    text = pattern,
                                    style = MaterialTheme.typography.bodyMedium,
                                    modifier = Modifier.weight(1f)
                                )

                                IconButton(
                                    onClick = {
                                        urlPatterns = urlPatterns.filter { it != pattern }
                                    }
                                ) {
                                    Icon(
                                        imageVector = Icons.Default.Delete,
                                        contentDescription = "删除规则"
                                    )
                                }
                            }
                        }
                    }

                    if (urlPatterns.isEmpty()) {
                        Card(
                            modifier = Modifier.fillMaxWidth()
                        ) {
                            Box(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(24.dp),
                                contentAlignment = Alignment.Center
                            ) {
                                Text(
                                    text = "暂无匹配规则\n请在上方添加URL匹配规则",
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = MaterialTheme.colorScheme.onSurfaceVariant
                                )
                            }
                        }
                    }
                }
            }

            // 高级选项配置
            ConfigSection(
                title = "高级选项",
                description = "配置URL匹配的高级选项和平台启用状态"
            ) {
                Column(verticalArrangement = Arrangement.spacedBy(16.dp)) {
                    // 使用正则表达式
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Column(modifier = Modifier.weight(1f)) {
                            Text(
                                text = "使用正则表达式",
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onSurface
                            )
                            Text(
                                text = "启用后将使用正则表达式匹配URL",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        Switch(
                            checked = useRegex,
                            onCheckedChange = { useRegex = it }
                        )
                    }

                    // 大小写敏感
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Column(modifier = Modifier.weight(1f)) {
                            Text(
                                text = "大小写敏感",
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onSurface
                            )
                            Text(
                                text = "启用后将区分URL中的大小写",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        Switch(
                            checked = caseSensitive,
                            onCheckedChange = { caseSensitive = it }
                        )
                    }

                    // 启用此平台
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Column(modifier = Modifier.weight(1f)) {
                            Text(
                                text = "启用此平台",
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onSurface
                            )
                            Text(
                                text = "关闭后将不会检测此平台的链接",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        Switch(
                            checked = enabled,
                            onCheckedChange = { enabled = it }
                        )
                    }
                }
            }
        }
    }
}

/**
 * 配置区域组件
 */
@Composable
private fun ConfigSection(
    title: String,
    description: String,
    content: @Composable () -> Unit
) {
    Card(
        shape = androidx.compose.foundation.shape.RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceContainerLow
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Column(verticalArrangement = Arrangement.spacedBy(4.dp)) {
                Text(
                    text = title,
                    style = MaterialTheme.typography.titleMedium,
                    color = MaterialTheme.colorScheme.onSurface
                )
                Text(
                    text = description,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            content()
        }
    }
}
