package com.weinuo.quickcommands22.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import com.weinuo.quickcommands22.model.ShareTarget
import com.weinuo.quickcommands22.model.ShareTargetSelectionMode
import com.weinuo.quickcommands22.utils.ShareTargetsCacheManager
import com.weinuo.quickcommands22.ui.components.themed.ThemedSearchTextField
import kotlinx.coroutines.launch

/**
 * 可复用的分享目标选择界面
 *
 * 提供分享目标列表选择界面，支持搜索功能和单选模式
 * 使用全屏界面替代对话框，提供更好的用户体验
 * 专门用于分享文本功能，显示具体的分享选项（如"发送给好友"、"分享到朋友圈"）
 * 使用按需加载策略和智能缓存，避免不必要的电量消耗
 *
 * @param selectionMode 选择模式（目前只支持单选）
 * @param onShareTargetSelected 分享目标选择完成回调，返回选中的分享目标
 * @param onDismiss 取消回调
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ShareTargetSelectionScreen(
    selectionMode: ShareTargetSelectionMode = ShareTargetSelectionMode.SINGLE,
    onShareTargetSelected: (ShareTarget) -> Unit,
    onDismiss: () -> Unit
) {
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()
    val focusManager = LocalFocusManager.current

    // 状态管理
    var shareTargets by remember { mutableStateOf<List<ShareTarget>>(emptyList()) }
    var isLoading by remember { mutableStateOf(true) }
    var errorMessage by remember { mutableStateOf<String?>(null) }
    var searchQuery by remember { mutableStateOf("") }

    // 加载分享目标列表 - 使用按需加载和智能缓存
    LaunchedEffect(Unit) {
        coroutineScope.launch {
            try {
                isLoading = true
                errorMessage = null

                shareTargets = ShareTargetsCacheManager.getShareTargets(context)
                errorMessage = null
            } catch (e: Exception) {
                errorMessage = "加载分享目标失败：${e.message}"
            } finally {
                isLoading = false
            }
        }
    }

    // 过滤分享目标列表
    val filteredTargets = remember(shareTargets, searchQuery) {
        if (searchQuery.isBlank()) {
            shareTargets
        } else {
            shareTargets.filter { target ->
                target.appName.contains(searchQuery, ignoreCase = true) ||
                target.targetLabel.contains(searchQuery, ignoreCase = true) ||
                target.packageName.contains(searchQuery, ignoreCase = true)
            }
        }
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(text = "选择分享方式")
                },
                navigationIcon = {
                    IconButton(onClick = onDismiss) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "返回"
                        )
                    }
                }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            // 搜索框
            ThemedSearchTextField(
                searchQuery = searchQuery,
                onSearchQueryChange = { searchQuery = it },
                onClearSearch = {
                    searchQuery = ""
                    focusManager.clearFocus()
                },
                placeholder = "搜索应用名称或分享方式",
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp)
            )

            when {
                isLoading -> {
                    // 加载状态
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally,
                            verticalArrangement = Arrangement.spacedBy(16.dp)
                        ) {
                            CircularProgressIndicator()
                            Text("正在加载分享目标...")
                        }
                    }
                }

                errorMessage != null -> {
                    // 错误状态
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally,
                            verticalArrangement = Arrangement.spacedBy(16.dp)
                        ) {
                            Text(
                                text = errorMessage!!,
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.error
                            )
                            Button(
                                onClick = {
                                    coroutineScope.launch {
                                        try {
                                            isLoading = true
                                            errorMessage = null
                                            // 清除缓存并重新加载
                                            ShareTargetsCacheManager.clearCache()
                                            shareTargets = ShareTargetsCacheManager.getShareTargets(context)
                                        } catch (e: Exception) {
                                            errorMessage = "加载分享目标失败：${e.message}"
                                        } finally {
                                            isLoading = false
                                        }
                                    }
                                }
                            ) {
                                Text("重试")
                            }
                        }
                    }
                }

                filteredTargets.isEmpty() -> {
                    // 空状态
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = if (searchQuery.isBlank()) "没有找到支持分享文本的应用" else "没有找到匹配的分享目标",
                            style = MaterialTheme.typography.bodyLarge,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }

                else -> {
                    // 分享目标列表
                    LazyColumn(
                        modifier = Modifier.fillMaxSize(),
                        contentPadding = PaddingValues(horizontal = 16.dp, vertical = 8.dp),
                        verticalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        items(filteredTargets) { target ->
                            ShareTargetItem(
                                target = target,
                                onTargetClick = { clickedTarget ->
                                    // 单选模式：直接返回选中的分享目标
                                    onShareTargetSelected(clickedTarget)
                                }
                            )
                        }
                    }
                }
            }
        }
    }
}

/**
 * 分享目标列表项组件
 *
 * @param target 分享目标信息
 * @param onTargetClick 点击回调
 */
@Composable
private fun ShareTargetItem(
    target: ShareTarget,
    onTargetClick: (ShareTarget) -> Unit
) {
    Surface(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onTargetClick(target) },
        shape = RoundedCornerShape(12.dp),
        color = MaterialTheme.colorScheme.surfaceContainerLow
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 应用图标占位符
            Box(
                modifier = Modifier
                    .size(48.dp)
                    .background(
                        MaterialTheme.colorScheme.primary.copy(alpha = 0.1f),
                        CircleShape
                    ),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = target.appName.take(1).uppercase(),
                    style = MaterialTheme.typography.titleMedium,
                    color = MaterialTheme.colorScheme.primary
                )
            }

            Spacer(modifier = Modifier.width(16.dp))

            // 分享目标信息
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = target.getDisplayText(),
                    style = MaterialTheme.typography.titleMedium,
                    color = MaterialTheme.colorScheme.onSurface,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
                Text(
                    text = target.packageName,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )

                // 显示应用类型标签
                if (target.isSystemApp) {
                    Text(
                        text = "系统应用",
                        style = MaterialTheme.typography.labelSmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.8f)
                    )
                }
            }
        }
    }
}
