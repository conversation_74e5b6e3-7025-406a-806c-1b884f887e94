package com.weinuo.quickcommands22.ui.configuration

import android.content.Context
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.selection.selectable
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.ui.unit.dp
import com.weinuo.quickcommands22.R
import com.weinuo.quickcommands22.model.*
import com.weinuo.quickcommands22.ui.components.ConfigurationCardItem
import java.util.UUID

/**
 * 音量任务配置数据提供器
 *
 * 提供音量任务特定的配置项列表，为每个操作类型定义配置内容组件，
 * 复用现有的配置逻辑，支持编辑模式的数据预填充。
 */
object VolumeTaskConfigProvider {

    /**
     * 获取音量任务配置项列表
     *
     * @param context 上下文，用于获取字符串资源
     * @return 音量任务配置项列表
     */
    fun getConfigurationItems(context: Context): List<ConfigurationCardItem<VolumeOperation>> {
        return listOf(
            // 音量变化
            ConfigurationCardItem(
                id = "volume_change",
                title = context.getString(R.string.volume_volume_change),
                description = context.getString(R.string.volume_volume_change_description),
                operationType = VolumeOperation.VOLUME_CHANGE,
                permissionRequired = false,
                content = { operation, onComplete ->
                    VolumeChangeConfigContent(operation, onComplete)
                }
            ),

            // 音量调节
            ConfigurationCardItem(
                id = "volume_adjust",
                title = context.getString(R.string.volume_volume_adjust),
                description = context.getString(R.string.volume_volume_adjust_description),
                operationType = VolumeOperation.VOLUME_ADJUST,
                permissionRequired = false,
                content = { operation, onComplete ->
                    VolumeAdjustConfigContent(operation, onComplete)
                }
            ),

            // 免提通话控制
            ConfigurationCardItem(
                id = "speakerphone_control",
                title = "免提通话控制",
                description = "控制免提通话的开启、关闭或切换",
                operationType = VolumeOperation.SPEAKERPHONE_CONTROL,
                permissionRequired = false,
                content = { operation, onComplete ->
                    SpeakerphoneControlConfigContent(operation, onComplete)
                }
            ),

            // 振动模式
            ConfigurationCardItem(
                id = "vibration_mode",
                title = "振动模式",
                description = "设置设备的振动模式",
                operationType = VolumeOperation.VIBRATION_MODE_CONTROL,
                permissionRequired = false,
                content = { operation, onComplete ->
                    VibrationModeConfigContent(operation, onComplete)
                }
            ),

            // 音量弹出窗口
            ConfigurationCardItem(
                id = "volume_popup",
                title = "音量弹出窗口",
                description = "显示系统音量调节弹出窗口",
                operationType = VolumeOperation.SHOW_VOLUME_POPUP,
                permissionRequired = false,
                content = { operation, onComplete ->
                    VolumePopupConfigContent(operation, onComplete)
                }
            ),



            // 勿扰模式
            ConfigurationCardItem(
                id = "do_not_disturb",
                title = "勿扰模式",
                description = "设置勿扰模式状态",
                operationType = VolumeOperation.DO_NOT_DISTURB_MODE,
                permissionRequired = true,
                content = { operation, onComplete ->
                    DoNotDisturbConfigContent(operation, onComplete)
                }
            )
        )
    }
}

/**
 * 音量变化配置内容组件
 *
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun VolumeChangeConfigContent(
    operation: VolumeOperation,
    onComplete: (Any) -> Unit
) {

    var volumeStreamType by rememberSaveable { mutableStateOf(VolumeStreamType.MEDIA_MUSIC) }
    var volumeMode by rememberSaveable { mutableStateOf(VolumeMode.PERCENTAGE) }
    var volumeValue by rememberSaveable { mutableStateOf("50") }
    var volumeError by rememberSaveable { mutableStateOf(false) }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "音量变化设置",
            style = MaterialTheme.typography.titleMedium
        )

        // 音频流类型选择
        Text(
            text = "音频流类型",
            style = MaterialTheme.typography.bodyLarge
        )

        VolumeStreamType.values().forEach { streamType ->
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .selectable(
                        selected = (volumeStreamType == streamType),
                        onClick = { volumeStreamType = streamType }
                    ),
                verticalAlignment = Alignment.CenterVertically
            ) {
                RadioButton(
                    selected = (volumeStreamType == streamType),
                    onClick = { volumeStreamType = streamType }
                )
                Text(
                    text = when (streamType) {
                        VolumeStreamType.MEDIA_MUSIC -> "媒体音乐"
                        VolumeStreamType.RING -> "铃声"
                        VolumeStreamType.NOTIFICATION -> "通知"
                        VolumeStreamType.ALARM -> "闹钟"
                        VolumeStreamType.VOICE_CALL -> "通话"
                        VolumeStreamType.SYSTEM -> "系统"
                        VolumeStreamType.BLUETOOTH -> "蓝牙声音"
                        VolumeStreamType.ACCESSIBILITY -> "无障碍"
                    },
                    modifier = Modifier.padding(start = 8.dp)
                )
            }
        }

        // 音量模式选择
        Text(
            text = "音量模式",
            style = MaterialTheme.typography.bodyLarge
        )

        VolumeMode.values().forEach { mode ->
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .selectable(
                        selected = (volumeMode == mode),
                        onClick = { volumeMode = mode }
                    ),
                verticalAlignment = Alignment.CenterVertically
            ) {
                RadioButton(
                    selected = (volumeMode == mode),
                    onClick = { volumeMode = mode }
                )
                Text(
                    text = when (mode) {
                        VolumeMode.PERCENTAGE -> "百分比 (0-100)"
                        VolumeMode.ABSOLUTE -> "绝对值 (0-15)"
                    },
                    modifier = Modifier.padding(start = 8.dp)
                )
            }
        }

        // 音量值输入
        OutlinedTextField(
            value = volumeValue,
            onValueChange = { newValue ->
                volumeValue = newValue
                val intValue = newValue.toIntOrNull()
                volumeError = when {
                    intValue == null -> true
                    volumeMode == VolumeMode.PERCENTAGE -> intValue < 0 || intValue > 100
                    volumeMode == VolumeMode.ABSOLUTE -> intValue < 0 || intValue > 15
                    else -> false
                }
            },
            label = {
                Text(
                    if (volumeMode == VolumeMode.PERCENTAGE) "音量百分比 (0-100)"
                    else "音量绝对值 (0-15)"
                )
            },
            modifier = Modifier.fillMaxWidth(),
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
            isError = volumeError,
            supportingText = if (volumeError) {
                {
                    Text(
                        if (volumeMode == VolumeMode.PERCENTAGE) "请输入0-100之间的数值"
                        else "请输入0-15之间的数值"
                    )
                }
            } else null
        )

        // 确认按钮
        Button(
            onClick = {
                val parsedVolumeValue = volumeValue.toIntOrNull()?.coerceIn(0, 100) ?: 50
                val task = VolumeTask(
                    id = UUID.randomUUID().toString(),
                    operation = operation,
                    volumeStreamType = volumeStreamType,
                    volumeMode = volumeMode,
                    volumeValue = parsedVolumeValue
                )
                // 验证任务数据完整性
                if (task.validateData()) {
                    onComplete(task)
                }
            },
            modifier = Modifier.fillMaxWidth(),
            enabled = !volumeError && volumeValue.isNotBlank()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 音量调节配置内容组件
 *
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun VolumeAdjustConfigContent(
    operation: VolumeOperation,
    onComplete: (Any) -> Unit
) {

    var volumeAdjustOperation by rememberSaveable { mutableStateOf(VolumeAdjustOperation.VOLUME_UP) }
    var volumeAdjustStreamType by rememberSaveable { mutableStateOf(VolumeStreamType.MEDIA_MUSIC) }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "音量调节设置",
            style = MaterialTheme.typography.titleMedium
        )

        // 调节操作选择
        Text(
            text = "调节操作",
            style = MaterialTheme.typography.bodyLarge
        )

        VolumeAdjustOperation.values().forEach { adjustOp ->
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .selectable(
                        selected = (volumeAdjustOperation == adjustOp),
                        onClick = { volumeAdjustOperation = adjustOp }
                    ),
                verticalAlignment = Alignment.CenterVertically
            ) {
                RadioButton(
                    selected = (volumeAdjustOperation == adjustOp),
                    onClick = { volumeAdjustOperation = adjustOp }
                )
                Text(
                    text = when (adjustOp) {
                        VolumeAdjustOperation.VOLUME_UP -> "音量增加"
                        VolumeAdjustOperation.VOLUME_DOWN -> "音量减少"
                    },
                    modifier = Modifier.padding(start = 8.dp)
                )
            }
        }

        // 音频流类型选择
        Text(
            text = "音频流类型",
            style = MaterialTheme.typography.bodyLarge
        )

        VolumeStreamType.values().forEach { streamType ->
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .selectable(
                        selected = (volumeAdjustStreamType == streamType),
                        onClick = { volumeAdjustStreamType = streamType }
                    ),
                verticalAlignment = Alignment.CenterVertically
            ) {
                RadioButton(
                    selected = (volumeAdjustStreamType == streamType),
                    onClick = { volumeAdjustStreamType = streamType }
                )
                Text(
                    text = when (streamType) {
                        VolumeStreamType.MEDIA_MUSIC -> "媒体音乐"
                        VolumeStreamType.RING -> "铃声"
                        VolumeStreamType.NOTIFICATION -> "通知"
                        VolumeStreamType.ALARM -> "闹钟"
                        VolumeStreamType.VOICE_CALL -> "通话"
                        VolumeStreamType.SYSTEM -> "系统"
                        VolumeStreamType.BLUETOOTH -> "蓝牙声音"
                        VolumeStreamType.ACCESSIBILITY -> "无障碍"
                    },
                    modifier = Modifier.padding(start = 8.dp)
                )
            }
        }

        // 确认按钮
        Button(
            onClick = {
                val task = VolumeTask(
                    id = UUID.randomUUID().toString(),
                    operation = operation,
                    volumeAdjustOperation = volumeAdjustOperation,
                    volumeAdjustStreamType = volumeAdjustStreamType
                )
                // 验证任务数据完整性
                if (task.validateData()) {
                    onComplete(task)
                }
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 免提通话控制配置内容组件
 *
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun SpeakerphoneControlConfigContent(
    operation: VolumeOperation,
    onComplete: (Any) -> Unit
) {

    var speakerphoneOperation by rememberSaveable { mutableStateOf(SpeakerphoneOperation.TURN_ON) }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "免提通话控制设置",
            style = MaterialTheme.typography.titleMedium
        )

        // 免提通话操作选择
        SpeakerphoneOperation.values().forEach { speakerOp ->
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .selectable(
                        selected = (speakerphoneOperation == speakerOp),
                        onClick = { speakerphoneOperation = speakerOp }
                    ),
                verticalAlignment = Alignment.CenterVertically
            ) {
                RadioButton(
                    selected = (speakerphoneOperation == speakerOp),
                    onClick = { speakerphoneOperation = speakerOp }
                )
                Text(
                    text = when (speakerOp) {
                        SpeakerphoneOperation.TURN_ON -> "启用免提通话"
                        SpeakerphoneOperation.TURN_OFF -> "禁用免提通话"
                        SpeakerphoneOperation.TOGGLE -> "切换免提通话状态"
                    },
                    modifier = Modifier.padding(start = 8.dp)
                )
            }
        }

        // 确认按钮
        Button(
            onClick = {
                val task = VolumeTask(
                    id = UUID.randomUUID().toString(),
                    operation = operation,
                    speakerphoneOperation = speakerphoneOperation
                )
                // 验证任务数据完整性
                if (task.validateData()) {
                    onComplete(task)
                }
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 振动模式配置内容组件
 *
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun VibrationModeConfigContent(
    operation: VolumeOperation,
    onComplete: (Any) -> Unit
) {

    var vibrationModeType by rememberSaveable { mutableStateOf(VibrationModeType.SILENT_WITH_VIBRATION) }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "振动模式设置",
            style = MaterialTheme.typography.titleMedium
        )

        // 振动模式选择
        VibrationModeType.values().forEach { modeType ->
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .selectable(
                        selected = (vibrationModeType == modeType),
                        onClick = { vibrationModeType = modeType }
                    ),
                verticalAlignment = Alignment.CenterVertically
            ) {
                RadioButton(
                    selected = (vibrationModeType == modeType),
                    onClick = { vibrationModeType = modeType }
                )
                Text(
                    text = when (modeType) {
                        VibrationModeType.SILENT_WITH_VIBRATION -> "静音但振动"
                        VibrationModeType.NORMAL_WITHOUT_VIBRATION -> "标准（振动关闭）"
                        VibrationModeType.RING_WITH_VIBRATION -> "响铃时振动"
                        VibrationModeType.RING_WITHOUT_VIBRATION -> "响铃时振动关闭"
                        VibrationModeType.RING_VIBRATION_TOGGLE -> "响铃时振动切换"
                    },
                    modifier = Modifier.padding(start = 8.dp)
                )
            }
        }

        // 确认按钮
        Button(
            onClick = {
                val task = VolumeTask(
                    id = UUID.randomUUID().toString(),
                    operation = operation,
                    vibrationModeType = vibrationModeType
                )
                // 验证任务数据完整性
                if (task.validateData()) {
                    onComplete(task)
                }
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 音量弹出窗口配置内容组件
 *
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun VolumePopupConfigContent(
    operation: VolumeOperation,
    onComplete: (Any) -> Unit
) {

    var volumePopupType by rememberSaveable { mutableStateOf(VolumePopupType.MEDIA_MUSIC) }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "音量弹出窗口设置",
            style = MaterialTheme.typography.titleMedium
        )

        // 弹出窗口类型选择
        VolumePopupType.values().forEach { popupType ->
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .selectable(
                        selected = (volumePopupType == popupType),
                        onClick = { volumePopupType = popupType }
                    ),
                verticalAlignment = Alignment.CenterVertically
            ) {
                RadioButton(
                    selected = (volumePopupType == popupType),
                    onClick = { volumePopupType = popupType }
                )
                Text(
                    text = when (popupType) {
                        VolumePopupType.MEDIA_MUSIC -> "媒体音乐"
                        VolumePopupType.RING -> "铃声"
                        VolumePopupType.NOTIFICATION -> "通知"
                        VolumePopupType.ALARM -> "闹钟"
                        VolumePopupType.VOICE_CALL -> "通话"
                        VolumePopupType.SYSTEM -> "系统"
                        VolumePopupType.BLUETOOTH -> "蓝牙声音"
                    },
                    modifier = Modifier.padding(start = 8.dp)
                )
            }
        }

        // 确认按钮
        Button(
            onClick = {
                val task = VolumeTask(
                    id = UUID.randomUUID().toString(),
                    operation = operation,
                    volumePopupType = volumePopupType
                )
                // 验证任务数据完整性
                if (task.validateData()) {
                    onComplete(task)
                }
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}



/**
 * 勿扰模式配置内容组件
 *
 * 配置内容组件专注于UI逻辑，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun DoNotDisturbConfigContent(
    operation: VolumeOperation,
    onComplete: (Any) -> Unit
) {
    var doNotDisturbMode by rememberSaveable { mutableStateOf(DoNotDisturbMode.NORMAL) }
    var disableVibrationWhenSilent by rememberSaveable { mutableStateOf(false) }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "勿扰模式设置",
            style = MaterialTheme.typography.titleMedium
        )

        // 勿扰模式选择
        DoNotDisturbMode.values().forEach { dndMode ->
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .selectable(
                        selected = (doNotDisturbMode == dndMode),
                        onClick = { doNotDisturbMode = dndMode }
                    ),
                verticalAlignment = Alignment.CenterVertically
            ) {
                RadioButton(
                    selected = (doNotDisturbMode == dndMode),
                    onClick = { doNotDisturbMode = dndMode }
                )
                Text(
                    text = when (dndMode) {
                        DoNotDisturbMode.NORMAL -> "正常模式"
                        DoNotDisturbMode.CALLS_ONLY -> "仅限来电响铃"
                        DoNotDisturbMode.COMPLETE_SILENCE -> "完全静音"
                        DoNotDisturbMode.ALARMS_ONLY -> "仅闹钟"
                    },
                    modifier = Modifier.padding(start = 8.dp)
                )
            }
        }

        // 完全静音时的振动控制选项
        if (doNotDisturbMode == DoNotDisturbMode.COMPLETE_SILENCE) {
            Spacer(modifier = Modifier.height(8.dp))

            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.fillMaxWidth()
            ) {
                Checkbox(
                    checked = disableVibrationWhenSilent,
                    onCheckedChange = { disableVibrationWhenSilent = it }
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "同时禁用来电/通知振动",
                    style = MaterialTheme.typography.bodyMedium
                )
            }

            if (disableVibrationWhenSilent) {
                Text(
                    text = "注意：此选项会尝试取消当前振动，但无法完全禁用系统振动功能",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    modifier = Modifier.padding(start = 32.dp)
                )
            }
        }

        // 确认按钮
        Button(
            onClick = {
                val task = VolumeTask(
                    id = UUID.randomUUID().toString(),
                    operation = operation,
                    doNotDisturbMode = doNotDisturbMode,
                    disableVibrationWhenSilent = disableVibrationWhenSilent
                )
                // 验证任务数据完整性
                if (task.validateData()) {
                    onComplete(task)
                }
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}
