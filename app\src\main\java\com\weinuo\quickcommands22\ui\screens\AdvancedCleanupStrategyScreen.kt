package com.weinuo.quickcommands22.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavController
import com.weinuo.quickcommands22.data.SettingsRepository
import com.weinuo.quickcommands22.model.*
import com.weinuo.quickcommands22.navigation.Screen
import com.weinuo.quickcommands22.ui.theme.manager.ThemeManager
import com.weinuo.quickcommands22.ui.components.skyblue.SkyBlueBackButton
import com.weinuo.quickcommands22.ui.components.skyblue.SkyBlueSaveButton

/**
 * 高级清理策略自定义界面
 * 允许用户自定义清理规则的顺序和参数
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AdvancedCleanupStrategyScreen(
    initialStrategy: CleanupStrategy? = null,
    onStrategyConfigured: (CleanupStrategy) -> Unit,
    onNavigateBack: () -> Unit,
    navController: NavController? = null
) {
    val context = LocalContext.current
    val scope = rememberCoroutineScope()
    val settingsRepository = remember { SettingsRepository(context) }
    val localNavController = navController ?: LocalNavController.current

    // 获取主题管理器和设置仓库
    val themeManager = remember { ThemeManager.getInstance(context) }
    val globalSettings by settingsRepository.globalSettings.collectAsState()

    var strategyName by rememberSaveable { mutableStateOf(initialStrategy?.name ?: "自定义策略") }
    var strategyDescription by rememberSaveable { mutableStateOf(initialStrategy?.description ?: "用户自定义的清理策略") }
    var rules by remember { mutableStateOf(initialStrategy?.rules?.toMutableList() ?: getDefaultRules().toMutableList()) }

    // 监听添加规则的结果 - 使用原生数据类型存储
    LaunchedEffect(Unit) {
        val savedStateHandle = localNavController?.currentBackStackEntry?.savedStateHandle
        val addedRuleNavigationKey = savedStateHandle?.get<String>("added_cleanup_rule_json")
        if (addedRuleNavigationKey != null) {
            try {
                val navigationManager = com.weinuo.quickcommands22.storage.NavigationDataStorageManager(context)
                val ruleData = navigationManager.loadSimpleNavigationData(addedRuleNavigationKey)

                if (ruleData.isNotEmpty()) {
                    // 从结构化数据重建CleanupRule
                    val ruleId = ruleData["id"] as? String ?: ""
                    val ruleTypeName = ruleData["type"] as? String ?: ""
                    val ruleType = try {
                        com.weinuo.quickcommands22.model.CleanupRuleType.valueOf(ruleTypeName)
                    } catch (e: Exception) {
                        com.weinuo.quickcommands22.model.CleanupRuleType.NORMAL_APPS
                    }
                    val order = (ruleData["order"] as? Number)?.toInt() ?: 0
                    val enabled = ruleData["enabled"] as? Boolean ?: true
                    val parametersCount = (ruleData["parameters_count"] as? Number)?.toInt() ?: 0

                    // 重建参数
                    val parameters = mutableMapOf<String, Any>()
                    for (index in 0 until parametersCount) {
                        val key = ruleData["param_${index}_key"] as? String ?: ""
                        val type = ruleData["param_${index}_type"] as? String ?: "string"
                        val value: Any = when (type) {
                            "string" -> ruleData["param_${index}_value"] as? String ?: ""
                            "int" -> (ruleData["param_${index}_value"] as? Number)?.toInt() ?: 0
                            "boolean" -> ruleData["param_${index}_value"] as? Boolean ?: false
                            "float" -> (ruleData["param_${index}_value"] as? Number)?.toFloat() ?: 0f
                            "long" -> (ruleData["param_${index}_value"] as? Number)?.toLong() ?: 0L
                            else -> ruleData["param_${index}_value"]?.toString() ?: ""
                        }
                        if (key.isNotEmpty()) {
                            parameters[key] = value
                        }
                    }

                    // 创建新规则并添加到列表
                    val newRule = com.weinuo.quickcommands22.model.CleanupRule(
                        id = ruleId,
                        type = ruleType,
                        order = rules.size, // 设置为列表末尾
                        enabled = enabled,
                        parameters = parameters
                    )

                    rules = (rules + newRule).toMutableList()
                }

                // 清理导航数据
                navigationManager.clearNavigationData(addedRuleNavigationKey)
                savedStateHandle.remove<String>("added_cleanup_rule_json")
            } catch (e: Exception) {
                android.util.Log.e("AdvancedCleanupStrategy", "Error loading added rule", e)
            }
        }
    }

    Column(modifier = Modifier.fillMaxSize()) {
        // 顶部应用栏 - 主题感知
        TopAppBar(
            title = {
                Text(
                    text = "高级清理策略配置",
                    style = if (themeManager.getCurrentThemeId() == "sky_blue") {
                        // 天空蓝主题：使用全局设置的标题字重和字体大小
                        MaterialTheme.typography.titleLarge.copy(
                            fontWeight = when (globalSettings.topAppBarTitleFontWeight) {
                                "normal" -> FontWeight.Normal
                                "medium" -> FontWeight.Medium
                                "bold" -> FontWeight.Bold
                                else -> FontWeight.Medium
                            },
                            fontSize = globalSettings.screenTitleFontSize.sp
                        )
                    } else {
                        // 海洋蓝主题：保持原有样式
                        MaterialTheme.typography.titleLarge
                    }
                )
            },
            navigationIcon = {
                if (themeManager.getCurrentThemeId() == "sky_blue") {
                    // 天空蓝主题：使用专用的返回按钮
                    SkyBlueBackButton(onClick = onNavigateBack)
                } else {
                    // 其他主题：使用原有的箭头图标
                    IconButton(onClick = onNavigateBack) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                            contentDescription = "返回"
                        )
                    }
                }
            },
            actions = {
                // 保存按钮 - 主题感知，始终显示但根据验证状态控制可用性
                val isValid = strategyName.isNotBlank() && rules.isNotEmpty()

                if (themeManager.getCurrentThemeId() == "sky_blue") {
                    // 天空蓝主题：使用专用的保存按钮（勾号图标）
                    SkyBlueSaveButton(
                        onClick = {
                            val strategy = CleanupStrategy(
                                name = strategyName,
                                description = strategyDescription,
                                rules = rules.mapIndexed { index, rule ->
                                    rule.copy(order = index + 1)
                                }
                            )
                            onStrategyConfigured(strategy)
                        },
                        enabled = isValid
                    )
                } else {
                    // 其他主题：使用原有的文本按钮
                    TextButton(
                        onClick = {
                            val strategy = CleanupStrategy(
                                name = strategyName,
                                description = strategyDescription,
                                rules = rules.mapIndexed { index, rule ->
                                    rule.copy(order = index + 1)
                                }
                            )
                            onStrategyConfigured(strategy)
                        },
                        enabled = isValid
                    ) {
                        Text("保存")
                    }
                }
            }
        )

        LazyColumn(
            modifier = Modifier.fillMaxSize(),
            contentPadding = PaddingValues(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 策略基本信息
            item {
                Card(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        verticalArrangement = Arrangement.spacedBy(12.dp)
                    ) {
                        Text(
                            text = "策略信息",
                            style = MaterialTheme.typography.titleMedium
                        )

                        OutlinedTextField(
                            value = strategyName,
                            onValueChange = { strategyName = it },
                            label = { Text("策略名称") },
                            modifier = Modifier.fillMaxWidth()
                        )

                        OutlinedTextField(
                            value = strategyDescription,
                            onValueChange = { strategyDescription = it },
                            label = { Text("策略描述") },
                            modifier = Modifier.fillMaxWidth(),
                            maxLines = 2
                        )
                    }
                }
            }

            // 规则列表标题
            item {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "清理规则 (按顺序执行)",
                        style = MaterialTheme.typography.titleMedium
                    )

                    IconButton(
                        onClick = {
                            if (localNavController != null) {
                                val route = Screen.AddCleanupRule.createRoute()
                                localNavController.navigate(route)
                            }
                        }
                    ) {
                        Icon(Icons.Default.Add, contentDescription = "添加规则")
                    }
                }
            }

            // 规则列表
            itemsIndexed(rules) { index, rule ->
                CleanupRuleItem(
                    rule = rule,
                    index = index,
                    canMoveUp = index > 0,
                    canMoveDown = index < rules.size - 1,
                    onMoveUp = {
                        if (index > 0) {
                            val newRules = rules.toMutableList()
                            val temp = newRules[index]
                            newRules[index] = newRules[index - 1]
                            newRules[index - 1] = temp
                            rules = newRules
                        }
                    },
                    onMoveDown = {
                        if (index < rules.size - 1) {
                            val newRules = rules.toMutableList()
                            val temp = newRules[index]
                            newRules[index] = newRules[index + 1]
                            newRules[index + 1] = temp
                            rules = newRules
                        }
                    },
                    onToggleEnabled = {
                        val newRules = rules.toMutableList()
                        newRules[index] = rule.copy(enabled = !rule.enabled)
                        rules = newRules
                    },
                    onEdit = { editedRule ->
                        val newRules = rules.toMutableList()
                        newRules[index] = editedRule
                        rules = newRules
                    },
                    onDelete = {
                        val newRules = rules.toMutableList()
                        newRules.removeAt(index)
                        rules = newRules
                    }
                )
            }

            // 策略预览
            item {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.secondaryContainer.copy(alpha = 0.3f)
                    )
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        verticalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                imageVector = Icons.Default.Preview,
                                contentDescription = null,
                                tint = MaterialTheme.colorScheme.secondary,
                                modifier = Modifier.size(20.dp)
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(
                                text = "策略预览",
                                style = MaterialTheme.typography.titleSmall,
                                color = MaterialTheme.colorScheme.secondary
                            )
                        }

                        val enabledRules = rules.filter { it.enabled }
                        if (enabledRules.isEmpty()) {
                            Text(
                                text = "没有启用的规则",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        } else {
                            enabledRules.forEachIndexed { index, rule ->
                                Text(
                                    text = "${index + 1}. ${rule.type.displayName}${getParameterDescription(rule)}",
                                    style = MaterialTheme.typography.bodySmall,
                                    color = MaterialTheme.colorScheme.onSurfaceVariant
                                )
                            }
                        }
                    }
                }
            }
        }
    }
}

/**
 * 获取默认规则列表
 */
private fun getDefaultRules(): List<CleanupRule> {
    return listOf(
        CleanupRule(
            type = CleanupRuleType.LOW_PRIORITY_APPS,
            order = 1,
            parameters = mapOf("description" to "最先清理不重要的应用")
        ),
        CleanupRule(
            type = CleanupRuleType.LONG_UNUSED_APPS,
            order = 2,
            parameters = mapOf(
                "hours" to 6,
                "description" to "然后清理超过6小时未使用的应用"
            )
        ),
        CleanupRule(
            type = CleanupRuleType.NORMAL_APPS,
            order = 3,
            parameters = mapOf("description" to "接着清理普通应用")
        ),
        CleanupRule(
            type = CleanupRuleType.HIGH_PRIORITY_APPS,
            order = 4,
            parameters = mapOf("description" to "最后清理重要应用")
        )
    )
}

/**
 * 清理规则项组件
 */
@Composable
private fun CleanupRuleItem(
    rule: CleanupRule,
    index: Int,
    canMoveUp: Boolean,
    canMoveDown: Boolean,
    onMoveUp: () -> Unit,
    onMoveDown: () -> Unit,
    onToggleEnabled: () -> Unit,
    onEdit: (CleanupRule) -> Unit,
    onDelete: () -> Unit
) {
    var showEditDialog by remember { mutableStateOf(false) }

    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = if (rule.enabled) {
                MaterialTheme.colorScheme.surface
            } else {
                MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.5f)
            }
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 顺序号
                Surface(
                    modifier = Modifier.size(32.dp),
                    shape = MaterialTheme.shapes.small,
                    color = MaterialTheme.colorScheme.primary
                ) {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = "${index + 1}",
                            style = MaterialTheme.typography.labelMedium,
                            color = MaterialTheme.colorScheme.onPrimary
                        )
                    }
                }

                Spacer(modifier = Modifier.width(12.dp))

                // 规则信息
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = rule.type.displayName,
                        style = MaterialTheme.typography.bodyLarge,
                        color = if (rule.enabled) {
                            MaterialTheme.colorScheme.onSurface
                        } else {
                            MaterialTheme.colorScheme.onSurfaceVariant
                        }
                    )
                    Text(
                        text = rule.type.description + getParameterDescription(rule),
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        maxLines = 2,
                        overflow = TextOverflow.Ellipsis
                    )
                }

                // 启用/禁用开关
                Switch(
                    checked = rule.enabled,
                    onCheckedChange = { onToggleEnabled() }
                )
            }

            Spacer(modifier = Modifier.height(12.dp))

            // 操作按钮
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                // 上移按钮
                OutlinedButton(
                    onClick = onMoveUp,
                    enabled = canMoveUp,
                    modifier = Modifier.weight(1f)
                ) {
                    Icon(
                        imageVector = Icons.Default.KeyboardArrowUp,
                        contentDescription = null,
                        modifier = Modifier.size(18.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("上移")
                }

                // 下移按钮
                OutlinedButton(
                    onClick = onMoveDown,
                    enabled = canMoveDown,
                    modifier = Modifier.weight(1f)
                ) {
                    Icon(
                        imageVector = Icons.Default.KeyboardArrowDown,
                        contentDescription = null,
                        modifier = Modifier.size(18.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("下移")
                }

                // 编辑按钮
                OutlinedButton(
                    onClick = { showEditDialog = true },
                    modifier = Modifier.weight(1f)
                ) {
                    Icon(
                        imageVector = Icons.Default.Edit,
                        contentDescription = null,
                        modifier = Modifier.size(18.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("编辑")
                }

                // 删除按钮
                OutlinedButton(
                    onClick = onDelete,
                    colors = ButtonDefaults.outlinedButtonColors(
                        contentColor = MaterialTheme.colorScheme.error
                    ),
                    modifier = Modifier.weight(1f)
                ) {
                    Icon(
                        imageVector = Icons.Default.Delete,
                        contentDescription = null,
                        modifier = Modifier.size(18.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("删除")
                }
            }
        }
    }

    // 编辑规则对话框
    if (showEditDialog) {
        EditRuleDialog(
            rule = rule,
            onRuleEdited = { editedRule ->
                onEdit(editedRule)
                showEditDialog = false
            },
            onDismiss = { showEditDialog = false }
        )
    }
}



/**
 * 编辑规则对话框
 */
@Composable
private fun EditRuleDialog(
    rule: CleanupRule,
    onRuleEdited: (CleanupRule) -> Unit,
    onDismiss: () -> Unit
) {
    var hours by remember { mutableStateOf(rule.parameters["hours"] as? Int ?: 6) }
    var memoryThreshold by remember { mutableStateOf(rule.parameters["memoryThresholdMB"] as? Int ?: 200) }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("编辑规则: ${rule.type.displayName}") },
        text = {
            Column(
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                Text(
                    text = rule.type.description,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )

                // 参数配置
                when (rule.type) {
                    CleanupRuleType.LONG_UNUSED_APPS -> {
                        OutlinedTextField(
                            value = hours.toString(),
                            onValueChange = {
                                it.toIntOrNull()?.let { value ->
                                    if (value > 0) hours = value
                                }
                            },
                            label = { Text("未使用时间阈值（小时）") },
                            modifier = Modifier.fillMaxWidth()
                        )
                        Text(
                            text = "设置多少小时未使用的应用将被此规则处理",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                    CleanupRuleType.MEMORY_HEAVY_APPS -> {
                        OutlinedTextField(
                            value = memoryThreshold.toString(),
                            onValueChange = {
                                it.toIntOrNull()?.let { value ->
                                    if (value > 0) memoryThreshold = value
                                }
                            },
                            label = { Text("内存阈值（MB）") },
                            modifier = Modifier.fillMaxWidth()
                        )
                        Text(
                            text = "设置占用内存超过多少MB的应用将被此规则处理",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                    else -> {
                        Text(
                            text = "此规则类型无需额外参数配置",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
            }
        },
        confirmButton = {
            TextButton(
                onClick = {
                    val newParameters = when (rule.type) {
                        CleanupRuleType.LONG_UNUSED_APPS -> mapOf(
                            "hours" to hours,
                            "description" to "清理超过${hours}小时未使用的应用"
                        )
                        CleanupRuleType.MEMORY_HEAVY_APPS -> mapOf(
                            "memoryThresholdMB" to memoryThreshold,
                            "description" to "清理占用内存超过${memoryThreshold}MB的应用"
                        )
                        else -> rule.parameters
                    }

                    val editedRule = rule.copy(parameters = newParameters)
                    onRuleEdited(editedRule)
                }
            ) {
                Text("保存")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("取消")
            }
        }
    )
}

/**
 * 获取参数描述
 */
private fun getParameterDescription(rule: CleanupRule): String {
    return when (rule.type) {
        CleanupRuleType.LONG_UNUSED_APPS -> {
            val hours = rule.parameters["hours"] as? Int ?: 6
            " (超过${hours}小时)"
        }
        CleanupRuleType.MEMORY_HEAVY_APPS -> {
            val threshold = rule.parameters["memoryThresholdMB"] as? Int ?: 200
            " (超过${threshold}MB)"
        }
        else -> ""
    }
}

/**
 * 高级清理策略配置界面内容组件
 *
 * 不依赖NavController的可复用组件，用于Activity中显示高级清理策略配置界面。
 * 通过回调函数处理导航和配置完成事件。
 *
 * @param initialStrategy 初始策略（编辑模式使用）
 * @param onStrategyConfigured 策略配置完成回调
 * @param onNavigateBack 返回回调
 * @param onNavigateToAddCleanupRule 导航到添加清理规则界面回调
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AdvancedCleanupStrategyActivityContent(
    initialStrategy: CleanupStrategy? = null,
    onStrategyConfigured: (CleanupStrategy) -> Unit,
    onNavigateBack: () -> Unit,
    onNavigateToAddCleanupRule: () -> Unit
) {
    val context = LocalContext.current
    val settingsRepository = remember { SettingsRepository(context) }

    // 获取主题管理器和设置仓库
    val themeManager = remember { ThemeManager.getInstance(context) }
    val globalSettings by settingsRepository.globalSettings.collectAsState()

    var strategyName by rememberSaveable { mutableStateOf(initialStrategy?.name ?: "自定义策略") }
    var strategyDescription by rememberSaveable { mutableStateOf(initialStrategy?.description ?: "用户自定义的清理策略") }
    var rules by remember { mutableStateOf(initialStrategy?.rules?.toMutableList() ?: getDefaultRules().toMutableList()) }

    Column(modifier = Modifier.fillMaxSize()) {
        // 顶部应用栏 - 主题感知
        TopAppBar(
            title = {
                Text(
                    text = "高级清理策略配置",
                    style = if (themeManager.getCurrentThemeId() == "sky_blue") {
                        // 天空蓝主题：使用全局设置的标题字重和字体大小
                        MaterialTheme.typography.titleLarge.copy(
                            fontWeight = when (globalSettings.topAppBarTitleFontWeight) {
                                "normal" -> FontWeight.Normal
                                "medium" -> FontWeight.Medium
                                "bold" -> FontWeight.Bold
                                else -> FontWeight.Medium
                            },
                            fontSize = globalSettings.screenTitleFontSize.sp
                        )
                    } else {
                        // 海洋蓝主题：保持原有样式
                        MaterialTheme.typography.titleLarge
                    }
                )
            },
            navigationIcon = {
                if (themeManager.getCurrentThemeId() == "sky_blue") {
                    // 天空蓝主题：使用专用的返回按钮
                    SkyBlueBackButton(onClick = onNavigateBack)
                } else {
                    // 其他主题：使用原有的箭头图标
                    IconButton(onClick = onNavigateBack) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                            contentDescription = "返回"
                        )
                    }
                }
            },
            actions = {
                // 保存按钮 - 主题感知，始终显示但根据验证状态控制可用性
                val isValid = strategyName.isNotBlank() && rules.isNotEmpty()

                if (themeManager.getCurrentThemeId() == "sky_blue") {
                    // 天空蓝主题：使用专用的保存按钮（勾号图标）
                    SkyBlueSaveButton(
                        onClick = {
                            val strategy = CleanupStrategy(
                                name = strategyName,
                                description = strategyDescription,
                                rules = rules.mapIndexed { index, rule ->
                                    rule.copy(order = index + 1)
                                }
                            )
                            onStrategyConfigured(strategy)
                        },
                        enabled = isValid
                    )
                } else {
                    // 其他主题：使用原有的文本按钮
                    TextButton(
                        onClick = {
                            val strategy = CleanupStrategy(
                                name = strategyName,
                                description = strategyDescription,
                                rules = rules.mapIndexed { index, rule ->
                                    rule.copy(order = index + 1)
                                }
                            )
                            onStrategyConfigured(strategy)
                        },
                        enabled = isValid
                    ) {
                        Text("保存")
                    }
                }
            }
        )

        // 内容区域
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 策略基本信息
            item {
                Card(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp),
                        verticalArrangement = Arrangement.spacedBy(12.dp)
                    ) {
                        Text(
                            text = "策略信息",
                            style = MaterialTheme.typography.titleMedium
                        )

                        OutlinedTextField(
                            value = strategyName,
                            onValueChange = { strategyName = it },
                            label = { Text("策略名称") },
                            modifier = Modifier.fillMaxWidth()
                        )

                        OutlinedTextField(
                            value = strategyDescription,
                            onValueChange = { strategyDescription = it },
                            label = { Text("策略描述") },
                            modifier = Modifier.fillMaxWidth(),
                            maxLines = 3
                        )
                    }
                }
            }

            // 规则列表标题
            item {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "清理规则 (按顺序执行)",
                        style = MaterialTheme.typography.titleMedium
                    )

                    IconButton(
                        onClick = onNavigateToAddCleanupRule
                    ) {
                        Icon(Icons.Default.Add, contentDescription = "添加规则")
                    }
                }
            }

            // 规则列表
            itemsIndexed(rules) { index, rule ->
                CleanupRuleItem(
                    rule = rule,
                    index = index,
                    canMoveUp = index > 0,
                    canMoveDown = index < rules.size - 1,
                    onMoveUp = {
                        if (index > 0) {
                            val newRules = rules.toMutableList()
                            val temp = newRules[index]
                            newRules[index] = newRules[index - 1]
                            newRules[index - 1] = temp
                            rules = newRules
                        }
                    },
                    onMoveDown = {
                        if (index < rules.size - 1) {
                            val newRules = rules.toMutableList()
                            val temp = newRules[index]
                            newRules[index] = newRules[index + 1]
                            newRules[index + 1] = temp
                            rules = newRules
                        }
                    },
                    onToggleEnabled = {
                        val newRules = rules.toMutableList()
                        newRules[index] = rule.copy(enabled = !rule.enabled)
                        rules = newRules
                    },
                    onEdit = { editedRule ->
                        rules = rules.toMutableList().apply { set(index, editedRule) }
                    },
                    onDelete = {
                        rules = rules.toMutableList().apply { removeAt(index) }
                    }
                )
            }

            // 空状态提示
            if (rules.isEmpty()) {
                item {
                    Card(
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Column(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(32.dp),
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            Text(
                                text = "暂无清理规则",
                                style = MaterialTheme.typography.bodyLarge
                            )
                            Text(
                                text = "点击上方的 + 按钮添加规则",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant,
                                modifier = Modifier.padding(top = 8.dp)
                            )
                        }
                    }
                }
            }
        }
    }
}
