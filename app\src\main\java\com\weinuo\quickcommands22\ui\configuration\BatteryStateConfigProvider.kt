package com.weinuo.quickcommands22.ui.configuration

import android.content.Context
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import com.weinuo.quickcommands22.R
import com.weinuo.quickcommands22.model.*
import com.weinuo.quickcommands22.ui.components.ConfigurationCardItem
import java.util.UUID

/**
 * 电池状态条件配置数据提供器
 *
 * 提供电池状态条件特定的配置项列表，为每个电池状态类型定义配置内容组件，
 * 复用现有的配置逻辑，支持编辑模式的数据预填充。
 */
object BatteryStateConfigProvider {

    /**
     * 获取电池状态条件配置项列表
     *
     * @param context 上下文，用于获取字符串资源
     * @return 电池状态条件配置项列表
     */
    fun getConfigurationItems(context: Context): List<ConfigurationCardItem<BatteryConditionType>> {
        return listOf(
            // 电池电量条件
            ConfigurationCardItem(
                id = "battery_level",
                title = context.getString(R.string.battery_battery_level),
                description = context.getString(R.string.battery_battery_level_description),
                operationType = BatteryConditionType.BATTERY_LEVEL,
                permissionRequired = false,
                content = { type, onComplete ->
                    BatteryLevelConfigContent(type, onComplete)
                }
            ),

            // 充电状态条件
            ConfigurationCardItem(
                id = "charging_state",
                title = context.getString(R.string.battery_charging_state),
                description = context.getString(R.string.battery_charging_state_description),
                operationType = BatteryConditionType.CHARGING_STATE,
                permissionRequired = false,
                content = { type, onComplete ->
                    ChargingStateConfigContent(type, onComplete)
                }
            ),

            // 电池温度条件
            ConfigurationCardItem(
                id = "battery_temperature",
                title = "电池温度",
                description = "监控电池温度变化",
                operationType = BatteryConditionType.BATTERY_TEMPERATURE,
                permissionRequired = false,
                content = { type, onComplete ->
                    BatteryTemperatureConfigContent(type, onComplete)
                }
            ),

            // 电源键条件
            ConfigurationCardItem(
                id = "power_button",
                title = "电源键",
                description = "监控电源键按下事件",
                operationType = BatteryConditionType.POWER_BUTTON,
                permissionRequired = false,
                content = { type, onComplete ->
                    PowerButtonConfigContent(type, onComplete)
                }
            ),

            // 省电模式条件
            ConfigurationCardItem(
                id = "power_save_mode",
                title = "省电模式",
                description = "监控省电模式状态变化",
                operationType = BatteryConditionType.POWER_SAVE_MODE,
                permissionRequired = false,
                content = { type, onComplete ->
                    PowerSaveModeConfigContent(type, onComplete)
                }
            )
        )
    }
}

/**
 * 电池电量配置内容组件
 *
 * 电池状态不需要特殊权限，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
fun BatteryLevelConfigContent(
    conditionType: BatteryConditionType,
    onComplete: (Any) -> Unit
) {
    var selectedLevelSubType by rememberSaveable { mutableStateOf(BatteryLevelSubType.BELOW) }
    var levelThreshold by rememberSaveable { mutableStateOf("20") }
    var levelChangeThreshold by rememberSaveable { mutableStateOf("5") }

    // 输入验证
    val levelThresholdError = levelThreshold.toIntOrNull()?.let { it < 0 || it > 100 } ?: true
    val levelChangeThresholdError = levelChangeThreshold.toIntOrNull()?.let { it < 1 || it > 50 } ?: true

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp)
    ) {
        // 子类型选择
        Text(
            text = "选择电量条件类型",
            style = MaterialTheme.typography.bodyLarge,
            modifier = Modifier.padding(bottom = 16.dp)
        )

        BatteryLevelSubType.values().forEach { subType ->
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable { selectedLevelSubType = subType }
                    .padding(vertical = 8.dp)
            ) {
                RadioButton(
                    selected = selectedLevelSubType == subType,
                    onClick = { selectedLevelSubType = subType }
                )
                Spacer(modifier = Modifier.width(8.dp))
                Column {
                    Text(
                        text = when (subType) {
                            BatteryLevelSubType.BELOW -> "低于阈值"
                            BatteryLevelSubType.ABOVE -> "高于阈值"
                            BatteryLevelSubType.CHANGED -> "变化超过阈值"
                            BatteryLevelSubType.LOW_WARNING -> "低电量警告"
                        },
                        style = MaterialTheme.typography.bodyMedium
                    )
                    Text(
                        text = when (subType) {
                            BatteryLevelSubType.BELOW -> "当电池电量低于指定阈值时触发"
                            BatteryLevelSubType.ABOVE -> "当电池电量高于指定阈值时触发"
                            BatteryLevelSubType.CHANGED -> "当电池电量变化超过指定幅度时触发"
                            BatteryLevelSubType.LOW_WARNING -> "当电池电量过低（15%以下）时触发"
                        },
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }

        Spacer(modifier = Modifier.height(24.dp))

        // 参数配置
        Text(
            text = "参数配置",
            style = MaterialTheme.typography.bodyLarge,
            modifier = Modifier.padding(bottom = 16.dp)
        )

        when (selectedLevelSubType) {
            BatteryLevelSubType.BELOW, BatteryLevelSubType.ABOVE -> {
                OutlinedTextField(
                    value = levelThreshold,
                    onValueChange = { levelThreshold = it },
                    label = { Text("电量阈值 (%)") },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                    isError = levelThresholdError,
                    supportingText = {
                        if (levelThresholdError) {
                            Text("请输入 0-100 之间的数字")
                        } else {
                            Text(when (selectedLevelSubType) {
                                BatteryLevelSubType.BELOW -> "当电池电量低于此值时触发"
                                BatteryLevelSubType.ABOVE -> "当电池电量高于此值时触发"
                                else -> ""
                            })
                        }
                    },
                    modifier = Modifier.fillMaxWidth()
                )
            }
            BatteryLevelSubType.CHANGED -> {
                OutlinedTextField(
                    value = levelChangeThreshold,
                    onValueChange = { levelChangeThreshold = it },
                    label = { Text("变化幅度 (%)") },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                    isError = levelChangeThresholdError,
                    supportingText = {
                        if (levelChangeThresholdError) {
                            Text("请输入 1-50 之间的数字")
                        } else {
                            Text("当电池电量变化超过此幅度时触发")
                        }
                    },
                    modifier = Modifier.fillMaxWidth()
                )
            }
            BatteryLevelSubType.LOW_WARNING -> {
                Text(
                    text = "此条件无需额外参数，将在电池电量低于15%时触发",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }

        Spacer(modifier = Modifier.height(24.dp))

        // 确认按钮
        Button(
            onClick = {
                val condition = BatteryStateCondition(
                    id = UUID.randomUUID().toString(),
                    conditionType = BatteryConditionType.BATTERY_LEVEL,
                    levelSubType = selectedLevelSubType,
                    levelThreshold = levelThreshold.toIntOrNull() ?: 20,
                    levelChangeThreshold = levelChangeThreshold.toIntOrNull() ?: 5,
                    chargingSubType = ChargingSubType.STARTED,
                    temperatureSubType = TemperatureSubType.CHANGED,
                    temperatureThreshold = 2.0f,
                    powerButtonSubType = PowerButtonSubType.PRESSED,
                    powerButtonPressCount = 1,
                    powerSaveModeSubType = PowerSaveModeSubType.ENABLED
                )
                onComplete(condition)
            },
            enabled = when (selectedLevelSubType) {
                BatteryLevelSubType.BELOW, BatteryLevelSubType.ABOVE -> !levelThresholdError
                BatteryLevelSubType.CHANGED -> !levelChangeThresholdError
                BatteryLevelSubType.LOW_WARNING -> true
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 充电状态配置内容组件
 *
 * 充电状态不需要特殊权限，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
fun ChargingStateConfigContent(
    conditionType: BatteryConditionType,
    onComplete: (Any) -> Unit
) {
    var selectedChargingSubType by rememberSaveable { mutableStateOf(ChargingSubType.STARTED) }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp)
    ) {
        // 子类型选择
        Text(
            text = "选择充电状态类型",
            style = MaterialTheme.typography.bodyLarge,
            modifier = Modifier.padding(bottom = 16.dp)
        )

        ChargingSubType.values().forEach { subType ->
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable { selectedChargingSubType = subType }
                    .padding(vertical = 8.dp)
            ) {
                RadioButton(
                    selected = selectedChargingSubType == subType,
                    onClick = { selectedChargingSubType = subType }
                )
                Spacer(modifier = Modifier.width(8.dp))
                Column {
                    Text(
                        text = when (subType) {
                            ChargingSubType.STARTED -> "开始充电"
                            ChargingSubType.STOPPED -> "停止充电"
                            ChargingSubType.FULLY_CHARGED -> "充电完成"
                        },
                        style = MaterialTheme.typography.bodyMedium
                    )
                    Text(
                        text = when (subType) {
                            ChargingSubType.STARTED -> "当设备开始充电时触发"
                            ChargingSubType.STOPPED -> "当设备停止充电时触发"
                            ChargingSubType.FULLY_CHARGED -> "当设备充电完成（100%）时触发"
                        },
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }

        Spacer(modifier = Modifier.height(24.dp))

        Text(
            text = "此条件无需额外参数，将在充电状态变化时触发",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        Spacer(modifier = Modifier.height(24.dp))

        // 确认按钮
        Button(
            onClick = {
                val condition = BatteryStateCondition(
                    id = UUID.randomUUID().toString(),
                    conditionType = BatteryConditionType.CHARGING_STATE,
                    levelSubType = BatteryLevelSubType.BELOW,
                    levelThreshold = 20,
                    levelChangeThreshold = 5,
                    chargingSubType = selectedChargingSubType,
                    temperatureSubType = TemperatureSubType.CHANGED,
                    temperatureThreshold = 2.0f,
                    powerButtonSubType = PowerButtonSubType.PRESSED,
                    powerButtonPressCount = 1,
                    powerSaveModeSubType = PowerSaveModeSubType.ENABLED
                )
                onComplete(condition)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 电池温度配置内容组件
 *
 * 电池温度不需要特殊权限，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
fun BatteryTemperatureConfigContent(
    conditionType: BatteryConditionType,
    onComplete: (Any) -> Unit
) {
    var selectedTemperatureSubType by rememberSaveable { mutableStateOf(TemperatureSubType.CHANGED) }
    var temperatureThreshold by rememberSaveable { mutableStateOf("2.0") }

    // 输入验证
    val temperatureThresholdError = temperatureThreshold.toFloatOrNull()?.let { it < 0.5f || it > 10.0f } ?: true

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp)
    ) {
        // 子类型选择
        Text(
            text = "选择温度变化类型",
            style = MaterialTheme.typography.bodyLarge,
            modifier = Modifier.padding(bottom = 16.dp)
        )

        TemperatureSubType.values().forEach { subType ->
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable { selectedTemperatureSubType = subType }
                    .padding(vertical = 8.dp)
            ) {
                RadioButton(
                    selected = selectedTemperatureSubType == subType,
                    onClick = { selectedTemperatureSubType = subType }
                )
                Spacer(modifier = Modifier.width(8.dp))
                Column {
                    Text(
                        text = when (subType) {
                            TemperatureSubType.INCREASED -> "温度上升"
                            TemperatureSubType.DECREASED -> "温度下降"
                            TemperatureSubType.CHANGED -> "温度变化"
                        },
                        style = MaterialTheme.typography.bodyMedium
                    )
                    Text(
                        text = when (subType) {
                            TemperatureSubType.INCREASED -> "当电池温度上升超过指定阈值时触发"
                            TemperatureSubType.DECREASED -> "当电池温度下降超过指定阈值时触发"
                            TemperatureSubType.CHANGED -> "当电池温度变化超过指定阈值时触发"
                        },
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }

        Spacer(modifier = Modifier.height(24.dp))

        // 参数配置
        Text(
            text = "参数配置",
            style = MaterialTheme.typography.bodyLarge,
            modifier = Modifier.padding(bottom = 16.dp)
        )

        OutlinedTextField(
            value = temperatureThreshold,
            onValueChange = { temperatureThreshold = it },
            label = { Text("温度变化阈值 (°C)") },
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
            isError = temperatureThresholdError,
            supportingText = {
                if (temperatureThresholdError) {
                    Text("请输入 0.5-10.0 之间的数字")
                } else {
                    Text("当电池温度变化超过此值时触发")
                }
            },
            modifier = Modifier.fillMaxWidth()
        )

        Spacer(modifier = Modifier.height(24.dp))

        // 确认按钮
        Button(
            onClick = {
                val condition = BatteryStateCondition(
                    id = UUID.randomUUID().toString(),
                    conditionType = BatteryConditionType.BATTERY_TEMPERATURE,
                    levelSubType = BatteryLevelSubType.BELOW,
                    levelThreshold = 20,
                    levelChangeThreshold = 5,
                    chargingSubType = ChargingSubType.STARTED,
                    temperatureSubType = selectedTemperatureSubType,
                    temperatureThreshold = temperatureThreshold.toFloatOrNull() ?: 2.0f,
                    powerButtonSubType = PowerButtonSubType.PRESSED,
                    powerButtonPressCount = 1,
                    powerSaveModeSubType = PowerSaveModeSubType.ENABLED
                )
                onComplete(condition)
            },
            enabled = !temperatureThresholdError,
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 电源键配置内容组件
 *
 * 电源键事件不需要特殊权限，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
fun PowerButtonConfigContent(
    conditionType: BatteryConditionType,
    onComplete: (Any) -> Unit
) {
    var selectedPowerButtonSubType by rememberSaveable { mutableStateOf(PowerButtonSubType.PRESSED) }
    var powerButtonPressCount by rememberSaveable { mutableStateOf("1") }

    // 输入验证
    val powerButtonPressCountError = powerButtonPressCount.toIntOrNull()?.let { it < 1 || it > 10 } ?: true

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp)
    ) {
        // 子类型选择
        Text(
            text = "选择电源键事件类型",
            style = MaterialTheme.typography.bodyLarge,
            modifier = Modifier.padding(bottom = 16.dp)
        )

        PowerButtonSubType.values().forEach { subType ->
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable { selectedPowerButtonSubType = subType }
                    .padding(vertical = 8.dp)
            ) {
                RadioButton(
                    selected = selectedPowerButtonSubType == subType,
                    onClick = { selectedPowerButtonSubType = subType }
                )
                Spacer(modifier = Modifier.width(8.dp))
                Column {
                    Text(
                        text = when (subType) {
                            PowerButtonSubType.PRESSED -> "连续按下"
                        },
                        style = MaterialTheme.typography.bodyMedium
                    )
                    Text(
                        text = when (subType) {
                            PowerButtonSubType.PRESSED -> "当电源键连续按下指定次数时触发"
                        },
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }

        Spacer(modifier = Modifier.height(24.dp))

        // 参数配置
        Text(
            text = "参数配置",
            style = MaterialTheme.typography.bodyLarge,
            modifier = Modifier.padding(bottom = 16.dp)
        )

        OutlinedTextField(
            value = powerButtonPressCount,
            onValueChange = { powerButtonPressCount = it },
            label = { Text("按下次数") },
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
            isError = powerButtonPressCountError,
            supportingText = {
                if (powerButtonPressCountError) {
                    Text("请输入 1-10 之间的数字")
                } else {
                    Text("当电源键连续按下此次数时触发")
                }
            },
            modifier = Modifier.fillMaxWidth()
        )

        Spacer(modifier = Modifier.height(24.dp))

        // 确认按钮
        Button(
            onClick = {
                val condition = BatteryStateCondition(
                    id = UUID.randomUUID().toString(),
                    conditionType = BatteryConditionType.POWER_BUTTON,
                    levelSubType = BatteryLevelSubType.BELOW,
                    levelThreshold = 20,
                    levelChangeThreshold = 5,
                    chargingSubType = ChargingSubType.STARTED,
                    temperatureSubType = TemperatureSubType.CHANGED,
                    temperatureThreshold = 2.0f,
                    powerButtonSubType = selectedPowerButtonSubType,
                    powerButtonPressCount = powerButtonPressCount.toIntOrNull() ?: 1,
                    powerSaveModeSubType = PowerSaveModeSubType.ENABLED
                )
                onComplete(condition)
            },
            enabled = !powerButtonPressCountError,
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 省电模式配置内容组件
 *
 * 省电模式不需要特殊权限，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
fun PowerSaveModeConfigContent(
    conditionType: BatteryConditionType,
    onComplete: (Any) -> Unit
) {
    var selectedPowerSaveModeSubType by rememberSaveable { mutableStateOf(PowerSaveModeSubType.ENABLED) }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp)
    ) {
        // 子类型选择
        Text(
            text = "选择省电模式状态",
            style = MaterialTheme.typography.bodyLarge,
            modifier = Modifier.padding(bottom = 16.dp)
        )

        PowerSaveModeSubType.values().forEach { subType ->
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable { selectedPowerSaveModeSubType = subType }
                    .padding(vertical = 8.dp)
            ) {
                RadioButton(
                    selected = selectedPowerSaveModeSubType == subType,
                    onClick = { selectedPowerSaveModeSubType = subType }
                )
                Spacer(modifier = Modifier.width(8.dp))
                Column {
                    Text(
                        text = when (subType) {
                            PowerSaveModeSubType.ENABLED -> "启用"
                            PowerSaveModeSubType.DISABLED -> "禁用"
                        },
                        style = MaterialTheme.typography.bodyMedium
                    )
                    Text(
                        text = when (subType) {
                            PowerSaveModeSubType.ENABLED -> "当省电模式启用时触发"
                            PowerSaveModeSubType.DISABLED -> "当省电模式禁用时触发"
                        },
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }

        Spacer(modifier = Modifier.height(24.dp))

        Text(
            text = "此条件无需额外参数，将在省电模式状态变化时触发",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        Spacer(modifier = Modifier.height(24.dp))

        // 确认按钮
        Button(
            onClick = {
                val condition = BatteryStateCondition(
                    id = UUID.randomUUID().toString(),
                    conditionType = BatteryConditionType.POWER_SAVE_MODE,
                    levelSubType = BatteryLevelSubType.BELOW,
                    levelThreshold = 20,
                    levelChangeThreshold = 5,
                    chargingSubType = ChargingSubType.STARTED,
                    temperatureSubType = TemperatureSubType.CHANGED,
                    temperatureThreshold = 2.0f,
                    powerButtonSubType = PowerButtonSubType.PRESSED,
                    powerButtonPressCount = 1,
                    powerSaveModeSubType = selectedPowerSaveModeSubType
                )
                onComplete(condition)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}
