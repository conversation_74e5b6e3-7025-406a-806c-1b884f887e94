package com.weinuo.quickcommands22.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import com.weinuo.quickcommands22.ui.theme.manager.SkyBlueColorConfigurationManager
import com.weinuo.quickcommands22.ui.components.themed.ThemedCard

/**
 * 颜色设置项组件
 *
 * 用于在设置界面中配置颜色，支持：
 * - 颜色代码直接输入（十六进制格式）
 * - 实时颜色预览
 * - 颜色格式验证
 * - 立即生效的颜色更新
 *
 * 设计原则：
 * - 用户可以直接输入颜色代码，自由度更高
 * - 提供实时的颜色预览，方便用户确认
 * - 支持多种颜色格式输入（0xFF123456、#123456、123456）
 * - 输入错误时显示错误提示，不会破坏应用
 * - 简洁布局，不显示描述文字以保持界面整洁
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ColorSettingItem(
    title: String,
    currentColorString: String,
    onColorChange: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    var colorText by remember(currentColorString) { mutableStateOf(currentColorString) }
    var isError by remember { mutableStateOf(false) }
    
    // 解析当前颜色用于预览
    val previewColor = remember(colorText) {
        if (SkyBlueColorConfigurationManager.isValidColorString(colorText)) {
            SkyBlueColorConfigurationManager.parseColorString(colorText)
        } else {
            SkyBlueColorConfigurationManager.parseColorString(currentColorString)
        }
    }

    Column(
        modifier = modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        // 标题（删除描述以保持简洁布局）
        Text(
            text = title,
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onSurface
        )

        // 颜色输入和预览
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 颜色输入框
            OutlinedTextField(
                value = colorText,
                onValueChange = { newValue ->
                    colorText = newValue
                    val isValid = SkyBlueColorConfigurationManager.isValidColorString(newValue)
                    isError = !isValid
                    if (isValid) {
                        onColorChange(newValue)
                    }
                },
                label = { Text("颜色代码") },
                placeholder = { Text("0xFF123456") },
                isError = isError,
                supportingText = if (isError) {
                    { Text("请输入有效的颜色代码（如：0xFF123456、#123456）") }
                } else null,
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Text),
                modifier = Modifier.weight(1f),
                singleLine = true
            )

            // 颜色预览
            Box(
                modifier = Modifier
                    .size(48.dp)
                    .background(
                        color = previewColor,
                        shape = RoundedCornerShape(8.dp)
                    )
                    .border(
                        width = 1.dp,
                        color = MaterialTheme.colorScheme.outline,
                        shape = RoundedCornerShape(8.dp)
                    )
            )
        }

        // 格式说明
        Text(
            text = "支持格式：0xFF123456、#123456、123456",
            style = MaterialTheme.typography.labelSmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

/**
 * 颜色设置分组组件
 *
 * 用于将相关的颜色设置项组织在一起，提供更好的视觉层次
 */
@Composable
fun ColorSettingGroup(
    title: String,
    description: String? = null,
    modifier: Modifier = Modifier,
    content: @Composable ColumnScope.() -> Unit
) {
    ThemedCard(
        modifier = modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 分组标题和描述
            Column(
                verticalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                Text(
                    text = title,
                    style = MaterialTheme.typography.titleMedium,
                    color = MaterialTheme.colorScheme.onSurface
                )
                if (description != null) {
                    Text(
                        text = description,
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }

            // 分组内容
            content()
        }
    }
}

/**
 * 颜色重置按钮组件
 *
 * 提供重置颜色配置为默认值的功能
 */
@Composable
fun ColorResetButton(
    onReset: () -> Unit,
    modifier: Modifier = Modifier
) {
    OutlinedButton(
        onClick = onReset,
        modifier = modifier
    ) {
        Text("重置为默认颜色")
    }
}

/**
 * 颜色配置预览卡片
 *
 * 显示当前颜色配置的整体预览效果
 */
@Composable
fun ColorConfigurationPreview(
    title: String,
    primaryColor: Color,
    backgroundColor: Color,
    surfaceColor: Color,
    modifier: Modifier = Modifier
) {
    ThemedCard(
        modifier = modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.titleMedium,
                color = MaterialTheme.colorScheme.onSurface
            )
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                // 主色预览
                Box(
                    modifier = Modifier
                        .size(32.dp)
                        .background(
                            color = primaryColor,
                            shape = RoundedCornerShape(6.dp)
                        )
                        .border(
                            width = 1.dp,
                            color = MaterialTheme.colorScheme.outline,
                            shape = RoundedCornerShape(6.dp)
                        )
                )
                
                // 背景色预览
                Box(
                    modifier = Modifier
                        .size(32.dp)
                        .background(
                            color = backgroundColor,
                            shape = RoundedCornerShape(6.dp)
                        )
                        .border(
                            width = 1.dp,
                            color = MaterialTheme.colorScheme.outline,
                            shape = RoundedCornerShape(6.dp)
                        )
                )
                
                // 表面色预览
                Box(
                    modifier = Modifier
                        .size(32.dp)
                        .background(
                            color = surfaceColor,
                            shape = RoundedCornerShape(6.dp)
                        )
                        .border(
                            width = 1.dp,
                            color = MaterialTheme.colorScheme.outline,
                            shape = RoundedCornerShape(6.dp)
                        )
                )
            }
            
            Text(
                text = "预览效果会在保存后立即生效",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}
