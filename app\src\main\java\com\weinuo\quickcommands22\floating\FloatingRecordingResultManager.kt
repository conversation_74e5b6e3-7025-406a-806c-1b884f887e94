package com.weinuo.quickcommands22.floating

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow

/**
 * 悬浮窗录制结果管理器
 *
 * 负责管理悬浮窗录制的结果数据，提供轮询机制来检查录制完成状态。
 */
class FloatingRecordingResultManager(private val context: Context) {

    companion object {
        private const val TAG = "FloatingRecordingResultManager"
        private const val PREFS_NAME = "floating_recording_result"
        private const val KEY_LATEST_RECORDING_ID = "latest_recording_id"
        private const val KEY_RECORDING_TIMESTAMP = "recording_timestamp"
        private const val KEY_CONSUMED_TIMESTAMP = "consumed_timestamp"
        private const val KEY_EDIT_COMPLETED = "edit_completed"

        // 轮询间隔（毫秒）
        private const val POLLING_INTERVAL = 500L

        // 录制结果有效期（毫秒）- 2分钟
        private const val RESULT_VALIDITY_DURATION = 2 * 60 * 1000L
    }

    private val sharedPrefs: SharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)

    /**
     * 轮询检查是否有新的录制结果
     *
     * @return Flow<String?> 录制ID的流，null表示没有新结果
     */
    fun pollForRecordingResult(): Flow<String?> = flow {
        var lastConsumedTimestamp = getCurrentConsumedTimestamp()

        while (true) {
            try {
                val recordingId = checkForNewRecording(lastConsumedTimestamp)
                if (recordingId != null) {
                    // 发现新的录制结果
                    Log.d(TAG, "发现新的录制结果: $recordingId")

                    // 标记为已消费
                    markAsConsumed()
                    lastConsumedTimestamp = System.currentTimeMillis()

                    emit(recordingId)
                } else {
                    emit(null)
                }
            } catch (e: Exception) {
                Log.e(TAG, "轮询录制结果时发生错误", e)
                emit(null)
            }

            delay(POLLING_INTERVAL)
        }
    }

    /**
     * 检查是否有新的录制结果或重新录制请求
     *
     * @param lastConsumedTimestamp 上次消费的时间戳
     * @return 录制ID，如果没有新结果则返回null，如果是重新录制请求则返回空字符串
     */
    private fun checkForNewRecording(lastConsumedTimestamp: Long): String? {
        val recordingTimestamp = sharedPrefs.getLong(KEY_RECORDING_TIMESTAMP, 0L)
        val recordingId = sharedPrefs.getString(KEY_LATEST_RECORDING_ID, null)
        val editCompleted = sharedPrefs.getBoolean(KEY_EDIT_COMPLETED, false)
        val reRecordRequested = sharedPrefs.getBoolean("re_record_requested", false)

        // 检查是否有重新录制请求
        if (reRecordRequested && recordingTimestamp > lastConsumedTimestamp) {
            Log.d(TAG, "检测到重新录制请求")
            // 只清除重新录制标志，保留其他数据以避免悬浮窗服务意外关闭
            sharedPrefs.edit()
                .remove("re_record_requested")
                .apply()
            return "" // 返回空字符串表示重新录制
        }

        // 检查是否有有效的录制结果
        if (recordingId.isNullOrEmpty() || recordingTimestamp == 0L) {
            return null
        }

        // 检查录制结果是否过期
        val currentTime = System.currentTimeMillis()
        if (currentTime - recordingTimestamp > RESULT_VALIDITY_DURATION) {
            Log.d(TAG, "录制结果已过期，清理数据")
            clearRecordingResult()
            return null
        }

        // 检查是否是新的录制结果
        // 如果是悬浮窗录制且已完成编辑，或者是新的录制结果，则返回录制ID
        if (recordingTimestamp > lastConsumedTimestamp) {
            // 如果编辑已完成，清除编辑完成标记
            if (editCompleted) {
                sharedPrefs.edit()
                    .remove(KEY_EDIT_COMPLETED)
                    .apply()
                Log.d(TAG, "检测到编辑完成的录制结果: $recordingId")
            }
            return recordingId
        }

        return null
    }

    /**
     * 获取当前已消费的时间戳
     */
    private fun getCurrentConsumedTimestamp(): Long {
        return sharedPrefs.getLong(KEY_CONSUMED_TIMESTAMP, 0L)
    }

    /**
     * 标记录制结果为已消费
     */
    private fun markAsConsumed() {
        sharedPrefs.edit()
            .putLong(KEY_CONSUMED_TIMESTAMP, System.currentTimeMillis())
            .apply()
    }

    /**
     * 清理录制结果数据
     */
    private fun clearRecordingResult() {
        sharedPrefs.edit()
            .remove(KEY_LATEST_RECORDING_ID)
            .remove(KEY_RECORDING_TIMESTAMP)
            .remove(KEY_EDIT_COMPLETED)
            .apply()
    }

    /**
     * 清理所有录制结果数据
     * 供外部调用，用于清理临时数据
     */
    fun clearAllData() {
        sharedPrefs.edit().clear().apply()
        Log.d(TAG, "已清理所有悬浮窗录制结果数据")
    }


}
