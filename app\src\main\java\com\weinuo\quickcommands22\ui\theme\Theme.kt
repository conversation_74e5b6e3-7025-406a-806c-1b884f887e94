package com.weinuo.quickcommands22.ui.theme

import android.app.Activity
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.SideEffect
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.LocalView
import androidx.core.view.WindowCompat
import com.weinuo.quickcommands22.ui.theme.provider.AppThemeProvider
import com.weinuo.quickcommands22.ui.theme.manager.LocalThemeContext

// 注意：旧的静态颜色方案已移除，现在使用动态主题系统

/**
 * QuickCommands应用主题
 *
 * 集成了完整的主题系统，包括主题感知组件和动态颜色方案
 */
@Composable
fun QuickCommandsTheme(
    content: @Composable () -> Unit
) {
    AppThemeProvider {
        val themeContext = LocalThemeContext.current
        val colorScheme = themeContext.colorScheme

        // 设置状态栏为透明并配置沉浸式体验
        val view = LocalView.current
        if (!view.isInEditMode) {
            SideEffect {
                val window = (view.context as Activity).window
                // 设置状态栏为透明
                window.statusBarColor = Color.Transparent.toArgb()
                // 告诉系统窗口内容将延伸到状态栏区域
                WindowCompat.setDecorFitsSystemWindows(window, false)
                // 设置状态栏图标为亮色（因为我们的应用背景是浅色）
                WindowCompat.getInsetsController(window, view).apply {
                    isAppearanceLightStatusBars = true
                }
            }
        }

        MaterialTheme(
            colorScheme = colorScheme,
            typography = Typography,
            content = content
        )
    }
}

// 传统版本已移除，现在只使用主题感知版本