package com.weinuo.quickcommands22.ui.screens.themed

import androidx.compose.runtime.Composable
import com.weinuo.quickcommands22.data.SettingsRepository
import com.weinuo.quickcommands22.ui.theme.manager.LocalThemeContext
import com.weinuo.quickcommands22.utils.ExperimentalFeatureDetector

/**
 * 主题感知的全局设置界面
 *
 * 根据当前主题自动选择合适的界面实现：
 * - 海洋蓝主题：使用OceanBlueGlobalSettingsScreen（分层设计风格）
 * - 天空蓝主题：使用SkyBlueGlobalSettingsScreen（整合设计风格，包含模糊效果设置）
 * - 未来主题：可以使用各自独有的实现
 *
 * 这是主题系统的核心界面组件之一，允许不同主题有完全不同的设置界面，
 * 比如天空蓝主题可以包含模糊效果设置等特有功能。
 */
@Composable
fun ThemedGlobalSettingsScreen(
    settingsRepository: SettingsRepository,
    experimentalFeatureDetector: ExperimentalFeatureDetector? = null
) {
    val themeContext = LocalThemeContext.current
    val screenFactory = themeContext.theme.themeProvider.getScreenFactory()

    // 使用界面工厂创建主题特定的界面实现
    screenFactory.createGlobalSettingsScreen(
        settingsRepository = settingsRepository,
        experimentalFeatureDetector = experimentalFeatureDetector
    )
}
