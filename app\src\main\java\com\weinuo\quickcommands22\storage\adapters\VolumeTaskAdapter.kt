package com.weinuo.quickcommands22.storage.adapters

import android.util.Log
import com.weinuo.quickcommands22.model.*
import com.weinuo.quickcommands22.storage.NativeTypeStorageManager
import com.weinuo.quickcommands22.storage.StorageDomain
import com.weinuo.quickcommands22.storage.StorageOperation

/**
 * 音量任务存储适配器
 *
 * 负责VolumeTask的原生数据类型存储和重建。
 * 将复杂的音量任务对象拆分为独立的原生字段进行存储，
 * 完全避免JSON序列化问题。
 *
 * 支持的字段类型：
 * - 基础字段：id、operation
 * - 免提通话相关：speakerphoneOperation
 * - 振动模式相关：vibrationModeType
 * - 音量弹出窗口相关：volumePopupType
 * - 勿扰模式相关：doNotDisturbMode、disableVibrationWhenSilent
 * - 音量变化相关：volumeStreamType、volumeMode、volumeValue
 * - 音量调节相关：volumeAdjustOperation、volumeAdjustStreamType
 *
 * 存储格式示例：
 * task_{id}_type = "volume"
 * task_{id}_operation = "VOLUME_CHANGE"
 * task_{id}_speakerphone_operation = "TOGGLE"
 * task_{id}_vibration_mode_type = "SILENT_WITH_VIBRATION"
 * task_{id}_volume_popup_type = "MEDIA_MUSIC"
 * task_{id}_do_not_disturb_mode = "NORMAL"
 * task_{id}_disable_vibration_when_silent = false
 * task_{id}_volume_stream_type = "MEDIA_MUSIC"
 * task_{id}_volume_mode = "PERCENTAGE"
 * task_{id}_volume_value = 50
 * task_{id}_volume_adjust_operation = "VOLUME_UP"
 * task_{id}_volume_adjust_stream_type = "MEDIA_MUSIC"
 *
 * @param storageManager 原生类型存储管理器
 */
class VolumeTaskAdapter(
    storageManager: NativeTypeStorageManager
) : BaseTaskAdapter<VolumeTask>(storageManager) {

    companion object {
        private const val TAG = "VolumeTaskAdapter"

        // 字段名常量
        private const val FIELD_OPERATION = "operation"
        private const val FIELD_SPEAKERPHONE_OPERATION = "speakerphone_operation"
        private const val FIELD_VIBRATION_MODE_TYPE = "vibration_mode_type"
        private const val FIELD_VOLUME_POPUP_TYPE = "volume_popup_type"
        private const val FIELD_DO_NOT_DISTURB_MODE = "do_not_disturb_mode"
        private const val FIELD_DISABLE_VIBRATION_WHEN_SILENT = "disable_vibration_when_silent"
        private const val FIELD_VOLUME_STREAM_TYPE = "volume_stream_type"
        private const val FIELD_VOLUME_MODE = "volume_mode"
        private const val FIELD_VOLUME_VALUE = "volume_value"
        private const val FIELD_VOLUME_ADJUST_OPERATION = "volume_adjust_operation"
        private const val FIELD_VOLUME_ADJUST_STREAM_TYPE = "volume_adjust_stream_type"
    }

    override fun getTaskType() = "volume"

    /**
     * 保存音量任务
     * 将VolumeTask的所有字段拆分为原生数据类型存储
     *
     * @param task 要保存的音量任务
     * @return 操作是否成功
     */
    override fun save(task: VolumeTask): Boolean {
        if (!isValidTaskId(task.id)) {
            logSaveError(task.id, "Invalid task ID")
            return false
        }

        Log.d(TAG, "开始保存音量任务: ${task.id}")

        return try {
            val operations = mutableListOf<StorageOperation>()

            // 保存基础字段
            operations.addAll(saveBaseFields(task.id, task))

            // 保存音量任务特有字段
            operations.addAll(listOf(
                saveEnum(generateKey(task.id, FIELD_OPERATION), task.operation),
                saveEnum(generateKey(task.id, FIELD_SPEAKERPHONE_OPERATION), task.speakerphoneOperation),
                saveEnum(generateKey(task.id, FIELD_VIBRATION_MODE_TYPE), task.vibrationModeType),
                saveEnum(generateKey(task.id, FIELD_VOLUME_POPUP_TYPE), task.volumePopupType),
                saveEnum(generateKey(task.id, FIELD_DO_NOT_DISTURB_MODE), task.doNotDisturbMode),
                StorageOperation.createBooleanOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_DISABLE_VIBRATION_WHEN_SILENT),
                    task.disableVibrationWhenSilent
                ),
                saveEnum(generateKey(task.id, FIELD_VOLUME_STREAM_TYPE), task.volumeStreamType),
                saveEnum(generateKey(task.id, FIELD_VOLUME_MODE), task.volumeMode),
                StorageOperation.createIntOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_VOLUME_VALUE),
                    task.volumeValue
                ),
                saveEnum(generateKey(task.id, FIELD_VOLUME_ADJUST_OPERATION), task.volumeAdjustOperation),
                saveEnum(generateKey(task.id, FIELD_VOLUME_ADJUST_STREAM_TYPE), task.volumeAdjustStreamType)
            ))

            val success = storageManager.executeBatch(operations)
            if (success) {
                Log.d(TAG, "音量任务保存成功: ${task.id}")
            } else {
                logSaveError(task.id, "Batch operation failed")
            }
            success

        } catch (e: Exception) {
            logSaveError(task.id, "Exception during save: ${e.message}")
            Log.e(TAG, "Exception while saving VolumeTask: ${task.id}", e)
            false
        }
    }

    /**
     * 加载音量任务
     * 从原生数据类型重建VolumeTask对象
     *
     * @param taskId 任务ID
     * @return 加载的音量任务，失败时返回null
     */
    override fun load(taskId: String): VolumeTask? {
        if (!isValidTaskId(taskId)) {
            logLoadError(taskId, "Invalid task ID")
            return null
        }

        Log.d(TAG, "开始加载音量任务: $taskId")

        return try {
            // 检查任务是否存在
            if (!exists(taskId)) {
                Log.d(TAG, "音量任务不存在: $taskId")
                return null
            }

            VolumeTask(
                id = taskId,
                operation = loadEnum(generateKey(taskId, FIELD_OPERATION)) { VolumeOperation.valueOf(it) }
                    ?: VolumeOperation.VOLUME_CHANGE,
                speakerphoneOperation = loadEnum(generateKey(taskId, FIELD_SPEAKERPHONE_OPERATION)) { SpeakerphoneOperation.valueOf(it) }
                    ?: SpeakerphoneOperation.TOGGLE,
                vibrationModeType = loadEnum(generateKey(taskId, FIELD_VIBRATION_MODE_TYPE)) { VibrationModeType.valueOf(it) }
                    ?: VibrationModeType.SILENT_WITH_VIBRATION,
                volumePopupType = loadEnum(generateKey(taskId, FIELD_VOLUME_POPUP_TYPE)) { VolumePopupType.valueOf(it) }
                    ?: VolumePopupType.MEDIA_MUSIC,
                doNotDisturbMode = loadEnum(generateKey(taskId, FIELD_DO_NOT_DISTURB_MODE)) { DoNotDisturbMode.valueOf(it) }
                    ?: DoNotDisturbMode.NORMAL,
                disableVibrationWhenSilent = storageManager.loadBoolean(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_DISABLE_VIBRATION_WHEN_SILENT),
                    false
                ),
                volumeStreamType = loadEnum(generateKey(taskId, FIELD_VOLUME_STREAM_TYPE)) { VolumeStreamType.valueOf(it) }
                    ?: VolumeStreamType.MEDIA_MUSIC,
                volumeMode = loadEnum(generateKey(taskId, FIELD_VOLUME_MODE)) { VolumeMode.valueOf(it) }
                    ?: VolumeMode.PERCENTAGE,
                volumeValue = storageManager.loadInt(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_VOLUME_VALUE),
                    50
                ),
                volumeAdjustOperation = loadEnum(generateKey(taskId, FIELD_VOLUME_ADJUST_OPERATION)) { VolumeAdjustOperation.valueOf(it) }
                    ?: VolumeAdjustOperation.VOLUME_UP,
                volumeAdjustStreamType = loadEnum(generateKey(taskId, FIELD_VOLUME_ADJUST_STREAM_TYPE)) { VolumeStreamType.valueOf(it) }
                    ?: VolumeStreamType.MEDIA_MUSIC
            ).also {
                Log.d(TAG, "音量任务加载成功: $taskId")
            }

        } catch (e: Exception) {
            logLoadError(taskId, "Exception during load: ${e.message}")
            Log.e(TAG, "Exception while loading VolumeTask: $taskId", e)
            null
        }
    }
}
