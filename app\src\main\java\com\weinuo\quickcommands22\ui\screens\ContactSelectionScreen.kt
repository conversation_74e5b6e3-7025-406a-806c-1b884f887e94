package com.weinuo.quickcommands22.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.icons.filled.Error
import androidx.compose.material.icons.filled.Group
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.sp
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import com.weinuo.quickcommands22.utils.ContactsHelper
import com.weinuo.quickcommands22.utils.ContactsCacheManager
import com.weinuo.quickcommands22.ui.components.themed.ThemedSearchTextField
import com.weinuo.quickcommands22.ui.components.themed.ThemedRadioButton
import com.weinuo.quickcommands22.ui.theme.config.RadioButtonConfig
import com.weinuo.quickcommands22.data.SettingsRepository
import com.weinuo.quickcommands22.ui.theme.manager.ThemeManager
import kotlinx.coroutines.launch

/**
 * 联系人选择模式枚举
 */
enum class ContactSelectionMode {
    SINGLE,  // 单选模式
    MULTI    // 多选模式
}

/**
 * 联系人分组选择模式枚举
 */
enum class ContactGroupSelectionMode {
    SINGLE  // 单选模式（分组选择通常只需要单选）
}

/**
 * 可复用的联系人选择界面
 *
 * 提供联系人列表选择界面，支持搜索功能和单选/多选模式
 * 使用全屏界面替代对话框，提供更好的用户体验
 * 可以被多个配置提供器复用，避免代码重复
 * 使用按需加载策略和智能缓存，避免不必要的电量消耗
 *
 * @param selectionMode 选择模式（单选/多选）
 * @param initialSelectedContactIds 初始选中的联系人ID列表（多选模式使用）
 * @param onContactsSelected 联系人选择完成回调，返回选中的联系人列表
 * @param onDismiss 取消回调
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ContactSelectionScreen(
    selectionMode: ContactSelectionMode,
    initialSelectedContactIds: List<String> = emptyList(),
    onContactsSelected: (List<ContactsHelper.ContactInfo>) -> Unit,
    onDismiss: () -> Unit
) {
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()
    val focusManager = LocalFocusManager.current

    // 获取主题管理器和设置仓库
    val themeManager = remember { ThemeManager.getInstance(context) }
    val settingsRepository = remember { SettingsRepository(context) }
    val globalSettings by settingsRepository.globalSettings.collectAsState()

    // 主题感知的标题样式
    val themeAwareTitleStyle = if (themeManager.getCurrentThemeId() == "sky_blue") {
        // 天空蓝主题：使用全局设置的标题字重和字体大小
        MaterialTheme.typography.titleLarge.copy(
            fontWeight = when (globalSettings.topAppBarTitleFontWeight) {
                "normal" -> FontWeight.Normal
                "medium" -> FontWeight.Medium
                "bold" -> FontWeight.Bold
                else -> FontWeight.Medium
            },
            fontSize = globalSettings.screenTitleFontSize.sp
        )
    } else {
        // 海洋蓝主题：保持原有样式
        MaterialTheme.typography.titleLarge
    }

    // 状态管理
    var contacts by remember { mutableStateOf<List<ContactsHelper.ContactInfo>>(emptyList()) }
    var isLoading by remember { mutableStateOf(true) }
    var searchQuery by remember { mutableStateOf("") }
    var selectedContactIds by remember { mutableStateOf(initialSelectedContactIds.toSet()) }
    var errorMessage by remember { mutableStateOf<String?>(null) }

    // 加载联系人列表 - 使用按需加载和智能缓存
    LaunchedEffect(Unit) {
        coroutineScope.launch {
            try {
                contacts = ContactsCacheManager.getContacts(context)
                errorMessage = null
            } catch (e: Exception) {
                errorMessage = "加载联系人失败：${e.message}"
            } finally {
                isLoading = false
            }
        }
    }

    // 过滤联系人列表
    val filteredContacts = remember(contacts, searchQuery) {
        if (searchQuery.isBlank()) {
            contacts
        } else {
            contacts.filter { contact ->
                contact.name.contains(searchQuery, ignoreCase = true) ||
                contact.phoneNumber.contains(searchQuery, ignoreCase = true)
            }
        }
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = when (selectionMode) {
                            ContactSelectionMode.SINGLE -> "选择联系人"
                            ContactSelectionMode.MULTI -> "选择联系人 (${selectedContactIds.size})"
                        },
                        style = themeAwareTitleStyle
                    )
                },
                navigationIcon = {
                    IconButton(onClick = onDismiss) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "返回"
                        )
                    }
                },
                actions = {
                    // 多选模式下显示确认按钮
                    if (selectionMode == ContactSelectionMode.MULTI && selectedContactIds.isNotEmpty()) {
                        IconButton(
                            onClick = {
                                val selectedContacts = contacts.filter { it.id in selectedContactIds }
                                onContactsSelected(selectedContacts)
                            }
                        ) {
                            Icon(
                                imageVector = Icons.Default.Check,
                                contentDescription = "确认选择"
                            )
                        }
                    }
                }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            // 搜索框
            ThemedSearchTextField(
                searchQuery = searchQuery,
                onSearchQueryChange = { searchQuery = it },
                onClearSearch = {
                    searchQuery = ""
                    focusManager.clearFocus()
                },
                placeholder = "搜索联系人姓名或号码",
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp)
            )

            when {
                isLoading -> {
                    // 加载状态
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally,
                            verticalArrangement = Arrangement.spacedBy(16.dp)
                        ) {
                            CircularProgressIndicator()
                            Text("正在加载联系人...")
                        }
                    }
                }

                errorMessage != null -> {
                    // 错误状态
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally,
                            verticalArrangement = Arrangement.spacedBy(16.dp)
                        ) {
                            Text(
                                text = errorMessage!!,
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.error
                            )
                            Button(
                                onClick = {
                                    isLoading = true
                                    errorMessage = null
                                    coroutineScope.launch {
                                        try {
                                            contacts = ContactsCacheManager.refreshContacts(context)
                                        } catch (e: Exception) {
                                            errorMessage = "加载联系人失败：${e.message}"
                                        } finally {
                                            isLoading = false
                                        }
                                    }
                                }
                            ) {
                                Text("重试")
                            }
                        }
                    }
                }

                filteredContacts.isEmpty() -> {
                    // 空状态
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = if (searchQuery.isBlank()) "没有联系人" else "没有找到匹配的联系人",
                            style = MaterialTheme.typography.bodyLarge,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }

                else -> {
                    // 联系人列表
                    LazyColumn(
                        modifier = Modifier.fillMaxSize(),
                        contentPadding = PaddingValues(horizontal = 16.dp, vertical = 8.dp),
                        verticalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        items(filteredContacts) { contact ->
                            ContactItem(
                                contact = contact,
                                isSelected = contact.id in selectedContactIds,
                                selectionMode = selectionMode,
                                onContactClick = { clickedContact ->
                                    when (selectionMode) {
                                        ContactSelectionMode.SINGLE -> {
                                            // 单选模式：直接返回选中的联系人
                                            onContactsSelected(listOf(clickedContact))
                                        }
                                        ContactSelectionMode.MULTI -> {
                                            // 多选模式：切换选中状态
                                            selectedContactIds = if (clickedContact.id in selectedContactIds) {
                                                selectedContactIds - clickedContact.id
                                            } else {
                                                selectedContactIds + clickedContact.id
                                            }
                                        }
                                    }
                                }
                            )
                        }
                    }
                }
            }
        }
    }
}

/**
 * 联系人列表项组件
 *
 * @param contact 联系人信息
 * @param isSelected 是否选中
 * @param selectionMode 选择模式
 * @param onContactClick 点击回调
 */
@Composable
private fun ContactItem(
    contact: ContactsHelper.ContactInfo,
    isSelected: Boolean,
    selectionMode: ContactSelectionMode,
    onContactClick: (ContactsHelper.ContactInfo) -> Unit
) {
    Surface(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onContactClick(contact) },
        shape = RoundedCornerShape(12.dp),
        color = if (isSelected) {
            MaterialTheme.colorScheme.primaryContainer
        } else {
            MaterialTheme.colorScheme.surfaceContainerLow
        },

    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 联系人头像占位符
            Box(
                modifier = Modifier
                    .size(48.dp)
                    .background(
                        if (isSelected) {
                            MaterialTheme.colorScheme.primary
                        } else {
                            MaterialTheme.colorScheme.primary.copy(alpha = 0.1f)
                        },
                        CircleShape
                    ),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = contact.name.take(1).uppercase(),
                    style = MaterialTheme.typography.titleMedium,
                    color = if (isSelected) {
                        MaterialTheme.colorScheme.onPrimary
                    } else {
                        MaterialTheme.colorScheme.primary
                    }
                )
            }

            Spacer(modifier = Modifier.width(16.dp))

            // 联系人信息
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = contact.name,
                    style = MaterialTheme.typography.titleMedium,
                    color = if (isSelected) {
                        MaterialTheme.colorScheme.onPrimaryContainer
                    } else {
                        MaterialTheme.colorScheme.onSurface
                    },
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
                Text(
                    text = contact.phoneNumber,
                    style = MaterialTheme.typography.bodyMedium,
                    color = if (isSelected) {
                        MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.7f)
                    } else {
                        MaterialTheme.colorScheme.onSurfaceVariant
                    },
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
            }

            // 多选模式下显示选中状态指示器
            if (selectionMode == ContactSelectionMode.MULTI) {
                Spacer(modifier = Modifier.width(8.dp))
                if (isSelected) {
                    Icon(
                        imageVector = Icons.Default.Check,
                        contentDescription = "已选中",
                        tint = MaterialTheme.colorScheme.primary,
                        modifier = Modifier.size(24.dp)
                    )
                }
            }
        }
    }
}

/**
 * 可复用的联系人分组选择界面
 *
 * 提供联系人分组列表选择界面，支持搜索功能和单选模式
 * 使用全屏界面替代对话框，提供更好的用户体验
 * 可以被多个配置提供器复用，避免代码重复
 *
 * @param selectionMode 选择模式（目前只支持单选）
 * @param initialSelectedGroupId 初始选中的分组ID
 * @param onGroupSelected 分组选择完成回调，返回选中的分组
 * @param onDismiss 取消回调
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ContactGroupSelectionScreen(
    selectionMode: ContactGroupSelectionMode = ContactGroupSelectionMode.SINGLE,
    initialSelectedGroupId: String = "",
    onGroupSelected: (ContactsHelper.ContactGroup) -> Unit,
    onDismiss: () -> Unit
) {
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()
    val focusManager = LocalFocusManager.current

    // 获取主题管理器和设置仓库
    val themeManager = remember { ThemeManager.getInstance(context) }
    val settingsRepository = remember { SettingsRepository(context) }
    val globalSettings by settingsRepository.globalSettings.collectAsState()

    // 主题感知的标题样式
    val themeAwareTitleStyle = if (themeManager.getCurrentThemeId() == "sky_blue") {
        // 天空蓝主题：使用全局设置的标题字重和字体大小
        MaterialTheme.typography.titleLarge.copy(
            fontWeight = when (globalSettings.topAppBarTitleFontWeight) {
                "normal" -> FontWeight.Normal
                "medium" -> FontWeight.Medium
                "bold" -> FontWeight.Bold
                else -> FontWeight.Medium
            },
            fontSize = globalSettings.screenTitleFontSize.sp
        )
    } else {
        // 海洋蓝主题：保持原有样式
        MaterialTheme.typography.titleLarge
    }

    // 状态管理
    var groups by remember { mutableStateOf<List<ContactsHelper.ContactGroup>>(emptyList()) }
    var isLoading by remember { mutableStateOf(true) }
    var errorMessage by remember { mutableStateOf<String?>(null) }
    var searchQuery by remember { mutableStateOf("") }
    var selectedGroupId by remember { mutableStateOf(initialSelectedGroupId) }

    // 过滤后的分组列表
    val filteredGroups = remember(groups, searchQuery) {
        if (searchQuery.isBlank()) {
            groups
        } else {
            groups.filter { group ->
                group.title.contains(searchQuery, ignoreCase = true)
            }
        }
    }

    // 加载联系人分组数据 - 使用按需加载和智能缓存
    LaunchedEffect(Unit) {
        coroutineScope.launch {
            try {
                isLoading = true
                errorMessage = null
                // 注意：联系人分组暂时没有专门的缓存管理器，直接调用
                // 如果需要频繁使用，可以考虑添加ContactGroupsCacheManager
                val loadedGroups = ContactsHelper.getContactGroups(context)
                groups = loadedGroups
            } catch (e: Exception) {
                errorMessage = "加载联系人分组失败: ${e.message}"
            } finally {
                isLoading = false
            }
        }
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = when (selectionMode) {
                            ContactGroupSelectionMode.SINGLE -> "选择联系人分组"
                        },
                        style = themeAwareTitleStyle
                    )
                },
                navigationIcon = {
                    IconButton(onClick = onDismiss) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "返回"
                        )
                    }
                },
                actions = {
                    // 单选模式下，如果有选中项，显示确认按钮
                    if (selectionMode == ContactGroupSelectionMode.SINGLE && selectedGroupId.isNotEmpty()) {
                        IconButton(
                            onClick = {
                                val selectedGroup = groups.find { it.id == selectedGroupId }
                                if (selectedGroup != null) {
                                    onGroupSelected(selectedGroup)
                                }
                            }
                        ) {
                            Icon(
                                imageVector = Icons.Default.Check,
                                contentDescription = "确认选择"
                            )
                        }
                    }
                }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            // 搜索框
            ThemedSearchTextField(
                searchQuery = searchQuery,
                onSearchQueryChange = { searchQuery = it },
                onClearSearch = {
                    searchQuery = ""
                    focusManager.clearFocus()
                },
                placeholder = "搜索分组名称",
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp)
            )

            // 内容区域
            when {
                isLoading -> {
                    // 加载状态
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally,
                            verticalArrangement = Arrangement.spacedBy(16.dp)
                        ) {
                            CircularProgressIndicator()
                            Text(
                                text = "正在加载联系人分组...",
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }
                }

                errorMessage != null -> {
                    // 错误状态
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally,
                            verticalArrangement = Arrangement.spacedBy(16.dp)
                        ) {
                            Icon(
                                imageVector = Icons.Default.Error,
                                contentDescription = "错误",
                                tint = MaterialTheme.colorScheme.error,
                                modifier = Modifier.size(48.dp)
                            )
                            Text(
                                text = errorMessage!!,
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.error,
                                textAlign = TextAlign.Center
                            )
                            Button(
                                onClick = {
                                    coroutineScope.launch {
                                        try {
                                            isLoading = true
                                            errorMessage = null
                                            // 重新加载联系人分组
                                            val loadedGroups = ContactsHelper.getContactGroups(context)
                                            groups = loadedGroups
                                        } catch (e: Exception) {
                                            errorMessage = "加载联系人分组失败: ${e.message}"
                                        } finally {
                                            isLoading = false
                                        }
                                    }
                                }
                            ) {
                                Text("重试")
                            }
                        }
                    }
                }

                filteredGroups.isEmpty() -> {
                    // 空状态
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally,
                            verticalArrangement = Arrangement.spacedBy(16.dp)
                        ) {
                            Icon(
                                imageVector = Icons.Default.Group,
                                contentDescription = "无分组",
                                tint = MaterialTheme.colorScheme.onSurfaceVariant,
                                modifier = Modifier.size(48.dp)
                            )
                            Text(
                                text = if (searchQuery.isNotEmpty()) {
                                    "未找到匹配的联系人分组"
                                } else {
                                    "暂无可用的联系人分组"
                                },
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onSurfaceVariant,
                                textAlign = TextAlign.Center
                            )
                            if (searchQuery.isNotEmpty()) {
                                TextButton(onClick = { searchQuery = "" }) {
                                    Text("清除搜索条件")
                                }
                            }
                        }
                    }
                }

                else -> {
                    // 分组列表
                    LazyColumn(
                        modifier = Modifier.weight(1f),
                        contentPadding = PaddingValues(horizontal = 16.dp, vertical = 8.dp),
                        verticalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        items(filteredGroups) { group ->
                            ContactGroupItem(
                                group = group,
                                isSelected = group.id == selectedGroupId,
                                selectionMode = selectionMode,
                                onGroupClick = { clickedGroup ->
                                    when (selectionMode) {
                                        ContactGroupSelectionMode.SINGLE -> {
                                            // 单选模式：直接返回选中的分组
                                            onGroupSelected(clickedGroup)
                                        }
                                    }
                                }
                            )
                        }
                    }
                }
            }
        }
    }
}

/**
 * 联系人分组列表项组件
 *
 * @param group 分组信息
 * @param isSelected 是否选中
 * @param selectionMode 选择模式
 * @param onGroupClick 点击回调
 */
@Composable
private fun ContactGroupItem(
    group: ContactsHelper.ContactGroup,
    isSelected: Boolean,
    selectionMode: ContactGroupSelectionMode,
    onGroupClick: (ContactsHelper.ContactGroup) -> Unit
) {
    Surface(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onGroupClick(group) },
        shape = RoundedCornerShape(12.dp),
        color = if (isSelected) {
            MaterialTheme.colorScheme.primaryContainer
        } else {
            MaterialTheme.colorScheme.surfaceContainerLow
        },

    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 分组图标
            Box(
                modifier = Modifier
                    .size(48.dp)
                    .background(
                        if (isSelected) {
                            MaterialTheme.colorScheme.primary
                        } else {
                            MaterialTheme.colorScheme.primary.copy(alpha = 0.1f)
                        },
                        CircleShape
                    ),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = Icons.Default.Group,
                    contentDescription = "分组",
                    tint = if (isSelected) {
                        MaterialTheme.colorScheme.onPrimary
                    } else {
                        MaterialTheme.colorScheme.primary
                    },
                    modifier = Modifier.size(24.dp)
                )
            }

            Spacer(modifier = Modifier.width(16.dp))

            // 分组信息
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = group.title,
                    style = MaterialTheme.typography.titleMedium,
                    color = if (isSelected) {
                        MaterialTheme.colorScheme.onPrimaryContainer
                    } else {
                        MaterialTheme.colorScheme.onSurface
                    },
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
                if (group.contactCount > 0) {
                    Text(
                        text = "${group.contactCount} 个联系人",
                        style = MaterialTheme.typography.bodyMedium,
                        color = if (isSelected) {
                            MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.7f)
                        } else {
                            MaterialTheme.colorScheme.onSurfaceVariant
                        },
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                }
            }

            // 选择指示器
            when (selectionMode) {
                ContactGroupSelectionMode.SINGLE -> {
                    ThemedRadioButton(
                        config = RadioButtonConfig(
                            selected = isSelected,
                            onClick = { onGroupClick(group) },
                            enabled = true
                        )
                    )
                }
            }
        }
    }
}