package com.weinuo.quickcommands22.ui.screens.skyblue

import android.app.Activity
import android.content.Intent
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.statusBars
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.*
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier

import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.navigation.NavController
import com.weinuo.quickcommands22.R
import com.weinuo.quickcommands22.model.SmartReminderConfig
import com.weinuo.quickcommands22.model.SmartReminderConfigType
import com.weinuo.quickcommands22.model.SmartReminderType
import com.weinuo.quickcommands22.service.QuickCommandsService
import com.weinuo.quickcommands22.storage.SmartReminderConfigAdapter
import com.weinuo.quickcommands22.storage.UIStateStorageManager
import com.weinuo.quickcommands22.ui.theme.config.TopAppBarStyle
import com.weinuo.quickcommands22.ui.theme.config.TopAppBarConfig
import com.weinuo.quickcommands22.data.SettingsRepository
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.remember
import com.weinuo.quickcommands22.ui.components.themed.ThemedSearchTextField
import com.weinuo.quickcommands22.ui.components.themed.ThemedSmartReminderCard
import com.weinuo.quickcommands22.ui.theme.skyblue.SkyBlueStyleConfiguration
import com.weinuo.quickcommands22.ui.activities.SmartReminderDetailConfigActivity
import com.weinuo.quickcommands22.ui.activities.AppSelectionActivity
import com.weinuo.quickcommands22.ui.components.integrated.SetIntegratedTopAppBar
import com.weinuo.quickcommands22.ui.components.integrated.rememberIntegratedTopAppBarScrollBehavior
// import dev.chrisbanes.haze.hazeSource // 不再需要，由ThemedScaffold内部处理

/**
 * 天空蓝主题专用 - 智慧提醒主界面
 *
 * 显示所有可用的智慧提醒功能，用户可以：
 * - 查看所有智慧提醒功能
 * - 搜索特定功能
 * - 开启/关闭提醒功能
 * - 进入详细配置
 *
 * 设计特点：
 * - 使用主题感知的ThemedSearchTextField搜索组件
 * - 显示所有可用功能卡片（而非仅已添加的）
 * - 支持实时状态更新
 * - 高可扩展性架构
 * - 此版本专为天空蓝主题设计，支持整合设计风格
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SkyBlueSmartRemindersScreen(
    navController: NavController
) {
    val context = LocalContext.current
    val focusManager = LocalFocusManager.current

    // 创建存储适配器
    val configAdapter = remember { SmartReminderConfigAdapter(context) }

    // 获取页面布局配置（天空蓝主题专用）
    val settingsRepository = remember { SettingsRepository(context) }
    val pageLayoutConfig = SkyBlueStyleConfiguration.getDynamicPageLayoutConfig(settingsRepository)
    val cardStyleConfig = SkyBlueStyleConfiguration.getDynamicCardStyleConfig(settingsRepository)

    // 搜索状态
    var searchQuery by remember { mutableStateOf("") }

    // 配置状态
    var configs by remember { mutableStateOf<Map<String, SmartReminderConfig>>(emptyMap()) }

    // 应用选择ActivityResultLauncher
    val appSelectionLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            val resultKey = result.data?.getStringExtra(AppSelectionActivity.RESULT_SELECTED_APPS) ?: ""

            // 根据resultKey确定是哪个智慧提醒类型
            if (resultKey.startsWith("smart_reminder_")) {
                val reminderTypeId = resultKey.removePrefix("smart_reminder_").removeSuffix("_apps")
                val reminderType = SmartReminderType.fromId(reminderTypeId)

                // 使用UIStateStorageManager获取选择的应用
                val uiStateManager = UIStateStorageManager(context)
                val selectedApps = uiStateManager.loadAppListState(resultKey, "selected_apps")

                if (selectedApps.isNotEmpty()) {
                    // 保存选中的应用到对应的配置
                    when (reminderType) {
                        SmartReminderType.MUSIC_APP_REMINDER -> {
                            val musicConfig = configAdapter.loadMusicAppReminderConfig(reminderType.id)
                            val updatedConfig = musicConfig.copy(
                                selectedMusicApps = selectedApps.map { app ->
                                    SmartReminderConfigAdapter.SelectedMusicApp(
                                        packageName = app.packageName,
                                        appName = app.appName,
                                        isEnabled = true
                                    )
                                }
                            )
                            configAdapter.saveMusicAppReminderConfig(reminderType.id, updatedConfig)

                            // 标记为已配置
                            configAdapter.updateConfiguredState(reminderType.id, true)
                        }
                        SmartReminderType.ADDRESS_REMINDER -> {
                            val addressConfig = configAdapter.loadAddressReminderConfig(reminderType.id)
                            val updatedConfig = addressConfig.copy(
                                selectedMapApps = selectedApps.map { app ->
                                    SmartReminderConfigAdapter.SelectedMapApp(
                                        packageName = app.packageName,
                                        appName = app.appName,
                                        isEnabled = true
                                    )
                                }
                            )
                            configAdapter.saveAddressReminderConfig(reminderType.id, updatedConfig)

                            // 标记为已配置
                            configAdapter.updateConfiguredState(reminderType.id, true)
                        }
                        else -> {
                            // 其他类型暂不处理
                        }
                    }

                    // 重新加载配置以更新UI
                    configs = configAdapter.loadAllConfigs()

                    // 通知服务重新加载配置
                    QuickCommandsService.recheckSmartReminderMonitoring(context)

                    // 清理临时存储的数据
                    uiStateManager.clearAppListState(resultKey, "selected_apps")
                }
            }
        }
    }

    // 获取所有智慧提醒类型
    val allReminderTypes = remember { SmartReminderType.getAllTypes() }

    // 加载配置
    LaunchedEffect(Unit) {
        configs = configAdapter.loadAllConfigs()
    }

    // 监听应用选择结果
    LaunchedEffect(navController.currentBackStackEntry) {
        val savedStateHandle = navController.currentBackStackEntry?.savedStateHandle

        // 检查是否有智慧提醒的应用选择结果
        allReminderTypes.forEach { reminderType ->
            if (reminderType.configType == SmartReminderConfigType.APP_SELECTION) {
                val resultKey = "smart_reminder_${reminderType.id}_apps"
                val navigationKeyName = "${resultKey}_navigation_key"
                val selectedAppsNavigationKey = savedStateHandle?.get<String>(navigationKeyName)

                if (selectedAppsNavigationKey != null) {
                    // 处理应用选择结果
                    when (reminderType) {
                        SmartReminderType.MUSIC_APP_REMINDER -> {
                            // 音乐应用提醒：保存选中的应用并标记为已配置
                            val uiStateManager = UIStateStorageManager(context)
                            val selectedAppsResult = uiStateManager.loadAppListState(selectedAppsNavigationKey, resultKey)

                            if (selectedAppsResult.isNotEmpty()) {
                                // 加载当前配置
                                val currentConfig = configAdapter.loadMusicAppReminderConfig(reminderType.id)

                                // 转换为新的数据结构
                                val selectedMusicApps = selectedAppsResult.map { app ->
                                    SmartReminderConfigAdapter.SelectedMusicApp(
                                        packageName = app.packageName,
                                        appName = app.appName,
                                        isEnabled = true
                                    )
                                }

                                // 为每个应用创建默认按钮配置，智能分配位置避免重叠
                                val buttonConfigs = selectedAppsResult.mapIndexed { index, app ->
                                    val (position, marginX, marginY) = calculateButtonPosition(index)
                                    SmartReminderConfigAdapter.MusicAppButtonConfig(
                                        appPackageName = app.packageName,
                                        buttonPosition = position,
                                        buttonSize = 56,
                                        buttonMarginX = marginX,
                                        buttonMarginY = marginY,
                                        isEnabled = true
                                    )
                                }

                                val updatedConfig = currentConfig.copy(
                                    selectedMusicApps = selectedMusicApps,
                                    buttonConfigs = buttonConfigs
                                )
                                configAdapter.saveMusicAppReminderConfig(reminderType.id, updatedConfig)

                                // 标记为已配置
                                configAdapter.updateConfiguredState(reminderType.id, true)

                                // 重新加载配置
                                configs = configAdapter.loadAllConfigs()
                            }

                            // 清除结果
                            savedStateHandle.remove<String>(navigationKeyName)
                            uiStateManager.clearAppListState(selectedAppsNavigationKey, resultKey)
                        }
                        SmartReminderType.ADDRESS_REMINDER -> {
                            // 地址提醒：保存选中的应用并标记为已配置
                            val uiStateManager = UIStateStorageManager(context)
                            val selectedAppsResult = uiStateManager.loadAppListState(selectedAppsNavigationKey, resultKey)

                            if (selectedAppsResult.isNotEmpty()) {
                                // 加载当前配置
                                val currentConfig = configAdapter.loadAddressReminderConfig(reminderType.id)

                                // 转换为新的数据结构
                                val selectedMapApps = selectedAppsResult.map { app ->
                                    SmartReminderConfigAdapter.SelectedMapApp(
                                        packageName = app.packageName,
                                        appName = app.appName,
                                        isEnabled = true
                                    )
                                }

                                // 为每个应用创建默认按钮配置，智能分配位置避免重叠
                                val buttonConfigs = selectedAppsResult.mapIndexed { index, app ->
                                    val (position, marginX, marginY) = calculateButtonPosition(index)
                                    SmartReminderConfigAdapter.AddressReminderButtonConfig(
                                        appPackageName = app.packageName,
                                        buttonPosition = position,
                                        buttonSize = 56,
                                        buttonMarginX = marginX,
                                        buttonMarginY = marginY,
                                        isEnabled = true
                                    )
                                }

                                val updatedConfig = currentConfig.copy(
                                    selectedMapApps = selectedMapApps,
                                    buttonConfigs = buttonConfigs
                                )
                                configAdapter.saveAddressReminderConfig(reminderType.id, updatedConfig)

                                // 标记为已配置
                                configAdapter.updateConfiguredState(reminderType.id, true)

                                // 重新加载配置
                                configs = configAdapter.loadAllConfigs()
                            }

                            // 清除结果
                            savedStateHandle.remove<String>(navigationKeyName)
                            uiStateManager.clearAppListState(selectedAppsNavigationKey, resultKey)
                        }
                        else -> {
                            // 其他应用选择类型的处理逻辑可以在这里添加
                        }
                    }
                }
            }
        }
    }

    // 根据搜索查询过滤提醒类型
    val filteredReminderTypes = remember(allReminderTypes, searchQuery, context) {
        if (searchQuery.isEmpty()) {
            allReminderTypes
        } else {
            allReminderTypes.filter { reminderType ->
                reminderType.getLocalizedTitle(context).contains(searchQuery, ignoreCase = true) ||
                reminderType.getLocalizedDescription(context).contains(searchQuery, ignoreCase = true)
            }
        }
    }

    // 计算TopAppBar高度，用于内容的初始顶部padding
    val globalSettings by settingsRepository.globalSettings.collectAsState()
    val density = LocalDensity.current
    val statusBarHeight = with(density) { WindowInsets.statusBars.getTop(density).toDp() }
    val topAppBarHeight = globalSettings.topAppBarHeight.dp + statusBarHeight // StandardTopAppBar高度

    // 创建LazyColumn滚动状态
    val lazyListState = rememberLazyListState()

    // 创建滚动行为 - 支持可折叠标题栏，只有在内容可滚动时才允许标题栏折叠
    val scrollBehavior = rememberIntegratedTopAppBarScrollBehavior(
        canScroll = {
            // 检查LazyColumn是否可以滚动
            lazyListState.canScrollBackward || lazyListState.canScrollForward
        }
    )

    // 配置TopAppBar - 支持可折叠类型
    SetIntegratedTopAppBar(
        config = TopAppBarConfig(
            title = stringResource(R.string.nav_smart_reminders),
            style = TopAppBarStyle.STANDARD,
            scrollBehavior = scrollBehavior, // 关键：添加滚动行为
            windowInsets = WindowInsets.statusBars
        )
    )

    // 动态计算顶部padding - 根据标题栏类型优化内容定位
    val topPadding = remember(globalSettings.topAppBarType, statusBarHeight, topAppBarHeight) {
        if (globalSettings.topAppBarType == "collapsible") {
            // 可折叠模式：使用展开状态高度，确保内容不被遮挡
            152.dp + statusBarHeight
        } else {
            // 标准模式：使用固定高度
            topAppBarHeight
        }
    }

    // 直接返回页面内容，TopAppBar由IntegratedMainLayout处理
    Box(modifier = Modifier.fillMaxSize()) {
        LazyColumn(
            state = lazyListState,
            modifier = Modifier
                .fillMaxSize()
                .nestedScroll(scrollBehavior.nestedScrollConnection),
            contentPadding = PaddingValues(
                top = topPadding, // 动态计算的顶部padding
                bottom = pageLayoutConfig.bottomPadding // 使用动态配置的底部边距
            ),
            verticalArrangement = Arrangement.spacedBy(cardStyleConfig.itemSpacing)
        ) {
            // 搜索框
            item {
                ThemedSearchTextField(
                    searchQuery = searchQuery,
                    onSearchQueryChange = { searchQuery = it },
                    onClearSearch = {
                        searchQuery = ""
                        focusManager.clearFocus()
                    },
                    placeholder = stringResource(R.string.search_smart_reminders),
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(pageLayoutConfig.searchFieldMargin)
                )
            }
                items(filteredReminderTypes) { reminderType ->
                    val config = configs[reminderType.id] ?: SmartReminderConfig.createDefault(reminderType)

                    Box(modifier = Modifier.padding(horizontal = pageLayoutConfig.contentHorizontalPadding)) {
                        ThemedSmartReminderCard(
                            reminderType = reminderType,
                            config = config,
                            onCardClick = {
                                // iOS风格：点击卡片进入配置/编辑
                                if (!config.isConfigured) {
                                    // 未配置：进入配置流程
                                    when (reminderType.configType) {
                                        SmartReminderConfigType.APP_SELECTION -> {
                                            // 应用选择类型：跳转到多选应用选择界面
                                            val intent = Intent(context, AppSelectionActivity::class.java).apply {
                                                putExtra("selection_mode", "MULTI")
                                                putStringArrayListExtra("initial_selected_apps", ArrayList<String>())
                                                putExtra("result_key", "smart_reminder_${reminderType.id}_apps")
                                            }
                                            appSelectionLauncher.launch(intent)
                                        }
                                        else -> {
                                            // 其他类型：跳转到详细配置界面
                                            SmartReminderDetailConfigActivity.startForCreate(context, reminderType.id)
                                        }
                                    }
                                } else {
                                    // 已配置：统一跳转到详细设置界面（编辑模式）
                                    SmartReminderDetailConfigActivity.startForEdit(context, reminderType.id)
                                }
                            },
                            onToggle = { isEnabled ->
                                // 开关控制（仅在已配置时可用）
                                if (config.canBeEnabled() || !isEnabled) {
                                    configAdapter.updateEnabledState(reminderType.id, isEnabled)
                                    // 更新本地状态
                                    configs = configs.toMutableMap().apply {
                                        this[reminderType.id] = config.copyWithUpdate(isEnabled = isEnabled)
                                    }

                                    // 通知服务重新加载配置
                                    QuickCommandsService.recheckSmartReminderMonitoring(context)
                                }
                            }
                        )
                    }
                }

                // 如果没有搜索结果，显示提示
                if (filteredReminderTypes.isEmpty() && searchQuery.isNotEmpty()) {
                    item {
                        Card(
                            modifier = Modifier.fillMaxWidth(),
                            colors = CardDefaults.cardColors(
                                containerColor = MaterialTheme.colorScheme.surfaceVariant
                            )
                        ) {
                            Column(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(24.dp),
                                verticalArrangement = Arrangement.Center
                            ) {
                                Text(
                                    text = "未找到匹配的智慧提醒功能",
                                    style = MaterialTheme.typography.bodyLarge,
                                    color = MaterialTheme.colorScheme.onSurfaceVariant
                                )
                                Spacer(modifier = Modifier.height(8.dp))
                                Text(
                                    text = "请尝试其他搜索关键词",
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = MaterialTheme.colorScheme.onSurfaceVariant
                                )
                            }
                        }
                    }
                }
        }
    }
}

/**
 * 智能计算按钮位置，避免重叠
 * 支持无限数量的应用，通过增加边距和调整位置来避免重叠
 *
 * @param index 应用索引（从0开始）
 * @return Triple(位置, 水平边距, 垂直边距)
 */
private fun calculateButtonPosition(index: Int): Triple<String, Int, Int> {
    // 基础位置循环：右下角 -> 左下角 -> 右上角 -> 左上角
    val basePositions = listOf("bottom_right", "bottom_left", "top_right", "top_left")
    val position = basePositions[index % 4]

    // 计算层级（每4个应用为一层）
    val layer = index / 4

    // 基础边距
    val baseMarginX = 24
    val baseMarginY = 24

    // 根据层级和位置计算边距，确保不重叠且不超出屏幕
    val (marginX, marginY) = when (position) {
        "bottom_right" -> {
            // 右下角：向左和向上偏移
            val x = baseMarginX + (layer * 80).coerceAtMost(200) // 最大不超过200dp
            val y = baseMarginY + (layer * 80).coerceAtMost(300) // 最大不超过300dp
            Pair(x, y)
        }
        "bottom_left" -> {
            // 左下角：向右和向上偏移
            val x = baseMarginX + (layer * 80).coerceAtMost(200)
            val y = baseMarginY + (layer * 80).coerceAtMost(300)
            Pair(x, y)
        }
        "top_right" -> {
            // 右上角：向左和向下偏移
            val x = baseMarginX + (layer * 80).coerceAtMost(200)
            val y = baseMarginY + (layer * 80).coerceAtMost(300)
            Pair(x, y)
        }
        "top_left" -> {
            // 左上角：向右和向下偏移
            val x = baseMarginX + (layer * 80).coerceAtMost(200)
            val y = baseMarginY + (layer * 80).coerceAtMost(300)
            Pair(x, y)
        }
        else -> Pair(baseMarginX, baseMarginY)
    }

    return Triple(position, marginX, marginY)
}
