package com.weinuo.quickcommands22.storage.adapters

import android.util.Log
import com.weinuo.quickcommands22.model.*
import com.weinuo.quickcommands22.storage.NativeTypeStorageManager
import com.weinuo.quickcommands22.storage.StorageDomain
import com.weinuo.quickcommands22.storage.StorageOperation

/**
 * 设备动作任务存储适配器
 *
 * 负责DeviceActionTask的原生数据类型存储和重建。
 * 将复杂的设备动作任务对象拆分为独立的原生字段进行存储，
 * 完全避免JSON序列化问题。
 *
 * 支持的字段类型：
 * - 基础字段：id、operation
 * - 分享文本相关：shareText、shareAppPackage、shareAppName、shareTargetActivityName、shareTargetLabel
 * - 剪贴板相关：clipboardText
 * - 状态栏相关：statusBarOperation
 * - 手电筒相关：flashlightOperation
 * - 震动相关：vibrationPattern
 * - TTS相关：ttsText、ttsPitch、ttsSpeed、ttsAudioStream、ttsLanguage、ttsQueueMode、ttsWaitForCompletion、ttsSpellOutNumbers
 * - Shizuku命令相关：shizukuCommands
 * - 等待延迟相关：waitMinutes、waitSeconds
 * - 系统操作相关：systemOperationType
 *
 * 存储格式示例：
 * task_{id}_type = "device_action"
 * task_{id}_operation = "SHARE_TEXT"
 * task_{id}_share_text = "Hello World"
 * task_{id}_share_app_package = "com.example.app"
 * task_{id}_flashlight_operation = "TOGGLE"
 * task_{id}_vibration_pattern = "SHORT_BUZZ"
 * task_{id}_tts_text = "Hello"
 * task_{id}_tts_pitch = 1.0
 * task_{id}_tts_speed = 1.0
 *
 * @param storageManager 原生类型存储管理器
 */
class DeviceActionTaskAdapter(
    storageManager: NativeTypeStorageManager
) : BaseTaskAdapter<DeviceActionTask>(storageManager) {

    companion object {
        private const val TAG = "DeviceActionTaskAdapter"

        // 字段名常量
        private const val FIELD_OPERATION = "operation"
        private const val FIELD_SHARE_TEXT = "share_text"
        private const val FIELD_SHARE_APP_PACKAGE = "share_app_package"
        private const val FIELD_SHARE_APP_NAME = "share_app_name"
        private const val FIELD_SHARE_TARGET_ACTIVITY_NAME = "share_target_activity_name"
        private const val FIELD_SHARE_TARGET_LABEL = "share_target_label"
        private const val FIELD_CLIPBOARD_TEXT = "clipboard_text"
        private const val FIELD_STATUS_BAR_OPERATION = "status_bar_operation"
        private const val FIELD_FLASHLIGHT_OPERATION = "flashlight_operation"
        private const val FIELD_VIBRATION_PATTERN = "vibration_pattern"
        private const val FIELD_TTS_TEXT = "tts_text"
        private const val FIELD_TTS_PITCH = "tts_pitch"
        private const val FIELD_TTS_SPEED = "tts_speed"
        private const val FIELD_TTS_AUDIO_STREAM = "tts_audio_stream"
        private const val FIELD_TTS_LANGUAGE = "tts_language"
        private const val FIELD_TTS_QUEUE_MODE = "tts_queue_mode"
        private const val FIELD_TTS_WAIT_FOR_COMPLETION = "tts_wait_for_completion"
        private const val FIELD_TTS_SPELL_OUT_NUMBERS = "tts_spell_out_numbers"
        private const val FIELD_SHIZUKU_COMMANDS = "shizuku_commands"
        private const val FIELD_WAIT_MINUTES = "wait_minutes"
        private const val FIELD_WAIT_SECONDS = "wait_seconds"
        private const val FIELD_SYSTEM_OPERATION_TYPE = "system_operation_type"
    }

    override fun getTaskType() = "device_action"

    /**
     * 保存设备动作任务
     * 将DeviceActionTask的所有字段拆分为原生数据类型存储
     *
     * @param task 要保存的设备动作任务
     * @return 操作是否成功
     */
    override fun save(task: DeviceActionTask): Boolean {
        if (!isValidTaskId(task.id)) {
            logSaveError(task.id, "Invalid task ID")
            return false
        }

        Log.d(TAG, "开始保存设备动作任务: ${task.id}")

        return try {
            val operations = mutableListOf<StorageOperation>()

            // 保存基础字段
            operations.addAll(saveBaseFields(task.id, task))

            // 保存设备动作任务特有字段
            operations.addAll(listOf(
                saveEnum(generateKey(task.id, FIELD_OPERATION), task.operation),

                // 分享文本相关字段
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_SHARE_TEXT),
                    task.shareText
                ),
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_SHARE_APP_PACKAGE),
                    task.shareAppPackage
                ),
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_SHARE_APP_NAME),
                    task.shareAppName
                ),
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_SHARE_TARGET_ACTIVITY_NAME),
                    task.shareTargetActivityName
                ),
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_SHARE_TARGET_LABEL),
                    task.shareTargetLabel
                ),

                // 剪贴板相关字段
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_CLIPBOARD_TEXT),
                    task.clipboardText
                ),

                // 状态栏相关字段
                saveEnum(generateKey(task.id, FIELD_STATUS_BAR_OPERATION), task.statusBarOperation),

                // 手电筒相关字段
                saveEnum(generateKey(task.id, FIELD_FLASHLIGHT_OPERATION), task.flashlightOperation),

                // 震动相关字段
                saveEnum(generateKey(task.id, FIELD_VIBRATION_PATTERN), task.vibrationPattern),

                // TTS相关字段
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_TTS_TEXT),
                    task.ttsText
                ),
                StorageOperation.createFloatOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_TTS_PITCH),
                    task.ttsPitch
                ),
                StorageOperation.createFloatOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_TTS_SPEED),
                    task.ttsSpeed
                ),
                saveEnum(generateKey(task.id, FIELD_TTS_AUDIO_STREAM), task.ttsAudioStream),
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_TTS_LANGUAGE),
                    task.ttsLanguage
                ),
                StorageOperation.createBooleanOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_TTS_QUEUE_MODE),
                    task.ttsQueueMode
                ),
                StorageOperation.createBooleanOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_TTS_WAIT_FOR_COMPLETION),
                    task.ttsWaitForCompletion
                ),
                StorageOperation.createBooleanOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_TTS_SPELL_OUT_NUMBERS),
                    task.ttsSpellOutNumbers
                ),

                // Shizuku命令相关字段
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_SHIZUKU_COMMANDS),
                    task.shizukuCommands
                ),

                // 等待延迟相关字段
                StorageOperation.createIntOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_WAIT_MINUTES),
                    task.waitMinutes
                ),
                StorageOperation.createIntOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_WAIT_SECONDS),
                    task.waitSeconds
                ),

                // 系统操作相关字段
                saveEnum(generateKey(task.id, FIELD_SYSTEM_OPERATION_TYPE), task.systemOperationType)
            ))

            val success = storageManager.executeBatch(operations)
            if (success) {
                Log.d(TAG, "设备动作任务保存成功: ${task.id}")
            } else {
                logSaveError(task.id, "Batch operation failed")
            }
            success

        } catch (e: Exception) {
            logSaveError(task.id, "Exception during save: ${e.message}")
            false
        }
    }

    /**
     * 加载设备动作任务
     * 从原生数据类型重建DeviceActionTask对象
     *
     * @param taskId 任务ID
     * @return 加载的设备动作任务，失败时返回null
     */
    override fun load(taskId: String): DeviceActionTask? {
        if (!isValidTaskId(taskId)) {
            logLoadError(taskId, "Invalid task ID")
            return null
        }

        Log.d(TAG, "开始加载设备动作任务: $taskId")

        return try {
            // 检查任务是否存在
            if (!exists(taskId)) {
                Log.d(TAG, "设备动作任务不存在: $taskId")
                return null
            }

            DeviceActionTask(
                id = taskId,
                operation = loadEnum(generateKey(taskId, FIELD_OPERATION)) { DeviceActionOperation.valueOf(it) }
                    ?: DeviceActionOperation.SHARE_TEXT,

                // 分享文本相关字段
                shareText = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_SHARE_TEXT),
                    ""
                ),
                shareAppPackage = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_SHARE_APP_PACKAGE),
                    ""
                ),
                shareAppName = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_SHARE_APP_NAME),
                    ""
                ),
                shareTargetActivityName = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_SHARE_TARGET_ACTIVITY_NAME),
                    ""
                ),
                shareTargetLabel = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_SHARE_TARGET_LABEL),
                    ""
                ),

                // 剪贴板相关字段
                clipboardText = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_CLIPBOARD_TEXT),
                    ""
                ),

                // 状态栏相关字段
                statusBarOperation = loadEnum(generateKey(taskId, FIELD_STATUS_BAR_OPERATION)) { StatusBarOperation.valueOf(it) }
                    ?: StatusBarOperation.TOGGLE,

                // 手电筒相关字段
                flashlightOperation = loadEnum(generateKey(taskId, FIELD_FLASHLIGHT_OPERATION)) { FlashlightControlType.valueOf(it) }
                    ?: FlashlightControlType.TOGGLE,

                // 震动相关字段
                vibrationPattern = loadEnum(generateKey(taskId, FIELD_VIBRATION_PATTERN)) { DeviceVibrationPattern.valueOf(it) }
                    ?: DeviceVibrationPattern.SHORT_BUZZ,

                // TTS相关字段
                ttsText = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_TTS_TEXT),
                    ""
                ),
                ttsPitch = storageManager.loadFloat(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_TTS_PITCH),
                    1.0f
                ),
                ttsSpeed = storageManager.loadFloat(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_TTS_SPEED),
                    1.0f
                ),
                ttsAudioStream = loadEnum(generateKey(taskId, FIELD_TTS_AUDIO_STREAM)) { TTSAudioStream.valueOf(it) }
                    ?: TTSAudioStream.MUSIC,
                ttsLanguage = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_TTS_LANGUAGE),
                    "zh-CN"
                ),
                ttsQueueMode = storageManager.loadBoolean(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_TTS_QUEUE_MODE),
                    true
                ),
                ttsWaitForCompletion = storageManager.loadBoolean(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_TTS_WAIT_FOR_COMPLETION),
                    false
                ),
                ttsSpellOutNumbers = storageManager.loadBoolean(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_TTS_SPELL_OUT_NUMBERS),
                    false
                ),

                // Shizuku命令相关字段
                shizukuCommands = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_SHIZUKU_COMMANDS),
                    ""
                ),

                // 等待延迟相关字段
                waitMinutes = storageManager.loadInt(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_WAIT_MINUTES),
                    0
                ),
                waitSeconds = storageManager.loadInt(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_WAIT_SECONDS),
                    0
                ),

                // 系统操作相关字段
                systemOperationType = loadEnum(generateKey(taskId, FIELD_SYSTEM_OPERATION_TYPE)) { SystemOperationType.valueOf(it) }
                    ?: SystemOperationType.QUICK_SETTINGS
            ).also {
                Log.d(TAG, "设备动作任务加载成功: $taskId")
            }

        } catch (e: Exception) {
            logLoadError(taskId, "Exception during load: ${e.message}")
            null
        }
    }
}
