package com.weinuo.quickcommands22.ui.components

import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.background
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.MoreVert
import androidx.compose.material.icons.outlined.Delete
import androidx.compose.material.icons.outlined.PlayArrow
import androidx.compose.material.icons.outlined.Stop
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.MenuDefaults
import androidx.compose.material3.SegmentedButton
import androidx.compose.material3.SegmentedButtonDefaults
import androidx.compose.material3.SingleChoiceSegmentedButtonRow
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp

import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.vectorResource
import com.weinuo.quickcommands22.model.SharedTask
import com.weinuo.quickcommands22.model.SharedTriggerCondition
import com.weinuo.quickcommands22.ui.theme.manager.ThemeManager
import com.weinuo.quickcommands22.ui.theme.manager.LocalThemeContext
import com.weinuo.quickcommands22.ui.theme.skyblue.SkyBlueStyleConfiguration
import com.weinuo.quickcommands22.data.SettingsRepository
import com.weinuo.quickcommands22.R
import com.weinuo.quickcommands22.ui.theme.config.DividerConfig
import com.weinuo.quickcommands22.ui.theme.manager.UISpacingConfigurationManager

/**
 * 任务列表项
 *
 * 显示任务信息，并提供更多操作菜单和可选的编辑功能
 *
 * @param task 要显示的任务
 * @param isEnabled 任务是否启用
 * @param onDelete 删除按钮点击回调
 * @param onEnabledChanged 启用状态变更回调

 * @param onEdit 可选的编辑回调，如果提供则支持点击卡片编辑任务
 */
@Composable
fun TaskItem(
    task: SharedTask,
    isEnabled: Boolean = true,
    onDelete: () -> Unit,
    onEnabledChanged: (Boolean) -> Unit = {},

    onEdit: (() -> Unit)? = null
) {
    var showMenu by remember { mutableStateOf(false) }

    val context = LocalContext.current
    val themeManager = remember { ThemeManager.getInstance(context) }

    // 获取动态卡片样式配置（仅在天空蓝主题下使用）
    val cardStyle = if (themeManager.getCurrentThemeId() == "sky_blue") {
        val settingsRepository = remember { SettingsRepository(context) }
        SkyBlueStyleConfiguration.getDynamicCardStyleConfig(settingsRepository)
    } else {
        val themeContext = LocalThemeContext.current
        themeContext.styleConfiguration.cardStyle
    }

    val cornerRadius = cardStyle.defaultCornerRadius

    Card(
        modifier = Modifier
            .fillMaxWidth()
            .let { modifier ->
                if (onEdit != null) {
                    modifier.clickable { onEdit() }
                } else {
                    modifier
                }
            },
        shape = RoundedCornerShape(cornerRadius),
        colors = CardDefaults.cardColors(
            containerColor = if (isEnabled) {
                MaterialTheme.colorScheme.surfaceContainerLow
            } else {
                MaterialTheme.colorScheme.surfaceContainerLowest
            }
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = task.displayName,
                    style = MaterialTheme.typography.titleSmall,
                    color = if (isEnabled) {
                        MaterialTheme.colorScheme.onSurface
                    } else {
                        MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                    }
                )

                Text(
                    text = task.getDescription(),
                    style = MaterialTheme.typography.bodyMedium,
                    color = if (isEnabled) {
                        MaterialTheme.colorScheme.onSurfaceVariant
                    } else {
                        MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.6f)
                    }
                )
            }



            // 更多操作按钮
            Box {
                IconButton(
                    onClick = { showMenu = true },
                    modifier = Modifier.size(40.dp)
                ) {
                    Icon(
                        imageVector = if (themeManager.getCurrentThemeId() == "sky_blue") {
                            ImageVector.vectorResource(R.drawable.ic_sky_blue_more_vert)
                        } else {
                            Icons.Filled.MoreVert
                        },
                        contentDescription = "更多操作",
                        tint = if (themeManager.getCurrentThemeId() == "sky_blue") {
                            MaterialTheme.colorScheme.onSurface
                        } else {
                            MaterialTheme.colorScheme.onSurfaceVariant
                        }
                    )
                }

                // 下拉菜单
                DropdownMenu(
                    expanded = showMenu,
                    onDismissRequest = { showMenu = false },
                    modifier = if (themeManager.getCurrentThemeId() == "sky_blue") {
                        Modifier
                            .widthIn(min = 160.dp)
                            .background(
                                color = MaterialTheme.colorScheme.surfaceContainerLow,
                                shape = RoundedCornerShape(cornerRadius)
                            )
                    } else {
                        Modifier
                    },
                    shape = if (themeManager.getCurrentThemeId() == "sky_blue") {
                        RoundedCornerShape(cornerRadius)
                    } else {
                        RoundedCornerShape(4.dp)
                    }
                ) {
                    // 启用/停用选项
                    DropdownMenuItem(
                        text = {
                            Row(
                                verticalAlignment = Alignment.CenterVertically,
                                horizontalArrangement = Arrangement.spacedBy(12.dp)
                            ) {
                                Icon(
                                    imageVector = if (isEnabled) Icons.Outlined.Stop else Icons.Outlined.PlayArrow,
                                    contentDescription = if (isEnabled) "停用任务" else "启用任务",
                                    tint = MaterialTheme.colorScheme.onSurface
                                )
                                Text(
                                    text = if (isEnabled) "停用" else "启用",
                                    color = MaterialTheme.colorScheme.onSurface,
                                    style = MaterialTheme.typography.bodyMedium
                                )
                            }
                        },
                        onClick = {
                            onEnabledChanged(!isEnabled)
                            showMenu = false
                        },
                        colors = MenuDefaults.itemColors(
                            textColor = MaterialTheme.colorScheme.onSurface
                        ),
                        contentPadding = PaddingValues(horizontal = 16.dp, vertical = 12.dp)
                    )

                    // 添加分割线（在菜单项之间）
                    if (themeManager.getCurrentThemeId() == "sky_blue") {
                        val themeContext = LocalThemeContext.current
                        val settingsRepository = remember { SettingsRepository(context) }
                        val uiSpacingConfigManager = remember { UISpacingConfigurationManager.getInstance(context, settingsRepository) }
                        val uiSpacingConfig = uiSpacingConfigManager.getUISpacingConfiguration()

                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = (16 + uiSpacingConfig.dividerHorizontalPadding).dp),
                                color = if (uiSpacingConfig.dividerVisible) androidx.compose.ui.graphics.Color(0x33202020) else androidx.compose.ui.graphics.Color.Transparent
                            )
                        )
                    }

                    // 删除选项
                    DropdownMenuItem(
                        text = {
                            Row(
                                verticalAlignment = Alignment.CenterVertically,
                                horizontalArrangement = Arrangement.spacedBy(12.dp)
                            ) {
                                Icon(
                                    imageVector = Icons.Outlined.Delete,
                                    contentDescription = "删除任务",
                                    tint = MaterialTheme.colorScheme.error
                                )
                                Text(
                                    text = "删除",
                                    color = MaterialTheme.colorScheme.error,
                                    style = MaterialTheme.typography.bodyMedium
                                )
                            }
                        },
                        onClick = {
                            onDelete()
                            showMenu = false
                        },
                        colors = MenuDefaults.itemColors(
                            textColor = MaterialTheme.colorScheme.error
                        ),
                        contentPadding = PaddingValues(horizontal = 16.dp, vertical = 12.dp)
                    )
                }
            }
        }
    }
}

/**
 * 触发条件列表项
 *
 * 显示触发条件信息，并提供更多操作菜单和可选的编辑功能
 *
 * @param condition 要显示的触发条件
 * @param isEnabled 条件是否启用
 * @param onDelete 删除按钮点击回调
 * @param onEnabledChanged 启用状态变更回调
 * @param onEdit 可选的编辑回调，如果提供则支持点击卡片编辑条件
 */
@Composable
fun TriggerConditionItem(
    condition: SharedTriggerCondition,
    isEnabled: Boolean = true,
    onDelete: () -> Unit,
    onEnabledChanged: (Boolean) -> Unit = {},
    onEdit: (() -> Unit)? = null
) {
    var showMenu by remember { mutableStateOf(false) }

    val context = LocalContext.current
    val themeManager = remember { ThemeManager.getInstance(context) }

    // 获取动态卡片样式配置（仅在天空蓝主题下使用）
    val cardStyle = if (themeManager.getCurrentThemeId() == "sky_blue") {
        val settingsRepository = remember { SettingsRepository(context) }
        SkyBlueStyleConfiguration.getDynamicCardStyleConfig(settingsRepository)
    } else {
        val themeContext = LocalThemeContext.current
        themeContext.styleConfiguration.cardStyle
    }

    val cornerRadius = cardStyle.defaultCornerRadius

    Card(
        modifier = Modifier
            .fillMaxWidth()
            .let { modifier ->
                if (onEdit != null) {
                    modifier.clickable { onEdit() }
                } else {
                    modifier
                }
            },
        shape = RoundedCornerShape(cornerRadius),
        colors = CardDefaults.cardColors(
            containerColor = if (isEnabled) {
                MaterialTheme.colorScheme.surfaceContainerLow
            } else {
                MaterialTheme.colorScheme.surfaceContainerLowest
            }
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = condition.displayName,
                    style = MaterialTheme.typography.titleSmall,
                    color = if (isEnabled) {
                        MaterialTheme.colorScheme.onSurface
                    } else {
                        MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                    }
                )

                Text(
                    text = condition.getDescription(),
                    style = MaterialTheme.typography.bodyMedium,
                    color = if (isEnabled) {
                        MaterialTheme.colorScheme.onSurfaceVariant
                    } else {
                        MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.6f)
                    }
                )
            }

            // 更多操作按钮
            Box {
                IconButton(
                    onClick = { showMenu = true },
                    modifier = Modifier.size(40.dp)
                ) {
                    Icon(
                        imageVector = if (themeManager.getCurrentThemeId() == "sky_blue") {
                            ImageVector.vectorResource(R.drawable.ic_sky_blue_more_vert)
                        } else {
                            Icons.Filled.MoreVert
                        },
                        contentDescription = "更多操作",
                        tint = if (themeManager.getCurrentThemeId() == "sky_blue") {
                            MaterialTheme.colorScheme.onSurface
                        } else {
                            MaterialTheme.colorScheme.onSurfaceVariant
                        }
                    )
                }

                // 下拉菜单
                DropdownMenu(
                    expanded = showMenu,
                    onDismissRequest = { showMenu = false },
                    modifier = if (themeManager.getCurrentThemeId() == "sky_blue") {
                        Modifier
                            .widthIn(min = 160.dp)
                            .background(
                                color = MaterialTheme.colorScheme.surfaceContainerLow,
                                shape = RoundedCornerShape(cornerRadius)
                            )
                    } else {
                        Modifier
                    },
                    shape = if (themeManager.getCurrentThemeId() == "sky_blue") {
                        RoundedCornerShape(cornerRadius)
                    } else {
                        RoundedCornerShape(4.dp)
                    }
                ) {
                    // 启用/停用选项
                    DropdownMenuItem(
                        text = {
                            Row(
                                verticalAlignment = Alignment.CenterVertically,
                                horizontalArrangement = Arrangement.spacedBy(12.dp)
                            ) {
                                Icon(
                                    imageVector = if (isEnabled) Icons.Outlined.Stop else Icons.Outlined.PlayArrow,
                                    contentDescription = if (isEnabled) "停用条件" else "启用条件",
                                    tint = MaterialTheme.colorScheme.onSurface
                                )
                                Text(
                                    text = if (isEnabled) "停用" else "启用",
                                    color = MaterialTheme.colorScheme.onSurface,
                                    style = MaterialTheme.typography.bodyMedium
                                )
                            }
                        },
                        onClick = {
                            onEnabledChanged(!isEnabled)
                            showMenu = false
                        },
                        colors = MenuDefaults.itemColors(
                            textColor = MaterialTheme.colorScheme.onSurface
                        ),
                        contentPadding = PaddingValues(horizontal = 16.dp, vertical = 12.dp)
                    )

                    // 添加分割线（在菜单项之间）
                    if (themeManager.getCurrentThemeId() == "sky_blue") {
                        val themeContext = LocalThemeContext.current
                        val settingsRepository = remember { SettingsRepository(context) }
                        val uiSpacingConfigManager = remember { UISpacingConfigurationManager.getInstance(context, settingsRepository) }
                        val uiSpacingConfig = uiSpacingConfigManager.getUISpacingConfiguration()

                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = (16 + uiSpacingConfig.dividerHorizontalPadding).dp),
                                color = if (uiSpacingConfig.dividerVisible) androidx.compose.ui.graphics.Color(0x33202020) else androidx.compose.ui.graphics.Color.Transparent
                            )
                        )
                    }

                    // 删除选项
                    DropdownMenuItem(
                        text = {
                            Row(
                                verticalAlignment = Alignment.CenterVertically,
                                horizontalArrangement = Arrangement.spacedBy(12.dp)
                            ) {
                                Icon(
                                    imageVector = Icons.Outlined.Delete,
                                    contentDescription = "删除条件",
                                    tint = MaterialTheme.colorScheme.error
                                )
                                Text(
                                    text = "删除",
                                    color = MaterialTheme.colorScheme.error,
                                    style = MaterialTheme.typography.bodyMedium
                                )
                            }
                        },
                        onClick = {
                            onDelete()
                            showMenu = false
                        },
                        colors = MenuDefaults.itemColors(
                            textColor = MaterialTheme.colorScheme.error
                        ),
                        contentPadding = PaddingValues(horizontal = 16.dp, vertical = 12.dp)
                    )
                }
            }
        }
    }
}

/**
 * 条件组合逻辑选择器
 *
 * 用于选择"当所有条件满足时"或"当任一条件满足时"
 * 根据当前主题自动选择合适的样式：
 * - 天空蓝主题：使用圆角按钮样式（类似"我的"、"探索"按钮）
 * - 其他主题：使用标准的SegmentedButton样式
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ConditionCombinationLogic(
    requireAllConditions: Boolean,
    onSelectionChanged: (Boolean) -> Unit
) {
    val context = LocalContext.current
    val themeManager = remember { ThemeManager.getInstance(context) }
    val currentThemeId = themeManager.getCurrentThemeId()

    Column(
        modifier = Modifier.fillMaxWidth()
    ) {
        Text(
            text = "条件组合逻辑:",
            style = MaterialTheme.typography.bodyMedium,
            modifier = Modifier.padding(bottom = 8.dp)
        )

        if (currentThemeId == "sky_blue") {
            // 天空蓝主题：使用圆角按钮样式
            SkyBlueRoundedChoiceSelector(
                option1Text = "当所有条件满足时",
                option2Text = "当任一条件满足时",
                selectedOption = if (requireAllConditions) 0 else 1,
                onSelectionChanged = { selectedIndex ->
                    onSelectionChanged(selectedIndex == 0)
                }
            )
        } else {
            // 其他主题：使用标准的SegmentedButton样式
            SingleChoiceSegmentedButtonRow(
                modifier = Modifier.fillMaxWidth()
            ) {
                SegmentedButton(
                    selected = requireAllConditions,
                    onClick = { onSelectionChanged(true) },
                    shape = SegmentedButtonDefaults.itemShape(index = 0, count = 2),
                    label = { Text("当所有条件满足时") }
                )

                SegmentedButton(
                    selected = !requireAllConditions,
                    onClick = { onSelectionChanged(false) },
                    shape = SegmentedButtonDefaults.itemShape(index = 1, count = 2),
                    label = { Text("当任一条件满足时") }
                )
            }
        }
    }
}

/**
 * 天空蓝主题专用的圆角选择器
 *
 * 实现类似图片中"我的"、"探索"按钮的圆角样式
 * 特点：
 * - 灰色轨道背景
 * - 选中状态：白色背景平滑移动到新位置
 * - 优雅的背景移动动画
 */
@Composable
fun SkyBlueRoundedChoiceSelector(
    option1Text: String,
    option2Text: String,
    selectedOption: Int, // 0 或 1
    onSelectionChanged: (Int) -> Unit
) {
    // 背景移动动画
    val animatedOffset by animateFloatAsState(
        targetValue = if (selectedOption == 0) 0f else 1f,
        animationSpec = tween(durationMillis = 300, easing = FastOutSlowInEasing),
        label = "background_offset"
    )

    // 轨道背景
    Surface(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(28.dp),
        color = androidx.compose.ui.graphics.Color(0xFFE6E8E9), // 轨道背景颜色：#FFE6E8E9
        shadowElevation = 0.dp
    ) {
        Box(
            modifier = Modifier.padding(4.dp) // 轨道内边距
        ) {
            // 移动的选中背景
            BoxWithConstraints {
                val buttonWidth = (maxWidth - 4.dp) / 2 // 减去中间间距
                Surface(
                    modifier = Modifier
                        .width(buttonWidth)
                        .height(36.dp)
                        .offset(x = (buttonWidth + 4.dp) * animatedOffset),
                    shape = RoundedCornerShape(24.dp),
                    color = androidx.compose.ui.graphics.Color(0xFFFFFFFF), // 选中按钮背景颜色：#FFFFFFFF
                    shadowElevation = 0.dp
                ) {}
            }

            // 前景文字按钮
            Row(
                horizontalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                // 选项1
                RoundedChoiceButton(
                    text = option1Text,
                    selected = selectedOption == 0,
                    onClick = { onSelectionChanged(0) },
                    modifier = Modifier.weight(1f),
                    useTransparentBackground = true
                )

                // 选项2
                RoundedChoiceButton(
                    text = option2Text,
                    selected = selectedOption == 1,
                    onClick = { onSelectionChanged(1) },
                    modifier = Modifier.weight(1f),
                    useTransparentBackground = true
                )
            }
        }
    }
}

/**
 * 单个圆角选择按钮
 *
 * 实现类似图片中"我的"、"探索"按钮的样式
 */
@Composable
private fun RoundedChoiceButton(
    text: String,
    selected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    useTransparentBackground: Boolean = false
) {
    // 文字颜色
    val textColor = if (selected) {
        androidx.compose.ui.graphics.Color(0xFF1A1A1A) // 选中按钮文字颜色：#FF1A1A1A
    } else {
        androidx.compose.ui.graphics.Color(0xFF737475) // 未选中按钮文字颜色：#FF737475
    }

    Surface(
        modifier = modifier
            .height(36.dp) // 稍微降低高度，适应轨道内的按钮
            .clickable(
                indication = null, // 移除点击涟漪效果
                interactionSource = remember { MutableInteractionSource() }
            ) { onClick() },
        shape = RoundedCornerShape(24.dp), // 稍微小一点的圆角，适应轨道内的按钮
        color = if (useTransparentBackground) androidx.compose.ui.graphics.Color.Transparent else androidx.compose.ui.graphics.Color(0xFFFFFFFF), // 选中按钮背景颜色：#FFFFFFFF
        shadowElevation = 0.dp // 天空蓝主题不使用阴影
    ) {
        Box(
            contentAlignment = Alignment.Center,
            modifier = Modifier.fillMaxSize()
        ) {
            Text(
                text = text,
                style = MaterialTheme.typography.bodyMedium,
                color = textColor,
                textAlign = TextAlign.Center,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
        }
    }
}
