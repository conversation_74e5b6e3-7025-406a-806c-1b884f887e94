package com.weinuo.quickcommands22.utils

import android.app.usage.UsageStatsManager
import android.content.Context
import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * 应用状态监控工具类
 * 提供应用状态监控功能，复用现有的前台应用检测逻辑，支持应用状态变化检测和缓存管理
 */
object AppStateMonitor {
    private const val TAG = "AppStateMonitor"
    
    // 缓存相关常量
    private var cachedForegroundPackage: String? = null
    private var cachedForegroundTimestamp: Long = 0
    private const val FOREGROUND_CACHE_VALID_TIME = 5000L // 5秒缓存
    
    // 前台应用检测相关常量（复用现有逻辑）
    private const val TIME_WINDOW = 30 * 60 * 1000L // 30分钟窗口
    private const val ACTIVE_THRESHOLD = 15 * 60 * 1000L // 15分钟活跃阈值

    /**
     * 获取当前前台应用包名
     * 复用现有的前台应用检测逻辑，使用UsageStatsManager进行检测
     * 
     * @param context 上下文
     * @return 当前前台应用的包名，如果没有前台应用则返回null
     */
    suspend fun getCurrentForegroundApp(context: Context): String? = withContext(Dispatchers.IO) {
        try {
            // 检查缓存是否有效
            val currentTime = System.currentTimeMillis()
            if (cachedForegroundPackage != null && 
                currentTime - cachedForegroundTimestamp < FOREGROUND_CACHE_VALID_TIME) {
                Log.d(TAG, "Using cached foreground app: $cachedForegroundPackage")
                return@withContext cachedForegroundPackage
            }

            // 获取UsageStatsManager
            val usageStatsManager = context.getSystemService(Context.USAGE_STATS_SERVICE) as? UsageStatsManager
            if (usageStatsManager == null) {
                Log.e(TAG, "UsageStatsManager not available")
                return@withContext null
            }

            // 查询使用统计数据
            val endTime = currentTime
            val startTime = endTime - TIME_WINDOW
            
            val usageStats = usageStatsManager.queryUsageStats(
                UsageStatsManager.INTERVAL_DAILY,
                startTime,
                endTime
            )

            if (usageStats.isNullOrEmpty()) {
                Log.d(TAG, "No usage stats available")
                cachedForegroundPackage = null
                cachedForegroundTimestamp = currentTime
                return@withContext null
            }

            // 查找最近活跃的应用
            var mostRecentApp: String? = null
            var mostRecentTime = 0L

            for (stat in usageStats) {
                val lastTimeUsed = stat.lastTimeUsed
                val totalTimeInForeground = stat.totalTimeInForeground

                // 检查是否在活跃阈值内
                if (currentTime - lastTimeUsed <= ACTIVE_THRESHOLD && 
                    totalTimeInForeground > 0 && 
                    lastTimeUsed > mostRecentTime) {
                    mostRecentTime = lastTimeUsed
                    mostRecentApp = stat.packageName
                }
            }

            // 更新缓存
            cachedForegroundPackage = mostRecentApp
            cachedForegroundTimestamp = currentTime

            Log.d(TAG, "Current foreground app: $mostRecentApp")
            return@withContext mostRecentApp

        } catch (e: Exception) {
            Log.e(TAG, "Error getting current foreground app", e)
            return@withContext null
        }
    }

    /**
     * 检查应用状态变化
     * 返回前一次的前台应用和当前的前台应用
     * 
     * @param context 上下文
     * @return Pair<String?, String?> 前一次前台应用包名和当前前台应用包名
     */
    suspend fun checkAppStateChanges(context: Context): Pair<String?, String?> = withContext(Dispatchers.IO) {
        try {
            val previousApp = cachedForegroundPackage
            val currentApp = getCurrentForegroundApp(context)

            if (currentApp != previousApp) {
                Log.d(TAG, "App state changed: $previousApp -> $currentApp")
                
                // 记录状态变化的详细信息
                when {
                    previousApp == null && currentApp != null -> {
                        Log.d(TAG, "App entered foreground: $currentApp")
                    }
                    previousApp != null && currentApp == null -> {
                        Log.d(TAG, "App entered background: $previousApp")
                    }
                    previousApp != null && currentApp != null -> {
                        Log.d(TAG, "App switched: $previousApp -> $currentApp")
                    }
                }
            }

            return@withContext Pair(previousApp, currentApp)

        } catch (e: Exception) {
            Log.e(TAG, "Error checking app state changes", e)
            return@withContext Pair(null, null)
        }
    }

    /**
     * 清除缓存
     * 用于重置应用状态监控的缓存数据
     */
    fun clearCache() {
        cachedForegroundPackage = null
        cachedForegroundTimestamp = 0
        Log.d(TAG, "Cache cleared")
    }

    /**
     * 获取缓存的前台应用包名（用于调试）
     * 
     * @return 当前缓存的前台应用包名
     */
    fun getCachedForegroundApp(): String? {
        return cachedForegroundPackage
    }

    /**
     * 检查缓存是否有效（用于调试）
     * 
     * @return 缓存是否在有效期内
     */
    fun isCacheValid(): Boolean {
        val currentTime = System.currentTimeMillis()
        return cachedForegroundPackage != null && 
               currentTime - cachedForegroundTimestamp < FOREGROUND_CACHE_VALID_TIME
    }
}
