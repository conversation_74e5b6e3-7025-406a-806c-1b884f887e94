package com.weinuo.quickcommands22.ui.components

import androidx.compose.ui.graphics.Color

/**
 * 水球高级材质颜色方案
 *
 * 提供预计算的高级材质颜色方案，包括渐变、高光、圆环强调等效果。
 * 基于第一性原理的颜色生成算法，确保视觉效果的一致性和美观性。
 *
 * 设计原则：
 * - 基于HSL颜色空间进行科学的颜色计算
 * - 渐变色营造立体感和深度
 * - 高光色模拟光照反射效果
 * - 圆环强调色增强边界定义
 * - 粒子色提供装饰性视觉元素
 *
 * 适用场景：
 * - 天空蓝主题的水球组件
 * - 需要高级材质效果的UI元素
 * - 主题感知的视觉增强
 */

/**
 * 水球颜色集合
 *
 * 包含单个水球状态（优秀/良好/需要优化）的完整颜色方案
 *
 * @property gradientStart 渐变起始色（水球底部）- 更亮、更鲜艳，营造通透感
 * @property gradientMid 渐变中间色（基准色）- 保持原有颜色特征
 * @property gradientEnd 渐变结束色（水球顶部）- 与基准色接近，略微提高亮度
 * @property highlight 高光色 - 模拟光照反射，饱和度低、亮度高
 * @property ringEmphasis 圆环强调色 - 更深、更饱和，用于边界强调
 * @property ringFade 圆环渐变色 - 圆环向透明过渡的中间色
 * @property particle 粒子色 - 装饰性气泡和粒子效果
 */
data class WaterBallColorSet(
    val gradientStart: Color,
    val gradientMid: Color,
    val gradientEnd: Color,
    val highlight: Color,
    val ringEmphasis: Color,
    val ringFade: Color,
    val particle: Color
)

/**
 * 水球高级材质颜色方案集合
 *
 * 包含不同分数等级的完整颜色方案
 *
 * @property excellent 优秀等级（80-100分）颜色方案
 * @property good 良好等级（60-79分）颜色方案
 * @property needsImprovement 需要优化等级（0-59分）颜色方案
 */
data class WaterBallAdvancedColors(
    val excellent: WaterBallColorSet,
    val good: WaterBallColorSet,
    val needsImprovement: WaterBallColorSet
)

/**
 * 水球高级材质颜色方案提供者
 *
 * 提供预计算的颜色方案和颜色选择逻辑
 */
object WaterBallAdvancedColorSchemes {
    
    /**
     * 天空蓝主题的水球高级材质颜色方案
     *
     * 基于天空蓝主题的品牌色、iOS风格橙色和错误色计算得出
     * 使用科学的HSL颜色空间变换，确保视觉效果的协调性
     */
    val skyBlue = WaterBallAdvancedColors(
        excellent = WaterBallColorSet(
            gradientStart = Color(0xFF67CCFF),
            gradientMid = Color(0xFF0A58F6),
            gradientEnd = Color(0xFF2257F7),
            highlight = Color(0xFFECF0F7),
            ringEmphasis = Color(0xFF001EB4),
            ringFade = Color(0xFF3F4875),
            particle = Color(0xFF4498FC)
        ),
        good = WaterBallColorSet(
            gradientStart = Color(0xFFFF8765),
            gradientMid = Color(0xFFFF8C00),
            gradientEnd = Color(0xFFFFAA19),
            highlight = Color(0xFFF8F2EB),
            ringEmphasis = Color(0xFFB27F00),
            ringFade = Color(0xFF74643E),
            particle = Color(0xFFFF903F)
        ),
        needsImprovement = WaterBallColorSet(
            gradientStart = Color(0xFFFF7490),
            gradientMid = Color(0xFFE74026),
            gradientEnd = Color(0xFFEA623D),
            highlight = Color(0xFFF6EFEE),
            ringEmphasis = Color(0xFFC13A00),
            ringFade = Color(0xFF7D5543),
            particle = Color(0xFF57F5F6)
        )
    )
    
    /**
     * 根据分数获取对应的颜色集合
     *
     * @param score 分数（0-100）
     * @param themeColors 主题颜色方案（默认使用天空蓝主题）
     * @return 对应分数等级的颜色集合
     */
    fun getColorSetForScore(
        score: Int,
        themeColors: WaterBallAdvancedColors = skyBlue
    ): WaterBallColorSet {
        return when {
            score >= 80 -> themeColors.excellent
            score >= 60 -> themeColors.good
            else -> themeColors.needsImprovement
        }
    }
    
    /**
     * 获取渐变色列表
     *
     * 用于创建线性渐变效果
     *
     * @param colorSet 颜色集合
     * @return 渐变色列表（从底部到顶部）
     */
    fun getGradientColors(colorSet: WaterBallColorSet): List<Color> {
        return listOf(
            colorSet.gradientStart,
            colorSet.gradientMid,
            colorSet.gradientEnd
        )
    }
    
    /**
     * 获取高光色（带透明度）
     *
     * @param colorSet 颜色集合
     * @param alpha 透明度（0.0-1.0，默认0.8）
     * @return 带透明度的高光色
     */
    fun getHighlightColor(
        colorSet: WaterBallColorSet,
        alpha: Float = 0.8f
    ): Color {
        return colorSet.highlight.copy(alpha = alpha)
    }
    
    /**
     * 获取圆环颜色（带透明度）
     *
     * @param colorSet 颜色集合
     * @param alpha 透明度（0.0-1.0，默认0.9）
     * @return 带透明度的圆环强调色
     */
    fun getRingColor(
        colorSet: WaterBallColorSet,
        alpha: Float = 0.9f
    ): Color {
        return colorSet.ringEmphasis.copy(alpha = alpha)
    }
    
    /**
     * 获取粒子颜色（带透明度）
     *
     * @param colorSet 颜色集合
     * @param alpha 透明度（0.0-1.0，默认0.3）
     * @return 带透明度的粒子色
     */
    fun getParticleColor(
        colorSet: WaterBallColorSet,
        alpha: Float = 0.3f
    ): Color {
        return colorSet.particle.copy(alpha = alpha)
    }
}

/**
 * 水球高级材质效果类型
 *
 * 定义不同的材质效果类型，便于扩展
 */
enum class WaterBallMaterialType {
    /**
     * 标准材质 - 使用原有的单色填充
     */
    STANDARD,
    
    /**
     * 高级材质 - 使用渐变、高光、圆环等高级效果
     */
    ADVANCED
}

/**
 * 水球材质配置
 *
 * 用于配置水球的材质效果
 *
 * @property materialType 材质类型
 * @property enableGradient 是否启用渐变效果
 * @property enableHighlight 是否启用高光效果
 * @property enableRingEmphasis 是否启用圆环强调效果
 * @property enableParticleEffects 是否启用粒子效果
 */
data class WaterBallMaterialConfig(
    val materialType: WaterBallMaterialType = WaterBallMaterialType.STANDARD,
    val enableGradient: Boolean = true,
    val enableHighlight: Boolean = true,
    val enableRingEmphasis: Boolean = true,
    val enableParticleEffects: Boolean = true
) {
    companion object {
        /**
         * 标准材质配置
         */
        val standard = WaterBallMaterialConfig(
            materialType = WaterBallMaterialType.STANDARD,
            enableGradient = false,
            enableHighlight = false,
            enableRingEmphasis = false,
            enableParticleEffects = false
        )
        
        /**
         * 高级材质配置
         */
        val advanced = WaterBallMaterialConfig(
            materialType = WaterBallMaterialType.ADVANCED,
            enableGradient = true,
            enableHighlight = true,
            enableRingEmphasis = true,
            enableParticleEffects = true
        )
    }
}
