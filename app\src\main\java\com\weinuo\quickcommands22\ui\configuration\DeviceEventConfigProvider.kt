package com.weinuo.quickcommands22.ui.configuration

import android.content.Context
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import com.weinuo.quickcommands22.R
import com.weinuo.quickcommands22.navigation.Screen
import com.weinuo.quickcommands22.model.*
import com.weinuo.quickcommands22.ui.components.ConfigurationCardItem
import com.weinuo.quickcommands22.ui.screens.LocalNavController
import com.weinuo.quickcommands22.model.SimpleAppInfo
import com.weinuo.quickcommands22.storage.UIStateStorageManager
import com.weinuo.quickcommands22.ui.activities.AdvancedMemoryConfigActivity
import java.util.*

/**
 * 设备事件配置数据提供器
 *
 * 基于通用组件复用配置系统架构，提供17种设备事件类型的配置项。
 * 使用ConfigurationCardItem数据结构和DetailConfigurationScreen通用界面，
 * 实现高度模块化和可扩展的设备事件配置系统。
 */
object DeviceEventConfigProvider {

    /**
     * 获取设备事件配置项列表
     *
     * @param context 上下文，用于获取字符串资源
     * @return 设备事件配置项列表
     */
    fun getConfigurationItems(context: Context): List<ConfigurationCardItem<DeviceEventType>> {
        return listOf(
            // GPS状态配置项
            ConfigurationCardItem(
                id = "gps_state",
                title = context.getString(R.string.device_gps_state),
                description = context.getString(R.string.device_gps_state_description),
                operationType = DeviceEventType.GPS_STATE,
                permissionRequired = true,
                content = { type, onComplete ->
                    GpsStateConfigContent(type, onComplete)
                }
            ),

            // Logcat消息配置项
            ConfigurationCardItem(
                id = "logcat_message",
                title = context.getString(R.string.device_logcat_message),
                description = context.getString(R.string.device_logcat_message_description),
                operationType = DeviceEventType.LOGCAT_MESSAGE,
                permissionRequired = true,
                content = { type, onComplete ->
                    LogcatMessageConfigContent(type, onComplete)
                }
            ),

            // 剪贴板变化配置项
            ConfigurationCardItem(
                id = "clipboard_changed",
                title = context.getString(R.string.device_clipboard_changed),
                description = context.getString(R.string.device_clipboard_changed_description),
                operationType = DeviceEventType.CLIPBOARD_CHANGED,
                permissionRequired = false,
                content = { type, onComplete ->
                    ClipboardChangedConfigContent(type, onComplete)
                }
            ),

            // 屏幕状态配置项
            ConfigurationCardItem(
                id = "screen_state",
                title = "屏幕状态",
                description = "当屏幕状态改变时触发条件",
                operationType = DeviceEventType.SCREEN_STATE,
                permissionRequired = false,
                content = { type, onComplete ->
                    ScreenStateConfigContent(type, onComplete)
                }
            ),

            // 底座连接配置项
            ConfigurationCardItem(
                id = "dock_state",
                title = "底座连接",
                description = "当设备连接或断开底座时触发条件",
                operationType = DeviceEventType.DOCK_STATE,
                permissionRequired = false,
                content = { type, onComplete ->
                    DockStateConfigContent(type, onComplete)
                }
            ),

            // Intent接收配置项
            ConfigurationCardItem(
                id = "intent_received",
                title = "Intent接收",
                description = "当接收到指定Intent时触发条件",
                operationType = DeviceEventType.INTENT_RECEIVED,
                permissionRequired = false,
                content = { type, onComplete ->
                    IntentReceivedConfigContent(type, onComplete)
                }
            ),

            // SIM卡状态配置项
            ConfigurationCardItem(
                id = "sim_card_state",
                title = "SIM卡状态",
                description = "当SIM卡状态改变时触发条件",
                operationType = DeviceEventType.SIM_CARD_STATE,
                permissionRequired = true,
                content = { type, onComplete ->
                    SimCardStateConfigContent(type, onComplete)
                }
            ),

            // 深色主题配置项
            ConfigurationCardItem(
                id = "dark_theme_changed",
                title = "深色主题",
                description = "当深色主题状态改变时触发条件",
                operationType = DeviceEventType.DARK_THEME_CHANGED,
                permissionRequired = false,
                content = { type, onComplete ->
                    DarkThemeChangedConfigContent(type, onComplete)
                }
            ),

            // 登录失败配置项
            ConfigurationCardItem(
                id = "login_attempt_failed",
                title = "登录失败",
                description = "当登录尝试失败时触发条件",
                operationType = DeviceEventType.LOGIN_ATTEMPT_FAILED,
                permissionRequired = true,
                content = { type, onComplete ->
                    LoginAttemptFailedConfigContent(type, onComplete)
                }
            ),

            // 系统设置变化配置项
            ConfigurationCardItem(
                id = "system_setting_changed",
                title = "系统设置变化",
                description = "当系统设置发生变化时触发条件",
                operationType = DeviceEventType.SYSTEM_SETTING_CHANGED,
                permissionRequired = false,
                content = { type, onComplete ->
                    SystemSettingChangedConfigContent(type, onComplete)
                }
            ),

            // 自动同步配置项
            ConfigurationCardItem(
                id = "auto_sync_state",
                title = "自动同步",
                description = "当自动同步状态改变时触发条件",
                operationType = DeviceEventType.AUTO_SYNC_STATE,
                permissionRequired = false,
                content = { type, onComplete ->
                    AutoSyncStateConfigContent(type, onComplete)
                }
            ),

            // 设备启动配置项
            ConfigurationCardItem(
                id = "device_boot_completed",
                title = "设备启动",
                description = "当设备启动完成时触发条件",
                operationType = DeviceEventType.DEVICE_BOOT_COMPLETED,
                permissionRequired = false,
                content = { type, onComplete ->
                    DeviceBootCompletedConfigContent(type, onComplete)
                }
            ),

            // 通知事件配置项
            ConfigurationCardItem(
                id = "notification_event",
                title = "通知事件",
                description = "当通知事件发生时触发条件",
                operationType = DeviceEventType.NOTIFICATION_EVENT,
                permissionRequired = true,
                content = { type, onComplete ->
                    NotificationEventConfigContent(type, onComplete)
                }
            ),

            // 静音模式配置项
            ConfigurationCardItem(
                id = "ringer_mode_changed",
                title = "静音模式",
                description = "当静音模式改变时触发条件",
                operationType = DeviceEventType.RINGER_MODE_CHANGED,
                permissionRequired = false,
                content = { type, onComplete ->
                    RingerModeChangedConfigContent(type, onComplete)
                }
            ),

            // 音乐播放配置项
            ConfigurationCardItem(
                id = "music_playback_state",
                title = "音乐播放",
                description = "当音乐播放状态改变时触发条件",
                operationType = DeviceEventType.MUSIC_PLAYBACK_STATE,
                permissionRequired = false,
                content = { type, onComplete ->
                    MusicPlaybackStateConfigContent(type, onComplete)
                }
            ),

            // 飞行模式配置项
            ConfigurationCardItem(
                id = "airplane_mode_state",
                title = "飞行模式",
                description = "当飞行模式状态改变时触发条件",
                operationType = DeviceEventType.AIRPLANE_MODE_STATE,
                permissionRequired = false,
                content = { type, onComplete ->
                    AirplaneModeStateConfigContent(type, onComplete)
                }
            ),

            // 音量变化配置项
            ConfigurationCardItem(
                id = "volume_changed",
                title = "音量变化",
                description = "当音量改变时触发条件",
                operationType = DeviceEventType.VOLUME_CHANGED,
                permissionRequired = false,
                content = { type, onComplete ->
                    VolumeChangedConfigContent(type, onComplete)
                }
            ),

            // 内存状态配置项
            ConfigurationCardItem(
                id = "memory_state",
                title = "内存状态",
                description = "当内存状态改变时触发条件",
                operationType = DeviceEventType.MEMORY_STATE,
                permissionRequired = false,
                content = { type, onComplete ->
                    MemoryStateConfigContent(type, onComplete)
                }
            )
        )
    }
}

/**
 * GPS状态配置内容组件
 *
 * GPS状态需要位置权限，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun GpsStateConfigContent(
    type: DeviceEventType,
    onComplete: (Any) -> Unit
) {
    var selectedGpsStateType by rememberSaveable { mutableStateOf(GpsStateType.ENABLED) }

    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "配置GPS状态触发条件",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        Text(
            text = "GPS状态类型",
            style = MaterialTheme.typography.bodyMedium
        )

        // GPS状态类型选择
        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            GpsStateType.values().forEach { gpsStateType ->
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    RadioButton(
                        selected = selectedGpsStateType == gpsStateType,
                        onClick = { selectedGpsStateType = gpsStateType }
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(gpsStateType.displayName)
                }
            }
        }

        // 确认按钮
        Button(
            onClick = {
                val condition = DeviceEventCondition(
                    id = UUID.randomUUID().toString(),
                    eventType = type,
                    gpsStateType = selectedGpsStateType
                )
                onComplete(condition)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * Logcat消息配置内容组件
 *
 * Logcat消息需要日志读取权限，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun LogcatMessageConfigContent(
    type: DeviceEventType,
    onComplete: (Any) -> Unit
) {
    var logcatComponent by rememberSaveable { mutableStateOf("") }
    var logcatText by rememberSaveable { mutableStateOf("") }
    var selectedLogcatBufferTypes by rememberSaveable { mutableStateOf(setOf(LogcatBufferType.MAIN)) } // 改为多选
    var logcatCaseSensitive by rememberSaveable { mutableStateOf(false) }

    Column(
        modifier = Modifier
            .fillMaxWidth(), // 移除高度限制，让ExpandableConfigurationCard的滚动机制处理
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "配置Logcat消息触发条件",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        // 组件名称输入
        OutlinedTextField(
            value = logcatComponent,
            onValueChange = { logcatComponent = it },
            label = { Text("组件名称（可选）") },
            placeholder = { Text("例如：ActivityManager") },
            modifier = Modifier.fillMaxWidth()
        )

        // 消息文本输入
        OutlinedTextField(
            value = logcatText,
            onValueChange = { logcatText = it },
            label = { Text("消息文本") },
            placeholder = { Text("支持通配符 * 和 ?") },
            modifier = Modifier.fillMaxWidth()
        )

        // 缓冲区类型选择（多选）
        Text(
            text = "缓冲区类型（可多选）",
            style = MaterialTheme.typography.bodyMedium
        )

        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            LogcatBufferType.values().forEach { bufferType ->
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Checkbox(
                        checked = selectedLogcatBufferTypes.contains(bufferType),
                        onCheckedChange = { isChecked ->
                            selectedLogcatBufferTypes = if (isChecked) {
                                selectedLogcatBufferTypes + bufferType
                            } else {
                                selectedLogcatBufferTypes - bufferType
                            }
                        }
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(bufferType.displayName)
                }
            }
        }

        // 大小写敏感选项（改为不区分大小写）
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.fillMaxWidth()
        ) {
            Checkbox(
                checked = !logcatCaseSensitive, // 反转逻辑
                onCheckedChange = { logcatCaseSensitive = !it }
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text("不区分大小写")
        }

        // 确认按钮
        Button(
            onClick = {
                val condition = DeviceEventCondition(
                    id = UUID.randomUUID().toString(),
                    eventType = type,
                    logcatComponent = logcatComponent,
                    logcatText = logcatText,
                    logcatBufferTypes = selectedLogcatBufferTypes.toList(), // 转换为列表
                    logcatCaseSensitive = logcatCaseSensitive
                )
                onComplete(condition)
            },
            modifier = Modifier.fillMaxWidth(),
            enabled = selectedLogcatBufferTypes.isNotEmpty() // 至少选择一个缓冲区类型
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 剪贴板变化配置内容组件
 *
 * 剪贴板变化不需要特殊权限，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun ClipboardChangedConfigContent(
    type: DeviceEventType,
    onComplete: (Any) -> Unit
) {
    var clipboardText by rememberSaveable { mutableStateOf("") }
    var clipboardUseRegex by rememberSaveable { mutableStateOf(false) }
    var clipboardCaseSensitive by rememberSaveable { mutableStateOf(false) } // 新增：大小写敏感

    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "配置剪贴板变化触发条件",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        // 剪贴板文本输入
        OutlinedTextField(
            value = clipboardText,
            onValueChange = { clipboardText = it },
            label = { Text("匹配文本（可选）") },
            placeholder = { Text("留空表示任何剪贴板变化") },
            modifier = Modifier.fillMaxWidth()
        )

        // 正则表达式选项
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.fillMaxWidth()
        ) {
            Checkbox(
                checked = clipboardUseRegex,
                onCheckedChange = { clipboardUseRegex = it }
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text("使用正则表达式")
        }

        // 不区分大小写选项
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.fillMaxWidth()
        ) {
            Checkbox(
                checked = !clipboardCaseSensitive, // 反转逻辑
                onCheckedChange = { clipboardCaseSensitive = !it }
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text("不区分大小写")
        }

        // 确认按钮
        Button(
            onClick = {
                val condition = DeviceEventCondition(
                    id = UUID.randomUUID().toString(),
                    eventType = type,
                    clipboardText = clipboardText,
                    clipboardUseRegex = clipboardUseRegex,
                    clipboardCaseSensitive = clipboardCaseSensitive
                )
                onComplete(condition)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 屏幕状态配置内容组件
 *
 * 屏幕状态不需要特殊权限，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun ScreenStateConfigContent(
    type: DeviceEventType,
    onComplete: (Any) -> Unit
) {
    var selectedScreenEventType by rememberSaveable { mutableStateOf(ScreenEventType.ON) }

    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "配置屏幕状态触发条件",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        Text(
            text = "屏幕事件类型",
            style = MaterialTheme.typography.bodyMedium
        )

        // 屏幕事件类型选择
        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            ScreenEventType.values().forEach { screenEventType ->
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    RadioButton(
                        selected = selectedScreenEventType == screenEventType,
                        onClick = { selectedScreenEventType = screenEventType }
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(screenEventType.displayName)
                }
            }
        }

        // 确认按钮
        Button(
            onClick = {
                val condition = DeviceEventCondition(
                    id = UUID.randomUUID().toString(),
                    eventType = type,
                    screenEventType = selectedScreenEventType
                )
                onComplete(condition)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 底座连接配置内容组件
 *
 * 底座连接不需要特殊权限，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun DockStateConfigContent(
    type: DeviceEventType,
    onComplete: (Any) -> Unit
) {
    var selectedDockStateType by rememberSaveable { mutableStateOf(DockStateType.ANY_DOCK) }

    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "配置底座连接触发条件",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        Text(
            text = "底座状态类型",
            style = MaterialTheme.typography.bodyMedium
        )

        // 底座状态类型选择
        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            DockStateType.values().forEach { dockStateType ->
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    RadioButton(
                        selected = selectedDockStateType == dockStateType,
                        onClick = { selectedDockStateType = dockStateType }
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(dockStateType.displayName)
                }
            }
        }

        // 确认按钮
        Button(
            onClick = {
                val condition = DeviceEventCondition(
                    id = UUID.randomUUID().toString(),
                    eventType = type,
                    dockStateType = selectedDockStateType
                )
                onComplete(condition)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * Intent接收配置内容组件
 *
 * Intent接收不需要特殊权限，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun IntentReceivedConfigContent(
    type: DeviceEventType,
    onComplete: (Any) -> Unit
) {
    var intentAction by rememberSaveable { mutableStateOf("") }
    var intentExtraKey by rememberSaveable { mutableStateOf("") }
    var intentExtraValue by rememberSaveable { mutableStateOf("") }

    Column(
        modifier = Modifier
            .fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "配置Intent接收触发条件",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        // Intent动作输入
        OutlinedTextField(
            value = intentAction,
            onValueChange = { intentAction = it },
            label = { Text("Intent动作") },
            placeholder = { Text("例如：android.intent.action.BATTERY_LOW") },
            modifier = Modifier.fillMaxWidth()
        )

        // Extra键输入
        OutlinedTextField(
            value = intentExtraKey,
            onValueChange = { intentExtraKey = it },
            label = { Text("Extra键（可选）") },
            placeholder = { Text("例如：level") },
            modifier = Modifier.fillMaxWidth()
        )

        // Extra值输入
        OutlinedTextField(
            value = intentExtraValue,
            onValueChange = { intentExtraValue = it },
            label = { Text("Extra值（可选）") },
            placeholder = { Text("支持通配符 * 和 ?") },
            modifier = Modifier.fillMaxWidth()
        )

        // 确认按钮
        Button(
            onClick = {
                val condition = DeviceEventCondition(
                    id = UUID.randomUUID().toString(),
                    eventType = type,
                    intentAction = intentAction,
                    intentExtraKey = intentExtraKey,
                    intentExtraValue = intentExtraValue
                )
                onComplete(condition)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * SIM卡状态配置内容组件
 *
 * SIM卡状态需要电话状态权限，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun SimCardStateConfigContent(
    type: DeviceEventType,
    onComplete: (Any) -> Unit
) {
    var selectedSimCardStateType by rememberSaveable { mutableStateOf(SimCardStateType.STATE_CHANGED) }

    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "配置SIM卡状态触发条件",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        Text(
            text = "SIM卡状态类型",
            style = MaterialTheme.typography.bodyMedium
        )

        // SIM卡状态类型选择
        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            SimCardStateType.values().forEach { simCardStateType ->
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    RadioButton(
                        selected = selectedSimCardStateType == simCardStateType,
                        onClick = { selectedSimCardStateType = simCardStateType }
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(simCardStateType.displayName)
                }
            }
        }

        // 确认按钮
        Button(
            onClick = {
                val condition = DeviceEventCondition(
                    id = UUID.randomUUID().toString(),
                    eventType = type,
                    simCardStateType = selectedSimCardStateType
                )
                onComplete(condition)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 深色主题配置内容组件
 *
 * 深色主题状态不需要特殊权限，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun DarkThemeChangedConfigContent(
    type: DeviceEventType,
    onComplete: (Any) -> Unit
) {
    var selectedDarkThemeStateType by rememberSaveable { mutableStateOf(DarkThemeStateType.ENABLED) }

    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "配置深色主题触发条件",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        Text(
            text = "深色主题状态",
            style = MaterialTheme.typography.bodyMedium
        )

        // 深色主题状态选择
        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            DarkThemeStateType.values().forEach { darkThemeStateType ->
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    RadioButton(
                        selected = selectedDarkThemeStateType == darkThemeStateType,
                        onClick = { selectedDarkThemeStateType = darkThemeStateType }
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(darkThemeStateType.displayName)
                }
            }
        }

        // 确认按钮
        Button(
            onClick = {
                val condition = DeviceEventCondition(
                    id = UUID.randomUUID().toString(),
                    eventType = type,
                    darkThemeStateType = selectedDarkThemeStateType
                )
                onComplete(condition)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}



/**
 * 其他简单设备事件的通用配置内容组件
 * 用于不需要额外参数的设备事件类型
 */
@Composable
private fun SimpleDeviceEventConfigContent(
    type: DeviceEventType,
    title: String,
    onComplete: (Any) -> Unit
) {
    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "配置${title}触发条件",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        Text(
            text = "此事件类型无需额外配置参数，点击确认即可完成配置。",
            style = MaterialTheme.typography.bodyMedium
        )

        // 确认按钮
        Button(
            onClick = {
                val condition = DeviceEventCondition(
                    id = UUID.randomUUID().toString(),
                    eventType = type
                )
                onComplete(condition)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

// 为简单的设备事件类型创建配置组件

@Composable
private fun AutoSyncStateConfigContent(type: DeviceEventType, onComplete: (Any) -> Unit) {
    SimpleDeviceEventConfigContent(type, "自动同步状态", onComplete)
}

@Composable
private fun DeviceBootCompletedConfigContent(type: DeviceEventType, onComplete: (Any) -> Unit) {
    SimpleDeviceEventConfigContent(type, "设备启动完成", onComplete)
}

/**
 * 通知事件配置内容组件
 *
 * 通知事件需要通知使用权限，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun NotificationEventConfigContent(type: DeviceEventType, onComplete: (Any) -> Unit) {
    // 基本配置状态
    var selectedNotificationEventType by rememberSaveable { mutableStateOf(NotificationEventType.RECEIVED) } // 改为收到通知
    var notificationAppSelectionMode by rememberSaveable { mutableStateOf(NotificationAppSelectionMode.ANY) }

    // 分别保存应用信息的各个字段，避免SimpleAppInfo序列化问题
    var notificationSelectedAppPackageNames by rememberSaveable { mutableStateOf<List<String>>(emptyList()) }
    var notificationSelectedAppNames by rememberSaveable { mutableStateOf<List<String>>(emptyList()) }
    var notificationSelectedAppIsSystemApps by rememberSaveable { mutableStateOf<List<Boolean>>(emptyList()) }

    // 根据保存的字段重建SimpleAppInfo对象列表
    val notificationSelectedApps = if (notificationSelectedAppPackageNames.isNotEmpty() &&
                                      notificationSelectedAppNames.size == notificationSelectedAppPackageNames.size &&
                                      notificationSelectedAppIsSystemApps.size == notificationSelectedAppPackageNames.size) {
        notificationSelectedAppPackageNames.mapIndexed { index, packageName ->
            SimpleAppInfo(
                packageName = packageName,
                appName = notificationSelectedAppNames[index],
                isSystemApp = notificationSelectedAppIsSystemApps[index]
            )
        }
    } else {
        emptyList()
    }

    var notificationAppIncludeMode by rememberSaveable { mutableStateOf(NotificationAppIncludeMode.INCLUDE) }
    var notificationContentMatchMode by rememberSaveable { mutableStateOf(NotificationContentMatchMode.ANY) }
    var notificationTitle by rememberSaveable { mutableStateOf("") }
    var notificationText by rememberSaveable { mutableStateOf("") }

    // 高级选项状态
    var notificationCaseSensitive by rememberSaveable { mutableStateOf(false) }
    var notificationUseRegex by rememberSaveable { mutableStateOf(false) }
    var notificationIgnoreOngoing by rememberSaveable { mutableStateOf(false) }
    var notificationPreventMultipleTrigger by rememberSaveable { mutableStateOf(false) }
    var notificationSoundMode by rememberSaveable { mutableStateOf(NotificationSoundMode.ANY) }

    // 导航相关
    val context = LocalContext.current
    val navController = LocalNavController.current
    val uiStateManager = remember { UIStateStorageManager(context) }
    val stateKey = "notification_event_config"

    // 处理应用选择结果（使用原生数据类型存储）
    LaunchedEffect(navController) {
        val savedStateHandle = navController?.currentBackStackEntry?.savedStateHandle
        // 获取导航键
        val navigationKey = savedStateHandle?.get<String>("selected_apps_navigation_key")

        if (navigationKey != null) {
            // 从原生数据类型存储加载应用选择结果
            val selectedAppsResult = uiStateManager.loadAppListState(navigationKey, "selected_apps")
            if (selectedAppsResult.isNotEmpty()) {
                // 自动切换到选择应用模式
                notificationAppSelectionMode = NotificationAppSelectionMode.SELECTED
                // 分别保存应用信息的各个字段
                notificationSelectedAppPackageNames = selectedAppsResult.map { it.packageName }
                notificationSelectedAppNames = selectedAppsResult.map { it.appName }
                notificationSelectedAppIsSystemApps = selectedAppsResult.map { it.isSystemApp }
                // 保存到持久化存储
                uiStateManager.saveAppListState(stateKey, "selected_apps", selectedAppsResult)
            }
            // 清除结果，避免重复处理
            savedStateHandle.remove<String>("selected_apps_navigation_key")
            uiStateManager.clearAppListState(navigationKey, "selected_apps")
        }
    }

    // 初始化时加载保存的应用列表
    LaunchedEffect(Unit) {
        val savedApps = uiStateManager.loadAppListState(stateKey, "selected_apps")
        if (savedApps.isNotEmpty()) {
            notificationSelectedAppPackageNames = savedApps.map { it.packageName }
            notificationSelectedAppNames = savedApps.map { it.appName }
            notificationSelectedAppIsSystemApps = savedApps.map { it.isSystemApp }
        }
    }

    Column(
        modifier = Modifier
            .fillMaxWidth(), // 移除verticalScroll，依赖外层LazyColumn的滚动机制
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "配置通知事件触发条件",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        // 通知事件类型选择
        Text(
            text = "通知事件类型",
            style = MaterialTheme.typography.bodyMedium
        )
        NotificationEventType.values().forEach { eventType ->
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier
                    .fillMaxWidth()
                    .selectable(
                        selected = selectedNotificationEventType == eventType,
                        onClick = { selectedNotificationEventType = eventType }
                    )
                    .padding(vertical = 4.dp)
            ) {
                RadioButton(
                    selected = selectedNotificationEventType == eventType,
                    onClick = { selectedNotificationEventType = eventType }
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(eventType.displayName)
            }
        }

        // 应用选择模式
        Text(
            text = "应用选择",
            style = MaterialTheme.typography.bodyMedium
        )
        NotificationAppSelectionMode.values().forEach { mode ->
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier
                    .fillMaxWidth()
                    .selectable(
                        selected = notificationAppSelectionMode == mode,
                        onClick = { notificationAppSelectionMode = mode }
                    )
                    .padding(vertical = 4.dp)
            ) {
                RadioButton(
                    selected = notificationAppSelectionMode == mode,
                    onClick = { notificationAppSelectionMode = mode }
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(mode.displayName)
            }
        }

        // 选择应用按钮（仅在选择应用模式下显示）
        if (notificationAppSelectionMode == NotificationAppSelectionMode.SELECTED) {
            OutlinedButton(
                onClick = {
                    if (navController != null) {
                        // 导航到应用选择界面
                        val selectedPackageNames = notificationSelectedApps.map { it.packageName }
                        val route = Screen.AppSelection.createMultiSelectionRoute(selectedPackageNames)
                        navController.navigate(route)
                    }
                },
                modifier = Modifier.fillMaxWidth()
            ) {
                Text(
                    if (notificationSelectedApps.isEmpty()) "选择应用"
                    else "已选择 ${notificationSelectedApps.size} 个应用"
                )
            }

            // 显示已选择的应用
            if (notificationSelectedApps.isNotEmpty()) {
                Text(
                    text = "已选择的应用：${notificationSelectedApps.joinToString(", ") { it.appName }}",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }

            // 包括/排除模式选择
            Text(
                text = "应用筛选模式",
                style = MaterialTheme.typography.bodyMedium
            )
            NotificationAppIncludeMode.values().forEach { mode ->
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier
                        .fillMaxWidth()
                        .selectable(
                            selected = notificationAppIncludeMode == mode,
                            onClick = { notificationAppIncludeMode = mode }
                        )
                        .padding(vertical = 4.dp)
                ) {
                    RadioButton(
                        selected = notificationAppIncludeMode == mode,
                        onClick = { notificationAppIncludeMode = mode }
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(mode.displayName)
                }
            }
        }

        // 内容匹配模式
        Text(
            text = "匹配内容",
            style = MaterialTheme.typography.bodyMedium
        )
        NotificationContentMatchMode.values().forEach { mode ->
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier
                    .fillMaxWidth()
                    .selectable(
                        selected = notificationContentMatchMode == mode,
                        onClick = { notificationContentMatchMode = mode }
                    )
                    .padding(vertical = 4.dp)
            ) {
                RadioButton(
                    selected = notificationContentMatchMode == mode,
                    onClick = { notificationContentMatchMode = mode }
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(mode.displayName)
            }
        }

        // 标题和内容输入（仅在非任何模式下显示）
        if (notificationContentMatchMode != NotificationContentMatchMode.ANY) {
            OutlinedTextField(
                value = notificationTitle,
                onValueChange = { notificationTitle = it },
                label = { Text("通知标题（可选）") },
                placeholder = { Text("留空表示不匹配标题") },
                modifier = Modifier.fillMaxWidth()
            )

            OutlinedTextField(
                value = notificationText,
                onValueChange = { notificationText = it },
                label = { Text("通知内容（可选）") },
                placeholder = { Text("留空表示不匹配内容") },
                modifier = Modifier.fillMaxWidth()
            )
        }

        // 高级选项
        Text(
            text = "高级选项",
            style = MaterialTheme.typography.bodyMedium
        )

        // 不区分大小写
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.fillMaxWidth()
        ) {
            Checkbox(
                checked = !notificationCaseSensitive, // 反转逻辑
                onCheckedChange = { notificationCaseSensitive = !it }
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text("不区分大小写")
        }

        // 使用正则表达式
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.fillMaxWidth()
        ) {
            Checkbox(
                checked = notificationUseRegex,
                onCheckedChange = { notificationUseRegex = it }
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text("使用正则表达式")
        }

        // 忽略正在进行的通知
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.fillMaxWidth()
        ) {
            Checkbox(
                checked = notificationIgnoreOngoing,
                onCheckedChange = { notificationIgnoreOngoing = it }
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text("忽略正在进行的通知")
        }

        // 防止多重触发
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.fillMaxWidth()
        ) {
            Checkbox(
                checked = notificationPreventMultipleTrigger,
                onCheckedChange = { notificationPreventMultipleTrigger = it }
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text("防止多重触发")
        }

        // 通知声音模式
        Text(
            text = "通知声音",
            style = MaterialTheme.typography.bodyMedium
        )
        NotificationSoundMode.values().forEach { mode ->
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier
                    .fillMaxWidth()
                    .selectable(
                        selected = notificationSoundMode == mode,
                        onClick = { notificationSoundMode = mode }
                    )
                    .padding(vertical = 4.dp)
            ) {
                RadioButton(
                    selected = notificationSoundMode == mode,
                    onClick = { notificationSoundMode = mode }
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(mode.displayName)
            }
        }

        // 确认按钮
        Button(
            onClick = {
                val condition = DeviceEventCondition(
                    id = UUID.randomUUID().toString(),
                    eventType = type,
                    notificationEventType = selectedNotificationEventType,
                    notificationAppSelectionMode = notificationAppSelectionMode,
                    notificationSelectedApps = notificationSelectedApps.map { it.packageName },
                    notificationAppIncludeMode = notificationAppIncludeMode,
                    notificationContentMatchMode = notificationContentMatchMode,
                    notificationTitle = notificationTitle,
                    notificationText = notificationText,
                    notificationCaseSensitive = notificationCaseSensitive,
                    notificationUseRegex = notificationUseRegex,
                    notificationIgnoreOngoing = notificationIgnoreOngoing,
                    notificationPreventMultipleTrigger = notificationPreventMultipleTrigger,
                    notificationSoundMode = notificationSoundMode
                )
                onComplete(condition)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 铃声模式变化配置内容组件
 *
 * 铃声模式不需要特殊权限，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun RingerModeChangedConfigContent(type: DeviceEventType, onComplete: (Any) -> Unit) {
    var selectedRingerModeType by rememberSaveable { mutableStateOf(RingerModeType.NORMAL) }

    Column(
        modifier = Modifier
            .fillMaxWidth(), // 移除高度限制，让ExpandableConfigurationCard的滚动机制处理
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "配置铃声模式变化触发条件",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        // 铃声模式类型选择
        Text(
            text = "铃声模式类型",
            style = MaterialTheme.typography.bodyMedium
        )
        RingerModeType.values().forEach { modeType ->
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier
                    .fillMaxWidth()
                    .selectable(
                        selected = selectedRingerModeType == modeType,
                        onClick = { selectedRingerModeType = modeType }
                    )
                    .padding(vertical = 4.dp)
            ) {
                RadioButton(
                    selected = selectedRingerModeType == modeType,
                    onClick = { selectedRingerModeType = modeType }
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(modeType.displayName)
            }
        }

        // 确认按钮
        Button(
            onClick = {
                val condition = DeviceEventCondition(
                    id = UUID.randomUUID().toString(),
                    eventType = type,
                    ringerModeType = selectedRingerModeType
                )
                onComplete(condition)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 音乐播放状态配置内容组件
 *
 * 音乐播放状态不需要特殊权限，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun MusicPlaybackStateConfigContent(type: DeviceEventType, onComplete: (Any) -> Unit) {
    var selectedMusicPlaybackType by rememberSaveable { mutableStateOf(MusicPlaybackType.STARTED) }

    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "配置音乐播放状态触发条件",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        // 音乐播放状态选择
        Text(
            text = "音乐播放状态",
            style = MaterialTheme.typography.bodyMedium
        )
        MusicPlaybackType.values().forEach { playbackType ->
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier
                    .fillMaxWidth()
                    .selectable(
                        selected = selectedMusicPlaybackType == playbackType,
                        onClick = { selectedMusicPlaybackType = playbackType }
                    )
                    .padding(vertical = 4.dp)
            ) {
                RadioButton(
                    selected = selectedMusicPlaybackType == playbackType,
                    onClick = { selectedMusicPlaybackType = playbackType }
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(playbackType.displayName)
            }
        }

        // 确认按钮
        Button(
            onClick = {
                val condition = DeviceEventCondition(
                    id = UUID.randomUUID().toString(),
                    eventType = type,
                    musicPlaybackType = selectedMusicPlaybackType
                )
                onComplete(condition)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 飞行模式状态配置内容组件
 *
 * 飞行模式状态不需要特殊权限，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun AirplaneModeStateConfigContent(type: DeviceEventType, onComplete: (Any) -> Unit) {
    var selectedAirplaneModeType by rememberSaveable { mutableStateOf(AirplaneModeType.ENABLED) }

    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "配置飞行模式状态触发条件",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        // 飞行模式状态选择
        Text(
            text = "飞行模式状态",
            style = MaterialTheme.typography.bodyMedium
        )
        AirplaneModeType.values().forEach { modeType ->
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier
                    .fillMaxWidth()
                    .selectable(
                        selected = selectedAirplaneModeType == modeType,
                        onClick = { selectedAirplaneModeType = modeType }
                    )
                    .padding(vertical = 4.dp)
            ) {
                RadioButton(
                    selected = selectedAirplaneModeType == modeType,
                    onClick = { selectedAirplaneModeType = modeType }
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(modeType.displayName)
            }
        }

        // 确认按钮
        Button(
            onClick = {
                val condition = DeviceEventCondition(
                    id = UUID.randomUUID().toString(),
                    eventType = type,
                    airplaneModeType = selectedAirplaneModeType
                )
                onComplete(condition)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 音量变化配置内容组件
 *
 * 音量变化不需要特殊权限，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun VolumeChangedConfigContent(type: DeviceEventType, onComplete: (Any) -> Unit) {
    var selectedVolumeStreamType by rememberSaveable { mutableStateOf(VolumeStreamType.MEDIA_MUSIC) }
    var volumeThreshold by rememberSaveable { mutableStateOf("5") }

    Column(
        modifier = Modifier
            .fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "配置音量变化触发条件",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        // 音量流类型选择
        Text(
            text = "音量流类型",
            style = MaterialTheme.typography.bodyMedium
        )
        Row(
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            modifier = Modifier.horizontalScroll(rememberScrollState())
        ) {
            VolumeStreamType.values().forEach { streamType ->
                FilterChip(
                    selected = selectedVolumeStreamType == streamType,
                    onClick = { selectedVolumeStreamType = streamType },
                    label = { Text(streamType.displayName) }
                )
            }
        }

        OutlinedTextField(
            value = volumeThreshold,
            onValueChange = { volumeThreshold = it },
            label = { Text("音量变化阈值 (%)") },
            placeholder = { Text("例如：5") },
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
            modifier = Modifier.fillMaxWidth(),
            isError = volumeThreshold.toIntOrNull()?.let { it < 1 || it > 100 } ?: true
        )
        if (volumeThreshold.toIntOrNull()?.let { it < 1 || it > 100 } == true) {
            Text(
                text = "请输入1-100之间的数字",
                color = MaterialTheme.colorScheme.error,
                style = MaterialTheme.typography.bodySmall
            )
        }

        // 确认按钮
        Button(
            onClick = {
                val condition = DeviceEventCondition(
                    id = UUID.randomUUID().toString(),
                    eventType = type,
                    volumeStreamType = selectedVolumeStreamType,
                    volumeThreshold = volumeThreshold.toIntOrNull() ?: 5
                )
                onComplete(condition)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 内存状态配置内容组件
 *
 * 内存状态不需要特殊权限，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun MemoryStateConfigContent(type: DeviceEventType, onComplete: (Any) -> Unit) {
    var selectedMemoryStateType by rememberSaveable { mutableStateOf(MemoryStateType.BELOW_THRESHOLD) }
    var memoryThreshold by rememberSaveable { mutableStateOf("20") }
    var isPercentageMode by rememberSaveable { mutableStateOf(true) }
    var memoryChangeThreshold by rememberSaveable { mutableStateOf("10") }

    // 检测频率配置
    var checkFrequency by rememberSaveable { mutableStateOf(ConditionCheckFrequency.BALANCED) }
    var enableCustomCheckFrequency by rememberSaveable { mutableStateOf(false) }
    var customCheckFrequencySeconds by rememberSaveable { mutableStateOf(5) }

    // 内存检测模式配置
    var memoryCheckMode by rememberSaveable { mutableStateOf(MemoryCheckMode.TRADITIONAL) }

    // 事件驱动配置
    var appLaunchDelaySeconds by rememberSaveable { mutableStateOf(10) }
    var foregroundDelaySeconds by rememberSaveable { mutableStateOf(5) }
    var monitorDurationSeconds by rememberSaveable { mutableStateOf(30) }

    // 自适应配置
    var memoryAbundantFrequency by rememberSaveable { mutableStateOf(120) }
    var memoryTightFrequency by rememberSaveable { mutableStateOf(5) }
    var adaptiveStrategy by rememberSaveable { mutableStateOf(AdaptiveStrategy.BALANCED) }

    // 智能学习配置
    var enableLearning by rememberSaveable { mutableStateOf(true) }
    var minSamplesForPrediction by rememberSaveable { mutableStateOf(5) }
    var confidenceThreshold by rememberSaveable { mutableStateOf(0.7f) }

    // 混合模式配置
    var enableEventDriven by rememberSaveable { mutableStateOf(true) }
    var enableAdaptive by rememberSaveable { mutableStateOf(true) }
    var enableIntelligent by rememberSaveable { mutableStateOf(false) }
    var eventDrivenWeight by rememberSaveable { mutableStateOf(0.4f) }
    var adaptiveWeight by rememberSaveable { mutableStateOf(0.4f) }
    var intelligentWeight by rememberSaveable { mutableStateOf(0.2f) }

    Column(
        modifier = Modifier
            .fillMaxWidth(), // 移除高度限制，让ExpandableConfigurationCard的滚动机制处理
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "配置内存状态触发条件",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        // 内存状态类型选择
        Text(
            text = "内存状态类型",
            style = MaterialTheme.typography.bodyMedium
        )
        MemoryStateType.values().forEach { stateType ->
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier
                    .fillMaxWidth()
                    .selectable(
                        selected = selectedMemoryStateType == stateType,
                        onClick = { selectedMemoryStateType = stateType }
                    )
                    .padding(vertical = 4.dp)
            ) {
                RadioButton(
                    selected = selectedMemoryStateType == stateType,
                    onClick = { selectedMemoryStateType = stateType }
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(stateType.displayName)
            }
        }

        // 阈值配置（仅在非CHANGED模式下显示）
        if (selectedMemoryStateType != MemoryStateType.CHANGED) {
            OutlinedTextField(
                value = memoryThreshold,
                onValueChange = { memoryThreshold = it },
                label = { Text(if (isPercentageMode) "内存阈值 (%)" else "内存阈值 (GB)") },
                placeholder = { Text("例如：${if (isPercentageMode) "20" else "2"}") },
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                modifier = Modifier.fillMaxWidth(),
                isError = memoryThreshold.toIntOrNull()?.let {
                    if (isPercentageMode) it < 1 || it > 100 else it < 1 || it > 32
                } ?: true
            )

            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.fillMaxWidth()
            ) {
                Checkbox(
                    checked = isPercentageMode,
                    onCheckedChange = { isPercentageMode = it }
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text("使用百分比模式")
            }
        }

        // 内存变化阈值（仅在CHANGED模式下显示）
        if (selectedMemoryStateType == MemoryStateType.CHANGED) {
            OutlinedTextField(
                value = memoryChangeThreshold,
                onValueChange = { memoryChangeThreshold = it },
                label = { Text("内存变化阈值 (%)") },
                placeholder = { Text("例如：10") },
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                modifier = Modifier.fillMaxWidth(),
                isError = memoryChangeThreshold.toIntOrNull()?.let { it < 1 || it > 50 } ?: true
            )
        }

        // 检测模式配置
        Text(
            text = "检测模式",
            style = MaterialTheme.typography.bodyMedium,
            fontWeight = FontWeight.Medium
        )

        Text(
            text = "选择内存状态检测的触发方式",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        MemoryCheckMode.values().forEach { mode ->
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier
                    .fillMaxWidth()
                    .selectable(
                        selected = memoryCheckMode == mode,
                        onClick = { memoryCheckMode = mode }
                    )
                    .padding(vertical = 4.dp)
            ) {
                RadioButton(
                    selected = memoryCheckMode == mode,
                    onClick = { memoryCheckMode = mode }
                )
                Spacer(modifier = Modifier.width(8.dp))
                Column {
                    Text(
                        text = mode.displayName,
                        style = MaterialTheme.typography.bodyMedium
                    )
                    Text(
                        text = mode.description,
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }

        // 根据选择的模式显示对应的基础配置
        when (memoryCheckMode) {
            MemoryCheckMode.EVENT_DRIVEN -> {
                EventDrivenBasicConfig(
                    appLaunchDelaySeconds = appLaunchDelaySeconds,
                    onAppLaunchDelayChange = { appLaunchDelaySeconds = it },
                    foregroundDelaySeconds = foregroundDelaySeconds,
                    onForegroundDelayChange = { foregroundDelaySeconds = it },
                    monitorDurationSeconds = monitorDurationSeconds,
                    onMonitorDurationChange = { monitorDurationSeconds = it }
                )
            }
            MemoryCheckMode.INTELLIGENT -> {
                IntelligentBasicConfig(
                    enableLearning = enableLearning,
                    onEnableLearningChange = { enableLearning = it },
                    minSamplesForPrediction = minSamplesForPrediction,
                    onMinSamplesChange = { minSamplesForPrediction = it },
                    confidenceThreshold = confidenceThreshold,
                    onConfidenceThresholdChange = { confidenceThreshold = it },
                    appLaunchDelaySeconds = appLaunchDelaySeconds,
                    onAppLaunchDelayChange = { appLaunchDelaySeconds = it },
                    monitorDurationSeconds = monitorDurationSeconds,
                    onMonitorDurationChange = { monitorDurationSeconds = it }
                )
            }
            MemoryCheckMode.HYBRID -> {
                HybridBasicConfig(
                    enableLearning = enableLearning,
                    onEnableLearningChange = { enableLearning = it },
                    adaptiveStrategy = adaptiveStrategy,
                    onAdaptiveStrategyChange = { adaptiveStrategy = it },
                    appLaunchDelaySeconds = appLaunchDelaySeconds,
                    onAppLaunchDelayChange = { appLaunchDelaySeconds = it },
                    monitorDurationSeconds = monitorDurationSeconds,
                    onMonitorDurationChange = { monitorDurationSeconds = it },
                    enableEventDriven = enableEventDriven,
                    onEnableEventDrivenChange = { enableEventDriven = it },
                    enableAdaptive = enableAdaptive,
                    onEnableAdaptiveChange = { enableAdaptive = it },
                    enableIntelligent = enableIntelligent,
                    onEnableIntelligentChange = { enableIntelligent = it }
                )
            }
            MemoryCheckMode.TRADITIONAL -> {
                TraditionalModeConfig(
                    checkFrequency = checkFrequency,
                    onCheckFrequencyChange = { checkFrequency = it },
                    enableCustomCheckFrequency = enableCustomCheckFrequency,
                    onEnableCustomCheckFrequencyChange = { enableCustomCheckFrequency = it },
                    customCheckFrequencySeconds = customCheckFrequencySeconds,
                    onCustomCheckFrequencySecondsChange = { customCheckFrequencySeconds = it }
                )
            }
            MemoryCheckMode.ADAPTIVE -> {
                AdaptiveBasicConfig(
                    memoryAbundantFrequency = memoryAbundantFrequency,
                    onMemoryAbundantFrequencyChange = { memoryAbundantFrequency = it },
                    memoryTightFrequency = memoryTightFrequency,
                    onMemoryTightFrequencyChange = { memoryTightFrequency = it }
                )
            }
        }

        // 高级设置按钮
        if (memoryCheckMode != MemoryCheckMode.TRADITIONAL) {
            val context = LocalContext.current
            OutlinedButton(
                onClick = {
                    AdvancedMemoryConfigActivity.startForConfiguration(
                        context = context,
                        mode = memoryCheckMode,
                        conditionId = "temp_condition_id"
                    )
                },
                modifier = Modifier.fillMaxWidth()
            ) {
                Text("高级设置")
            }
        }


        // 确认按钮
        Button(
            onClick = {
                val condition = DeviceEventCondition(
                    id = UUID.randomUUID().toString(),
                    eventType = type,
                    memoryStateType = selectedMemoryStateType,
                    memoryThreshold = memoryThreshold.toIntOrNull() ?: 2,
                    isPercentageMode = isPercentageMode,
                    memoryChangeThreshold = memoryChangeThreshold.toIntOrNull() ?: 10,
                    checkFrequency = checkFrequency,
                    enableCustomCheckFrequency = enableCustomCheckFrequency,
                    customCheckFrequencySeconds = customCheckFrequencySeconds,
                    memoryCheckMode = memoryCheckMode,
                    appLaunchDelaySeconds = appLaunchDelaySeconds,
                    foregroundDelaySeconds = foregroundDelaySeconds,
                    monitorDurationSeconds = monitorDurationSeconds,
                    memoryAbundantFrequency = memoryAbundantFrequency,
                    memoryTightFrequency = memoryTightFrequency,
                    adaptiveStrategy = adaptiveStrategy,
                    enableLearning = enableLearning,
                    minSamplesForPrediction = minSamplesForPrediction,
                    confidenceThreshold = confidenceThreshold,
                    enableEventDriven = enableEventDriven,
                    enableAdaptive = enableAdaptive,
                    enableIntelligent = enableIntelligent,
                    eventDrivenWeight = eventDrivenWeight,
                    adaptiveWeight = adaptiveWeight,
                    intelligentWeight = intelligentWeight
                )
                onComplete(condition)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 事件驱动模式的基础配置组件
 */
@Composable
private fun EventDrivenBasicConfig(
    appLaunchDelaySeconds: Int,
    onAppLaunchDelayChange: (Int) -> Unit,
    foregroundDelaySeconds: Int,
    onForegroundDelayChange: (Int) -> Unit,
    monitorDurationSeconds: Int,
    onMonitorDurationChange: (Int) -> Unit
) {
    Column {
        Text(
            text = "事件驱动配置",
            style = MaterialTheme.typography.bodyMedium,
            fontWeight = FontWeight.Medium
        )

        Text(
            text = "配置应用启动后的检测延迟和监控时长",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        Spacer(modifier = Modifier.height(8.dp))

        // 应用启动延迟时间
        var appLaunchDelayText by remember { mutableStateOf(appLaunchDelaySeconds.toString()) }
        var appLaunchDelayError by remember { mutableStateOf(false) }

        OutlinedTextField(
            value = appLaunchDelayText,
            onValueChange = { newValue ->
                appLaunchDelayText = newValue
                val intValue = newValue.toIntOrNull()
                if (intValue != null && intValue in 3..60) {
                    onAppLaunchDelayChange(intValue)
                    appLaunchDelayError = false
                } else {
                    appLaunchDelayError = true
                }
            },
            label = { Text("应用启动后延迟检测") },
            suffix = { Text("秒") },
            placeholder = { Text("3-60秒") },
            isError = appLaunchDelayError,
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
            modifier = Modifier.fillMaxWidth()
        )

        if (appLaunchDelayError) {
            Text(
                text = "请输入 3 到 60 之间的数字",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.error
            )
        }

        Spacer(modifier = Modifier.height(8.dp))

        // 前台切换延迟时间
        var foregroundDelayText by remember { mutableStateOf(foregroundDelaySeconds.toString()) }
        var foregroundDelayError by remember { mutableStateOf(false) }

        OutlinedTextField(
            value = foregroundDelayText,
            onValueChange = { newValue ->
                foregroundDelayText = newValue
                val intValue = newValue.toIntOrNull()
                if (intValue != null && intValue in 1..30) {
                    onForegroundDelayChange(intValue)
                    foregroundDelayError = false
                } else {
                    foregroundDelayError = true
                }
            },
            label = { Text("前台切换后延迟检测") },
            suffix = { Text("秒") },
            placeholder = { Text("1-30秒") },
            isError = foregroundDelayError,
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
            modifier = Modifier.fillMaxWidth()
        )

        if (foregroundDelayError) {
            Text(
                text = "请输入 1 到 30 之间的数字",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.error
            )
        }

        Spacer(modifier = Modifier.height(8.dp))

        // 监控持续时间
        var monitorDurationText by remember { mutableStateOf(monitorDurationSeconds.toString()) }
        var monitorDurationError by remember { mutableStateOf(false) }

        OutlinedTextField(
            value = monitorDurationText,
            onValueChange = { newValue ->
                monitorDurationText = newValue
                val intValue = newValue.toIntOrNull()
                if (intValue != null && intValue in 10..300) {
                    onMonitorDurationChange(intValue)
                    monitorDurationError = false
                } else {
                    monitorDurationError = true
                }
            },
            label = { Text("连续监控时长") },
            suffix = { Text("秒") },
            placeholder = { Text("10-300秒") },
            isError = monitorDurationError,
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
            modifier = Modifier.fillMaxWidth()
        )

        if (monitorDurationError) {
            Text(
                text = "请输入 10 到 300 之间的数字",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.error
            )
        }

        if (appLaunchDelaySeconds <= 5) {
            Text(
                text = "⚠️ 延迟时间过短可能导致检测不准确",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.error
            )
        }
    }
}

/**
 * 传统模式的配置组件
 */
@Composable
private fun TraditionalModeConfig(
    checkFrequency: ConditionCheckFrequency,
    onCheckFrequencyChange: (ConditionCheckFrequency) -> Unit,
    enableCustomCheckFrequency: Boolean,
    onEnableCustomCheckFrequencyChange: (Boolean) -> Unit,
    customCheckFrequencySeconds: Int,
    onCustomCheckFrequencySecondsChange: (Int) -> Unit
) {
    Column {
        Text(
            text = "检测频率设置",
            style = MaterialTheme.typography.bodyMedium,
            fontWeight = FontWeight.Medium
        )

        Text(
            text = "设置内存状态检测的频率，频率越高响应越快但耗电越多",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        Spacer(modifier = Modifier.height(8.dp))

        // 预设检测频率选项
        ConditionCheckFrequency.values().forEach { frequency ->
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier
                    .fillMaxWidth()
                    .selectable(
                        selected = checkFrequency == frequency,
                        onClick = { onCheckFrequencyChange(frequency) }
                    )
                    .padding(vertical = 4.dp)
            ) {
                RadioButton(
                    selected = checkFrequency == frequency,
                    onClick = { onCheckFrequencyChange(frequency) }
                )
                Spacer(modifier = Modifier.width(8.dp))
                Column {
                    Text(
                        text = frequency.displayName,
                        style = MaterialTheme.typography.bodyMedium
                    )
                    Text(
                        text = frequency.description,
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }

        // 自定义检测频率选项
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.fillMaxWidth()
        ) {
            Checkbox(
                checked = enableCustomCheckFrequency,
                onCheckedChange = onEnableCustomCheckFrequencyChange
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = "启用自定义检测频率",
                style = MaterialTheme.typography.bodyMedium
            )
        }

        if (enableCustomCheckFrequency) {
            var customFrequencyText by remember { mutableStateOf(customCheckFrequencySeconds.toString()) }
            var customFrequencyError by remember { mutableStateOf(false) }

            OutlinedTextField(
                value = customFrequencyText,
                onValueChange = { newValue ->
                    customFrequencyText = newValue
                    val frequency = newValue.toIntOrNull()
                    if (frequency != null && frequency >= 1 && frequency <= 300) {
                        onCustomCheckFrequencySecondsChange(frequency)
                        customFrequencyError = false
                    } else {
                        customFrequencyError = true
                    }
                },
                label = { Text("检测间隔") },
                suffix = { Text("秒") },
                placeholder = { Text("1-300秒") },
                isError = customFrequencyError,
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                modifier = Modifier.fillMaxWidth()
            )

            if (customCheckFrequencySeconds <= 2) {
                Text(
                    text = "⚠️ 检测频率过高可能会显著增加耗电量",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.error
                )
            }
        }
    }
}

/**
 * 登录失败配置内容组件
 *
 * 登录失败检测需要设备管理员权限，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun LoginAttemptFailedConfigContent(
    type: DeviceEventType,
    onComplete: (Any) -> Unit
) {
    var loginFailureThreshold by remember { mutableStateOf("3") }

    Column(
        modifier = Modifier
            .fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "配置登录失败触发条件",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        OutlinedTextField(
            value = loginFailureThreshold,
            onValueChange = { loginFailureThreshold = it },
            label = { Text("失败次数阈值") },
            placeholder = { Text("例如：3") },
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
            modifier = Modifier.fillMaxWidth(),
            isError = loginFailureThreshold.toIntOrNull()?.let { it < 1 || it > 20 } ?: true
        )
        if (loginFailureThreshold.toIntOrNull()?.let { it < 1 || it > 20 } == true) {
            Text(
                text = "请输入1-20之间的数字",
                color = MaterialTheme.colorScheme.error,
                style = MaterialTheme.typography.bodySmall
            )
        }

        // 确认按钮
        Button(
            onClick = {
                val condition = DeviceEventCondition(
                    id = UUID.randomUUID().toString(),
                    eventType = type,
                    loginFailureThreshold = loginFailureThreshold.toIntOrNull() ?: 3
                )
                onComplete(condition)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 系统设置变化配置内容组件
 *
 * 系统设置变化不需要特殊权限，权限检查已在ExpandableConfigurationCard中统一处理
 */
@Composable
private fun SystemSettingChangedConfigContent(
    type: DeviceEventType,
    onComplete: (Any) -> Unit
) {
    var selectedSystemSettingTypes by rememberSaveable { mutableStateOf(setOf(SystemSettingType.GLOBAL)) } // 改为多选
    var systemSettingKey by rememberSaveable { mutableStateOf("") }
    var systemSettingValue by rememberSaveable { mutableStateOf("") }
    var systemSettingUseRegex by rememberSaveable { mutableStateOf(false) } // 新增：使用正则表达式

    Column(
        modifier = Modifier
            .fillMaxWidth(), // 移除高度限制，让ExpandableConfigurationCard的滚动机制处理
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "配置系统设置变化触发条件",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )

        // 系统设置类型选择（多选）
        Text(
            text = "系统设置类型（可多选）",
            style = MaterialTheme.typography.bodyMedium
        )
        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            SystemSettingType.values().forEach { settingType ->
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Checkbox(
                        checked = selectedSystemSettingTypes.contains(settingType),
                        onCheckedChange = { isChecked ->
                            selectedSystemSettingTypes = if (isChecked) {
                                selectedSystemSettingTypes + settingType
                            } else {
                                selectedSystemSettingTypes - settingType
                            }
                        }
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(settingType.displayName)
                }
            }
        }

        OutlinedTextField(
            value = systemSettingKey,
            onValueChange = { systemSettingKey = it },
            label = { Text("设置键") },
            placeholder = { Text("例如：screen_brightness") },
            modifier = Modifier.fillMaxWidth()
        )

        OutlinedTextField(
            value = systemSettingValue,
            onValueChange = { systemSettingValue = it },
            label = { Text("设置值（可选）") },
            placeholder = { Text("留空表示监控任何变化") },
            modifier = Modifier.fillMaxWidth()
        )

        // 使用正则表达式选项
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.fillMaxWidth()
        ) {
            Checkbox(
                checked = systemSettingUseRegex,
                onCheckedChange = { systemSettingUseRegex = it }
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text("使用正则表达式")
        }

        // 确认按钮
        Button(
            onClick = {
                val condition = DeviceEventCondition(
                    id = UUID.randomUUID().toString(),
                    eventType = type,
                    systemSettingTypes = selectedSystemSettingTypes.toList(), // 转换为列表
                    systemSettingKey = systemSettingKey,
                    systemSettingValue = systemSettingValue,
                    systemSettingUseRegex = systemSettingUseRegex
                )
                onComplete(condition)
            },
            modifier = Modifier.fillMaxWidth(),
            enabled = selectedSystemSettingTypes.isNotEmpty() // 至少选择一个设置类型
        ) {
            Text("确认配置")
        }
    }
}

/**
 * 自适应模式的基础配置组件
 */
@Composable
private fun AdaptiveBasicConfig(
    memoryAbundantFrequency: Int,
    onMemoryAbundantFrequencyChange: (Int) -> Unit,
    memoryTightFrequency: Int,
    onMemoryTightFrequencyChange: (Int) -> Unit
) {
    Column {
        Text(
            text = "自适应检测配置",
            style = MaterialTheme.typography.bodyMedium,
            fontWeight = FontWeight.Medium
        )

        Text(
            text = "根据系统内存状态自动调整检测频率",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        Spacer(modifier = Modifier.height(8.dp))

        // 内存充足时频率
        var abundantFrequencyText by remember { mutableStateOf(memoryAbundantFrequency.toString()) }
        var abundantFrequencyError by remember { mutableStateOf(false) }

        OutlinedTextField(
            value = abundantFrequencyText,
            onValueChange = { newValue ->
                abundantFrequencyText = newValue
                val intValue = newValue.toIntOrNull()
                if (intValue != null && intValue in 30..300) {
                    onMemoryAbundantFrequencyChange(intValue)
                    abundantFrequencyError = false
                } else {
                    abundantFrequencyError = true
                }
            },
            label = { Text("内存充足时检测频率") },
            suffix = { Text("秒") },
            placeholder = { Text("30-300秒") },
            isError = abundantFrequencyError,
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
            modifier = Modifier.fillMaxWidth()
        )

        if (abundantFrequencyError) {
            Text(
                text = "请输入 30 到 300 之间的数字",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.error
            )
        }

        Spacer(modifier = Modifier.height(8.dp))

        // 内存紧张时频率
        var tightFrequencyText by remember { mutableStateOf(memoryTightFrequency.toString()) }
        var tightFrequencyError by remember { mutableStateOf(false) }

        OutlinedTextField(
            value = tightFrequencyText,
            onValueChange = { newValue ->
                tightFrequencyText = newValue
                val intValue = newValue.toIntOrNull()
                if (intValue != null && intValue in 1..30) {
                    onMemoryTightFrequencyChange(intValue)
                    tightFrequencyError = false
                } else {
                    tightFrequencyError = true
                }
            },
            label = { Text("内存紧张时检测频率") },
            suffix = { Text("秒") },
            placeholder = { Text("1-30秒") },
            isError = tightFrequencyError,
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
            modifier = Modifier.fillMaxWidth()
        )

        if (tightFrequencyError) {
            Text(
                text = "请输入 1 到 30 之间的数字",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.error
            )
        }

        if (memoryTightFrequency <= 5) {
            Text(
                text = "⚠️ 检测频率过高可能会显著增加耗电量",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.error
            )
        }
    }
}

/**
 * 智能学习模式基础配置
 */
@Composable
private fun IntelligentBasicConfig(
    enableLearning: Boolean,
    onEnableLearningChange: (Boolean) -> Unit,
    minSamplesForPrediction: Int,
    onMinSamplesChange: (Int) -> Unit,
    confidenceThreshold: Float,
    onConfidenceThresholdChange: (Float) -> Unit,
    appLaunchDelaySeconds: Int,
    onAppLaunchDelayChange: (Int) -> Unit,
    monitorDurationSeconds: Int,
    onMonitorDurationChange: (Int) -> Unit
) {
    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Text(
            text = "智能学习配置",
            style = MaterialTheme.typography.bodyMedium,
            fontWeight = FontWeight.Medium
        )

        Text(
            text = "系统会学习应用的内存使用模式，自动优化检测策略",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        // 启用学习功能
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.fillMaxWidth()
        ) {
            Checkbox(
                checked = enableLearning,
                onCheckedChange = onEnableLearningChange
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = "启用智能学习",
                style = MaterialTheme.typography.bodyMedium
            )
        }

        if (enableLearning) {
            // 最小学习样本数
            var minSamplesText by remember { mutableStateOf(minSamplesForPrediction.toString()) }
            var minSamplesError by remember { mutableStateOf(false) }

            OutlinedTextField(
                value = minSamplesText,
                onValueChange = { newValue ->
                    minSamplesText = newValue
                    val intValue = newValue.toIntOrNull()
                    if (intValue != null && intValue in 2..50) {
                        onMinSamplesChange(intValue)
                        minSamplesError = false
                    } else {
                        minSamplesError = true
                    }
                },
                label = { Text("最小学习样本数") },
                suffix = { Text("次") },
                placeholder = { Text("2-50次") },
                isError = minSamplesError,
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                modifier = Modifier.fillMaxWidth()
            )

            if (minSamplesError) {
                Text(
                    text = "请输入 2 到 50 之间的数字",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.error
                )
            }

            Spacer(modifier = Modifier.height(8.dp))

            // 置信度阈值
            var confidenceText by remember { mutableStateOf((confidenceThreshold * 100).toInt().toString()) }
            var confidenceError by remember { mutableStateOf(false) }

            OutlinedTextField(
                value = confidenceText,
                onValueChange = { newValue ->
                    confidenceText = newValue
                    val intValue = newValue.toIntOrNull()
                    if (intValue != null && intValue in 30..95) {
                        onConfidenceThresholdChange(intValue / 100f)
                        confidenceError = false
                    } else {
                        confidenceError = true
                    }
                },
                label = { Text("置信度阈值") },
                suffix = { Text("%") },
                placeholder = { Text("30-95%") },
                isError = confidenceError,
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                modifier = Modifier.fillMaxWidth()
            )

            if (confidenceError) {
                Text(
                    text = "请输入 30 到 95 之间的数字",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.error
                )
            }
        }

        // 基础延迟配置
        Spacer(modifier = Modifier.height(8.dp))

        var appLaunchDelayText by remember { mutableStateOf(appLaunchDelaySeconds.toString()) }
        var appLaunchDelayError by remember { mutableStateOf(false) }

        OutlinedTextField(
            value = appLaunchDelayText,
            onValueChange = { newValue ->
                appLaunchDelayText = newValue
                val intValue = newValue.toIntOrNull()
                if (intValue != null && intValue in 3..60) {
                    onAppLaunchDelayChange(intValue)
                    appLaunchDelayError = false
                } else {
                    appLaunchDelayError = true
                }
            },
            label = { Text("应用启动后延迟检测") },
            suffix = { Text("秒") },
            placeholder = { Text("3-60秒") },
            isError = appLaunchDelayError,
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
            modifier = Modifier.fillMaxWidth()
        )

        if (appLaunchDelayError) {
            Text(
                text = "请输入 3 到 60 之间的数字",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.error
            )
        }

        Spacer(modifier = Modifier.height(8.dp))

        var monitorDurationText by remember { mutableStateOf(monitorDurationSeconds.toString()) }
        var monitorDurationError by remember { mutableStateOf(false) }

        OutlinedTextField(
            value = monitorDurationText,
            onValueChange = { newValue ->
                monitorDurationText = newValue
                val intValue = newValue.toIntOrNull()
                if (intValue != null && intValue in 10..300) {
                    onMonitorDurationChange(intValue)
                    monitorDurationError = false
                } else {
                    monitorDurationError = true
                }
            },
            label = { Text("连续监控时长") },
            suffix = { Text("秒") },
            placeholder = { Text("10-300秒") },
            isError = monitorDurationError,
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
            modifier = Modifier.fillMaxWidth()
        )

        if (monitorDurationError) {
            Text(
                text = "请输入 10 到 300 之间的数字",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.error
            )
        }

        if (enableLearning) {
            Text(
                text = "💡 智能学习模式会根据应用的实际内存使用情况自动调整检测策略",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.primary
            )
        }
    }
}

/**
 * 混合模式基础配置
 */
@Composable
private fun HybridBasicConfig(
    enableLearning: Boolean,
    onEnableLearningChange: (Boolean) -> Unit,
    adaptiveStrategy: AdaptiveStrategy,
    onAdaptiveStrategyChange: (AdaptiveStrategy) -> Unit,
    appLaunchDelaySeconds: Int,
    onAppLaunchDelayChange: (Int) -> Unit,
    monitorDurationSeconds: Int,
    onMonitorDurationChange: (Int) -> Unit,
    enableEventDriven: Boolean,
    onEnableEventDrivenChange: (Boolean) -> Unit,
    enableAdaptive: Boolean,
    onEnableAdaptiveChange: (Boolean) -> Unit,
    enableIntelligent: Boolean,
    onEnableIntelligentChange: (Boolean) -> Unit
) {
    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Text(
            text = "混合模式配置",
            style = MaterialTheme.typography.bodyMedium,
            fontWeight = FontWeight.Medium
        )

        Text(
            text = "结合多种检测策略的优点，提供最佳的检测效果",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        // 策略选择
        Text(
            text = "启用的策略",
            style = MaterialTheme.typography.bodyMedium,
            fontWeight = FontWeight.Medium
        )

        // 事件驱动策略
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.fillMaxWidth()
        ) {
            Checkbox(
                checked = enableEventDriven,
                onCheckedChange = onEnableEventDrivenChange
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = "事件驱动检测",
                style = MaterialTheme.typography.bodyMedium
            )
        }

        // 自适应策略
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.fillMaxWidth()
        ) {
            Checkbox(
                checked = enableAdaptive,
                onCheckedChange = onEnableAdaptiveChange
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = "自适应检测",
                style = MaterialTheme.typography.bodyMedium
            )
        }

        // 智能学习策略
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.fillMaxWidth()
        ) {
            Checkbox(
                checked = enableIntelligent,
                onCheckedChange = onEnableIntelligentChange
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = "智能学习",
                style = MaterialTheme.typography.bodyMedium
            )
        }

        // 启用学习功能（仅在智能学习启用时显示）
        if (enableIntelligent) {
            Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.fillMaxWidth()
        ) {
            Checkbox(
                checked = enableLearning,
                onCheckedChange = onEnableLearningChange
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = "启用智能学习",
                style = MaterialTheme.typography.bodyMedium
            )
        }
        }

        // 自适应策略选择（仅在自适应启用时显示）
        if (enableAdaptive) {
        Text(
            text = "自适应策略",
            style = MaterialTheme.typography.bodyMedium,
            fontWeight = FontWeight.Medium
        )

        AdaptiveStrategy.values().forEach { strategy ->
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier
                    .fillMaxWidth()
                    .selectable(
                        selected = adaptiveStrategy == strategy,
                        onClick = { onAdaptiveStrategyChange(strategy) }
                    )
                    .padding(vertical = 4.dp)
            ) {
                RadioButton(
                    selected = adaptiveStrategy == strategy,
                    onClick = { onAdaptiveStrategyChange(strategy) }
                )
                Spacer(modifier = Modifier.width(8.dp))
                Column {
                    Text(
                        text = strategy.displayName,
                        style = MaterialTheme.typography.bodyMedium
                    )
                    Text(
                        text = when (strategy) {
                            AdaptiveStrategy.CONSERVATIVE -> "优先考虑省电，检测频率较低"
                            AdaptiveStrategy.BALANCED -> "在响应速度和耗电之间平衡"
                            AdaptiveStrategy.AGGRESSIVE -> "优先考虑响应速度，检测频率较高"
                            AdaptiveStrategy.CUSTOM -> "完全自定义所有参数"
                        },
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }

        // 基础延迟配置
        Spacer(modifier = Modifier.height(8.dp))

        var appLaunchDelayText by remember { mutableStateOf(appLaunchDelaySeconds.toString()) }
        var appLaunchDelayError by remember { mutableStateOf(false) }

        OutlinedTextField(
            value = appLaunchDelayText,
            onValueChange = { newValue ->
                appLaunchDelayText = newValue
                val intValue = newValue.toIntOrNull()
                if (intValue != null && intValue in 3..60) {
                    onAppLaunchDelayChange(intValue)
                    appLaunchDelayError = false
                } else {
                    appLaunchDelayError = true
                }
            },
            label = { Text("应用启动后延迟检测") },
            suffix = { Text("秒") },
            placeholder = { Text("3-60秒") },
            isError = appLaunchDelayError,
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
            modifier = Modifier.fillMaxWidth()
        )

        if (appLaunchDelayError) {
            Text(
                text = "请输入 3 到 60 之间的数字",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.error
            )
        }

        Spacer(modifier = Modifier.height(8.dp))

        var monitorDurationText by remember { mutableStateOf(monitorDurationSeconds.toString()) }
        var monitorDurationError by remember { mutableStateOf(false) }

        OutlinedTextField(
            value = monitorDurationText,
            onValueChange = { newValue ->
                monitorDurationText = newValue
                val intValue = newValue.toIntOrNull()
                if (intValue != null && intValue in 10..300) {
                    onMonitorDurationChange(intValue)
                    monitorDurationError = false
                } else {
                    monitorDurationError = true
                }
            },
            label = { Text("连续监控时长") },
            suffix = { Text("秒") },
            placeholder = { Text("10-300秒") },
            isError = monitorDurationError,
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
            modifier = Modifier.fillMaxWidth()
        )

        if (monitorDurationError) {
            Text(
                text = "请输入 10 到 300 之间的数字",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.error
            )
        }

        Text(
            text = "🚀 混合模式结合了多种检测策略，提供最智能的内存管理体验",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.primary
        )
        }
    }
}