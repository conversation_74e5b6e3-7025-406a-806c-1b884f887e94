package com.weinuo.quickcommands22.monitoring

import android.content.Context
import android.hardware.Sensor
import android.hardware.SensorEvent
import android.hardware.SensorEventListener
import android.hardware.SensorManager
import android.util.Log
import com.weinuo.quickcommands22.model.*
import kotlinx.coroutines.*
import kotlin.math.*

/**
 * 传感器状态监听器
 * 监听各种传感器状态变化，触发相应的条件
 *
 * 支持的监听功能：
 * - 光线传感器（环境光照强度变化）
 * - 屏幕方向传感器（设备方向变化）
 * - 摇晃检测（加速度计）
 * - 睡眠检测（综合多种传感器和系统状态）
 * - 设备翻转检测（重力传感器）
 * - 接近传感器（距离检测）
 * - 运动识别（活动识别）
 */
class SensorStateMonitor(
    private val context: Context,
    private val onConditionTriggered: (SensorStateCondition, Map<String, Any>) -> Unit
) {

    companion object {
        private const val TAG = "SensorStateMonitor"
        private const val SENSOR_SAMPLING_RATE = SensorManager.SENSOR_DELAY_NORMAL
        private const val SHAKE_DETECTION_THRESHOLD = 12.0f
        private const val SHAKE_DETECTION_INTERVAL = 1000L // 1秒
    }

    // 协程作用域
    private val monitorScope = CoroutineScope(Dispatchers.Default + SupervisorJob())

    // 监听器状态
    private var isMonitoring = false

    // 注册的条件列表
    private val registeredConditions = mutableListOf<SensorStateCondition>()

    // 传感器管理器
    private val sensorManager: SensorManager by lazy {
        context.getSystemService(Context.SENSOR_SERVICE) as SensorManager
    }

    // 传感器监听器映射
    private val sensorListeners = mutableMapOf<SensorStateType, SensorEventListener>()

    // 传感器数据缓存
    private val sensorDataCache = mutableMapOf<String, Any>()

    // 上次传感器状态
    private val lastSensorStates = mutableMapOf<String, Any>()

    // 摇晃检测相关
    private val lastShakeDetectionTimes = mutableMapOf<String, Long>()
    private val lastAccelerometerValues = mutableMapOf<String, FloatArray>()

    /**
     * 注册传感器状态条件
     */
    fun registerCondition(condition: SensorStateCondition) {
        synchronized(registeredConditions) {
            if (!registeredConditions.any { it.id == condition.id }) {
                registeredConditions.add(condition)
                Log.d(TAG, "Registered sensor condition: ${condition.getDescription()}")

                // 如果监听器已启动，立即为新条件注册传感器监听器
                if (isMonitoring) {
                    registerSensorListener(condition.sensorType)
                }
            }
        }
    }

    /**
     * 取消注册传感器状态条件
     */
    fun unregisterCondition(conditionId: String) {
        synchronized(registeredConditions) {
            val removed = registeredConditions.removeAll { it.id == conditionId }
            if (removed) {
                Log.d(TAG, "Unregistered sensor condition: $conditionId")

                // 检查是否还有其他条件需要相同的传感器
                if (isMonitoring) {
                    cleanupUnusedSensorListeners()
                }
            }
        }
    }

    /**
     * 清除所有注册的条件
     */
    fun clearAllConditions() {
        synchronized(registeredConditions) {
            registeredConditions.clear()
            sensorDataCache.clear()
            lastSensorStates.clear()
            lastShakeDetectionTimes.clear()
            lastAccelerometerValues.clear()
            Log.d(TAG, "Cleared all sensor conditions")
        }
    }

    /**
     * 开始监听传感器状态
     */
    fun startMonitoring() {
        if (isMonitoring) {
            Log.d(TAG, "Sensor monitoring already started")
            return
        }

        try {
            // 为所有注册的条件启动传感器监听器
            val sensorTypes = registeredConditions.map { it.sensorType }.toSet()
            sensorTypes.forEach { sensorType ->
                registerSensorListener(sensorType)
            }

            isMonitoring = true
            Log.d(TAG, "Sensor monitoring started successfully with ${sensorTypes.size} sensor types")

        } catch (e: Exception) {
            Log.e(TAG, "Error starting sensor monitoring", e)
            stopMonitoring()
        }
    }

    /**
     * 停止监听传感器状态
     */
    fun stopMonitoring() {
        if (!isMonitoring) {
            Log.d(TAG, "Sensor monitoring already stopped")
            return
        }

        try {
            // 注销所有传感器监听器
            sensorListeners.values.forEach { listener ->
                sensorManager.unregisterListener(listener)
            }
            sensorListeners.clear()

            isMonitoring = false
            Log.d(TAG, "Sensor monitoring stopped successfully")

        } catch (e: Exception) {
            Log.e(TAG, "Error stopping sensor monitoring", e)
        }
    }

    /**
     * 清理资源
     */
    fun cleanup() {
        stopMonitoring()
        clearAllConditions()
        monitorScope.cancel()
        Log.d(TAG, "Sensor monitor cleanup completed")
    }

    /**
     * 注册传感器监听器
     */
    private fun registerSensorListener(sensorType: SensorStateType) {
        if (sensorListeners.containsKey(sensorType)) {
            Log.d(TAG, "Sensor listener for $sensorType already registered")
            return
        }

        when (sensorType) {
            SensorStateType.LIGHT_SENSOR -> registerLightSensorListener()
            SensorStateType.ORIENTATION_SENSOR -> registerOrientationSensorListener()
            SensorStateType.SHAKE_SENSOR -> registerShakeSensorListener()
            SensorStateType.FLIP_SENSOR -> registerFlipSensorListener()
            SensorStateType.PROXIMITY_SENSOR -> registerProximitySensorListener()
            SensorStateType.SLEEP_SENSOR -> registerSleepSensorListener()
            SensorStateType.ACTIVITY_RECOGNITION -> registerActivityRecognitionListener()
        }
    }

    /**
     * 注册光线传感器监听器
     */
    private fun registerLightSensorListener() {
        val lightSensor = sensorManager.getDefaultSensor(Sensor.TYPE_LIGHT)
        if (lightSensor == null) {
            Log.w(TAG, "Light sensor not available")
            return
        }

        val listener = object : SensorEventListener {
            override fun onSensorChanged(event: SensorEvent) {
                if (event.sensor.type == Sensor.TYPE_LIGHT) {
                    val lux = event.values[0]
                    sensorDataCache["light_lux"] = lux

                    // 检查光线传感器条件
                    checkLightSensorConditions(lux)
                }
            }

            override fun onAccuracyChanged(sensor: Sensor?, accuracy: Int) {
                // 不需要处理精度变化
            }
        }

        val success = sensorManager.registerListener(listener, lightSensor, SENSOR_SAMPLING_RATE)
        if (success) {
            sensorListeners[SensorStateType.LIGHT_SENSOR] = listener
            Log.d(TAG, "Light sensor listener registered successfully")
        } else {
            Log.w(TAG, "Failed to register light sensor listener")
        }
    }

    /**
     * 检查光线传感器条件
     */
    private fun checkLightSensorConditions(currentLux: Float) {
        monitorScope.launch {
            try {
                val lightConditions = registeredConditions.filter { it.sensorType == SensorStateType.LIGHT_SENSOR }

                for (condition in lightConditions) {
                    val conditionKey = "light_sensor_${condition.id}"
                    val lastLux = lastSensorStates[conditionKey] as? Float

                    val isTriggered = when (condition.thresholdType) {
                        LightThresholdType.DECREASE_TO -> {
                            // 光照强度减少到指定值
                            lastLux != null && lastLux > condition.luxValue && currentLux <= condition.luxValue
                        }
                        LightThresholdType.INCREASE_TO -> {
                            // 光照强度增加到指定值
                            lastLux != null && lastLux < condition.luxValue && currentLux >= condition.luxValue
                        }
                    }

                    if (isTriggered) {
                        Log.d(TAG, "Light sensor condition triggered: ${condition.getDescription()}")

                        val eventData = mapOf<String, Any>(
                            "sensorType" to "LIGHT_SENSOR",
                            "thresholdType" to condition.thresholdType.name,
                            "luxValue" to condition.luxValue,
                            "currentLux" to currentLux,
                            "lastLux" to (lastLux ?: 0f),
                            "timestamp" to System.currentTimeMillis()
                        )

                        onConditionTriggered(condition, eventData)
                    }

                    // 更新上次状态
                    lastSensorStates[conditionKey] = currentLux
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error checking light sensor conditions", e)
            }
        }
    }

    /**
     * 注册摇晃传感器监听器
     */
    private fun registerShakeSensorListener() {
        val accelerometer = sensorManager.getDefaultSensor(Sensor.TYPE_ACCELEROMETER)
        if (accelerometer == null) {
            Log.w(TAG, "Accelerometer not available")
            return
        }

        val listener = object : SensorEventListener {
            override fun onSensorChanged(event: SensorEvent) {
                if (event.sensor.type == Sensor.TYPE_ACCELEROMETER) {
                    val acceleration = event.values.clone()
                    sensorDataCache["acceleration"] = acceleration

                    // 检查摇晃传感器条件
                    checkShakeSensorConditions(acceleration)
                }
            }

            override fun onAccuracyChanged(sensor: Sensor?, accuracy: Int) {
                // 不需要处理精度变化
            }
        }

        val success = sensorManager.registerListener(listener, accelerometer, SENSOR_SAMPLING_RATE)
        if (success) {
            sensorListeners[SensorStateType.SHAKE_SENSOR] = listener
            Log.d(TAG, "Shake sensor listener registered successfully")
        } else {
            Log.w(TAG, "Failed to register shake sensor listener")
        }
    }

    /**
     * 检查摇晃传感器条件
     */
    private fun checkShakeSensorConditions(currentAcceleration: FloatArray) {
        monitorScope.launch {
            try {
                val shakeConditions = registeredConditions.filter { it.sensorType == SensorStateType.SHAKE_SENSOR }
                val currentTime = System.currentTimeMillis()

                for (condition in shakeConditions) {
                    val conditionKey = "shake_sensor_${condition.id}"
                    val lastShakeTime = lastShakeDetectionTimes[conditionKey] ?: 0L

                    // 防止频繁触发，至少间隔指定时间
                    if (currentTime - lastShakeTime < SHAKE_DETECTION_INTERVAL) {
                        continue
                    }

                    val lastAcceleration = lastAccelerometerValues[conditionKey]
                    if (lastAcceleration != null) {
                        // 计算加速度变化
                        val deltaX = abs(currentAcceleration[0] - lastAcceleration[0])
                        val deltaY = abs(currentAcceleration[1] - lastAcceleration[1])
                        val deltaZ = abs(currentAcceleration[2] - lastAcceleration[2])
                        val totalDelta = sqrt(deltaX * deltaX + deltaY * deltaY + deltaZ * deltaZ)

                        // 根据敏感度设置阈值
                        val threshold = when (condition.sensitivity) {
                            SensitivityLevel.LOW -> condition.shakeThreshold * 0.5f
                            SensitivityLevel.MEDIUM -> condition.shakeThreshold
                            SensitivityLevel.HIGH -> condition.shakeThreshold * 1.5f
                        }

                        if (totalDelta > threshold) {
                            Log.d(TAG, "Shake detected: delta=$totalDelta, threshold=$threshold, sensitivity=${condition.sensitivity}")

                            lastShakeDetectionTimes[conditionKey] = currentTime

                            val eventData = mapOf(
                                "sensorType" to "SHAKE_SENSOR",
                                "sensitivity" to condition.sensitivity.name,
                                "shakeThreshold" to condition.shakeThreshold,
                                "actualDelta" to totalDelta,
                                "threshold" to threshold,
                                "timestamp" to currentTime
                            )

                            onConditionTriggered(condition, eventData)
                        }
                    }

                    // 更新上次加速度值
                    lastAccelerometerValues[conditionKey] = currentAcceleration.clone()
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error checking shake sensor conditions", e)
            }
        }
    }

    /**
     * 注册接近传感器监听器
     */
    private fun registerProximitySensorListener() {
        val proximitySensor = sensorManager.getDefaultSensor(Sensor.TYPE_PROXIMITY)
        if (proximitySensor == null) {
            Log.w(TAG, "Proximity sensor not available")
            return
        }

        val listener = object : SensorEventListener {
            override fun onSensorChanged(event: SensorEvent) {
                if (event.sensor.type == Sensor.TYPE_PROXIMITY) {
                    val distance = event.values[0]
                    val maxRange = event.sensor.maximumRange
                    sensorDataCache["proximity_distance"] = distance
                    sensorDataCache["proximity_max_range"] = maxRange

                    // 检查接近传感器条件
                    checkProximitySensorConditions(distance, maxRange)
                }
            }

            override fun onAccuracyChanged(sensor: Sensor?, accuracy: Int) {
                // 不需要处理精度变化
            }
        }

        val success = sensorManager.registerListener(listener, proximitySensor, SENSOR_SAMPLING_RATE)
        if (success) {
            sensorListeners[SensorStateType.PROXIMITY_SENSOR] = listener
            Log.d(TAG, "Proximity sensor listener registered successfully")
        } else {
            Log.w(TAG, "Failed to register proximity sensor listener")
        }
    }

    /**
     * 检查接近传感器条件
     */
    private fun checkProximitySensorConditions(currentDistance: Float, maxRange: Float) {
        monitorScope.launch {
            try {
                val proximityConditions = registeredConditions.filter { it.sensorType == SensorStateType.PROXIMITY_SENSOR }

                for (condition in proximityConditions) {
                    val conditionKey = "proximity_sensor_${condition.id}"
                    val lastDistance = lastSensorStates[conditionKey] as? Float

                    val isNear = currentDistance < maxRange * 0.1f // 认为小于最大范围10%为接近
                    val wasNear = lastDistance?.let { it < maxRange * 0.1f } ?: false

                    val isTriggered = when (condition.proximityType) {
                        ProximityType.NEAR -> !wasNear && isNear
                        ProximityType.FAR -> wasNear && !isNear
                        ProximityType.SLOW_WAVE -> {
                            // 慢速挥手检测：检测距离的缓慢变化
                            lastDistance != null && kotlin.math.abs(currentDistance - lastDistance) > maxRange * 0.3f
                        }
                        ProximityType.FAST_WAVE -> {
                            // 快速挥手检测：检测距离的快速变化
                            lastDistance != null && kotlin.math.abs(currentDistance - lastDistance) > maxRange * 0.6f
                        }
                    }

                    if (isTriggered) {
                        Log.d(TAG, "Proximity sensor condition triggered: ${condition.getDescription()}")

                        val eventData = mapOf<String, Any>(
                            "sensorType" to "PROXIMITY_SENSOR",
                            "proximityType" to condition.proximityType.name,
                            "currentDistance" to currentDistance,
                            "lastDistance" to (lastDistance ?: 0f),
                            "maxRange" to maxRange,
                            "isNear" to isNear,
                            "timestamp" to System.currentTimeMillis()
                        )

                        onConditionTriggered(condition, eventData)
                    }

                    // 更新上次状态
                    lastSensorStates[conditionKey] = currentDistance
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error checking proximity sensor conditions", e)
            }
        }
    }

    /**
     * 注册屏幕方向传感器监听器
     */
    private fun registerOrientationSensorListener() {
        val orientationSensor = sensorManager.getDefaultSensor(Sensor.TYPE_ROTATION_VECTOR)
        if (orientationSensor == null) {
            Log.w(TAG, "Orientation sensor not available")
            return
        }

        val listener = object : SensorEventListener {
            override fun onSensorChanged(event: SensorEvent) {
                if (event.sensor.type == Sensor.TYPE_ROTATION_VECTOR) {
                    val rotationMatrix = FloatArray(9)
                    val orientationAngles = FloatArray(3)

                    SensorManager.getRotationMatrixFromVector(rotationMatrix, event.values)
                    SensorManager.getOrientation(rotationMatrix, orientationAngles)

                    sensorDataCache["orientation_angles"] = orientationAngles

                    // 检查屏幕方向条件
                    checkOrientationSensorConditions(orientationAngles)
                }
            }

            override fun onAccuracyChanged(sensor: Sensor?, accuracy: Int) {
                // 不需要处理精度变化
            }
        }

        val success = sensorManager.registerListener(listener, orientationSensor, SENSOR_SAMPLING_RATE)
        if (success) {
            sensorListeners[SensorStateType.ORIENTATION_SENSOR] = listener
            Log.d(TAG, "Orientation sensor listener registered successfully")
        } else {
            Log.w(TAG, "Failed to register orientation sensor listener")
        }
    }

    /**
     * 检查屏幕方向传感器条件
     */
    private fun checkOrientationSensorConditions(orientationAngles: FloatArray) {
        monitorScope.launch {
            try {
                val orientationConditions = registeredConditions.filter { it.sensorType == SensorStateType.ORIENTATION_SENSOR }

                // 计算当前方向
                val pitch = orientationAngles[1] * 180 / Math.PI.toFloat()
                val roll = orientationAngles[2] * 180 / Math.PI.toFloat()

                val currentOrientation = when {
                    abs(pitch) < 30 && abs(roll) < 30 -> OrientationType.PORTRAIT
                    abs(pitch) < 30 && abs(roll) > 60 -> OrientationType.LANDSCAPE
                    abs(pitch) > 60 && abs(roll) < 30 -> OrientationType.FACE_UP
                    abs(pitch) < -60 && abs(roll) < 30 -> OrientationType.FACE_DOWN
                    else -> {
                        // 更精确的方向判断
                        when {
                            abs(roll) > abs(pitch) -> {
                                if (roll > 0) OrientationType.LANDSCAPE else OrientationType.LANDSCAPE
                            }
                            pitch > 45 -> OrientationType.FACE_UP
                            pitch < -45 -> OrientationType.FACE_DOWN
                            else -> OrientationType.PORTRAIT
                        }
                    }
                }

                for (condition in orientationConditions) {
                    val conditionKey = "orientation_sensor_${condition.id}"
                    val lastOrientation = lastSensorStates[conditionKey] as? OrientationType

                    val isTriggered = lastOrientation != null &&
                                    lastOrientation != condition.orientationType &&
                                    currentOrientation == condition.orientationType

                    if (isTriggered) {
                        Log.d(TAG, "Orientation sensor condition triggered: ${condition.getDescription()}")

                        val eventData = mapOf(
                            "sensorType" to "ORIENTATION_SENSOR",
                            "orientationType" to condition.orientationType.name,
                            "currentOrientation" to currentOrientation.name,
                            "lastOrientation" to lastOrientation.name,
                            "pitch" to pitch,
                            "roll" to roll,
                            "timestamp" to System.currentTimeMillis()
                        )

                        onConditionTriggered(condition, eventData)
                    }

                    // 更新上次状态
                    lastSensorStates[conditionKey] = currentOrientation
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error checking orientation sensor conditions", e)
            }
        }
    }

    /**
     * 注册翻转传感器监听器
     */
    private fun registerFlipSensorListener() {
        val gravitySensor = sensorManager.getDefaultSensor(Sensor.TYPE_GRAVITY)
        if (gravitySensor == null) {
            Log.w(TAG, "Gravity sensor not available")
            return
        }

        val listener = object : SensorEventListener {
            override fun onSensorChanged(event: SensorEvent) {
                if (event.sensor.type == Sensor.TYPE_GRAVITY) {
                    val gravity = event.values.clone()
                    sensorDataCache["gravity"] = gravity

                    // 检查翻转传感器条件
                    checkFlipSensorConditions(gravity)
                }
            }

            override fun onAccuracyChanged(sensor: Sensor?, accuracy: Int) {
                // 不需要处理精度变化
            }
        }

        val success = sensorManager.registerListener(listener, gravitySensor, SENSOR_SAMPLING_RATE)
        if (success) {
            sensorListeners[SensorStateType.FLIP_SENSOR] = listener
            Log.d(TAG, "Flip sensor listener registered successfully")
        } else {
            Log.w(TAG, "Failed to register flip sensor listener")
        }
    }

    /**
     * 检查翻转传感器条件
     */
    private fun checkFlipSensorConditions(gravity: FloatArray) {
        monitorScope.launch {
            try {
                val flipConditions = registeredConditions.filter { it.sensorType == SensorStateType.FLIP_SENSOR }

                // 判断设备当前朝向
                val z = gravity[2]
                val currentFlipState = when {
                    z > 7.0f -> FlipType.FACE_UP_TO_DOWN // 正面朝上
                    z < -7.0f -> FlipType.FACE_DOWN_TO_UP // 正面朝下
                    else -> null // 中间状态，不确定
                }

                for (condition in flipConditions) {
                    val conditionKey = "flip_sensor_${condition.id}"
                    val lastFlipState = lastSensorStates[conditionKey] as? FlipType

                    val isTriggered = when (condition.flipType) {
                        FlipType.FACE_UP_TO_DOWN -> {
                            lastFlipState == FlipType.FACE_DOWN_TO_UP && currentFlipState == FlipType.FACE_UP_TO_DOWN
                        }
                        FlipType.FACE_DOWN_TO_UP -> {
                            lastFlipState == FlipType.FACE_UP_TO_DOWN && currentFlipState == FlipType.FACE_DOWN_TO_UP
                        }
                        FlipType.ANY_TO_FACE_DOWN -> {
                            // 任何状态翻转到朝下
                            currentFlipState == FlipType.FACE_UP_TO_DOWN
                        }
                    }

                    if (isTriggered) {
                        Log.d(TAG, "Flip sensor condition triggered: ${condition.getDescription()}")

                        val eventData = mapOf<String, Any>(
                            "sensorType" to "FLIP_SENSOR",
                            "flipType" to condition.flipType.name,
                            "currentFlipState" to (currentFlipState?.name ?: "UNKNOWN"),
                            "lastFlipState" to (lastFlipState?.name ?: "UNKNOWN"),
                            "gravityZ" to z,
                            "timestamp" to System.currentTimeMillis()
                        )

                        onConditionTriggered(condition, eventData)
                    }

                    // 更新上次状态（只有在确定状态时才更新）
                    if (currentFlipState != null) {
                        lastSensorStates[conditionKey] = currentFlipState
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error checking flip sensor conditions", e)
            }
        }
    }

    /**
     * 注册睡眠传感器监听器（综合多种传感器）
     */
    private fun registerSleepSensorListener() {
        // 睡眠检测不需要特定的传感器监听器，而是通过定时检查系统状态
        // 启动定时检查任务
        monitorScope.launch {
            while (isMonitoring) {
                try {
                    checkSleepSensorConditions()
                    delay(30000) // 每30秒检查一次
                } catch (e: Exception) {
                    Log.e(TAG, "Error in sleep sensor monitoring", e)
                    delay(30000)
                }
            }
        }

        Log.d(TAG, "Sleep sensor monitoring started")
    }

    /**
     * 检查睡眠传感器条件
     */
    private fun checkSleepSensorConditions() {
        try {
            val sleepConditions = registeredConditions.filter { it.sensorType == SensorStateType.SLEEP_SENSOR }

            for (condition in sleepConditions) {
                val conditionKey = "sleep_sensor_${condition.id}"

                // 综合睡眠状态检测逻辑
                val currentTime = System.currentTimeMillis()
                val calendar = java.util.Calendar.getInstance()
                val hour = calendar.get(java.util.Calendar.HOUR_OF_DAY)
                val minute = calendar.get(java.util.Calendar.MINUTE)

                // 获取传感器数据
                val lightLevel = sensorDataCache["light"] as? Float ?: 0f
                val acceleration = sensorDataCache["acceleration"] as? FloatArray
                val proximity = sensorDataCache["proximity"] as? Float ?: Float.MAX_VALUE

                // 计算运动强度
                val movementIntensity = if (acceleration != null) {
                    kotlin.math.sqrt(
                        acceleration[0] * acceleration[0] +
                        acceleration[1] * acceleration[1] +
                        acceleration[2] * acceleration[2]
                    )
                } else {
                    9.8f // 默认重力加速度
                }

                // 多因素睡眠状态判断
                val isNightTime = hour >= 22 || hour <= 6
                val isScreenOff = !isScreenOn()
                val isDeviceLocked = !isScreenUnlocked()
                val isDarkEnvironment = lightLevel < 10.0f // 环境光线很暗
                val isNearFace = proximity < 5.0f // 接近传感器检测到物体
                val isStill = movementIntensity < 10.5f // 设备基本静止
                val isDeepNight = hour >= 23 || hour <= 5 // 深夜时间

                // 计算睡眠可能性评分
                var sleepScore = 0
                if (isNightTime) sleepScore += 2
                if (isScreenOff) sleepScore += 2
                if (isDeviceLocked) sleepScore += 1
                if (isDarkEnvironment) sleepScore += 2
                if (isNearFace) sleepScore += 1
                if (isStill) sleepScore += 2
                if (isDeepNight) sleepScore += 1

                val currentSleepState = when {
                    sleepScore >= 7 -> SleepStateType.FALL_ASLEEP // 高可能性睡眠
                    sleepScore <= 3 -> SleepStateType.WAKE_UP // 高可能性清醒
                    else -> null // 不确定状态，不触发
                }

                Log.d(TAG, "Sleep detection - Score: $sleepScore, State: $currentSleepState, " +
                      "Night: $isNightTime, Screen: $isScreenOff, Light: $lightLevel, Movement: $movementIntensity")

                val lastSleepState = lastSensorStates[conditionKey] as? SleepStateType

                val isTriggered = lastSleepState != null &&
                                lastSleepState != condition.sleepStateType &&
                                currentSleepState == condition.sleepStateType

                if (isTriggered) {
                    Log.d(TAG, "Sleep sensor condition triggered: ${condition.getDescription()}")

                    val eventData = mapOf<String, Any>(
                        "sensorType" to "SLEEP_SENSOR",
                        "sleepStateType" to condition.sleepStateType.name,
                        "currentSleepState" to (currentSleepState?.name ?: "UNKNOWN"),
                        "lastSleepState" to lastSleepState.name,
                        "isNightTime" to isNightTime,
                        "isScreenOff" to isScreenOff,
                        "isDeviceLocked" to isDeviceLocked,
                        "timestamp" to currentTime
                    )

                    onConditionTriggered(condition, eventData)
                }

                // 更新上次状态
                if (currentSleepState != null) {
                    lastSensorStates[conditionKey] = currentSleepState
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error checking sleep sensor conditions", e)
        }
    }

    /**
     * 注册运动识别监听器
     */
    private fun registerActivityRecognitionListener() {
        Log.d(TAG, "Activity recognition monitoring started with enhanced detection")

        // 启动连续监控任务
        monitorScope.launch {
            val activityBuffer = mutableListOf<ActivityType>()
            val bufferSize = 6 // 保持最近6次检测结果

            while (isMonitoring) {
                try {
                    val detectedActivity = detectCurrentActivity()

                    // 添加到缓冲区
                    activityBuffer.add(detectedActivity)
                    if (activityBuffer.size > bufferSize) {
                        activityBuffer.removeAt(0)
                    }

                    // 使用多数投票来稳定检测结果
                    val stableActivity = getStableActivity(activityBuffer)
                    if (stableActivity != null) {
                        checkActivityRecognitionConditions(stableActivity)
                    }

                    delay(5000) // 每5秒检查一次
                } catch (e: Exception) {
                    Log.e(TAG, "Error in activity recognition monitoring", e)
                    delay(5000)
                }
            }
        }
    }

    /**
     * 检测当前活动类型
     */
    private fun detectCurrentActivity(): ActivityType {
        val acceleration = sensorDataCache["acceleration"] as? FloatArray
        val gyroscope = sensorDataCache["gyroscope"] as? FloatArray
        val linearAcceleration = sensorDataCache["linear_acceleration"] as? FloatArray

        return if (acceleration != null) {
            // 计算加速度幅值
            val magnitude = kotlin.math.sqrt(
                acceleration[0] * acceleration[0] +
                acceleration[1] * acceleration[1] +
                acceleration[2] * acceleration[2]
            )

            // 计算变化率（如果有陀螺仪数据）
            val rotationRate = if (gyroscope != null) {
                kotlin.math.sqrt(
                    gyroscope[0] * gyroscope[0] +
                    gyroscope[1] * gyroscope[1] +
                    gyroscope[2] * gyroscope[2]
                )
            } else {
                0f
            }

            // 计算线性加速度（如果可用）
            val linearMagnitude = if (linearAcceleration != null) {
                kotlin.math.sqrt(
                    linearAcceleration[0] * linearAcceleration[0] +
                    linearAcceleration[1] * linearAcceleration[1] +
                    linearAcceleration[2] * linearAcceleration[2]
                )
            } else {
                0f
            }

            // 综合判断活动类型
            when {
                magnitude > 20.0f || linearMagnitude > 8.0f -> ActivityType.RUNNING
                magnitude > 15.0f || (linearMagnitude > 3.0f && rotationRate > 2.0f) -> ActivityType.WALKING
                magnitude > 12.0f && rotationRate > 1.0f -> ActivityType.WALKING
                magnitude < 10.2f && rotationRate < 0.5f -> ActivityType.STILL
                else -> ActivityType.WALKING // 默认为步行
            }
        } else {
            ActivityType.STILL
        }
    }

    /**
     * 获取稳定的活动类型（多数投票）
     */
    private fun getStableActivity(buffer: List<ActivityType>): ActivityType? {
        if (buffer.size < 3) return null

        val counts = buffer.groupingBy { it }.eachCount()
        val maxCount = counts.maxByOrNull { it.value }

        // 只有当某个活动类型占多数时才认为是稳定的
        return if (maxCount != null && maxCount.value >= buffer.size / 2) {
            maxCount.key
        } else {
            null
        }
    }

    /**
     * 检查运动识别条件
     */
    private fun checkActivityRecognitionConditions(stableActivity: ActivityType? = null) {
        try {
            val activityConditions = registeredConditions.filter { it.sensorType == SensorStateType.ACTIVITY_RECOGNITION }

            for (condition in activityConditions) {
                val conditionKey = "activity_recognition_${condition.id}"

                // 使用稳定的活动类型，或者重新检测
                val currentActivity = stableActivity ?: detectCurrentActivity()
                val lastActivity = lastSensorStates[conditionKey] as? ActivityType

                val isTriggered = lastActivity != null &&
                                lastActivity != condition.activityType &&
                                currentActivity == condition.activityType

                if (isTriggered) {
                    Log.d(TAG, "Activity recognition condition triggered: ${condition.getDescription()}")

                    val eventData = mapOf(
                        "sensorType" to "ACTIVITY_RECOGNITION",
                        "activityType" to condition.activityType.name,
                        "currentActivity" to currentActivity.name,
                        "lastActivity" to lastActivity.name,
                        "timestamp" to System.currentTimeMillis()
                    )

                    onConditionTriggered(condition, eventData)
                }

                // 更新上次状态
                lastSensorStates[conditionKey] = currentActivity
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error checking activity recognition conditions", e)
        }
    }

    /**
     * 清理未使用的传感器监听器
     */
    private fun cleanupUnusedSensorListeners() {
        val usedSensorTypes = registeredConditions.map { it.sensorType }.toSet()
        val unusedSensorTypes = sensorListeners.keys - usedSensorTypes

        for (sensorType in unusedSensorTypes) {
            val listener = sensorListeners[sensorType]
            if (listener != null) {
                sensorManager.unregisterListener(listener)
                sensorListeners.remove(sensorType)
                Log.d(TAG, "Unregistered unused sensor listener for $sensorType")
            }
        }
    }

    /**
     * 检查屏幕是否开启
     */
    private fun isScreenOn(): Boolean {
        return try {
            val powerManager = context.getSystemService(Context.POWER_SERVICE) as android.os.PowerManager
            powerManager.isInteractive
        } catch (e: Exception) {
            Log.e(TAG, "Error checking screen state", e)
            true
        }
    }

    /**
     * 检查屏幕是否解锁
     */
    private fun isScreenUnlocked(): Boolean {
        return try {
            val kgManager = context.getSystemService(Context.KEYGUARD_SERVICE) as android.app.KeyguardManager
            val isLocked = kgManager.isKeyguardLocked
            !isLocked
        } catch (e: Exception) {
            Log.e(TAG, "Error checking keyguard state", e)
            false
        }
    }

    /**
     * 获取监听状态
     */
    fun isMonitoring(): Boolean = isMonitoring

    /**
     * 获取注册的条件数量
     */
    fun getRegisteredConditionCount(): Int = registeredConditions.size
}
