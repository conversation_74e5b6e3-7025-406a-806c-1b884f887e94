package com.weinuo.quickcommands22.ui.theme

import androidx.compose.ui.graphics.Color

/**
 * 颜色定义文件
 *
 * 注意：主要的颜色定义已迁移到各个主题的颜色方案中
 * 这里只保留一些通用的状态颜色和特殊用途颜色
 */

// 状态颜色（跨主题通用）
val RunningGreen = Color(0xFF146C2E) // 运行状态绿色
val RunningContainer = Color(0xFFE8F5E8) // 运行状态容器
val OnRunningContainer = Color(0xFF146C2E) // 运行状态容器上的文本

// 导航颜色（跨主题通用）
val UnselectedIconGray = Color(0xFF70787D) // 未选中图标的中灰色

// 注意：其他颜色定义已迁移到主题系统中：
// - 海洋蓝主题颜色：ui/theme/oceanblue/OceanBlueColorScheme.kt
// - 天空蓝主题颜色：ui/theme/skyblue/SkyBlueColorScheme.kt（待实现）