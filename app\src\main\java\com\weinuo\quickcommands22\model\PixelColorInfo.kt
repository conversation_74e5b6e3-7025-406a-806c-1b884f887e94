package com.weinuo.quickcommands22.model

import android.graphics.Color

/**
 * 像素颜色信息
 *
 * 包含像素颜色的详细信息，支持多种格式表示
 */
data class PixelColorInfo(
    val x: Int,
    val y: Int,
    val colorInt: Int
) {
    /**
     * 红色分量 (0-255)
     */
    val red: Int = Color.red(colorInt)

    /**
     * 绿色分量 (0-255)
     */
    val green: Int = Color.green(colorInt)

    /**
     * 蓝色分量 (0-255)
     */
    val blue: Int = Color.blue(colorInt)

    /**
     * Alpha分量 (0-255)
     */
    val alpha: Int = Color.alpha(colorInt)

    /**
     * 颜色的十六进制字符串表示 (格式: #AARRGGBB)
     */
    val colorHex: String = String.format("#%08X", colorInt)

    /**
     * 颜色的RGB十六进制字符串表示 (格式: #RRGGBB)
     */
    val colorHexRgb: String = String.format("#%06X", colorInt and 0xFFFFFF)

    /**
     * 获取格式化的颜色信息字符串
     */
    fun getFormattedInfo(): String {
        return buildString {
            appendLine("像素位置: ($x, $y)")
            appendLine("[R]-红色值: $red")
            appendLine("[G]-绿色值: $green")
            appendLine("[B]-蓝色值: $blue")
            appendLine("[A]-透明度: $alpha")
            appendLine("[colorInt]-颜色整数表示: $colorInt")
            appendLine("[colorHex]-颜色十六进制表示: $colorHex")
            appendLine("[colorHexRgb]-RGB十六进制表示: $colorHexRgb")
        }
    }
}
