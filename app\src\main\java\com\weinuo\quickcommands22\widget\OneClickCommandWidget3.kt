package com.weinuo.quickcommands22.widget

import android.app.PendingIntent
import android.appwidget.AppWidgetManager
import android.appwidget.AppWidgetProvider
import android.content.Context
import android.content.Intent
import android.widget.RemoteViews
import com.weinuo.quickcommands22.R

/**
 * 快捷指令3桌面小组件
 */
class OneClickCommandWidget3 : AppWidgetProvider() {

    override fun onUpdate(
        context: Context,
        appWidgetManager: AppWidgetManager,
        appWidgetIds: IntArray
    ) {
        // 检查用户是否启用了小组件更新
        val sharedPreferences = context.getSharedPreferences("background_manager_settings", Context.MODE_PRIVATE)
        val widgetUpdateEnabled = sharedPreferences.getBoolean("global_widget_update_enabled", false)

        // 只有在用户启用更新时才执行更新
        if (widgetUpdateEnabled) {
            // 为每个小组件实例执行更新
            appWidgetIds.forEach { appWidgetId ->
                updateAppWidget(context, appWidgetManager, appWidgetId)
            }
        }
    }

    companion object {
        /**
         * 更新小组件
         */
        fun updateAppWidget(
            context: Context,
            appWidgetManager: AppWidgetManager,
            appWidgetId: Int
        ) {
            // 创建RemoteViews对象
            val views = RemoteViews(context.packageName, R.layout.widget_one_click_command)
            
            // 设置文本
            views.setTextViewText(R.id.widget_text_line1, "快捷")
            views.setTextViewText(R.id.widget_text_line2, "指令3")
            
            // 创建点击意图
            val intent = Intent(context, WidgetClickHandlerActivity::class.java).apply {
                putExtra(WidgetClickHandlerActivity.EXTRA_WIDGET_ID, WIDGET_ID)
            }
            
            val pendingIntent = PendingIntent.getActivity(
                context,
                appWidgetId,
                intent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )
            
            // 设置点击事件
            views.setOnClickPendingIntent(R.id.widget_text_line1, pendingIntent)
            views.setOnClickPendingIntent(R.id.widget_text_line2, pendingIntent)
            views.setOnClickPendingIntent(android.R.id.background, pendingIntent)
            
            // 更新小组件
            appWidgetManager.updateAppWidget(appWidgetId, views)
        }
        
        // 小组件ID
        const val WIDGET_ID = "widget_3"
    }
}
