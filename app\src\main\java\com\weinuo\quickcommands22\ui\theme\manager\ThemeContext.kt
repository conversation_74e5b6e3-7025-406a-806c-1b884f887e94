package com.weinuo.quickcommands22.ui.theme.manager

import androidx.compose.material3.ColorScheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.compositionLocalOf
import androidx.compose.runtime.remember
import androidx.compose.ui.platform.LocalContext
import com.weinuo.quickcommands22.ui.theme.system.AppTheme
import com.weinuo.quickcommands22.ui.theme.system.ComponentFactory
import com.weinuo.quickcommands22.ui.theme.system.StyleConfiguration
import com.weinuo.quickcommands22.ui.theme.system.InteractionConfiguration
import com.weinuo.quickcommands22.ui.theme.system.AnimationConfiguration
import com.weinuo.quickcommands22.ui.theme.system.DesignApproach
import com.weinuo.quickcommands22.ui.theme.config.BlurConfiguration

/**
 * 主题上下文数据类
 *
 * 包含当前主题的所有配置信息
 * 通过CompositionLocal在Compose树中传递
 */
data class ThemeContext(
    /**
     * 当前主题
     */
    val theme: AppTheme,

    /**
     * 颜色方案
     */
    val colorScheme: ColorScheme,

    /**
     * 组件工厂
     */
    val componentFactory: ComponentFactory,

    /**
     * 样式配置
     */
    val styleConfiguration: StyleConfiguration,

    /**
     * 交互配置
     */
    val interactionConfiguration: InteractionConfiguration,

    /**
     * 动画配置
     */
    val animationConfiguration: AnimationConfiguration,

    /**
     * 模糊配置
     */
    val blurConfiguration: BlurConfiguration,

    /**
     * 设计方法
     */
    val designApproach: DesignApproach
) {
    /**
     * 主题ID
     */
    val themeId: String get() = theme.id

    /**
     * 主题显示名称
     */
    val displayName: String get() = theme.displayName

    /**
     * 检查是否支持模糊效果
     */
    val supportsBlur: Boolean get() = theme.supportsBlur

    /**
     * 检查是否支持阴影效果
     */
    val supportsShadows: Boolean get() = theme.supportsShadows

    /**
     * 检查是否支持透明度
     */
    val supportsTransparency: Boolean get() = theme.supportsTransparency

    /**
     * 获取主题特性列表
     */
    val features: List<String> get() = theme.features

    /**
     * 检查主题是否支持特定功能
     */
    fun supportsFeature(feature: com.weinuo.quickcommands22.ui.theme.system.ThemeFeature): Boolean {
        return theme.supportsFeature(feature)
    }

    companion object {
        /**
         * 创建默认主题上下文
         */
        fun createDefault(): ThemeContext {
            val defaultTheme = AppTheme.getDefault()
            val provider = defaultTheme.themeProvider
            
            return ThemeContext(
                theme = defaultTheme,
                colorScheme = provider.getColorScheme(),
                componentFactory = provider.getComponentFactory(),
                styleConfiguration = provider.getStyleConfiguration(),
                interactionConfiguration = provider.getInteractionConfiguration(),
                animationConfiguration = provider.getAnimationConfiguration(),
                blurConfiguration = provider.getBlurConfiguration(),
                designApproach = defaultTheme.designApproach
            )
        }

        /**
         * 从主题创建上下文
         */
        fun fromTheme(theme: AppTheme): ThemeContext {
            val provider = theme.themeProvider
            
            return ThemeContext(
                theme = theme,
                colorScheme = provider.getColorScheme(),
                componentFactory = provider.getComponentFactory(),
                styleConfiguration = provider.getStyleConfiguration(),
                interactionConfiguration = provider.getInteractionConfiguration(),
                animationConfiguration = provider.getAnimationConfiguration(),
                blurConfiguration = provider.getBlurConfiguration(),
                designApproach = theme.designApproach
            )
        }

        /**
         * 从缓存创建上下文（性能优化）
         */
        fun fromCache(theme: AppTheme, cache: ThemeCache): ThemeContext {
            return ThemeContext(
                theme = theme,
                colorScheme = cache.getColorScheme(theme),
                componentFactory = cache.getComponentFactory(theme),
                styleConfiguration = cache.getStyleConfiguration(theme),
                interactionConfiguration = cache.getInteractionConfiguration(theme),
                animationConfiguration = cache.getAnimationConfiguration(theme),
                blurConfiguration = cache.getBlurConfiguration(theme),
                designApproach = theme.designApproach
            )
        }
    }
}

/**
 * 主题上下文的CompositionLocal
 */
val LocalThemeContext = compositionLocalOf<ThemeContext> {
    ThemeContext.createDefault()
}

/**
 * 主题上下文提供者组件
 *
 * 为子组件提供主题上下文
 */
@Composable
fun ThemeContextProvider(
    content: @Composable () -> Unit
) {
    val context = LocalContext.current
    val themeManager = remember { ThemeManager.getInstance(context) }
    val currentTheme = themeManager.currentTheme.value
    
    // 使用缓存创建主题上下文以提高性能
    val themeContext = remember(currentTheme) {
        val cache = ThemeCache()
        ThemeContext.fromCache(currentTheme, cache)
    }
    
    CompositionLocalProvider(
        LocalThemeContext provides themeContext
    ) {
        content()
    }
}

/**
 * 获取当前主题上下文的便捷函数
 */
@Composable
fun currentThemeContext(): ThemeContext = LocalThemeContext.current

/**
 * 获取当前主题的便捷函数
 */
@Composable
fun currentTheme(): AppTheme = LocalThemeContext.current.theme

/**
 * 获取当前颜色方案的便捷函数
 */
@Composable
fun currentColorScheme(): ColorScheme = LocalThemeContext.current.colorScheme

/**
 * 获取当前组件工厂的便捷函数
 */
@Composable
fun currentComponentFactory(): ComponentFactory = LocalThemeContext.current.componentFactory

/**
 * 获取当前样式配置的便捷函数
 */
@Composable
fun currentStyleConfiguration(): StyleConfiguration = LocalThemeContext.current.styleConfiguration

/**
 * 获取当前交互配置的便捷函数
 */
@Composable
fun currentInteractionConfiguration(): InteractionConfiguration = LocalThemeContext.current.interactionConfiguration

/**
 * 获取当前动画配置的便捷函数
 */
@Composable
fun currentAnimationConfiguration(): AnimationConfiguration = LocalThemeContext.current.animationConfiguration

/**
 * 获取当前模糊配置的便捷函数
 */
@Composable
fun currentBlurConfiguration(): BlurConfiguration = LocalThemeContext.current.blurConfiguration

/**
 * 获取当前设计方法的便捷函数
 */
@Composable
fun currentDesignApproach(): DesignApproach = LocalThemeContext.current.designApproach

/**
 * 检查当前主题是否支持特定功能的便捷函数
 */
@Composable
fun currentThemeSupportsFeature(feature: com.weinuo.quickcommands22.ui.theme.system.ThemeFeature): Boolean {
    return LocalThemeContext.current.supportsFeature(feature)
}

/**
 * 检查当前主题是否支持模糊效果的便捷函数
 */
@Composable
fun currentThemeSupportsBlur(): Boolean = LocalThemeContext.current.supportsBlur

/**
 * 检查当前主题是否支持阴影效果的便捷函数
 */
@Composable
fun currentThemeSupportsShadows(): Boolean = LocalThemeContext.current.supportsShadows

/**
 * 检查当前主题是否支持透明度的便捷函数
 */
@Composable
fun currentThemeSupportsTransparency(): Boolean = LocalThemeContext.current.supportsTransparency
