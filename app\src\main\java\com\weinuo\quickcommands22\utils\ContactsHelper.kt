package com.weinuo.quickcommands22.utils

import android.content.Context
import android.database.Cursor
import android.provider.ContactsContract
import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * 联系人辅助工具类
 * 提供联系人数据访问和管理功能
 * 支持联系人查询、分组管理、号码匹配等功能
 */
object ContactsHelper {

    private const val TAG = "ContactsHelper"

    /**
     * 联系人信息数据类
     */
    data class ContactInfo(
        val id: String,
        val name: String,
        val phoneNumber: String,
        val displayName: String = name
    )

    /**
     * 联系人分组信息数据类
     */
    data class ContactGroup(
        val id: String,
        val title: String,
        val contactCount: Int = 0
    )

    // 缓存机制
    private var cachedContacts: List<ContactInfo>? = null
    private var cachedGroups: List<ContactGroup>? = null
    private var lastContactsUpdateTime: Long = 0
    private var lastGroupsUpdateTime: Long = 0
    private val CACHE_VALID_TIME = 5 * 60 * 1000L // 5分钟缓存

    /**
     * 获取所有联系人列表
     * 包含缓存机制，减少重复查询
     */
    suspend fun getAllContacts(context: Context): List<ContactInfo> = withContext(Dispatchers.IO) {
        try {
            // 检查权限
            if (!CommunicationPermissionUtil.hasContactsPermission(context)) {
                Log.w(TAG, "No contacts permission, returning empty list")
                return@withContext emptyList()
            }

            // 检查缓存
            val currentTime = System.currentTimeMillis()
            if (cachedContacts != null && (currentTime - lastContactsUpdateTime) < CACHE_VALID_TIME) {
                Log.d(TAG, "Returning cached contacts (${cachedContacts!!.size} items)")
                return@withContext cachedContacts!!
            }

            val contacts = mutableListOf<ContactInfo>()
            val contentResolver = context.contentResolver

            // 查询联系人
            val projection = arrayOf(
                ContactsContract.CommonDataKinds.Phone.CONTACT_ID,
                ContactsContract.CommonDataKinds.Phone.DISPLAY_NAME,
                ContactsContract.CommonDataKinds.Phone.NUMBER
            )

            val cursor: Cursor? = contentResolver.query(
                ContactsContract.CommonDataKinds.Phone.CONTENT_URI,
                projection,
                null,
                null,
                ContactsContract.CommonDataKinds.Phone.DISPLAY_NAME + " ASC"
            )

            cursor?.use { c ->
                val idIndex = c.getColumnIndex(ContactsContract.CommonDataKinds.Phone.CONTACT_ID)
                val nameIndex = c.getColumnIndex(ContactsContract.CommonDataKinds.Phone.DISPLAY_NAME)
                val numberIndex = c.getColumnIndex(ContactsContract.CommonDataKinds.Phone.NUMBER)

                while (c.moveToNext()) {
                    val id = c.getString(idIndex) ?: continue
                    val name = c.getString(nameIndex) ?: "未知联系人"
                    val number = c.getString(numberIndex) ?: continue

                    // 清理电话号码格式
                    val cleanNumber = cleanPhoneNumber(number)
                    if (cleanNumber.isNotEmpty()) {
                        contacts.add(
                            ContactInfo(
                                id = id,
                                name = name,
                                phoneNumber = cleanNumber,
                                displayName = "$name ($cleanNumber)"
                            )
                        )
                    }
                }
            }

            // 去重（同一联系人可能有多个号码）
            val uniqueContacts = contacts.distinctBy { "${it.name}_${it.phoneNumber}" }

            // 更新缓存
            cachedContacts = uniqueContacts
            lastContactsUpdateTime = currentTime

            Log.d(TAG, "Loaded ${uniqueContacts.size} contacts from database")
            return@withContext uniqueContacts

        } catch (e: Exception) {
            Log.e(TAG, "Error loading contacts", e)
            return@withContext emptyList()
        }
    }

    /**
     * 获取联系人分组信息
     */
    suspend fun getContactGroups(context: Context): List<ContactGroup> = withContext(Dispatchers.IO) {
        try {
            // 检查权限
            if (!CommunicationPermissionUtil.hasContactsPermission(context)) {
                Log.w(TAG, "No contacts permission, returning empty groups list")
                return@withContext emptyList()
            }

            // 检查缓存
            val currentTime = System.currentTimeMillis()
            if (cachedGroups != null && (currentTime - lastGroupsUpdateTime) < CACHE_VALID_TIME) {
                Log.d(TAG, "Returning cached groups (${cachedGroups!!.size} items)")
                return@withContext cachedGroups!!
            }

            val groups = mutableListOf<ContactGroup>()
            val contentResolver = context.contentResolver

            // 查询联系人分组
            val projection = arrayOf(
                ContactsContract.Groups._ID,
                ContactsContract.Groups.TITLE
            )

            val cursor: Cursor? = contentResolver.query(
                ContactsContract.Groups.CONTENT_URI,
                projection,
                null,
                null,
                ContactsContract.Groups.TITLE + " ASC"
            )

            cursor?.use { c ->
                val idIndex = c.getColumnIndex(ContactsContract.Groups._ID)
                val titleIndex = c.getColumnIndex(ContactsContract.Groups.TITLE)

                while (c.moveToNext()) {
                    val id = c.getString(idIndex) ?: continue
                    val title = c.getString(titleIndex) ?: "未命名分组"

                    groups.add(
                        ContactGroup(
                            id = id,
                            title = title
                        )
                    )
                }
            }

            // 更新缓存
            cachedGroups = groups
            lastGroupsUpdateTime = currentTime

            Log.d(TAG, "Loaded ${groups.size} contact groups from database")
            return@withContext groups

        } catch (e: Exception) {
            Log.e(TAG, "Error loading contact groups", e)
            return@withContext emptyList()
        }
    }

    /**
     * 根据电话号码查找联系人
     */
    suspend fun getContactByNumber(context: Context, phoneNumber: String): ContactInfo? = withContext(Dispatchers.IO) {
        try {
            // 检查权限
            if (!CommunicationPermissionUtil.hasContactsPermission(context)) {
                Log.w(TAG, "No contacts permission, cannot find contact by number")
                return@withContext null
            }

            val cleanNumber = cleanPhoneNumber(phoneNumber)
            if (cleanNumber.isEmpty()) {
                return@withContext null
            }

            // 先从缓存中查找
            val allContacts = getAllContacts(context)
            val cachedContact = allContacts.find { contact ->
                cleanPhoneNumber(contact.phoneNumber) == cleanNumber
            }

            if (cachedContact != null) {
                Log.d(TAG, "Found contact in cache: ${cachedContact.name}")
                return@withContext cachedContact
            }

            // 如果缓存中没有，直接查询数据库
            val contentResolver = context.contentResolver
            val projection = arrayOf(
                ContactsContract.CommonDataKinds.Phone.CONTACT_ID,
                ContactsContract.CommonDataKinds.Phone.DISPLAY_NAME,
                ContactsContract.CommonDataKinds.Phone.NUMBER
            )

            val selection = ContactsContract.CommonDataKinds.Phone.NUMBER + " LIKE ?"
            val selectionArgs = arrayOf("%$cleanNumber%")

            val cursor: Cursor? = contentResolver.query(
                ContactsContract.CommonDataKinds.Phone.CONTENT_URI,
                projection,
                selection,
                selectionArgs,
                null
            )

            cursor?.use { c ->
                if (c.moveToFirst()) {
                    val idIndex = c.getColumnIndex(ContactsContract.CommonDataKinds.Phone.CONTACT_ID)
                    val nameIndex = c.getColumnIndex(ContactsContract.CommonDataKinds.Phone.DISPLAY_NAME)
                    val numberIndex = c.getColumnIndex(ContactsContract.CommonDataKinds.Phone.NUMBER)

                    val id = c.getString(idIndex) ?: return@withContext null
                    val name = c.getString(nameIndex) ?: "未知联系人"
                    val number = c.getString(numberIndex) ?: return@withContext null

                    val contact = ContactInfo(
                        id = id,
                        name = name,
                        phoneNumber = cleanPhoneNumber(number),
                        displayName = "$name (${cleanPhoneNumber(number)})"
                    )

                    Log.d(TAG, "Found contact in database: ${contact.name}")
                    return@withContext contact
                }
            }

            Log.d(TAG, "No contact found for number: $cleanNumber")
            return@withContext null

        } catch (e: Exception) {
            Log.e(TAG, "Error finding contact by number: $phoneNumber", e)
            return@withContext null
        }
    }

    /**
     * 根据联系人ID获取电话号码
     */
    suspend fun getContactPhoneNumber(context: Context, contactId: String): String? = withContext(Dispatchers.IO) {
        try {
            // 检查权限
            if (!CommunicationPermissionUtil.hasContactsPermission(context)) {
                Log.w(TAG, "No contacts permission, cannot get contact phone number")
                return@withContext null
            }

            if (contactId.isEmpty()) {
                return@withContext null
            }

            val cursor = context.contentResolver.query(
                ContactsContract.CommonDataKinds.Phone.CONTENT_URI,
                arrayOf(ContactsContract.CommonDataKinds.Phone.NUMBER),
                "${ContactsContract.CommonDataKinds.Phone.CONTACT_ID} = ?",
                arrayOf(contactId),
                null
            )

            cursor?.use { c ->
                if (c.moveToFirst()) {
                    val numberIndex = c.getColumnIndex(ContactsContract.CommonDataKinds.Phone.NUMBER)
                    val phoneNumber = c.getString(numberIndex) ?: ""
                    return@withContext cleanPhoneNumber(phoneNumber)
                }
            }

            Log.w(TAG, "No phone number found for contact ID: $contactId")
            return@withContext null

        } catch (e: Exception) {
            Log.e(TAG, "Error getting phone number for contact ID: $contactId", e)
            return@withContext null
        }
    }

    /**
     * 根据联系人姓名查找联系人
     */
    suspend fun getContactByName(context: Context, contactName: String): ContactInfo? = withContext(Dispatchers.IO) {
        try {
            // 检查权限
            if (!CommunicationPermissionUtil.hasContactsPermission(context)) {
                Log.w(TAG, "No contacts permission, cannot find contact by name")
                return@withContext null
            }

            if (contactName.isEmpty()) {
                return@withContext null
            }

            // 先从缓存中查找
            val allContacts = getAllContacts(context)
            val cachedContact = allContacts.find { contact ->
                contact.name.equals(contactName, ignoreCase = true)
            }

            if (cachedContact != null) {
                Log.d(TAG, "Found contact in cache: ${cachedContact.name}")
                return@withContext cachedContact
            }

            // 如果缓存中没有，直接查询数据库
            val cursor = context.contentResolver.query(
                ContactsContract.CommonDataKinds.Phone.CONTENT_URI,
                arrayOf(
                    ContactsContract.CommonDataKinds.Phone.CONTACT_ID,
                    ContactsContract.CommonDataKinds.Phone.DISPLAY_NAME,
                    ContactsContract.CommonDataKinds.Phone.NUMBER
                ),
                "${ContactsContract.CommonDataKinds.Phone.DISPLAY_NAME} = ?",
                arrayOf(contactName),
                null
            )

            cursor?.use { c ->
                if (c.moveToFirst()) {
                    val idIndex = c.getColumnIndex(ContactsContract.CommonDataKinds.Phone.CONTACT_ID)
                    val nameIndex = c.getColumnIndex(ContactsContract.CommonDataKinds.Phone.DISPLAY_NAME)
                    val numberIndex = c.getColumnIndex(ContactsContract.CommonDataKinds.Phone.NUMBER)

                    val id = c.getString(idIndex) ?: return@withContext null
                    val name = c.getString(nameIndex) ?: "未知联系人"
                    val number = c.getString(numberIndex) ?: return@withContext null

                    val contact = ContactInfo(
                        id = id,
                        name = name,
                        phoneNumber = cleanPhoneNumber(number),
                        displayName = "$name (${cleanPhoneNumber(number)})"
                    )

                    Log.d(TAG, "Found contact in database: ${contact.name}")
                    return@withContext contact
                }
            }

            Log.w(TAG, "Contact not found: $contactName")
            null
        } catch (e: Exception) {
            Log.e(TAG, "Error finding contact by name: $contactName", e)
            null
        }
    }

    /**
     * 清理电话号码格式
     * 移除空格、括号、连字符等格式字符
     */
    fun cleanPhoneNumber(phoneNumber: String): String {
        return phoneNumber.replace(Regex("[^\\d+]"), "")
    }

    /**
     * 清除缓存
     * 在联系人数据可能发生变化时调用
     */
    fun clearCache() {
        cachedContacts = null
        cachedGroups = null
        lastContactsUpdateTime = 0
        lastGroupsUpdateTime = 0
        Log.d(TAG, "Contacts cache cleared")
    }

    /**
     * 检查两个电话号码是否匹配
     * 支持不同格式的号码比较
     */
    fun phoneNumbersMatch(number1: String, number2: String): Boolean {
        val clean1 = cleanPhoneNumber(number1)
        val clean2 = cleanPhoneNumber(number2)

        if (clean1 == clean2) return true

        // 处理国际号码格式（+86等）
        val withoutCountryCode1 = clean1.removePrefix("+86").removePrefix("86")
        val withoutCountryCode2 = clean2.removePrefix("+86").removePrefix("86")

        return withoutCountryCode1 == withoutCountryCode2
    }
}
