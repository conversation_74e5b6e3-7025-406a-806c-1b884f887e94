package com.weinuo.quickcommands22.utils

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.graphics.BitmapFactory
import android.media.RingtoneManager
import android.os.Build
import android.provider.Settings
import android.util.Log
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat
import androidx.core.graphics.drawable.IconCompat
import com.weinuo.quickcommands22.MainActivity
import com.weinuo.quickcommands22.R
import com.weinuo.quickcommands22.model.NotificationSoundType
import com.weinuo.quickcommands22.shizuku.ShizukuManager

/**
 * 通知辅助工具类
 * 封装通知相关的操作逻辑
 */
object NotificationHelper {

    private const val TAG = "NotificationHelper"
    private const val DEFAULT_CHANNEL_ID = "default"
    private const val DEFAULT_CHANNEL_NAME = "默认通知"

    // 前台服务通知常量
    const val FOREGROUND_SERVICE_CHANNEL_ID = "foreground_service_channel"
    const val FOREGROUND_SERVICE_CHANNEL_NAME = "前台服务"
    const val QUICK_COMMANDS_NOTIFICATION_ID = 1001
    const val CONDITIONAL_COMMAND_NOTIFICATION_ID = 1002

    /**
     * 初始化通知渠道
     * @param context 上下文
     */
    fun initializeNotificationChannels(context: Context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

            // 创建默认通知渠道
            val defaultChannel = NotificationChannel(
                DEFAULT_CHANNEL_ID,
                DEFAULT_CHANNEL_NAME,
                NotificationManager.IMPORTANCE_DEFAULT
            ).apply {
                description = "应用默认通知渠道"
            }
            notificationManager.createNotificationChannel(defaultChannel)

            // 创建高优先级通知渠道
            val highPriorityChannel = NotificationChannel(
                "high_priority",
                "高优先级通知",
                NotificationManager.IMPORTANCE_HIGH
            ).apply {
                description = "重要通知渠道"
            }
            notificationManager.createNotificationChannel(highPriorityChannel)

            // 创建低优先级通知渠道
            val lowPriorityChannel = NotificationChannel(
                "low_priority",
                "低优先级通知",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "次要通知渠道"
            }
            notificationManager.createNotificationChannel(lowPriorityChannel)

            // 创建前台服务通知渠道
            val foregroundServiceChannel = NotificationChannel(
                FOREGROUND_SERVICE_CHANNEL_ID,
                FOREGROUND_SERVICE_CHANNEL_NAME,
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "前台服务运行状态通知"
                setShowBadge(false)
                enableLights(false)
                enableVibration(false)
                setSound(null, null)
            }
            notificationManager.createNotificationChannel(foregroundServiceChannel)

            // 创建气泡通知渠道（Android 10+）
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                val bubbleChannel = NotificationChannel(
                    "bubble_notifications",
                    "气泡通知",
                    NotificationManager.IMPORTANCE_HIGH
                ).apply {
                    description = "气泡样式的通知"
                    setAllowBubbles(true) // 允许气泡通知
                }
                notificationManager.createNotificationChannel(bubbleChannel)
            }

            // 创建智慧提醒通知渠道
            val smartRemindersChannel = NotificationChannel(
                "smart_reminders",
                "智慧提醒",
                NotificationManager.IMPORTANCE_DEFAULT
            ).apply {
                description = "智能检测并提供有用的提醒"
                enableLights(true)
                enableVibration(true)
                setShowBadge(true)
            }
            notificationManager.createNotificationChannel(smartRemindersChannel)
        }
    }

    /**
     * 显示系统通知
     * @param context 上下文
     * @param title 通知标题
     * @param text 通知内容
     * @param channelId 通知渠道ID
     * @param priority 通知优先级
     * @param soundType 通知声音类型
     * @param iconPath 图标文件路径（可选）
     * @return 是否成功显示
     */
    fun showNotification(
        context: Context,
        title: String,
        text: String,
        channelId: String = DEFAULT_CHANNEL_ID,
        priority: Int = 0,
        soundType: NotificationSoundType = NotificationSoundType.DEFAULT,
        iconPath: String = ""
    ): Boolean {
        return try {
            // 检查通知权限
            if (!NotificationPermissionUtil.hasPostNotificationPermission(context)) {
                Log.e(TAG, "No notification permission")
                return false
            }

            // 确保通知渠道存在
            initializeNotificationChannels(context)

            // 创建点击Intent
            val intent = Intent(context, MainActivity::class.java).apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            }
            val pendingIntent = PendingIntent.getActivity(
                context,
                0,
                intent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )

            // 获取通知声音URI
            val soundUri = when (soundType) {
                NotificationSoundType.RINGTONE -> RingtoneManager.getDefaultUri(RingtoneManager.TYPE_RINGTONE)
                NotificationSoundType.NOTIFICATION -> RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION)
                NotificationSoundType.ALARM -> RingtoneManager.getDefaultUri(RingtoneManager.TYPE_ALARM)
                NotificationSoundType.NONE -> null
                NotificationSoundType.DEFAULT -> RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION)
            }

            // 构建通知
            val notificationBuilder = NotificationCompat.Builder(context, channelId)
                .setSmallIcon(R.drawable.ic_notification)
                .setContentTitle(title)
                .setContentText(text)
                .setStyle(NotificationCompat.BigTextStyle().bigText(text))
                .setPriority(priority)
                .setContentIntent(pendingIntent)
                .setAutoCancel(true)

            // 设置自定义图标（如果提供了图标路径）
            if (iconPath.isNotEmpty()) {
                try {
                    val iconBitmap = BitmapFactory.decodeFile(iconPath)
                    if (iconBitmap != null) {
                        notificationBuilder.setLargeIcon(iconBitmap)
                        Log.d(TAG, "Custom icon set from path: $iconPath")
                    } else {
                        Log.w(TAG, "Failed to decode icon from path: $iconPath")
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Error loading custom icon from path: $iconPath", e)
                }
            }

            // 设置声音
            if (soundUri != null) {
                notificationBuilder.setSound(soundUri)
            }

            // 显示通知
            val notificationManager = NotificationManagerCompat.from(context)
            val notificationId = System.currentTimeMillis().toInt()
            notificationManager.notify(notificationId, notificationBuilder.build())

            Log.d(TAG, "Notification shown: title=$title, id=$notificationId")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error showing notification", e)
            false
        }
    }

    /**
     * 清除通知
     * @param context 上下文
     * @param packageName 应用包名（空表示清除所有）
     * @param notificationId 通知ID（-1表示清除所有）
     * @return 是否成功清除
     */
    fun clearNotifications(
        context: Context,
        packageName: String = "",
        notificationId: Int = -1
    ): Boolean {
        return try {
            // 检查通知使用权限
            if (!NotificationPermissionUtil.hasNotificationListenerPermission(context)) {
                Log.e(TAG, "No notification listener permission")
                return false
            }

            if (packageName.isEmpty()) {
                // 清除所有通知
                val command = "cmd notification clear_all"
                val result = ShizukuManager.executeCommand(command)
                Log.d(TAG, "Clear all notifications result: $result")
                !result.contains("Error") && !result.contains("错误")
            } else {
                // 清除指定应用的通知
                val command = if (notificationId == -1) {
                    "cmd notification clear $packageName"
                } else {
                    "cmd notification clear $packageName $notificationId"
                }
                val result = ShizukuManager.executeCommand(command)
                Log.d(TAG, "Clear notifications result: $result")
                !result.contains("Error") && !result.contains("错误")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error clearing notifications", e)
            false
        }
    }

    /**
     * 恢复隐藏的通知
     * @param context 上下文
     * @param packageName 应用包名（空表示恢复所有）
     * @return 是否成功恢复
     */
    fun restoreHiddenNotifications(
        context: Context,
        packageName: String = ""
    ): Boolean {
        return try {
            // 检查通知使用权限
            if (!NotificationPermissionUtil.hasNotificationListenerPermission(context)) {
                Log.e(TAG, "No notification listener permission")
                return false
            }

            // 通过Shizuku命令恢复通知
            val command = if (packageName.isEmpty()) {
                "cmd notification restore_hidden"
            } else {
                "cmd notification restore_hidden $packageName"
            }
            val result = ShizukuManager.executeCommand(command)
            Log.d(TAG, "Restore hidden notifications result: $result")
            !result.contains("Error") && !result.contains("错误")
        } catch (e: Exception) {
            Log.e(TAG, "Error restoring hidden notifications", e)
            false
        }
    }

    /**
     * 控制浮动通知
     * @param context 上下文
     * @param enable 是否启用
     * @return 是否成功控制
     */
    fun controlFloatingNotifications(context: Context, enable: Boolean): Boolean {
        return try {
            val command = if (enable) {
                "cmd notification set_floating_notifications 1"
            } else {
                "cmd notification set_floating_notifications 0"
            }
            val result = ShizukuManager.executeCommand(command)
            Log.d(TAG, "Control floating notifications result: $result")
            !result.contains("Error") && !result.contains("错误")
        } catch (e: Exception) {
            Log.e(TAG, "Error controlling floating notifications", e)
            false
        }
    }

    /**
     * 切换浮动通知状态
     * @param context 上下文
     * @return 是否成功切换
     */
    fun toggleFloatingNotifications(context: Context): Boolean {
        return try {
            // 获取当前状态并切换
            val getStatusCommand = "cmd notification get_floating_notifications"
            val statusResult = ShizukuManager.executeCommand(getStatusCommand)
            val currentEnabled = statusResult.contains("1") || statusResult.contains("enabled")

            controlFloatingNotifications(context, !currentEnabled)
        } catch (e: Exception) {
            Log.e(TAG, "Error toggling floating notifications", e)
            false
        }
    }

    /**
     * 打开通知铃声设置
     * @param context 上下文
     * @return 是否成功打开
     */
    fun openRingtoneSettings(context: Context): Boolean {
        return try {
            val intent = Intent(Settings.ACTION_SOUND_SETTINGS).apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }
            context.startActivity(intent)
            Log.d(TAG, "Ringtone settings opened successfully")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error opening ringtone settings", e)
            false
        }
    }

    /**
     * 向通知发送自动回复
     * @param context 上下文
     * @param packageName 应用包名
     * @param replyText 回复内容
     * @return 是否成功发送
     */
    fun replyToNotification(
        context: Context,
        packageName: String,
        replyText: String
    ): Boolean {
        return try {
            // 检查通知使用权限
            if (!NotificationPermissionUtil.hasNotificationListenerPermission(context)) {
                Log.e(TAG, "No notification listener permission")
                return false
            }

            // 通过Shizuku命令发送回复
            val command = "cmd notification reply $packageName \"$replyText\""
            val result = ShizukuManager.executeCommand(command)
            Log.d(TAG, "Reply to notification result: $result")
            !result.contains("Error") && !result.contains("错误")
        } catch (e: Exception) {
            Log.e(TAG, "Error replying to notification", e)
            false
        }
    }

    /**
     * 与通知进行交互
     * @param context 上下文
     * @param packageName 应用包名
     * @param action 交互动作
     * @return 是否成功交互
     */
    fun interactWithNotification(
        context: Context,
        packageName: String,
        action: String
    ): Boolean {
        return try {
            // 检查通知使用权限
            if (!NotificationPermissionUtil.hasNotificationListenerPermission(context)) {
                Log.e(TAG, "No notification listener permission")
                return false
            }

            // 通过Shizuku命令进行交互
            val command = "cmd notification interact $packageName $action"
            val result = ShizukuManager.executeCommand(command)
            Log.d(TAG, "Interact with notification result: $result")
            !result.contains("Error") && !result.contains("错误")
        } catch (e: Exception) {
            Log.e(TAG, "Error interacting with notification", e)
            false
        }
    }

    /**
     * 显示气泡通知
     * @param context 上下文
     * @param title 通知标题
     * @param text 通知内容
     * @param channelId 通知渠道ID
     * @param priority 通知优先级
     * @param soundType 通知声音类型
     * @return 是否成功显示
     */
    fun showBubbleNotification(
        context: Context,
        title: String,
        text: String,
        channelId: String = "bubble_notifications",
        priority: Int = NotificationCompat.PRIORITY_HIGH,
        soundType: NotificationSoundType = NotificationSoundType.NOTIFICATION
    ): Boolean {
        return try {
            // 检查Android版本（气泡通知需要Android 10+）
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.Q) {
                Log.w(TAG, "Bubble notifications require Android 10 (API 29) or higher")
                // 回退到普通通知
                return showNotification(context, title, text, channelId, priority, soundType)
            }

            // 检查气泡通知权限
            if (!NotificationPermissionUtil.areBubblesAllowed(context)) {
                Log.e(TAG, "Bubble notifications are not allowed")
                return false
            }

            // 确保通知渠道存在
            initializeNotificationChannels(context)

            // 创建气泡Intent（点击气泡时打开的Activity）
            val bubbleIntent = Intent(context, com.weinuo.quickcommands22.ui.BubbleActivity::class.java).apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
                putExtra(com.weinuo.quickcommands22.ui.BubbleActivity.EXTRA_TITLE, title)
                putExtra(com.weinuo.quickcommands22.ui.BubbleActivity.EXTRA_MESSAGE, text)
                putExtra(com.weinuo.quickcommands22.ui.BubbleActivity.EXTRA_TIMESTAMP, System.currentTimeMillis())
            }
            val bubblePendingIntent = PendingIntent.getActivity(
                context,
                0,
                bubbleIntent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_MUTABLE
            )

            // 创建气泡图标
            val bubbleIcon = IconCompat.createWithResource(context, R.drawable.ic_notification)

            // 创建气泡元数据
            val bubbleData = NotificationCompat.BubbleMetadata.Builder(
                bubblePendingIntent,
                bubbleIcon
            )
                .setDesiredHeight(600) // 设置气泡高度
                .setAutoExpandBubble(false) // 不自动展开
                .setSuppressNotification(false) // 不抑制通知
                .build()

            // 获取通知声音URI
            val soundUri = when (soundType) {
                NotificationSoundType.RINGTONE -> RingtoneManager.getDefaultUri(RingtoneManager.TYPE_RINGTONE)
                NotificationSoundType.NOTIFICATION -> RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION)
                NotificationSoundType.ALARM -> RingtoneManager.getDefaultUri(RingtoneManager.TYPE_ALARM)
                NotificationSoundType.NONE -> null
                NotificationSoundType.DEFAULT -> RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION)
            }

            // 构建气泡通知
            val notificationBuilder = NotificationCompat.Builder(context, channelId)
                .setSmallIcon(R.drawable.ic_notification)
                .setContentTitle(title)
                .setContentText(text)
                .setStyle(NotificationCompat.BigTextStyle().bigText(text))
                .setPriority(priority)
                .setContentIntent(bubblePendingIntent)
                .setBubbleMetadata(bubbleData) // 设置气泡元数据
                .setAutoCancel(true)
                .setCategory(NotificationCompat.CATEGORY_MESSAGE) // 设置为消息类别

            // 设置声音
            if (soundUri != null) {
                notificationBuilder.setSound(soundUri)
            }

            // 显示气泡通知
            val notificationManager = NotificationManagerCompat.from(context)
            val notificationId = System.currentTimeMillis().toInt()
            notificationManager.notify(notificationId, notificationBuilder.build())

            Log.d(TAG, "Bubble notification shown: title=$title, id=$notificationId")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error showing bubble notification", e)
            // 回退到普通通知
            showNotification(context, title, text, channelId, priority, soundType)
        }
    }

    /**
     * 创建前台服务通知
     * @param context 上下文
     * @param title 通知标题
     * @param text 通知内容
     * @return 通知对象
     */
    fun createForegroundServiceNotification(
        context: Context,
        title: String,
        text: String
    ): Notification {
        // 确保通知渠道存在
        initializeNotificationChannels(context)

        // 创建点击Intent
        val intent = Intent(context, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        }
        val pendingIntent = PendingIntent.getActivity(
            context,
            0,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        // 构建前台服务通知
        return NotificationCompat.Builder(context, FOREGROUND_SERVICE_CHANNEL_ID)
            .setSmallIcon(R.drawable.ic_notification)
            .setContentTitle(title)
            .setContentText(text)
            .setStyle(NotificationCompat.BigTextStyle().bigText(text))
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .setContentIntent(pendingIntent)
            .setOngoing(true)
            .setAutoCancel(false)
            .setShowWhen(false)
            .build()
    }
}
