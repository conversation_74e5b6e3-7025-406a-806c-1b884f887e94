package com.weinuo.quickcommands22.ui.theme.skyblue

import androidx.compose.material3.ColorScheme
import androidx.compose.runtime.*
import com.weinuo.quickcommands22.ui.theme.system.*
import com.weinuo.quickcommands22.ui.theme.config.BlurConfiguration
import com.weinuo.quickcommands22.ui.screens.ScreenFactory
import com.weinuo.quickcommands22.ui.screens.skyblue.SkyBlueScreenFactory
import com.weinuo.quickcommands22.data.SettingsRepository

/**
 * 天空蓝主题提供者
 *
 * 实现整合设计风格的主题
 * 特点：统一的视觉体验，支持模糊效果
 */
class SkyBlueThemeProvider : ThemeProvider {
    
    override fun getColorScheme(): ColorScheme {
        return SkyBlueColorScheme.createColorScheme()
    }

    /**
     * 获取动态颜色方案
     *
     * 从SettingsRepository中读取用户自定义的颜色配置
     * 支持实时颜色配置更新
     */
    @Composable
    fun getDynamicColorScheme(settingsRepository: SettingsRepository): ColorScheme {
        return SkyBlueColorScheme.createDynamicColorScheme(settingsRepository)
    }

    /**
     * 获取动态扩展颜色配置
     *
     * 从SettingsRepository中读取用户自定义的扩展颜色配置
     */
    @Composable
    fun getDynamicExtendedColors(settingsRepository: SettingsRepository): DynamicExtendedColors {
        return SkyBlueColorScheme.getDynamicExtendedColors(settingsRepository)
    }

    override fun getComponentFactory(): ComponentFactory {
        return SkyBlueComponentFactory()
    }

    override fun getStyleConfiguration(): StyleConfiguration {
        return SkyBlueStyleConfiguration()
    }

    override fun getInteractionConfiguration(): InteractionConfiguration {
        return SkyBlueInteractionConfiguration()
    }

    override fun getAnimationConfiguration(): AnimationConfiguration {
        return SkyBlueAnimationConfiguration()
    }

    override fun getBlurConfiguration(): BlurConfiguration {
        // 天空蓝主题支持模糊效果，默认启用所有模糊效果
        return BlurConfiguration(
            topBarBlurEnabled = true, // 启用顶部栏模糊效果
            topBarBlurIntensity = 0.6f,
            topBarBackgroundAlpha = 0.1f,
            bottomBarBlurEnabled = true, // 启用底部导航栏模糊
            bottomBarBlurIntensity = 0.6f,
            bottomBarBackgroundAlpha = 0.1f,
            dialogBlurEnabled = true, // 默认启用对话框模糊
            dialogBlurIntensity = 0.7f,
            dialogBackgroundAlpha = 0.2f,
            overlayBlurEnabled = true, // 默认启用覆盖层模糊
            overlayBlurIntensity = 0.8f, // 对应20dp模糊半径（20/25=0.8）
            overlayBackgroundAlpha = 0.3f // 对应硬编码的0.3f透明度
        )
    }

    override fun getScreenFactory(): ScreenFactory {
        return SkyBlueScreenFactory()
    }

    override fun getDesignApproach(): DesignApproach {
        return DesignApproach.INTEGRATED_DESIGN
    }

    override fun getThemeId(): String {
        return "sky_blue"
    }

    override fun getDisplayName(): String {
        return "天空蓝"
    }

    override fun supportsFeature(feature: ThemeFeature): Boolean {
        return when (feature) {
            ThemeFeature.SHADOW_EFFECTS -> false
            ThemeFeature.BLUR_EFFECTS -> true
            ThemeFeature.TRANSPARENCY -> true
            ThemeFeature.DYNAMIC_COLORS -> false
            ThemeFeature.CUSTOM_ANIMATIONS -> true
            ThemeFeature.GRADIENT_EFFECTS -> true
            ThemeFeature.ADAPTIVE_LAYOUT -> true
            ThemeFeature.DARK_MODE -> false
            ThemeFeature.HIGH_CONTRAST -> false
            ThemeFeature.ACCESSIBILITY_ENHANCED -> true
        }
    }

    override fun getVersion(): String {
        return "1.0.0"
    }

    override fun initialize() {
        // 天空蓝主题初始化逻辑
        // TODO: 在阶段三实现具体初始化逻辑
    }

    override fun cleanup() {
        // 天空蓝主题清理逻辑
        // TODO: 在阶段三实现具体清理逻辑
    }
}
