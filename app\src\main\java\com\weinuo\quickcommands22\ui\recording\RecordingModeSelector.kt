package com.weinuo.quickcommands22.ui.recording

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.selection.selectableGroup
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Fullscreen
import androidx.compose.material.icons.filled.OpenInNew
import androidx.compose.material.icons.filled.Add
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

/**
 * 录制模式选择器组件
 * 允许用户在全屏录制、悬浮窗录制（传统）和悬浮窗录制（高级）之间选择
 */
@Composable
fun RecordingModeSelector(
    selectedMode: RecordingMode,
    onModeSelected: (RecordingMode) -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "录制模式",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 12.dp)
            )

            Column(
                modifier = Modifier.selectableGroup()
            ) {
                RecordingModeOption(
                    mode = RecordingMode.FULLSCREEN,
                    title = "全屏录制",
                    description = "在专用界面中录制手势，适合复杂操作",
                    icon = Icons.Default.Fullscreen,
                    selected = selectedMode == RecordingMode.FULLSCREEN,
                    onSelected = { onModeSelected(RecordingMode.FULLSCREEN) },
                    enabled = enabled
                )

                Spacer(modifier = Modifier.height(8.dp))

                RecordingModeOption(
                    mode = RecordingMode.FLOATING,
                    title = "悬浮窗录制（传统）",
                    description = "通过悬浮窗录制，可在任意应用中操作",
                    icon = Icons.Default.OpenInNew,
                    selected = selectedMode == RecordingMode.FLOATING,
                    onSelected = { onModeSelected(RecordingMode.FLOATING) },
                    enabled = enabled
                )

                Spacer(modifier = Modifier.height(8.dp))

                RecordingModeOption(
                    mode = RecordingMode.FLOATING_ADVANCED,
                    title = "悬浮窗录制（高级）",
                    description = "可视化添加手势坐标，支持点击、长按、滑动等",
                    icon = Icons.Default.Add,
                    selected = selectedMode == RecordingMode.FLOATING_ADVANCED,
                    onSelected = { onModeSelected(RecordingMode.FLOATING_ADVANCED) },
                    enabled = enabled
                )
            }
        }
    }
}

/**
 * 录制模式选项组件
 */
@Composable
private fun RecordingModeOption(
    mode: RecordingMode,
    title: String,
    description: String,
    icon: ImageVector,
    selected: Boolean,
    onSelected: () -> Unit,
    enabled: Boolean,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .selectable(
                selected = selected,
                onClick = onSelected,
                role = Role.RadioButton,
                enabled = enabled
            )
            .padding(vertical = 8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        RadioButton(
            selected = selected,
            onClick = null,
            enabled = enabled
        )

        Spacer(modifier = Modifier.width(12.dp))

        Icon(
            imageVector = icon,
            contentDescription = null,
            modifier = Modifier.size(24.dp),
            tint = if (enabled) {
                MaterialTheme.colorScheme.onSurface
            } else {
                MaterialTheme.colorScheme.onSurface.copy(alpha = 0.38f)
            }
        )

        Spacer(modifier = Modifier.width(12.dp))

        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = title,
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium,
                color = if (enabled) {
                    MaterialTheme.colorScheme.onSurface
                } else {
                    MaterialTheme.colorScheme.onSurface.copy(alpha = 0.38f)
                }
            )

            Text(
                text = description,
                fontSize = 14.sp,
                color = if (enabled) {
                    MaterialTheme.colorScheme.onSurfaceVariant
                } else {
                    MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.38f)
                },
                modifier = Modifier.padding(top = 2.dp)
            )
        }
    }
}

/**
 * 简化版录制模式选择器
 * 用于配置界面等空间受限的场景
 */
@Composable
fun CompactRecordingModeSelector(
    selectedMode: RecordingMode,
    onModeSelected: (RecordingMode) -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true
) {
    Column(
        modifier = modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        // 第一行：全屏录制和传统悬浮窗录制
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            FilterChip(
                onClick = { onModeSelected(RecordingMode.FULLSCREEN) },
                label = { Text("全屏录制") },
                selected = selectedMode == RecordingMode.FULLSCREEN,
                enabled = enabled,
                leadingIcon = if (selectedMode == RecordingMode.FULLSCREEN) {
                    { Icon(Icons.Default.Fullscreen, contentDescription = null, modifier = Modifier.size(18.dp)) }
                } else null,
                modifier = Modifier.weight(1f)
            )

            FilterChip(
                onClick = { onModeSelected(RecordingMode.FLOATING) },
                label = { Text("悬浮窗（传统）") },
                selected = selectedMode == RecordingMode.FLOATING,
                enabled = enabled,
                leadingIcon = if (selectedMode == RecordingMode.FLOATING) {
                    { Icon(Icons.Default.OpenInNew, contentDescription = null, modifier = Modifier.size(18.dp)) }
                } else null,
                modifier = Modifier.weight(1f)
            )
        }

        // 第二行：高级悬浮窗录制
        FilterChip(
            onClick = { onModeSelected(RecordingMode.FLOATING_ADVANCED) },
            label = { Text("悬浮窗录制（高级）") },
            selected = selectedMode == RecordingMode.FLOATING_ADVANCED,
            enabled = enabled,
            leadingIcon = if (selectedMode == RecordingMode.FLOATING_ADVANCED) {
                { Icon(Icons.Default.Add, contentDescription = null, modifier = Modifier.size(18.dp)) }
            } else null,
            modifier = Modifier.fillMaxWidth()
        )
    }
}
