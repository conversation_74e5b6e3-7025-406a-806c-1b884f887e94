package com.weinuo.quickcommands22.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.selection.selectable
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.icons.filled.Timer
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import com.weinuo.quickcommands22.model.Stopwatch
import com.weinuo.quickcommands22.utils.StopwatchesCacheManager
import com.weinuo.quickcommands22.ui.components.themed.ThemedSearchTextField
import kotlinx.coroutines.launch

/**
 * 秒表选择模式枚举
 */
enum class StopwatchSelectionMode {
    SINGLE,  // 单选模式
    MULTI    // 多选模式
}

/**
 * 可复用的秒表选择界面
 *
 * 提供秒表列表选择界面，支持搜索功能和单选/多选模式
 * 使用全屏界面替代对话框，提供更好的用户体验
 * 可以被多个配置提供器复用，避免代码重复
 * 使用按需加载策略和智能缓存，避免不必要的电量消耗
 *
 * @param selectionMode 选择模式（单选/多选）
 * @param initialSelectedStopwatchIds 初始选中的秒表ID列表（多选模式使用）
 * @param onStopwatchesSelected 秒表选择完成回调，返回选中的秒表列表
 * @param onDismiss 取消回调
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun StopwatchSelectionScreen(
    selectionMode: StopwatchSelectionMode,
    initialSelectedStopwatchIds: List<String> = emptyList(),
    onStopwatchesSelected: (List<Stopwatch>) -> Unit,
    onDismiss: () -> Unit
) {
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()
    val focusManager = LocalFocusManager.current

    // 状态管理
    var stopwatches by remember { mutableStateOf<List<Stopwatch>>(emptyList()) }
    var searchQuery by remember { mutableStateOf("") }
    var selectedStopwatchIds by remember { mutableStateOf(initialSelectedStopwatchIds.toSet()) }
    var isLoading by remember { mutableStateOf(true) }
    var errorMessage by remember { mutableStateOf<String?>(null) }

    // 加载秒表列表 - 使用按需加载和智能缓存
    LaunchedEffect(Unit) {
        coroutineScope.launch {
            try {
                isLoading = true
                errorMessage = null
                stopwatches = StopwatchesCacheManager.getStopwatches(context)
                isLoading = false
            } catch (e: Exception) {
                errorMessage = "加载秒表列表失败: ${e.message}"
                isLoading = false
            }
        }
    }

    // 过滤后的秒表列表
    val filteredStopwatches = remember(stopwatches, searchQuery) {
        if (searchQuery.isBlank()) {
            stopwatches
        } else {
            stopwatches.filter { stopwatch ->
                stopwatch.name.contains(searchQuery, ignoreCase = true)
            }
        }
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = when (selectionMode) {
                            StopwatchSelectionMode.SINGLE -> "选择秒表"
                            StopwatchSelectionMode.MULTI -> "选择秒表 (${selectedStopwatchIds.size})"
                        }
                    )
                },
                navigationIcon = {
                    IconButton(onClick = onDismiss) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                            contentDescription = "返回"
                        )
                    }
                },
                actions = {
                    // 多选模式下显示确认按钮
                    if (selectionMode == StopwatchSelectionMode.MULTI && selectedStopwatchIds.isNotEmpty()) {
                        IconButton(
                            onClick = {
                                val selectedStopwatches = stopwatches.filter { it.id in selectedStopwatchIds }
                                onStopwatchesSelected(selectedStopwatches)
                            }
                        ) {
                            Icon(
                                imageVector = Icons.Default.Check,
                                contentDescription = "确认选择"
                            )
                        }
                    }
                }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            // 搜索框
            ThemedSearchTextField(
                searchQuery = searchQuery,
                onSearchQueryChange = { searchQuery = it },
                onClearSearch = {
                    searchQuery = ""
                    focusManager.clearFocus()
                },
                placeholder = "搜索秒表名称",
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp)
            )

            // 内容区域
            when {
                isLoading -> {
                    // 加载状态
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally,
                            verticalArrangement = Arrangement.spacedBy(16.dp)
                        ) {
                            CircularProgressIndicator()
                            Text("正在加载秒表列表...")
                        }
                    }
                }

                errorMessage != null -> {
                    // 错误状态
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally,
                            verticalArrangement = Arrangement.spacedBy(16.dp)
                        ) {
                            Text(
                                text = errorMessage!!,
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.error
                            )
                            Button(
                                onClick = {
                                    coroutineScope.launch {
                                        try {
                                            isLoading = true
                                            errorMessage = null
                                            // 清除缓存并重新加载
                                            StopwatchesCacheManager.clearCache()
                                            stopwatches = StopwatchesCacheManager.getStopwatches(context)
                                            isLoading = false
                                        } catch (e: Exception) {
                                            errorMessage = "加载秒表列表失败: ${e.message}"
                                            isLoading = false
                                        }
                                    }
                                }
                            ) {
                                Text("重试")
                            }
                        }
                    }
                }

                filteredStopwatches.isEmpty() -> {
                    // 空状态
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally,
                            verticalArrangement = Arrangement.spacedBy(16.dp)
                        ) {
                            Icon(
                                imageVector = Icons.Default.Timer,
                                contentDescription = null,
                                modifier = Modifier.size(64.dp),
                                tint = MaterialTheme.colorScheme.outline
                            )
                            Text(
                                text = if (searchQuery.isBlank()) "暂无秒表" else "未找到匹配的秒表",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.outline
                            )
                            if (searchQuery.isBlank()) {
                                Text(
                                    text = "您可以在配置中创建新的秒表",
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = MaterialTheme.colorScheme.outline
                                )
                            }
                        }
                    }
                }

                else -> {
                    // 秒表列表
                    LazyColumn(
                        modifier = Modifier.fillMaxSize(),
                        contentPadding = PaddingValues(horizontal = 16.dp, vertical = 8.dp),
                        verticalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        items(filteredStopwatches) { stopwatch ->
                            StopwatchItem(
                                stopwatch = stopwatch,
                                isSelected = stopwatch.id in selectedStopwatchIds,
                                selectionMode = selectionMode,
                                onStopwatchClick = { clickedStopwatch ->
                                    when (selectionMode) {
                                        StopwatchSelectionMode.SINGLE -> {
                                            // 单选模式：直接返回选中的秒表
                                            onStopwatchesSelected(listOf(clickedStopwatch))
                                        }
                                        StopwatchSelectionMode.MULTI -> {
                                            // 多选模式：切换选中状态
                                            selectedStopwatchIds = if (clickedStopwatch.id in selectedStopwatchIds) {
                                                selectedStopwatchIds - clickedStopwatch.id
                                            } else {
                                                selectedStopwatchIds + clickedStopwatch.id
                                            }
                                        }
                                    }
                                }
                            )
                        }
                    }
                }
            }
        }
    }
}

/**
 * 秒表项组件
 *
 * 显示单个秒表的信息，支持选择状态和点击操作
 *
 * @param stopwatch 秒表信息
 * @param isSelected 是否被选中
 * @param selectionMode 选择模式
 * @param onStopwatchClick 点击回调
 */
@Composable
private fun StopwatchItem(
    stopwatch: Stopwatch,
    isSelected: Boolean,
    selectionMode: StopwatchSelectionMode,
    onStopwatchClick: (Stopwatch) -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .selectable(
                selected = isSelected,
                onClick = { onStopwatchClick(stopwatch) }
            ),
        colors = CardDefaults.cardColors(
            containerColor = if (isSelected) {
                MaterialTheme.colorScheme.primaryContainer
            } else {
                MaterialTheme.colorScheme.surface
            }
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 秒表图标
            Icon(
                imageVector = Icons.Default.Timer,
                contentDescription = null,
                modifier = Modifier.size(40.dp),
                tint = if (isSelected) {
                    MaterialTheme.colorScheme.onPrimaryContainer
                } else {
                    MaterialTheme.colorScheme.primary
                }
            )

            Spacer(modifier = Modifier.width(16.dp))

            // 秒表信息
            Column(
                modifier = Modifier.weight(1f)
            ) {
                // 秒表名称
                Text(
                    text = stopwatch.name.ifEmpty { "未命名秒表" },
                    style = MaterialTheme.typography.titleMedium,
                    color = if (isSelected) {
                        MaterialTheme.colorScheme.onPrimaryContainer
                    } else {
                        MaterialTheme.colorScheme.onSurface
                    },
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )

                Spacer(modifier = Modifier.height(4.dp))

                // 秒表状态和时间信息
                val statusText = if (stopwatch.isRunning) {
                    "运行中"
                } else {
                    "已停止"
                }

                val elapsedTimeText = formatElapsedTime(stopwatch.elapsedTime)

                Text(
                    text = "$statusText • $elapsedTimeText",
                    style = MaterialTheme.typography.bodyMedium,
                    color = if (isSelected) {
                        MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.7f)
                    } else {
                        MaterialTheme.colorScheme.onSurfaceVariant
                    }
                )


            }

            // 多选模式下显示选择状态
            if (selectionMode == StopwatchSelectionMode.MULTI) {
                Spacer(modifier = Modifier.width(8.dp))
                if (isSelected) {
                    Icon(
                        imageVector = Icons.Default.Check,
                        contentDescription = "已选择",
                        tint = MaterialTheme.colorScheme.onPrimaryContainer
                    )
                }
            }
        }
    }
}

/**
 * 格式化经过时间
 *
 * @param elapsedTimeMs 经过的时间（毫秒）
 * @return 格式化的时间字符串
 */
private fun formatElapsedTime(elapsedTimeMs: Long): String {
    val totalSeconds = elapsedTimeMs / 1000
    val hours = totalSeconds / 3600
    val minutes = (totalSeconds % 3600) / 60
    val seconds = totalSeconds % 60

    return when {
        hours > 0 -> String.format("%02d:%02d:%02d", hours, minutes, seconds)
        else -> String.format("%02d:%02d", minutes, seconds)
    }
}
