package com.weinuo.quickcommands22.smartreminder

import android.content.Context
import android.hardware.Sensor
import android.hardware.SensorEvent
import android.hardware.SensorEventListener
import android.hardware.SensorManager
import android.provider.Settings
import android.util.Log
import com.weinuo.quickcommands22.R
import com.weinuo.quickcommands22.model.SmartReminderType
import com.weinuo.quickcommands22.service.SmartReminderOverlayService
import com.weinuo.quickcommands22.storage.SmartReminderConfigAdapter
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

import kotlin.math.abs

/**
 * 屏幕旋转提醒处理器
 *
 * 检测设备方向变化，当检测到用户翻转手机但屏幕自动旋转未开启时，
 * 发送智能提醒建议用户开启屏幕旋转功能。
 *
 * 检测逻辑：
 * - 监听设备方向传感器
 * - 检测从竖屏到横屏或从横屏到竖屏的变化
 * - 检查当前屏幕自动旋转设置状态
 * - 在合适的时机发送提醒通知
 *
 * 设计特点：
 * - 智能防重复：避免频繁提醒
 * - 延迟检测：给应用加载时间
 * - 状态感知：只在需要时提醒
 * - 用户友好：提供直接的操作建议
 */
class ScreenRotationReminderHandler(
    private val context: Context,
    private val onReminderTriggered: (Map<String, Any>) -> Unit
) {

    companion object {
        private const val TAG = "ScreenRotationReminder"
        private const val SENSOR_SAMPLING_RATE = SensorManager.SENSOR_DELAY_NORMAL

        // 默认配置值
        private const val DEFAULT_ORIENTATION_CHANGE_DELAY = 2000L // 2秒延迟检测
        private const val DEFAULT_REMINDER_COOLDOWN = 30000L // 30秒冷却时间
        private const val DEFAULT_ORIENTATION_THRESHOLD = 45f // 方向判断阈值（度）
    }

    private val sensorManager = context.getSystemService(Context.SENSOR_SERVICE) as SensorManager
    private val handlerScope = CoroutineScope(SupervisorJob() + Dispatchers.Default)
    private val configAdapter = SmartReminderConfigAdapter(context)

    private var orientationSensor: Sensor? = null
    private var sensorListener: SensorEventListener? = null
    private var isMonitoring = false

    // 状态跟踪
    private var lastOrientation: DeviceOrientation? = null
    private var lastReminderTime = 0L
    private var pendingOrientationCheck: kotlinx.coroutines.Job? = null

    // 动态配置参数
    private var orientationThreshold = DEFAULT_ORIENTATION_THRESHOLD
    private var orientationChangeDelay = DEFAULT_ORIENTATION_CHANGE_DELAY
    private var reminderCooldown = DEFAULT_REMINDER_COOLDOWN

    /**
     * 设备方向枚举
     */
    private enum class DeviceOrientation {
        PORTRAIT,    // 竖屏
        LANDSCAPE    // 横屏
    }

    /**
     * 启动屏幕旋转提醒监控
     */
    fun start() {
        if (isMonitoring) {
            Log.d(TAG, "Screen rotation reminder already monitoring")
            return
        }

        Log.d(TAG, "Starting screen rotation reminder monitoring")

        // 加载配置
        loadConfiguration()

        try {
            // 获取方向传感器
            orientationSensor = sensorManager.getDefaultSensor(Sensor.TYPE_ROTATION_VECTOR)
            if (orientationSensor == null) {
                Log.w(TAG, "Rotation vector sensor not available, trying accelerometer")
                orientationSensor = sensorManager.getDefaultSensor(Sensor.TYPE_ACCELEROMETER)
            }

            if (orientationSensor == null) {
                Log.e(TAG, "No suitable orientation sensor available")
                return
            }

            // 创建传感器监听器
            sensorListener = object : SensorEventListener {
                override fun onSensorChanged(event: SensorEvent) {
                    handleSensorData(event)
                }

                override fun onAccuracyChanged(sensor: Sensor?, accuracy: Int) {
                    // 不需要处理精度变化
                }
            }

            // 注册传感器监听器
            val success = sensorManager.registerListener(
                sensorListener,
                orientationSensor,
                SENSOR_SAMPLING_RATE
            )

            if (success) {
                isMonitoring = true
                Log.d(TAG, "Screen rotation reminder monitoring started successfully")
            } else {
                Log.e(TAG, "Failed to register sensor listener")
            }

        } catch (e: Exception) {
            Log.e(TAG, "Error starting screen rotation reminder monitoring", e)
        }
    }

    /**
     * 停止屏幕旋转提醒监控
     */
    fun stop() {
        if (!isMonitoring) {
            return
        }

        Log.d(TAG, "Stopping screen rotation reminder monitoring")

        try {
            // 取消注册传感器监听器
            sensorListener?.let { listener ->
                sensorManager.unregisterListener(listener)
            }

            // 取消待处理的检查任务
            pendingOrientationCheck?.cancel()
            pendingOrientationCheck = null

            // 重置状态
            isMonitoring = false
            lastOrientation = null
            sensorListener = null

            Log.d(TAG, "Screen rotation reminder monitoring stopped")
        } catch (e: Exception) {
            Log.e(TAG, "Error stopping screen rotation reminder monitoring", e)
        }
    }

    /**
     * 处理传感器数据
     */
    private fun handleSensorData(event: SensorEvent) {
        try {
            val currentOrientation = when (event.sensor.type) {
                Sensor.TYPE_ROTATION_VECTOR -> {
                    calculateOrientationFromRotationVector(event.values)
                }
                Sensor.TYPE_ACCELEROMETER -> {
                    calculateOrientationFromAccelerometer(event.values)
                }
                else -> return
            }

            // 检查方向是否发生变化
            if (lastOrientation != null && lastOrientation != currentOrientation) {
                Log.d(TAG, "Orientation changed: ${lastOrientation} -> $currentOrientation")

                // 取消之前的检查任务
                pendingOrientationCheck?.cancel()

                // 延迟检查，给应用时间适应新方向
                Log.d(TAG, "Scheduling orientation check with delay: ${orientationChangeDelay}ms")
                pendingOrientationCheck = handlerScope.launch {
                    delay(orientationChangeDelay)
                    checkAndTriggerReminder(lastOrientation!!, currentOrientation)
                }
            }

            lastOrientation = currentOrientation

        } catch (e: Exception) {
            Log.e(TAG, "Error handling sensor data", e)
        }
    }

    /**
     * 从旋转向量计算设备方向
     */
    private fun calculateOrientationFromRotationVector(values: FloatArray): DeviceOrientation {
        val rotationMatrix = FloatArray(9)
        val orientationAngles = FloatArray(3)

        SensorManager.getRotationMatrixFromVector(rotationMatrix, values)
        SensorManager.getOrientation(rotationMatrix, orientationAngles)

        // 将弧度转换为角度
        val roll = orientationAngles[2] * 180 / Math.PI.toFloat()

        return if (abs(roll) > orientationThreshold) {
            DeviceOrientation.LANDSCAPE
        } else {
            DeviceOrientation.PORTRAIT
        }
    }

    /**
     * 从加速度计计算设备方向
     */
    private fun calculateOrientationFromAccelerometer(values: FloatArray): DeviceOrientation {
        val x = values[0]
        val y = values[1]

        return if (abs(x) > abs(y)) {
            DeviceOrientation.LANDSCAPE
        } else {
            DeviceOrientation.PORTRAIT
        }
    }

    /**
     * 检查并触发提醒
     */
    private suspend fun checkAndTriggerReminder(
        fromOrientation: DeviceOrientation,
        toOrientation: DeviceOrientation
    ) {
        try {
            Log.d(TAG, "Checking reminder trigger: $fromOrientation -> $toOrientation")

            // 检查冷却时间
            val currentTime = System.currentTimeMillis()
            val timeSinceLastReminder = currentTime - lastReminderTime
            Log.d(TAG, "Time since last reminder: ${timeSinceLastReminder}ms, cooldown: ${reminderCooldown}ms")

            if (timeSinceLastReminder < reminderCooldown) {
                Log.d(TAG, "Reminder skipped due to cooldown")
                return
            }

            // 检查屏幕自动旋转是否已开启
            val autoRotateEnabled = isAutoRotateEnabled()
            Log.d(TAG, "Auto rotate enabled: $autoRotateEnabled")

            if (autoRotateEnabled) {
                Log.d(TAG, "Reminder skipped because auto rotate is already enabled")
                return
            }

            Log.d(TAG, "All checks passed, showing reminder")

            // 发送提醒通知
            showRotationReminder(fromOrientation, toOrientation)

            // 更新最后提醒时间
            lastReminderTime = currentTime

            // 触发回调
            val eventData = mapOf(
                "fromOrientation" to fromOrientation.name,
                "toOrientation" to toOrientation.name,
                "timestamp" to currentTime,
                "autoRotateEnabled" to false
            )
            onReminderTriggered(eventData)

        } catch (e: Exception) {
            Log.e(TAG, "Error checking and triggering reminder", e)
        }
    }

    /**
     * 检查屏幕自动旋转是否已开启
     */
    private fun isAutoRotateEnabled(): Boolean {
        return try {
            Settings.System.getInt(
                context.contentResolver,
                Settings.System.ACCELEROMETER_ROTATION,
                0
            ) == 1
        } catch (e: Exception) {
            Log.e(TAG, "Error checking auto rotate setting", e)
            false
        }
    }

    /**
     * 显示旋转提醒悬浮窗
     */
    private fun showRotationReminder(
        fromOrientation: DeviceOrientation,
        toOrientation: DeviceOrientation
    ) {
        try {
            Log.d(TAG, "Attempting to show rotation reminder: $fromOrientation -> $toOrientation")

            val title = context.getString(R.string.screen_rotation_reminder_title)
            val message = when (toOrientation) {
                DeviceOrientation.LANDSCAPE -> "检测到您将手机转为横屏，建议开启屏幕自动旋转以获得更好的体验"
                DeviceOrientation.PORTRAIT -> "检测到您将手机转为竖屏，建议开启屏幕自动旋转以获得更好的体验"
            }

            Log.d(TAG, "Calling SmartReminderOverlayService.showReminder with title: $title")

            SmartReminderOverlayService.showReminder(
                context = context,
                title = title,
                message = message,
                reminderType = "screen_rotation"
            )

            Log.d(TAG, "SmartReminderOverlayService.showReminder called successfully")

        } catch (e: Exception) {
            Log.e(TAG, "Error showing rotation reminder overlay", e)
        }
    }

    /**
     * 加载配置参数
     */
    private fun loadConfiguration() {
        try {
            // 使用原生存储方式加载屏幕旋转配置
            val rotationConfig = configAdapter.loadScreenRotationConfig(SmartReminderType.SCREEN_ROTATION_REMINDER.id)

            // 加载各项配置参数
            orientationThreshold = rotationConfig.sensitivity.toFloat()
            reminderCooldown = rotationConfig.cooldownTime.toLong() * 1000 // 转换为毫秒
            orientationChangeDelay = rotationConfig.delayTime.toLong() * 1000 // 转换为毫秒

            Log.d(TAG, "Configuration loaded: threshold=${orientationThreshold}, cooldown=${reminderCooldown}ms, delay=${orientationChangeDelay}ms")

        } catch (e: Exception) {
            Log.e(TAG, "Error loading configuration, using defaults", e)

            // 出错时使用默认配置
            orientationThreshold = DEFAULT_ORIENTATION_THRESHOLD
            reminderCooldown = DEFAULT_REMINDER_COOLDOWN
            orientationChangeDelay = DEFAULT_ORIENTATION_CHANGE_DELAY
        }
    }
}
