package com.weinuo.quickcommands22.smartreminder

import android.content.ClipboardManager
import android.content.Context
import android.content.pm.PackageManager
import android.util.Log
import com.weinuo.quickcommands22.R
import com.weinuo.quickcommands22.model.SmartReminderType
import com.weinuo.quickcommands22.service.SmartReminderOverlayService
import com.weinuo.quickcommands22.storage.SmartReminderConfigAdapter
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * 应用链接提醒处理器
 *
 * 监听剪贴板变化，检测应用链接并建议打开对应的应用。
 * 当用户复制应用链接时，自动识别平台并显示提醒悬浮窗。
 *
 * 功能特点：
 * - 实时监听剪贴板变化
 * - 智能识别应用链接
 * - 检查应用安装状态
 * - 显示应用图标的提醒悬浮窗
 * - 防重复提醒机制
 *
 * 设计原则：
 * - 遵循现有架构模式
 * - 与购物应用提醒保持一致的显示方式
 * - 最小化资源消耗
 * - 用户体验优先
 */
class AppLinkReminderHandler(
    private val context: Context,
    private val onReminderTriggered: (Map<String, Any>) -> Unit
) {

    companion object {
        private const val TAG = "AppLinkReminder"
        private const val DEFAULT_REMINDER_COOLDOWN = 30000L // 30秒冷却时间
        private const val DEFAULT_DETECTION_DELAY = 500L // 检测延迟，避免频繁触发
    }

    /**
     * 应用信息数据类
     */
    data class AppInfo(
        val packageName: String,
        val appName: String
    )

    private val clipboardManager = context.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
    private val packageManager = context.packageManager
    private val configAdapter = SmartReminderConfigAdapter(context)
    private val handlerScope = CoroutineScope(SupervisorJob() + Dispatchers.Default)

    private var clipboardListener: ClipboardManager.OnPrimaryClipChangedListener? = null
    private var isMonitoring = false
    private var lastReminderTime = 0L

    // 配置缓存
    private var currentConfig: SmartReminderConfigAdapter.AppLinkReminderConfig? = null

    /**
     * 开始监听剪贴板变化
     */
    fun startMonitoring() {
        if (isMonitoring) {
            Log.d(TAG, "Already monitoring clipboard")
            return
        }

        try {
            Log.d(TAG, "Starting app link reminder monitoring")

            // 加载配置
            loadConfiguration()

            // 创建剪贴板监听器
            clipboardListener = ClipboardManager.OnPrimaryClipChangedListener {
                handlerScope.launch {
                    // 使用配置的延迟时间
                    val delayTime = currentConfig?.detectionDelay?.toLong() ?: DEFAULT_DETECTION_DELAY
                    delay(delayTime)
                    handleClipboardChange()
                }
            }

            // 注册监听器
            clipboardManager.addPrimaryClipChangedListener(clipboardListener)
            isMonitoring = true

            Log.d(TAG, "App link reminder monitoring started")

        } catch (e: Exception) {
            Log.e(TAG, "Error starting app link reminder monitoring", e)
        }
    }

    /**
     * 停止监听剪贴板变化
     */
    fun stopMonitoring() {
        if (!isMonitoring) {
            Log.d(TAG, "Not monitoring clipboard")
            return
        }

        try {
            Log.d(TAG, "Stopping app link reminder monitoring")

            // 移除监听器
            clipboardListener?.let { listener ->
                clipboardManager.removePrimaryClipChangedListener(listener)
            }
            clipboardListener = null
            isMonitoring = false

            Log.d(TAG, "App link reminder monitoring stopped")

        } catch (e: Exception) {
            Log.e(TAG, "Error stopping app link reminder monitoring", e)
        }
    }

    /**
     * 处理剪贴板变化事件
     */
    private suspend fun handleClipboardChange() {
        try {
            // 检查冷却时间
            val currentTime = System.currentTimeMillis()
            val cooldownTime = (currentConfig?.cooldownTime ?: 30) * 1000L
            if (currentTime - lastReminderTime < cooldownTime) {
                Log.d(TAG, "Reminder in cooldown period, skipping")
                return
            }

            // 获取剪贴板内容
            val clipText = getClipboardText() ?: return

            Log.d(TAG, "Clipboard changed, checking for app links: ${clipText.take(50)}...")

            // 检测应用链接，使用配置的启用平台和自定义平台
            val enabledPlatforms = currentConfig?.enabledBuiltInPlatforms ?: setOf("抖音", "快手", "小红书", "百度网盘", "夸克网盘")
            val customPlatforms = currentConfig?.customPlatforms ?: emptyList()
            val intelligentRecognitionEnabled = currentConfig?.intelligentRecognitionEnabled ?: true
            val detectionResult = AppLinkDetector.detectAppLink(clipText, enabledPlatforms, customPlatforms, intelligentRecognitionEnabled)
            if (!detectionResult.isAppLink || detectionResult.platform == null) {
                Log.d(TAG, "No app link detected")
                return
            }

            val platform = detectionResult.platform
            Log.d(TAG, "Detected ${platform.name} app link")

            // 检查对应应用是否已安装
            if (!isAppInstalled(platform.packageName)) {
                Log.d(TAG, "App ${platform.name} (${platform.packageName}) not installed, skipping reminder")
                return
            }

            // 获取应用信息
            val appInfo = getAppInfo(platform.packageName) ?: return

            Log.d(TAG, "Showing app link reminder for: ${appInfo.appName}")

            // 显示提醒
            showAppLinkReminder(appInfo, platform.name)

            // 更新最后提醒时间
            lastReminderTime = currentTime

            // 触发回调
            onReminderTriggered(mapOf(
                "type" to "app_link_reminder",
                "timestamp" to currentTime,
                "packageName" to appInfo.packageName,
                "appName" to appInfo.appName,
                "platform" to platform.name,
                "clipboardText" to clipText.take(100)
            ))

        } catch (e: Exception) {
            Log.e(TAG, "Error handling clipboard change", e)
        }
    }

    /**
     * 获取剪贴板文本内容
     */
    private fun getClipboardText(): String? {
        return try {
            val clip = clipboardManager.primaryClip
            if (clip != null && clip.itemCount > 0) {
                val item = clip.getItemAt(0)
                item.text?.toString()
            } else {
                null
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting clipboard text", e)
            null
        }
    }

    /**
     * 检查应用是否已安装
     */
    private fun isAppInstalled(packageName: String): Boolean {
        return try {
            packageManager.getPackageInfo(packageName, 0)
            true
        } catch (e: PackageManager.NameNotFoundException) {
            false
        } catch (e: Exception) {
            Log.e(TAG, "Error checking if app is installed: $packageName", e)
            false
        }
    }

    /**
     * 获取应用信息
     */
    private fun getAppInfo(packageName: String): AppInfo? {
        return try {
            val applicationInfo = packageManager.getApplicationInfo(packageName, 0)
            val appName = packageManager.getApplicationLabel(applicationInfo).toString()
            AppInfo(packageName, appName)
        } catch (e: Exception) {
            Log.e(TAG, "Error getting app info for: $packageName", e)
            null
        }
    }

    /**
     * 显示应用链接提醒悬浮窗
     */
    private fun showAppLinkReminder(appInfo: AppInfo, platformName: String) {
        try {
            Log.d(TAG, "Attempting to show app link reminder for: ${appInfo.appName}")

            val title = context.getString(R.string.app_link_reminder_title)
            val message = "检测到${platformName}链接，建议打开「${appInfo.appName}」"

            Log.d(TAG, "Calling SmartReminderOverlayService.showAppLinkReminder")

            SmartReminderOverlayService.showAppLinkReminder(
                context = context,
                title = title,
                message = message,
                appInfo = appInfo
            )

            Log.d(TAG, "SmartReminderOverlayService.showAppLinkReminder called successfully")

        } catch (e: Exception) {
            Log.e(TAG, "Error showing app link reminder overlay", e)
        }
    }

    /**
     * 检查是否正在监听
     */
    fun isMonitoring(): Boolean {
        return isMonitoring
    }

    /**
     * 加载配置参数
     */
    private fun loadConfiguration() {
        try {
            currentConfig = configAdapter.loadAppLinkReminderConfig(SmartReminderType.APP_LINK_REMINDER.id)
            Log.d(TAG, "Configuration loaded: ${currentConfig}")
        } catch (e: Exception) {
            Log.e(TAG, "Error loading configuration", e)
            // 使用默认配置
            currentConfig = SmartReminderConfigAdapter.AppLinkReminderConfig()
        }
    }

    /**
     * 重新加载配置（用于配置更新后的刷新）
     */
    fun reloadConfiguration() {
        loadConfiguration()
    }
}
