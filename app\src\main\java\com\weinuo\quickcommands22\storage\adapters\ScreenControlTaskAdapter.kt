package com.weinuo.quickcommands22.storage.adapters

import android.util.Log
import com.weinuo.quickcommands22.model.*
import com.weinuo.quickcommands22.storage.NativeTypeStorageManager
import com.weinuo.quickcommands22.storage.StorageDomain
import com.weinuo.quickcommands22.storage.StorageOperation

/**
 * 屏幕控制任务存储适配器
 *
 * 负责ScreenControlTask的原生数据类型存储和重建。
 * 将复杂的屏幕控制任务对象拆分为独立的原生字段进行存储，
 * 完全避免JSON序列化问题。
 *
 * 支持的字段类型：
 * - 基础字段：id、operation
 * - 亮度控制相关：brightnessControlType、brightnessValue、enableAutoBrightness
 * - 保持唤醒相关：keepAwakeOperation
 * - 屏幕开关相关：screenOnOffOperation、screenOnOffImplementation
 * - 屏幕调光相关：screenDimnessSensorMode、screenDimnessPercentage
 * - 触摸阻止相关：touchBlockOperation、emergencyCloseEnabled、emergencyClickCount、emergencyTimeWindowSeconds
 * - 强制旋转相关：forceRotationMode
 * - 屏幕超时相关：screenTimeoutValue、screenTimeoutUnit
 *
 * 存储格式示例：
 * task_{id}_type = "screen_control"
 * task_{id}_operation = "BRIGHTNESS_CONTROL"
 * task_{id}_brightness_control_type = "PERCENTAGE"
 * task_{id}_brightness_value = 50
 * task_{id}_enable_auto_brightness = false
 * task_{id}_keep_awake_operation = "ENABLE"
 * task_{id}_screen_on_off_operation = "TURN_ON"
 * task_{id}_screen_on_off_implementation = "SHIZUKU"
 *
 * @param storageManager 原生类型存储管理器
 */
class ScreenControlTaskAdapter(
    storageManager: NativeTypeStorageManager
) : BaseTaskAdapter<ScreenControlTask>(storageManager) {

    companion object {
        private const val TAG = "ScreenControlTaskAdapter"

        // 字段名常量
        private const val FIELD_OPERATION = "operation"
        private const val FIELD_BRIGHTNESS_CONTROL_TYPE = "brightness_control_type"
        private const val FIELD_BRIGHTNESS_VALUE = "brightness_value"
        private const val FIELD_ENABLE_AUTO_BRIGHTNESS = "enable_auto_brightness"
        private const val FIELD_KEEP_AWAKE_OPERATION = "keep_awake_operation"
        private const val FIELD_SCREEN_ON_OFF_OPERATION = "screen_on_off_operation"
        private const val FIELD_SCREEN_ON_OFF_IMPLEMENTATION = "screen_on_off_implementation"
        private const val FIELD_SCREEN_DIMNESS_SENSOR_MODE = "screen_dimness_sensor_mode"
        private const val FIELD_SCREEN_DIMNESS_PERCENTAGE = "screen_dimness_percentage"
        private const val FIELD_TOUCH_BLOCK_OPERATION = "touch_block_operation"
        private const val FIELD_EMERGENCY_CLOSE_ENABLED = "emergency_close_enabled"
        private const val FIELD_EMERGENCY_CLICK_COUNT = "emergency_click_count"
        private const val FIELD_EMERGENCY_TIME_WINDOW_SECONDS = "emergency_time_window_seconds"
        private const val FIELD_FORCE_ROTATION_MODE = "force_rotation_mode"
        private const val FIELD_SCREEN_TIMEOUT_VALUE = "screen_timeout_value"
        private const val FIELD_SCREEN_TIMEOUT_UNIT = "screen_timeout_unit"

        // 检查屏幕文字相关字段
        private const val FIELD_CHECK_TEXT_CONTENT = "check_text_content"
        private const val FIELD_CHECK_TEXT_CASE_SENSITIVE = "check_text_case_sensitive"
        private const val FIELD_CHECK_TEXT_USE_REGEX = "check_text_use_regex"
        private const val FIELD_CHECK_TEXT_MATCH_MODE = "check_text_match_mode"
        private const val FIELD_CHECK_TEXT_INCLUDE_OVERLAY = "check_text_include_overlay"
        private const val FIELD_CHECK_TEXT_IGNORE_HIDDEN = "check_text_ignore_hidden"
        private const val FIELD_CHECK_TEXT_OUTPUT_FILE = "check_text_output_file"
        private const val FIELD_CHECK_TEXT_VIEW_ID_OUTPUT_FILE = "check_text_view_id_output_file"

        // 检查像素颜色相关字段
        private const val FIELD_CHECK_PIXEL_COORDINATE_TYPE = "check_pixel_coordinate_type"
        private const val FIELD_CHECK_PIXEL_X = "check_pixel_x"
        private const val FIELD_CHECK_PIXEL_Y = "check_pixel_y"
        private const val FIELD_CHECK_PIXEL_COLOR_OUTPUT_FILE = "check_pixel_color_output_file"

        // 读取屏幕内容相关字段
        private const val FIELD_READ_CONTENT_OUTPUT_FILE = "read_content_output_file"

        // 自动点击器相关字段
        private const val FIELD_AUTO_CLICKER_SOURCE_TYPE = "auto_clicker_source_type"
        private const val FIELD_QUICK_OPERATION_TYPE = "quick_operation_type"
        private const val FIELD_RECORDED_GESTURE = "recorded_gesture"
        private const val FIELD_PLAYBACK_LOOP_COUNT = "playback_loop_count"
        private const val FIELD_PLAYBACK_SPEED = "playback_speed"
        private const val FIELD_DELAY_BETWEEN_LOOPS = "delay_between_loops"
        private const val FIELD_ADAPT_TO_SCREEN_SIZE = "adapt_to_screen_size"
        private const val FIELD_STOP_ON_ERROR = "stop_on_error"

        // 快速操作相关字段
        private const val FIELD_CLICK_X = "click_x"
        private const val FIELD_CLICK_Y = "click_y"
        private const val FIELD_CLICK_COUNT = "click_count"
        private const val FIELD_CLICK_INTERVAL = "click_interval"
        private const val FIELD_LONG_PRESS_DURATION = "long_press_duration"
        private const val FIELD_SWIPE_START_X = "swipe_start_x"
        private const val FIELD_SWIPE_START_Y = "swipe_start_y"
        private const val FIELD_SWIPE_END_X = "swipe_end_x"
        private const val FIELD_SWIPE_END_Y = "swipe_end_y"
        private const val FIELD_SWIPE_DURATION = "swipe_duration"
    }

    override fun getTaskType() = "screen_control"

    /**
     * 删除屏幕控制任务
     * 重写删除方法以清理相关的手势录制数据
     *
     * @param taskId 任务ID
     * @return 操作是否成功
     */
    override fun delete(taskId: String): Boolean {
        return try {
            // 1. 先获取任务数据，检查是否有录制数据需要清理
            val task = load(taskId)
            Log.d(TAG, "准备删除屏幕控制任务: $taskId, 录制数据: ${task?.recordedGesture}")

            // 2. 删除任务数据
            val prefix = getPrefix(taskId)
            val taskDeleteSuccess = storageManager.deleteByPrefix(StorageDomain.TASKS, prefix)

            // 3. 如果任务删除成功且有录制数据，则清理录制数据
            if (taskDeleteSuccess && task != null && task.recordedGesture.isNotEmpty()) {
                try {
                    // 使用手势录制管理器删除录制数据
                    val gestureManager = com.weinuo.quickcommands22.storage.GestureRecordingNativeManager(
                        storageManager.getContext()
                    )
                    kotlinx.coroutines.runBlocking {
                        val gestureDeleteSuccess = gestureManager.deleteRecording(task.recordedGesture)
                        if (gestureDeleteSuccess) {
                            Log.d(TAG, "已清理关联的手势录制数据: ${task.recordedGesture}")
                        } else {
                            Log.w(TAG, "清理手势录制数据失败: ${task.recordedGesture}")
                        }
                    }

                    // 清理悬浮窗录制的临时数据
                    val floatingResultManager = com.weinuo.quickcommands22.floating.FloatingRecordingResultManager(
                        storageManager.getContext()
                    )
                    floatingResultManager.clearAllData()

                } catch (e: Exception) {
                    Log.e(TAG, "清理手势录制数据时发生异常: ${task.recordedGesture}", e)
                    // 不影响任务删除的成功状态
                }
            }

            if (taskDeleteSuccess) {
                Log.d(TAG, "屏幕控制任务删除成功: $taskId")
            } else {
                Log.e(TAG, "屏幕控制任务删除失败: $taskId")
            }

            taskDeleteSuccess

        } catch (e: Exception) {
            Log.e(TAG, "删除屏幕控制任务时发生异常: $taskId", e)
            false
        }
    }

    /**
     * 保存屏幕控制任务
     * 将ScreenControlTask的所有字段拆分为原生数据类型存储
     *
     * @param task 要保存的屏幕控制任务
     * @return 操作是否成功
     */
    override fun save(task: ScreenControlTask): Boolean {
        if (!isValidTaskId(task.id)) {
            logSaveError(task.id, "Invalid task ID")
            return false
        }

        Log.d(TAG, "开始保存屏幕控制任务: ${task.id}")

        return try {
            val operations = mutableListOf<StorageOperation>()

            // 保存基础字段
            operations.addAll(saveBaseFields(task.id, task))

            // 保存屏幕控制任务特有字段
            operations.addAll(listOf(
                saveEnum(generateKey(task.id, FIELD_OPERATION), task.operation),
                saveEnum(generateKey(task.id, FIELD_BRIGHTNESS_CONTROL_TYPE), task.brightnessControlType),
                StorageOperation.createIntOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_BRIGHTNESS_VALUE),
                    task.brightnessValue
                ),
                StorageOperation.createBooleanOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_ENABLE_AUTO_BRIGHTNESS),
                    task.enableAutoBrightness
                ),
                saveEnum(generateKey(task.id, FIELD_KEEP_AWAKE_OPERATION), task.keepAwakeOperation),
                saveEnum(generateKey(task.id, FIELD_SCREEN_ON_OFF_OPERATION), task.screenOnOffOperation),
                saveEnumNullable(generateKey(task.id, FIELD_SCREEN_ON_OFF_IMPLEMENTATION), task.screenOnOffImplementation),
                saveEnum(generateKey(task.id, FIELD_SCREEN_DIMNESS_SENSOR_MODE), task.screenDimnessSensorMode),
                StorageOperation.createIntOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_SCREEN_DIMNESS_PERCENTAGE),
                    task.screenDimnessPercentage
                ),
                saveEnum(generateKey(task.id, FIELD_TOUCH_BLOCK_OPERATION), task.touchBlockOperation),
                StorageOperation.createBooleanOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_EMERGENCY_CLOSE_ENABLED),
                    task.emergencyCloseEnabled
                ),
                StorageOperation.createIntOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_EMERGENCY_CLICK_COUNT),
                    task.emergencyClickCount
                ),
                StorageOperation.createIntOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_EMERGENCY_TIME_WINDOW_SECONDS),
                    task.emergencyTimeWindowSeconds
                ),
                saveEnum(generateKey(task.id, FIELD_FORCE_ROTATION_MODE), task.forceRotationMode),
                StorageOperation.createIntOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_SCREEN_TIMEOUT_VALUE),
                    task.screenTimeoutValue
                ),
                saveEnum(generateKey(task.id, FIELD_SCREEN_TIMEOUT_UNIT), task.screenTimeoutUnit),

                // 检查屏幕文字相关字段
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_CHECK_TEXT_CONTENT),
                    task.checkTextContent
                ),
                StorageOperation.createBooleanOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_CHECK_TEXT_CASE_SENSITIVE),
                    task.checkTextCaseSensitive
                ),
                StorageOperation.createBooleanOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_CHECK_TEXT_USE_REGEX),
                    task.checkTextUseRegex
                ),
                saveEnum(generateKey(task.id, FIELD_CHECK_TEXT_MATCH_MODE), task.checkTextMatchMode),
                StorageOperation.createBooleanOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_CHECK_TEXT_INCLUDE_OVERLAY),
                    task.checkTextIncludeOverlay
                ),
                StorageOperation.createBooleanOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_CHECK_TEXT_IGNORE_HIDDEN),
                    task.checkTextIgnoreHidden
                ),
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_CHECK_TEXT_OUTPUT_FILE),
                    task.checkTextOutputFile
                ),
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_CHECK_TEXT_VIEW_ID_OUTPUT_FILE),
                    task.checkTextViewIdOutputFile
                ),

                // 检查像素颜色相关字段
                saveEnum(generateKey(task.id, FIELD_CHECK_PIXEL_COORDINATE_TYPE), task.checkPixelCoordinateType),
                StorageOperation.createFloatOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_CHECK_PIXEL_X),
                    task.checkPixelX
                ),
                StorageOperation.createFloatOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_CHECK_PIXEL_Y),
                    task.checkPixelY
                ),
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_CHECK_PIXEL_COLOR_OUTPUT_FILE),
                    task.checkPixelColorOutputFile
                ),

                // 读取屏幕内容相关字段
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_READ_CONTENT_OUTPUT_FILE),
                    task.readContentOutputFile
                ),

                // 自动点击器相关字段
                saveEnum(generateKey(task.id, FIELD_AUTO_CLICKER_SOURCE_TYPE), task.autoClickerSourceType),
                saveEnum(generateKey(task.id, FIELD_QUICK_OPERATION_TYPE), task.quickOperationType),
                StorageOperation.createStringOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_RECORDED_GESTURE),
                    task.recordedGesture
                ),
                StorageOperation.createIntOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_PLAYBACK_LOOP_COUNT),
                    task.playbackLoopCount
                ),
                StorageOperation.createFloatOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_PLAYBACK_SPEED),
                    task.playbackSpeed
                ),
                StorageOperation.createLongOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_DELAY_BETWEEN_LOOPS),
                    task.delayBetweenLoops
                ),
                StorageOperation.createBooleanOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_ADAPT_TO_SCREEN_SIZE),
                    task.adaptToScreenSize
                ),
                StorageOperation.createBooleanOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_STOP_ON_ERROR),
                    task.stopOnError
                ),

                // 快速操作相关字段
                StorageOperation.createFloatOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_CLICK_X),
                    task.clickX
                ),
                StorageOperation.createFloatOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_CLICK_Y),
                    task.clickY
                ),
                StorageOperation.createIntOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_CLICK_COUNT),
                    task.clickCount
                ),
                StorageOperation.createLongOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_CLICK_INTERVAL),
                    task.clickInterval
                ),
                StorageOperation.createLongOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_LONG_PRESS_DURATION),
                    task.longPressDuration
                ),
                StorageOperation.createFloatOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_SWIPE_START_X),
                    task.swipeStartX
                ),
                StorageOperation.createFloatOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_SWIPE_START_Y),
                    task.swipeStartY
                ),
                StorageOperation.createFloatOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_SWIPE_END_X),
                    task.swipeEndX
                ),
                StorageOperation.createFloatOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_SWIPE_END_Y),
                    task.swipeEndY
                ),
                StorageOperation.createLongOperation(
                    StorageDomain.TASKS,
                    generateKey(task.id, FIELD_SWIPE_DURATION),
                    task.swipeDuration
                )
            ))

            val success = storageManager.executeBatch(operations)
            if (success) {
                Log.d(TAG, "屏幕控制任务保存成功: ${task.id}")
            } else {
                logSaveError(task.id, "Batch operation failed")
            }
            success

        } catch (e: Exception) {
            logSaveError(task.id, "Exception during save: ${e.message}")
            false
        }
    }

    /**
     * 加载屏幕控制任务
     * 从原生数据类型重建ScreenControlTask对象
     *
     * @param taskId 任务ID
     * @return 加载的屏幕控制任务，失败时返回null
     */
    override fun load(taskId: String): ScreenControlTask? {
        if (!isValidTaskId(taskId)) {
            logLoadError(taskId, "Invalid task ID")
            return null
        }

        Log.d(TAG, "开始加载屏幕控制任务: $taskId")

        return try {
            // 检查任务是否存在
            if (!exists(taskId)) {
                Log.d(TAG, "屏幕控制任务不存在: $taskId")
                return null
            }

            ScreenControlTask(
                id = taskId,
                operation = loadEnum(generateKey(taskId, FIELD_OPERATION)) { ScreenControlOperation.valueOf(it) }
                    ?: ScreenControlOperation.BRIGHTNESS_CONTROL,
                brightnessControlType = loadEnum(generateKey(taskId, FIELD_BRIGHTNESS_CONTROL_TYPE)) { BrightnessControlType.valueOf(it) }
                    ?: BrightnessControlType.PERCENTAGE,
                brightnessValue = storageManager.loadInt(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_BRIGHTNESS_VALUE),
                    50
                ),
                enableAutoBrightness = storageManager.loadBoolean(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_ENABLE_AUTO_BRIGHTNESS),
                    false
                ),
                keepAwakeOperation = loadEnum(generateKey(taskId, FIELD_KEEP_AWAKE_OPERATION)) { KeepAwakeOperation.valueOf(it) }
                    ?: KeepAwakeOperation.ENABLE,
                screenOnOffOperation = loadEnum(generateKey(taskId, FIELD_SCREEN_ON_OFF_OPERATION)) { ScreenOnOffOperation.valueOf(it) }
                    ?: ScreenOnOffOperation.TURN_ON,
                screenOnOffImplementation = loadEnumNullable(generateKey(taskId, FIELD_SCREEN_ON_OFF_IMPLEMENTATION)) { ScreenOnOffImplementation.valueOf(it) },
                screenDimnessSensorMode = loadEnum(generateKey(taskId, FIELD_SCREEN_DIMNESS_SENSOR_MODE)) { ScreenDimnessSensorMode.valueOf(it) }
                    ?: ScreenDimnessSensorMode.SENSOR_OFF,
                screenDimnessPercentage = storageManager.loadInt(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_SCREEN_DIMNESS_PERCENTAGE),
                    50
                ),
                touchBlockOperation = loadEnum(generateKey(taskId, FIELD_TOUCH_BLOCK_OPERATION)) { TouchBlockOperation.valueOf(it) }
                    ?: TouchBlockOperation.ENABLE,
                emergencyCloseEnabled = storageManager.loadBoolean(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_EMERGENCY_CLOSE_ENABLED),
                    true
                ),
                emergencyClickCount = storageManager.loadInt(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_EMERGENCY_CLICK_COUNT),
                    5
                ),
                emergencyTimeWindowSeconds = storageManager.loadInt(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_EMERGENCY_TIME_WINDOW_SECONDS),
                    3
                ),
                forceRotationMode = loadEnum(generateKey(taskId, FIELD_FORCE_ROTATION_MODE)) { ForceRotationMode.valueOf(it) }
                    ?: ForceRotationMode.FORCE_PORTRAIT,
                screenTimeoutValue = storageManager.loadInt(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_SCREEN_TIMEOUT_VALUE),
                    30
                ),
                screenTimeoutUnit = loadEnum(generateKey(taskId, FIELD_SCREEN_TIMEOUT_UNIT)) { ScreenTimeoutUnit.valueOf(it) }
                    ?: ScreenTimeoutUnit.SECONDS,

                // 检查屏幕文字相关字段
                checkTextContent = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_CHECK_TEXT_CONTENT),
                    ""
                ),
                checkTextCaseSensitive = storageManager.loadBoolean(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_CHECK_TEXT_CASE_SENSITIVE),
                    false
                ),
                checkTextUseRegex = storageManager.loadBoolean(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_CHECK_TEXT_USE_REGEX),
                    false
                ),
                checkTextMatchMode = loadEnum(generateKey(taskId, FIELD_CHECK_TEXT_MATCH_MODE)) { TextMatchMode.valueOf(it) }
                    ?: TextMatchMode.CONTAINS,
                checkTextIncludeOverlay = storageManager.loadBoolean(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_CHECK_TEXT_INCLUDE_OVERLAY),
                    false
                ),
                checkTextIgnoreHidden = storageManager.loadBoolean(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_CHECK_TEXT_IGNORE_HIDDEN),
                    true
                ),
                checkTextOutputFile = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_CHECK_TEXT_OUTPUT_FILE),
                    ""
                ),
                checkTextViewIdOutputFile = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_CHECK_TEXT_VIEW_ID_OUTPUT_FILE),
                    ""
                ),

                // 检查像素颜色相关字段
                checkPixelCoordinateType = loadEnum(generateKey(taskId, FIELD_CHECK_PIXEL_COORDINATE_TYPE)) { CoordinateType.valueOf(it) }
                    ?: CoordinateType.PIXEL,
                checkPixelX = storageManager.loadFloat(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_CHECK_PIXEL_X),
                    0.0f
                ),
                checkPixelY = storageManager.loadFloat(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_CHECK_PIXEL_Y),
                    0.0f
                ),
                checkPixelColorOutputFile = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_CHECK_PIXEL_COLOR_OUTPUT_FILE),
                    ""
                ),

                // 读取屏幕内容相关字段
                readContentOutputFile = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_READ_CONTENT_OUTPUT_FILE),
                    ""
                ),

                // 自动点击器相关字段
                autoClickerSourceType = loadEnum(generateKey(taskId, FIELD_AUTO_CLICKER_SOURCE_TYPE)) { AutoClickerSourceType.valueOf(it) }
                    ?: AutoClickerSourceType.INSTANT_RECORDING,
                quickOperationType = loadEnum(generateKey(taskId, FIELD_QUICK_OPERATION_TYPE)) { QuickOperationType.valueOf(it) }
                    ?: QuickOperationType.SINGLE_CLICK,
                recordedGesture = storageManager.loadString(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_RECORDED_GESTURE),
                    ""
                ),
                playbackLoopCount = storageManager.loadInt(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_PLAYBACK_LOOP_COUNT),
                    1
                ),
                playbackSpeed = storageManager.loadFloat(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_PLAYBACK_SPEED),
                    1.0f
                ),
                delayBetweenLoops = storageManager.loadLong(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_DELAY_BETWEEN_LOOPS),
                    1000L
                ),
                adaptToScreenSize = storageManager.loadBoolean(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_ADAPT_TO_SCREEN_SIZE),
                    true
                ),
                stopOnError = storageManager.loadBoolean(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_STOP_ON_ERROR),
                    true
                ),

                // 快速操作相关字段
                clickX = storageManager.loadFloat(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_CLICK_X),
                    0.5f
                ),
                clickY = storageManager.loadFloat(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_CLICK_Y),
                    0.5f
                ),
                clickCount = storageManager.loadInt(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_CLICK_COUNT),
                    1
                ),
                clickInterval = storageManager.loadLong(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_CLICK_INTERVAL),
                    500L
                ),
                longPressDuration = storageManager.loadLong(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_LONG_PRESS_DURATION),
                    1000L
                ),
                swipeStartX = storageManager.loadFloat(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_SWIPE_START_X),
                    0.5f
                ),
                swipeStartY = storageManager.loadFloat(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_SWIPE_START_Y),
                    0.8f
                ),
                swipeEndX = storageManager.loadFloat(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_SWIPE_END_X),
                    0.5f
                ),
                swipeEndY = storageManager.loadFloat(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_SWIPE_END_Y),
                    0.2f
                ),
                swipeDuration = storageManager.loadLong(
                    StorageDomain.TASKS,
                    generateKey(taskId, FIELD_SWIPE_DURATION),
                    300L
                )
            ).also {
                Log.d(TAG, "屏幕控制任务加载成功: $taskId")
            }

        } catch (e: Exception) {
            logLoadError(taskId, "Exception during load: ${e.message}")
            null
        }
    }
}
