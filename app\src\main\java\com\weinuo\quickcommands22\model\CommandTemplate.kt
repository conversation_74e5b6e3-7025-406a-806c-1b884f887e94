package com.weinuo.quickcommands22.model

import android.content.Context
import com.weinuo.quickcommands22.R

/**
 * 快捷指令模板数据类
 *
 * 用于定义预设的快捷指令模板，用户可以基于这些模板快速创建快捷指令
 *
 * @property id 模板唯一标识符
 * @property titleKey 模板标题的多语言键（用于国际化）
 * @property descriptionKey 模板描述的多语言键（用于国际化）
 * @property category 模板分类
 * @property command 预设的快捷指令内容
 * @property imageResId 可选的插图资源ID，用于大图片卡片显示
 * @property hasImage 是否有配套插图
 */
data class CommandTemplate(
    val id: String,
    val titleKey: String,
    val descriptionKey: String,
    val category: TemplateCategory,
    val command: QuickCommand,
    val imageResId: Int? = null,
    val hasImage: Boolean = false
) {
    /**
     * 获取本地化的标题
     * 根据当前语言设置返回对应的字符串资源
     */
    fun getLocalizedTitle(context: Context): String {
        val resourceId = when (titleKey) {
            "template_screen_off_network_title" -> R.string.template_screen_off_network_title
            "template_screen_on_network_title" -> R.string.template_screen_on_network_title
            "template_auto_brightness_title" -> R.string.template_auto_brightness_title
            "template_battery_saver_title" -> R.string.template_battery_saver_title
            "template_do_not_disturb_title" -> R.string.template_do_not_disturb_title
            "template_anti_embarrassment_mode_title" -> R.string.template_anti_embarrassment_mode_title
            else -> return titleKey // 默认返回键名
        }
        return context.getString(resourceId)
    }

    /**
     * 获取本地化的描述
     * 根据当前语言设置返回对应的字符串资源
     */
    fun getLocalizedDescription(context: Context): String {
        val resourceId = when (descriptionKey) {
            "template_screen_off_network_desc" -> R.string.template_screen_off_network_desc
            "template_screen_on_network_desc" -> R.string.template_screen_on_network_desc
            "template_auto_brightness_desc" -> R.string.template_auto_brightness_desc
            "template_battery_saver_desc" -> R.string.template_battery_saver_desc
            "template_do_not_disturb_desc" -> R.string.template_do_not_disturb_desc
            "template_anti_embarrassment_mode_desc" -> R.string.template_anti_embarrassment_mode_desc
            else -> return descriptionKey // 默认返回键名
        }
        return context.getString(resourceId)
    }
}

/**
 * 模板分类枚举
 */
enum class TemplateCategory(val displayNameKey: String) {
    NETWORK("template_category_network"),
    POWER("template_category_power"),
    DISPLAY("template_category_display"),
    SYSTEM("template_category_system"),
    AUTOMATION("template_category_automation");

    /**
     * 获取本地化的分类名称
     */
    fun getLocalizedDisplayName(context: Context): String {
        val resourceId = when (this) {
            NETWORK -> R.string.template_category_network
            POWER -> R.string.template_category_power
            DISPLAY -> R.string.template_category_display
            SYSTEM -> R.string.template_category_system
            AUTOMATION -> R.string.template_category_automation
        }
        return context.getString(resourceId)
    }
}

/**
 * 预设模板提供器
 *
 * 提供所有预设的快捷指令模板
 */
object CommandTemplateProvider {

    /**
     * 获取所有预设模板
     */
    fun getAllTemplates(): List<CommandTemplate> {
        return listOf(
            createScreenOffNetworkTemplate(),
            createScreenOnNetworkTemplate(),
            createAntiEmbarrassmentModeTemplate()
        )
    }

    /**
     * 根据分类获取模板
     */
    fun getTemplatesByCategory(category: TemplateCategory): List<CommandTemplate> {
        return getAllTemplates().filter { it.category == category }
    }

    /**
     * 根据ID获取模板
     */
    fun getTemplateById(id: String): CommandTemplate? {
        return getAllTemplates().find { it.id == id }
    }

    /**
     * 创建息屏关闭网络模板
     */
    private fun createScreenOffNetworkTemplate(): CommandTemplate {
        val command = QuickCommand(
            id = "", // 将在用户创建时生成新ID
            name = "息屏关闭网络",
            isEnabled = true,
            triggerConditions = listOf(
                DeviceEventCondition(
                    id = "",
                    eventType = DeviceEventType.SCREEN_STATE,
                    screenEventType = ScreenEventType.OFF
                )
            ),
            tasks = listOf(
                DeviceActionTask(
                    id = "",
                    operation = DeviceActionOperation.WAIT_DELAY,
                    waitMinutes = 2
                ),
                ConnectivityTask(
                    id = "",
                    operation = ConnectivityOperation.NETWORK_STATE_SAVE
                ),
                ConnectivityTask(
                    id = "",
                    operation = ConnectivityOperation.WIFI_CONTROL,
                    wifiOperation = SwitchOperation.DISABLE
                ),
                ConnectivityTask(
                    id = "",
                    operation = ConnectivityOperation.MOBILE_DATA_CONTROL,
                    mobileDataOperation = SwitchOperation.DISABLE
                )
            ),
            abortConditions = listOf(
                DeviceEventCondition(
                    id = "",
                    eventType = DeviceEventType.SCREEN_STATE,
                    screenEventType = ScreenEventType.ON
                )
            ),
            requireAllAbortConditions = false
        )

        return CommandTemplate(
            id = "screen_off_network",
            titleKey = "template_screen_off_network_title",
            descriptionKey = "template_screen_off_network_desc",
            category = TemplateCategory.NETWORK,
            command = command,
            imageResId = R.drawable.template_screen_off_network,
            hasImage = true
        )
    }

    /**
     * 创建亮屏恢复网络模板
     */
    private fun createScreenOnNetworkTemplate(): CommandTemplate {
        val command = QuickCommand(
            id = "", // 将在用户创建时生成新ID
            name = "亮屏恢复网络",
            isEnabled = true,
            triggerConditions = listOf(
                DeviceEventCondition(
                    id = "",
                    eventType = DeviceEventType.SCREEN_STATE,
                    screenEventType = ScreenEventType.ON
                )
            ),
            tasks = listOf(
                DeviceActionTask(
                    id = "",
                    operation = DeviceActionOperation.WAIT_DELAY,
                    waitMinutes = 1
                ),
                ConnectivityTask(
                    id = "",
                    operation = ConnectivityOperation.NETWORK_STATE_RESTORE
                )
            ),
            abortConditions = listOf(
                DeviceEventCondition(
                    id = "",
                    eventType = DeviceEventType.SCREEN_STATE,
                    screenEventType = ScreenEventType.OFF
                )
            ),
            requireAllAbortConditions = false
        )

        return CommandTemplate(
            id = "screen_on_network",
            titleKey = "template_screen_on_network_title",
            descriptionKey = "template_screen_on_network_desc",
            category = TemplateCategory.NETWORK,
            command = command,
            imageResId = R.drawable.template_screen_on_network,
            hasImage = true
        )
    }

    /**
     * 创建防社死模式模板
     */
    private fun createAntiEmbarrassmentModeTemplate(): CommandTemplate {
        val command = QuickCommand(
            id = "", // 将在用户创建时生成新ID
            name = "防社死模式",
            isEnabled = true,
            triggerConditions = emptyList(), // 触发条件留空，用户可自定义
            tasks = listOf(
                VolumeTask(
                    id = "",
                    operation = VolumeOperation.VOLUME_CHANGE,
                    volumeStreamType = VolumeStreamType.MEDIA_MUSIC,
                    volumeMode = VolumeMode.PERCENTAGE,
                    volumeValue = 13
                )
            ),
            abortConditions = emptyList(),
            requireAllAbortConditions = false
        )

        return CommandTemplate(
            id = "anti_embarrassment_mode",
            titleKey = "template_anti_embarrassment_mode_title",
            descriptionKey = "template_anti_embarrassment_mode_desc",
            category = TemplateCategory.SYSTEM,
            command = command,
            imageResId = R.drawable.template_anti_embarrassment_mode,
            hasImage = true
        )
    }

}
