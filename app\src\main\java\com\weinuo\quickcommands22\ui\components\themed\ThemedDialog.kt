package com.weinuo.quickcommands22.ui.components.themed

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.window.DialogProperties
import com.weinuo.quickcommands22.ui.theme.config.DialogConfig
import com.weinuo.quickcommands22.ui.theme.manager.LocalThemeContext

/**
 * 主题感知的对话框组件
 *
 * 根据当前主题自动选择合适的实现：
 * - 海洋蓝主题：使用分层设计风格（LayeredDialog）- 带阴影效果
 * - 天空蓝主题：使用整合设计风格（IntegratedDialog）- 支持模糊效果
 * - 未来主题：可以使用各自独有的实现
 *
 * 对话框是重要的交互组件，天空蓝主题的模糊效果
 * 可以为对话框提供更好的视觉层次感。
 */
@Composable
fun ThemedDialog(
    config: DialogConfig,
    modifier: Modifier = Modifier
) {
    val themeContext = LocalThemeContext.current
    
    // 使用当前主题的组件工厂创建对话框
    themeContext.componentFactory.createDialog()(
        config.copy(modifier = modifier)
    )
}

/**
 * 主题感知的对话框组件 - 便捷版本
 *
 * 提供更简洁的API，自动处理常见的配置
 */
@Composable
fun ThemedDialog(
    onDismissRequest: () -> Unit,
    modifier: Modifier = Modifier,
    properties: DialogProperties = DialogProperties(),
    blurEnabled: Boolean? = null,
    blurRadius: Dp? = null,
    content: @Composable () -> Unit
) {
    val themeContext = LocalThemeContext.current
    
    val config = DialogConfig(
        onDismissRequest = onDismissRequest,
        modifier = modifier,
        properties = properties,
        blurEnabled = blurEnabled ?: themeContext.blurConfiguration.dialogBlurEnabled,
        blurRadius = blurRadius,
        content = content
    )
    
    // 使用主题感知组件
    ThemedDialog(config = config)
}

/**
 * 主题感知的简单对话框 - 最简版本
 *
 * 只需要关闭回调和内容的最简单版本
 */
@Composable
fun ThemedDialog(
    onDismissRequest: () -> Unit,
    content: @Composable () -> Unit
) {
    ThemedDialog(
        onDismissRequest = onDismissRequest,
        properties = DialogProperties(),
        content = content
    )
}
