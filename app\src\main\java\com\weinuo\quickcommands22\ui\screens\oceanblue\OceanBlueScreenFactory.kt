package com.weinuo.quickcommands22.ui.screens.oceanblue

import androidx.compose.runtime.Composable
import androidx.navigation.NavController
import com.weinuo.quickcommands22.ui.screens.ScreenFactory
import com.weinuo.quickcommands22.ui.screens.ScreenType
import com.weinuo.quickcommands22.data.SettingsRepository
import com.weinuo.quickcommands22.data.QuickCommandRepository
import com.weinuo.quickcommands22.shortcut.ShortcutManager
import com.weinuo.quickcommands22.utils.ExperimentalFeatureDetector
// 海洋蓝专用界面函数在同一个包中，无需导入

/**
 * 海洋蓝主题界面工厂
 *
 * 创建分层设计风格的界面实现
 * 特点：使用现有界面文件，保持简洁清晰的视觉分离
 */
class OceanBlueScreenFactory : ScreenFactory {

    /**
     * 创建海洋蓝主题的快捷指令界面
     * 使用海洋蓝专用的OceanBlueQuickCommandsScreen
     */
    @Composable
    override fun createQuickCommandsScreen(
        navController: NavController,
        quickCommandRepository: QuickCommandRepository,
        shortcutManager: ShortcutManager
    ) {
        OceanBlueQuickCommandsScreen(navController, quickCommandRepository, shortcutManager)
    }

    /**
     * 创建海洋蓝主题的全局设置界面
     * 使用海洋蓝专用的OceanBlueGlobalSettingsScreen
     */
    @Composable
    override fun createGlobalSettingsScreen(
        settingsRepository: SettingsRepository,
        experimentalFeatureDetector: ExperimentalFeatureDetector?
    ) {
        OceanBlueGlobalSettingsScreen(settingsRepository, experimentalFeatureDetector)
    }

    /**
     * 创建海洋蓝主题的命令模板界面
     * 使用海洋蓝专用的OceanBlueCommandTemplatesScreen
     */
    @Composable
    override fun createCommandTemplatesScreen(
        navController: NavController,
        quickCommandRepository: QuickCommandRepository
    ) {
        OceanBlueCommandTemplatesScreen(navController, quickCommandRepository)
    }

    /**
     * 创建海洋蓝主题的智能提醒界面
     * 使用海洋蓝专用的OceanBlueSmartRemindersScreen
     */
    @Composable
    override fun createSmartRemindersScreen(navController: NavController) {
        OceanBlueSmartRemindersScreen(navController)
    }

    /**
     * 检查是否支持特定界面类型
     * 海洋蓝主题支持所有标准界面
     */
    override fun supportsScreen(screenType: ScreenType): Boolean {
        return when (screenType) {
            ScreenType.QUICK_COMMANDS -> true
            ScreenType.GLOBAL_SETTINGS -> true
            ScreenType.COMMAND_TEMPLATES -> true
            ScreenType.SMART_REMINDERS -> true
            // 其他界面类型暂时使用默认支持
            else -> true
        }
    }

    /**
     * 获取海洋蓝界面工厂的版本
     */
    override fun getVersion(): String {
        return "1.0.0"
    }
}
