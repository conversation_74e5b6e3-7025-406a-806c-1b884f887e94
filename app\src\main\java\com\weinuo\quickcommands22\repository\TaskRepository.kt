package com.weinuo.quickcommands22.repository

import android.content.Context
import android.util.Log
import com.weinuo.quickcommands22.model.SharedTask
import com.weinuo.quickcommands22.model.PhoneTask
import com.weinuo.quickcommands22.model.MediaTask
import com.weinuo.quickcommands22.model.ApplicationTask
import com.weinuo.quickcommands22.model.VolumeTask
import com.weinuo.quickcommands22.model.ConnectivityTask
import com.weinuo.quickcommands22.model.DateTimeTask
import com.weinuo.quickcommands22.model.DeviceActionTask
import com.weinuo.quickcommands22.model.InformationTask
import com.weinuo.quickcommands22.model.CameraTask
import com.weinuo.quickcommands22.model.FileOperationTask
import com.weinuo.quickcommands22.model.ScreenControlTask
import com.weinuo.quickcommands22.model.NotificationTask
import com.weinuo.quickcommands22.model.DeviceSettingsTask
import com.weinuo.quickcommands22.model.LocationTask
import com.weinuo.quickcommands22.storage.NativeTypeStorageManager
import com.weinuo.quickcommands22.storage.adapters.TaskAdapterManager
import com.weinuo.quickcommands22.storage.StorageDomain
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * 任务仓库类
 *
 * 提供统一的任务CRUD接口，支持所有类型的任务管理。
 * 使用原生数据类型存储架构，确保数据持久性和可靠性。
 *
 * 核心功能：
 * - 统一的任务CRUD操作
 * - 按类型查询任务
 * - 任务验证功能
 * - 响应式数据流支持
 * - 批量操作优化
 * - 完整的错误处理和日志记录
 *
 * 支持的任务类型：
 * - PhoneTask: 电话任务
 * - MediaTask: 媒体任务
 * - ApplicationTask: 应用程序任务
 * - VolumeTask: 音量任务
 * - ConnectivityTask: 连接任务
 * - DateTimeTask: 日期时间任务
 * - DeviceActionTask: 设备动作任务
 * - InformationTask: 信息任务
 * - CameraTask: 相机任务
 * - FileOperationTask: 文件操作任务
 * - ScreenControlTask: 屏幕控制任务
 * - NotificationTask: 通知任务
 * - DeviceSettingsTask: 设备设置任务
 * - LocationTask: 位置任务
 *
 * @param context Android上下文
 */
class TaskRepository(private val context: Context) {

    companion object {
        private const val TAG = "TaskRepository"

        // 任务类型常量
        const val TYPE_PHONE = "phone"
        const val TYPE_MEDIA = "media"
        const val TYPE_APPLICATION = "application"
        const val TYPE_VOLUME = "volume"
        const val TYPE_CONNECTIVITY = "connectivity"
        const val TYPE_DATE_TIME = "date_time"
        const val TYPE_DEVICE_ACTION = "device_action"
        const val TYPE_INFORMATION = "information"
        const val TYPE_CAMERA = "camera"
        const val TYPE_FILE_OPERATION = "file_operation"
        const val TYPE_SCREEN_CONTROL = "screen_control"
        const val TYPE_NOTIFICATION = "notification"
        const val TYPE_DEVICE_SETTINGS = "device_settings"
        const val TYPE_LOCATION = "location"
    }

    // 核心存储组件
    private val storageManager = NativeTypeStorageManager(context)
    private val taskAdapterManager = TaskAdapterManager(storageManager)

    // 响应式数据流
    private val _allTasks = MutableStateFlow<List<SharedTask>>(emptyList())
    val allTasks: StateFlow<List<SharedTask>> = _allTasks.asStateFlow()

    // 按类型分组的数据流
    private val _phoneTasks = MutableStateFlow<List<PhoneTask>>(emptyList())
    val phoneTasks: StateFlow<List<PhoneTask>> = _phoneTasks.asStateFlow()

    private val _mediaTasks = MutableStateFlow<List<MediaTask>>(emptyList())
    val mediaTasks: StateFlow<List<MediaTask>> = _mediaTasks.asStateFlow()

    private val _applicationTasks = MutableStateFlow<List<ApplicationTask>>(emptyList())
    val applicationTasks: StateFlow<List<ApplicationTask>> = _applicationTasks.asStateFlow()

    // 初始化时加载数据
    init {
        loadTasksToFlow()
    }

    /**
     * 保存任务
     *
     * @param task 要保存的任务
     * @return 操作是否成功
     */
    suspend fun saveTask(task: SharedTask): Boolean = withContext(Dispatchers.IO) {
        try {
            val success = taskAdapterManager.saveTask(task)
            if (success) {
                Log.d(TAG, "Successfully saved task: ${task.id} (type: ${getTaskType(task)})")
                // 更新数据流
                updateTaskInFlow(task)
            } else {
                Log.e(TAG, "Failed to save task: ${task.id}")
            }
            success
        } catch (e: Exception) {
            Log.e(TAG, "Error saving task: ${task.id}", e)
            false
        }
    }

    /**
     * 加载任务
     *
     * @param taskId 任务ID
     * @return 任务对象，如果不存在则返回null
     */
    suspend fun loadTask(taskId: String): SharedTask? = withContext(Dispatchers.IO) {
        try {
            // 首先读取任务类型
            val taskTypeKey = "task_${taskId}_type"
            val taskType = storageManager.loadString(StorageDomain.TASKS, taskTypeKey)

            val task = if (taskType.isNotEmpty()) {
                taskAdapterManager.loadTask<SharedTask>(taskId, taskType)
            } else {
                Log.w(TAG, "Task type not found: $taskId")
                null
            }

            if (task != null) {
                Log.d(TAG, "Successfully loaded task: ${task.id} (type: ${getTaskType(task)})")
            } else {
                Log.d(TAG, "Task not found: $taskId")
            }
            task
        } catch (e: Exception) {
            Log.e(TAG, "Error loading task: $taskId", e)
            null
        }
    }

    /**
     * 删除任务
     *
     * @param taskId 任务ID
     * @return 操作是否成功
     */
    suspend fun deleteTask(taskId: String): Boolean = withContext(Dispatchers.IO) {
        try {
            // 首先获取任务类型
            val taskTypeKey = "task_${taskId}_type"
            val taskType = storageManager.loadString(StorageDomain.TASKS, taskTypeKey)

            val success = if (taskType.isNotEmpty()) {
                taskAdapterManager.deleteTask(taskId, taskType)
            } else {
                Log.w(TAG, "Task type not found, attempting prefix deletion: $taskId")
                val prefix = "task_${taskId}_"
                storageManager.deleteByPrefix(StorageDomain.TASKS, prefix)
            }

            if (success) {
                Log.d(TAG, "Successfully deleted task: $taskId")
                // 更新数据流
                removeTaskFromFlow(taskId)
            } else {
                Log.e(TAG, "Failed to delete task: $taskId")
            }
            success
        } catch (e: Exception) {
            Log.e(TAG, "Error deleting task: $taskId", e)
            false
        }
    }

    /**
     * 检查任务是否存在
     *
     * @param taskId 任务ID
     * @return 任务是否存在
     */
    suspend fun taskExists(taskId: String): Boolean = withContext(Dispatchers.IO) {
        try {
            val taskTypeKey = "task_${taskId}_type"
            val exists = storageManager.containsKey(StorageDomain.TASKS, taskTypeKey)
            Log.d(TAG, "Task exists check: $taskId -> $exists")
            exists
        } catch (e: Exception) {
            Log.e(TAG, "Error checking task existence: $taskId", e)
            false
        }
    }

    /**
     * 加载所有任务
     *
     * @return 任务列表
     */
    suspend fun loadAllTasks(): List<SharedTask> = withContext(Dispatchers.IO) {
        try {
            val tasks = mutableListOf<SharedTask>()
            val taskIds = getAllTaskIds()

            taskIds.forEach { taskId ->
                try {
                    val task = loadTask(taskId)
                    if (task != null) {
                        tasks.add(task)
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Error loading task: $taskId", e)
                }
            }

            Log.d(TAG, "Successfully loaded ${tasks.size}/${taskIds.size} tasks")
            _allTasks.value = tasks
            updateTypeSpecificFlows(tasks)
            tasks
        } catch (e: Exception) {
            Log.e(TAG, "Error loading all tasks", e)
            emptyList()
        }
    }

    /**
     * 按类型查询任务
     *
     * @param taskType 任务类型
     * @return 指定类型的任务列表
     */
    suspend fun getTasksByType(taskType: String): List<SharedTask> = withContext(Dispatchers.IO) {
        try {
            val allTasks = loadAllTasks()
            val filteredTasks = allTasks.filter { getTaskType(it) == taskType }
            Log.d(TAG, "Found ${filteredTasks.size} tasks of type: $taskType")
            filteredTasks
        } catch (e: Exception) {
            Log.e(TAG, "Error getting tasks by type: $taskType", e)
            emptyList()
        }
    }

    /**
     * 批量保存任务
     *
     * @param tasks 任务列表
     * @return 成功保存的任务数量
     */
    suspend fun saveTasks(tasks: List<SharedTask>): Int = withContext(Dispatchers.IO) {
        var successCount = 0
        tasks.forEach { task ->
            if (saveTask(task)) {
                successCount++
            }
        }
        Log.d(TAG, "Batch save tasks completed: $successCount/${tasks.size}")
        return@withContext successCount
    }

    /**
     * 批量删除任务
     *
     * @param taskIds 任务ID列表
     * @return 成功删除的任务数量
     */
    suspend fun deleteTasks(taskIds: List<String>): Int = withContext(Dispatchers.IO) {
        var successCount = 0
        taskIds.forEach { taskId ->
            if (deleteTask(taskId)) {
                successCount++
            }
        }
        Log.d(TAG, "Batch delete tasks completed: $successCount/${taskIds.size}")
        return@withContext successCount
    }

    /**
     * 获取任务数量
     *
     * @return 任务总数
     */
    suspend fun getTaskCount(): Int = withContext(Dispatchers.IO) {
        try {
            val count = getAllTaskIds().size
            Log.d(TAG, "Total task count: $count")
            count
        } catch (e: Exception) {
            Log.e(TAG, "Error getting task count", e)
            0
        }
    }

    /**
     * 按类型获取任务数量
     *
     * @param taskType 任务类型
     * @return 指定类型的任务数量
     */
    suspend fun getTaskCountByType(taskType: String): Int = withContext(Dispatchers.IO) {
        try {
            val tasks = getTasksByType(taskType)
            val count = tasks.size
            Log.d(TAG, "Task count for type $taskType: $count")
            count
        } catch (e: Exception) {
            Log.e(TAG, "Error getting task count by type: $taskType", e)
            0
        }
    }

    /**
     * 验证任务
     *
     * @param task 要验证的任务
     * @return 验证结果
     */
    fun validateTask(task: SharedTask): TaskValidationResult {
        val issues = mutableListOf<String>()

        try {
            // 基本验证
            if (task.id.isBlank()) {
                issues.add("任务ID不能为空")
            }

            // 按类型进行特定验证
            when (task) {
                is PhoneTask -> validatePhoneTask(task, issues)
                is MediaTask -> validateMediaTask(task, issues)
                is ApplicationTask -> validateApplicationTask(task, issues)
                is VolumeTask -> validateVolumeTask(task, issues)
                is ConnectivityTask -> validateConnectivityTask(task, issues)
                is DateTimeTask -> validateDateTimeTask(task, issues)
                is DeviceActionTask -> validateDeviceActionTask(task, issues)
                is InformationTask -> validateInformationTask(task, issues)
                is CameraTask -> validateCameraTask(task, issues)
                is FileOperationTask -> validateFileOperationTask(task, issues)
                is ScreenControlTask -> validateScreenControlTask(task, issues)
                is NotificationTask -> validateNotificationTask(task, issues)
                is DeviceSettingsTask -> validateDeviceSettingsTask(task, issues)
                is LocationTask -> validateLocationTask(task, issues)
                else -> issues.add("未知的任务类型: ${task.javaClass.simpleName}")
            }

        } catch (e: Exception) {
            issues.add("验证过程中发生异常: ${e.message}")
            Log.e(TAG, "Error validating task: ${task.id}", e)
        }

        return TaskValidationResult(issues.isEmpty(), issues)
    }

    /**
     * 清空所有任务数据
     * 注意：这是一个危险操作，会删除所有任务数据
     *
     * @return 操作是否成功
     */
    suspend fun clearAllTasks(): Boolean = withContext(Dispatchers.IO) {
        try {
            val prefs = storageManager.getPreferences(StorageDomain.TASKS)
            val editor = prefs.edit()
            editor.clear()
            val success = editor.commit()

            if (success) {
                Log.d(TAG, "Successfully cleared all tasks")
                _allTasks.value = emptyList()
                updateTypeSpecificFlows(emptyList())
            } else {
                Log.e(TAG, "Failed to clear all tasks")
            }
            success
        } catch (e: Exception) {
            Log.e(TAG, "Error clearing all tasks", e)
            false
        }
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 加载任务到数据流
     */
    private fun loadTasksToFlow() {
        try {
            kotlinx.coroutines.GlobalScope.launch {
                val tasks = loadAllTasks()
                _allTasks.value = tasks
                updateTypeSpecificFlows(tasks)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error loading tasks to flow", e)
        }
    }

    /**
     * 更新数据流中的任务
     */
    private fun updateTaskInFlow(task: SharedTask) {
        try {
            val currentTasks = _allTasks.value.toMutableList()
            val existingIndex = currentTasks.indexOfFirst { it.id == task.id }

            if (existingIndex != -1) {
                currentTasks[existingIndex] = task
            } else {
                currentTasks.add(task)
            }

            _allTasks.value = currentTasks
            updateTypeSpecificFlows(currentTasks)
        } catch (e: Exception) {
            Log.e(TAG, "Error updating task in flow: ${task.id}", e)
        }
    }

    /**
     * 从数据流中移除任务
     */
    private fun removeTaskFromFlow(taskId: String) {
        try {
            val currentTasks = _allTasks.value.toMutableList()
            currentTasks.removeAll { it.id == taskId }

            _allTasks.value = currentTasks
            updateTypeSpecificFlows(currentTasks)
        } catch (e: Exception) {
            Log.e(TAG, "Error removing task from flow: $taskId", e)
        }
    }

    /**
     * 更新按类型分组的数据流
     */
    private fun updateTypeSpecificFlows(tasks: List<SharedTask>) {
        try {
            _phoneTasks.value = tasks.filterIsInstance<PhoneTask>()
            _mediaTasks.value = tasks.filterIsInstance<MediaTask>()
            _applicationTasks.value = tasks.filterIsInstance<ApplicationTask>()
        } catch (e: Exception) {
            Log.e(TAG, "Error updating type-specific flows", e)
        }
    }

    /**
     * 获取所有任务ID
     */
    private fun getAllTaskIds(): List<String> {
        return try {
            val prefs = storageManager.getPreferences(StorageDomain.TASKS)
            val allKeys = prefs.all.keys

            // 提取所有任务ID（从type字段的键中提取）
            val taskIds = mutableSetOf<String>()
            allKeys.forEach { key ->
                if (key.startsWith("task_") && key.endsWith("_type")) {
                    val taskId = key.removePrefix("task_").removeSuffix("_type")
                    taskIds.add(taskId)
                }
            }

            taskIds.toList()
        } catch (e: Exception) {
            Log.e(TAG, "Error getting all task IDs", e)
            emptyList()
        }
    }

    /**
     * 获取任务类型
     */
    private fun getTaskType(task: SharedTask): String {
        return when (task) {
            is PhoneTask -> TYPE_PHONE
            is MediaTask -> TYPE_MEDIA
            is ApplicationTask -> TYPE_APPLICATION
            is VolumeTask -> TYPE_VOLUME
            is ConnectivityTask -> TYPE_CONNECTIVITY
            is DateTimeTask -> TYPE_DATE_TIME
            is DeviceActionTask -> TYPE_DEVICE_ACTION
            is InformationTask -> TYPE_INFORMATION
            is CameraTask -> TYPE_CAMERA
            is FileOperationTask -> TYPE_FILE_OPERATION
            is ScreenControlTask -> TYPE_SCREEN_CONTROL
            is NotificationTask -> TYPE_NOTIFICATION
            is DeviceSettingsTask -> TYPE_DEVICE_SETTINGS
            is LocationTask -> TYPE_LOCATION
            else -> "unknown"
        }
    }

    // ==================== 任务验证方法 ====================

    /**
     * 验证电话任务
     */
    private fun validatePhoneTask(task: PhoneTask, issues: MutableList<String>) {
        when (task.operation) {
            com.weinuo.quickcommands22.model.PhoneOperation.MAKE_CALL -> {
                if (task.makeCallType == com.weinuo.quickcommands22.model.MakeCallType.MANUAL_INPUT && task.phoneNumber.isBlank()) {
                    issues.add("手动输入模式下电话号码不能为空")
                }
            }
            com.weinuo.quickcommands22.model.PhoneOperation.ANSWER_CALL -> {
                if (task.answerDelaySeconds < 0) {
                    issues.add("接听延迟时间不能为负数")
                }
            }
            else -> {
                // 其他操作的验证逻辑
            }
        }
    }

    /**
     * 验证媒体任务
     */
    private fun validateMediaTask(task: MediaTask, issues: MutableList<String>) {
        // 根据媒体任务的具体字段进行验证
        // 这里可以添加具体的验证逻辑
    }

    /**
     * 验证应用程序任务
     */
    private fun validateApplicationTask(task: ApplicationTask, issues: MutableList<String>) {
        if (task.appPackageName.isBlank()) {
            issues.add("目标应用包名不能为空")
        }
    }

    /**
     * 验证音量任务
     */
    private fun validateVolumeTask(task: VolumeTask, issues: MutableList<String>) {
        // 验证音量值范围
        if (task.volumeValue < 0 || task.volumeValue > 100) {
            issues.add("音量值必须在0-100之间")
        }
    }

    /**
     * 验证连接任务
     */
    private fun validateConnectivityTask(task: ConnectivityTask, issues: MutableList<String>) {
        // 根据连接任务的具体字段进行验证
        // 这里可以添加具体的验证逻辑
    }

    /**
     * 验证日期时间任务
     */
    private fun validateDateTimeTask(task: DateTimeTask, issues: MutableList<String>) {
        // 根据日期时间任务的具体字段进行验证
        // 这里可以添加具体的验证逻辑
    }

    /**
     * 验证设备动作任务
     */
    private fun validateDeviceActionTask(task: DeviceActionTask, issues: MutableList<String>) {
        // 根据设备动作任务的具体字段进行验证
        // 这里可以添加具体的验证逻辑
    }

    /**
     * 验证信息任务
     */
    private fun validateInformationTask(task: InformationTask, issues: MutableList<String>) {
        // 根据信息任务的具体字段进行验证
        // 这里可以添加具体的验证逻辑
    }

    /**
     * 验证相机任务
     */
    private fun validateCameraTask(task: CameraTask, issues: MutableList<String>) {
        // 根据相机任务的具体字段进行验证
        // 这里可以添加具体的验证逻辑
    }

    /**
     * 验证文件操作任务
     */
    private fun validateFileOperationTask(task: FileOperationTask, issues: MutableList<String>) {
        if (task.sourcePath.isBlank()) {
            issues.add("文件路径不能为空")
        }
    }

    /**
     * 验证屏幕控制任务
     */
    private fun validateScreenControlTask(task: ScreenControlTask, issues: MutableList<String>) {
        // 根据屏幕控制任务的具体字段进行验证
        // 这里可以添加具体的验证逻辑
    }

    /**
     * 验证通知任务
     */
    private fun validateNotificationTask(task: NotificationTask, issues: MutableList<String>) {
        when (task.operation) {
            com.weinuo.quickcommands22.model.NotificationOperation.SHOW_TOAST -> {
                if (task.toastMessage.isBlank()) {
                    issues.add("Toast消息不能为空")
                }
            }
            com.weinuo.quickcommands22.model.NotificationOperation.SHOW_DIALOG -> {
                if (task.dialogTitle.isBlank()) {
                    issues.add("对话框标题不能为空")
                }
            }
            com.weinuo.quickcommands22.model.NotificationOperation.SHOW_NOTIFICATION -> {
                if (task.notificationTitle.isBlank()) {
                    issues.add("通知标题不能为空")
                }
            }
            else -> {
                // 其他操作的验证逻辑
            }
        }
    }

    /**
     * 验证设备设置任务
     */
    private fun validateDeviceSettingsTask(task: DeviceSettingsTask, issues: MutableList<String>) {
        // 根据设备设置任务的具体字段进行验证
        // 这里可以添加具体的验证逻辑
    }

    /**
     * 验证位置任务
     */
    private fun validateLocationTask(task: LocationTask, issues: MutableList<String>) {
        // 根据位置任务的具体字段进行验证
        // 这里可以添加具体的验证逻辑
    }
}

/**
 * 任务验证结果数据类
 */
data class TaskValidationResult(
    val isValid: Boolean,
    val issues: List<String>
)
