package com.weinuo.quickcommands22.floating

import android.content.Context
import android.util.Log

/**
 * 高级悬浮窗录制管理器
 *
 * 统一管理高级悬浮窗录制功能，提供简单的API接口。
 * 负责权限检查、服务管理和状态跟踪。
 *
 * 与传统悬浮窗录制管理器的区别：
 * - 不监听用户实际触摸操作
 * - 通过可视化界面让用户直接设置手势
 * - 支持更丰富的手势类型选择
 * - 提供实时的坐标标记编辑功能
 */
object AdvancedFloatingRecordingManager {

    private const val TAG = "AdvancedFloatingRecordingManager"

    private var isAdvancedFloatingWindowActive = false

    /**
     * 启动高级悬浮录制窗口
     *
     * @param context 上下文
     * @return 是否成功启动
     */
    fun startAdvancedFloatingRecording(context: Context): Boolean {
        Log.d(TAG, "尝试启动高级悬浮录制窗口")

        // 检查悬浮窗权限
        if (!com.weinuo.quickcommands22.utils.OverlayPermissionUtil.hasOverlayPermission(context)) {
            Log.w(TAG, "没有悬浮窗权限，无法启动高级悬浮录制")
            return false
        }

        try {
            // 启动高级悬浮窗服务
            val success = AdvancedFloatingRecordingService.startAdvancedFloatingWindow(context)
            if (success) {
                isAdvancedFloatingWindowActive = true
                Log.d(TAG, "高级悬浮录制窗口启动成功")
            } else {
                Log.e(TAG, "高级悬浮录制窗口启动失败")
            }
            return success
        } catch (e: Exception) {
            Log.e(TAG, "启动高级悬浮录制窗口失败", e)
            isAdvancedFloatingWindowActive = false
            return false
        }
    }

    /**
     * 停止高级悬浮录制窗口
     *
     * @param context 上下文
     */
    fun stopAdvancedFloatingRecording(context: Context) {
        Log.d(TAG, "停止高级悬浮录制窗口")

        try {
            AdvancedFloatingRecordingService.stopAdvancedFloatingWindow(context)
            isAdvancedFloatingWindowActive = false
            Log.d(TAG, "高级悬浮录制窗口已停止")
        } catch (e: Exception) {
            Log.e(TAG, "停止高级悬浮录制窗口失败", e)
        }
    }

    /**
     * 检查高级悬浮窗是否处于活动状态
     *
     * @return 是否活动
     */
    fun isAdvancedFloatingWindowActive(): Boolean {
        return isAdvancedFloatingWindowActive
    }

    /**
     * 检查是否支持高级悬浮录制
     *
     * @param context 上下文
     * @return 是否支持
     */
    fun isAdvancedFloatingRecordingSupported(context: Context): Boolean {
        return com.weinuo.quickcommands22.utils.OverlayPermissionUtil.hasOverlayPermission(context)
    }

    /**
     * 获取高级悬浮录制的功能描述
     *
     * @return 功能描述
     */
    fun getAdvancedFloatingRecordingDescription(): String {
        return """
            高级悬浮窗录制模式特点：
            
            ✅ 可视化手势编辑
            - 通过拖拽坐标标记设置手势位置
            - 支持点击、长按、滑动等多种手势类型
            - 实时预览手势路径和连线
            
            ✅ 便捷的操作体验
            - 不阻挡用户查看底层应用
            - 可以自由滑动屏幕查看内容
            - 直观的数字标记和坐标显示
            
            ✅ 精确的坐标控制
            - 支持网格对齐功能
            - 显示精确的像素坐标
            - 可拖拽调整标记位置
            
            ✅ 丰富的编辑功能
            - 支持删除和编辑标记
            - 可调整手势延迟时间
            - 直接保存并跳转到编辑界面
        """.trimIndent()
    }

    /**
     * 获取使用建议
     *
     * @return 使用建议
     */
    fun getUsageTips(): List<String> {
        return listOf(
            "点击悬浮按钮的加号图标选择手势类型",
            "拖拽坐标标记调整手势位置",
            "滑动手势会显示起始点和终点连线",
            "长按悬浮按钮可访问保存、清空等功能",
            "数字标记表示手势执行顺序",
            "完成后点击保存会自动跳转到编辑界面"
        )
    }

    /**
     * 获取支持的手势类型
     *
     * @return 手势类型列表
     */
    fun getSupportedGestureTypes(): List<com.weinuo.quickcommands22.model.AdvancedGestureType> {
        return com.weinuo.quickcommands22.model.AdvancedGestureType.getAvailableTypes()
    }
}
