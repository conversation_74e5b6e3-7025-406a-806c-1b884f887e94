package com.weinuo.quickcommands22.storage

import android.content.Context
import android.util.Log
import com.weinuo.quickcommands22.model.QuickCommand
import com.weinuo.quickcommands22.model.SharedTask
import com.weinuo.quickcommands22.model.SharedTriggerCondition
import com.weinuo.quickcommands22.storage.adapters.ConditionAdapterManager
import com.weinuo.quickcommands22.storage.adapters.TaskAdapterManager

/**
 * 快捷指令存储协调器
 *
 * 协调QuickCommand及其关联条件和任务的存储操作。
 * 负责管理快捷指令的完整生命周期，包括创建、读取、更新、删除操作。
 * 确保数据一致性和完整性。
 *
 * 核心特性：
 * - 完整的快捷指令CRUD操作
 * - 数据一致性保证：条件和任务与快捷指令的关联关系
 * - 批量操作优化：减少磁盘I/O操作
 * - 事务性操作：保证操作的原子性
 * - 错误处理和恢复：完善的异常处理机制
 *
 * 存储格式：
 * quick_command_{id}_name = name
 * quick_command_{id}_is_all_day_effective = isAllDayEffective
 * quick_command_{id}_effective_start_time = effectiveStartTime
 * quick_command_{id}_effective_end_time = effectiveEndTime
 * quick_command_{id}_is_enabled = isEnabled
 * quick_command_{id}_require_all_abort_conditions = requireAllAbortConditions
 * quick_command_{id}_trigger_condition_ids_count = triggerConditions.size
 * quick_command_{id}_trigger_condition_ids_0 = conditionId1
 * quick_command_{id}_abort_condition_ids_count = abortConditions.size
 * quick_command_{id}_task_ids_count = tasks.size
 *
 * @param context Android上下文
 */
class QuickCommandStorageCoordinator(context: Context) {

    companion object {
        private const val TAG = "QuickCommandStorageCoordinator"

        // 字段名常量
        private const val FIELD_NAME = "name"
        private const val FIELD_IS_ALL_DAY_EFFECTIVE = "is_all_day_effective"
        private const val FIELD_EFFECTIVE_START_TIME = "effective_start_time"
        private const val FIELD_EFFECTIVE_END_TIME = "effective_end_time"
        private const val FIELD_IS_ENABLED = "is_enabled"
        private const val FIELD_REQUIRE_ALL_CONDITIONS = "require_all_conditions"
        private const val FIELD_REQUIRE_ALL_ABORT_CONDITIONS = "require_all_abort_conditions"
        private const val FIELD_TRIGGER_CONDITION_IDS = "trigger_condition_ids"
        private const val FIELD_ABORT_CONDITION_IDS = "abort_condition_ids"
        private const val FIELD_TASK_IDS = "task_ids"
        private const val FIELD_ICON_URI = "icon_uri"
        private const val FIELD_HAS_CUSTOM_ICON = "has_custom_icon"
    }

    // 核心存储组件
    private val storageManager = NativeTypeStorageManager(context)
    private val appListEngine = AppListStorageEngine(storageManager)
    private val conditionAdapterManager = ConditionAdapterManager(storageManager, appListEngine)
    private val taskAdapterManager = TaskAdapterManager(storageManager)

    /**
     * 保存快捷指令
     * 包括快捷指令基本信息、关联的条件和任务
     *
     * @param command 要保存的快捷指令
     * @return 操作是否成功
     */
    fun saveQuickCommand(command: QuickCommand): Boolean {
        Log.d(TAG, "开始保存快捷指令: ${command.name} (ID: ${command.id})")

        return try {
            // 1. 保存快捷指令基本信息
            val basicInfoSuccess = saveQuickCommandBasicInfo(command)
            if (!basicInfoSuccess) {
                Log.e(TAG, "保存快捷指令基本信息失败: ${command.id}")
                return false
            }

            // 2. 保存触发条件
            val triggerConditionsSuccess = saveConditions(command.triggerConditions)
            if (!triggerConditionsSuccess) {
                Log.e(TAG, "保存触发条件失败: ${command.id}")
                return false
            }

            // 3. 保存中止条件
            val abortConditionsSuccess = saveConditions(command.abortConditions)
            if (!abortConditionsSuccess) {
                Log.e(TAG, "保存中止条件失败: ${command.id}")
                return false
            }

            // 4. 保存任务
            val tasksSuccess = saveTasks(command.tasks)
            if (!tasksSuccess) {
                Log.e(TAG, "保存任务失败: ${command.id}")
                return false
            }

            // 5. 保存关联关系
            val relationshipsSuccess = saveQuickCommandRelationships(command)
            if (!relationshipsSuccess) {
                Log.e(TAG, "保存关联关系失败: ${command.id}")
                return false
            }

            Log.d(TAG, "快捷指令保存成功: ${command.name} (ID: ${command.id})")
            true

        } catch (e: Exception) {
            Log.e(TAG, "保存快捷指令时发生异常: ${command.id}", e)
            false
        }
    }

    /**
     * 加载快捷指令
     *
     * @param commandId 快捷指令ID
     * @return 加载的快捷指令，失败时返回null
     */
    fun loadQuickCommand(commandId: String): QuickCommand? {
        Log.d(TAG, "开始加载快捷指令: $commandId")

        return try {
            // 1. 检查快捷指令是否存在
            if (!quickCommandExists(commandId)) {
                Log.w(TAG, "快捷指令不存在: $commandId")
                return null
            }

            // 2. 加载基本信息
            val basicInfo = loadQuickCommandBasicInfo(commandId)
            if (basicInfo == null) {
                Log.e(TAG, "加载快捷指令基本信息失败: $commandId")
                return null
            }

            // 3. 加载关联的条件ID列表
            val triggerConditionIds = loadConditionIds(commandId, FIELD_TRIGGER_CONDITION_IDS)
            val abortConditionIds = loadConditionIds(commandId, FIELD_ABORT_CONDITION_IDS)

            // 4. 加载关联的任务ID列表
            val taskIds = loadTaskIds(commandId)

            // 5. 加载具体的条件对象
            val triggerConditions = loadConditionsByIds(triggerConditionIds)
            val abortConditions = loadConditionsByIds(abortConditionIds)

            // 6. 加载具体的任务对象
            val tasks = loadTasksByIds(taskIds)

            // 7. 构建完整的快捷指令对象
            val command = QuickCommand(
                id = commandId,
                name = basicInfo.name,
                isAllDayEffective = basicInfo.isAllDayEffective,
                effectiveStartTime = basicInfo.effectiveStartTime,
                effectiveEndTime = basicInfo.effectiveEndTime,
                triggerConditions = triggerConditions,
                requireAllConditions = basicInfo.requireAllConditions,
                tasks = tasks,
                abortConditions = abortConditions,
                requireAllAbortConditions = basicInfo.requireAllAbortConditions,
                isEnabled = basicInfo.isEnabled,
                iconUri = basicInfo.iconUri,
                hasCustomIcon = basicInfo.hasCustomIcon
            )

            Log.d(TAG, "快捷指令加载成功: ${command.name} (ID: $commandId)")
            command

        } catch (e: Exception) {
            Log.e(TAG, "加载快捷指令时发生异常: $commandId", e)
            null
        }
    }

    /**
     * 删除快捷指令
     * 级联删除关联的条件和任务
     *
     * @param commandId 快捷指令ID
     * @return 操作是否成功
     */
    fun deleteQuickCommand(commandId: String): Boolean {
        Log.d(TAG, "开始删除快捷指令: $commandId")

        return try {
            // 1. 检查快捷指令是否存在
            if (!quickCommandExists(commandId)) {
                Log.w(TAG, "要删除的快捷指令不存在: $commandId")
                return true // 已经不存在，认为删除成功
            }

            // 2. 获取关联的条件和任务ID
            val triggerConditionIds = loadConditionIds(commandId, FIELD_TRIGGER_CONDITION_IDS)
            val abortConditionIds = loadConditionIds(commandId, FIELD_ABORT_CONDITION_IDS)
            val taskIds = loadTaskIds(commandId)

            // 3. 删除关联的条件
            deleteConditionsByIds(triggerConditionIds + abortConditionIds)

            // 4. 删除关联的任务
            deleteTasksByIds(taskIds)

            // 5. 删除快捷指令本身
            val prefix = getQuickCommandPrefix(commandId)
            val success = storageManager.deleteByPrefix(StorageDomain.QUICK_COMMANDS, prefix)

            if (success) {
                Log.d(TAG, "快捷指令删除成功: $commandId")
            } else {
                Log.e(TAG, "快捷指令删除失败: $commandId")
            }

            success

        } catch (e: Exception) {
            Log.e(TAG, "删除快捷指令时发生异常: $commandId", e)
            false
        }
    }

    /**
     * 检查快捷指令是否存在
     *
     * @param commandId 快捷指令ID
     * @return 快捷指令是否存在
     */
    fun quickCommandExists(commandId: String): Boolean {
        val nameKey = generateQuickCommandKey(commandId, FIELD_NAME)
        return storageManager.containsKey(StorageDomain.QUICK_COMMANDS, nameKey)
    }

    /**
     * 获取快捷指令键前缀
     *
     * @param commandId 快捷指令ID
     * @return 键前缀
     */
    private fun getQuickCommandPrefix(commandId: String): String {
        return StorageKeyGenerator.getQuickCommandPrefix(commandId)
    }

    /**
     * 生成快捷指令字段键名
     *
     * @param commandId 快捷指令ID
     * @param fieldName 字段名
     * @return 完整键名
     */
    private fun generateQuickCommandKey(commandId: String, fieldName: String): String {
        return StorageKeyGenerator.generateQuickCommandKey(commandId, fieldName)
    }

    /**
     * 快捷指令基本信息数据类
     */
    private data class QuickCommandBasicInfo(
        val name: String,
        val isAllDayEffective: Boolean,
        val effectiveStartTime: String,
        val effectiveEndTime: String,
        val requireAllConditions: Boolean,
        val requireAllAbortConditions: Boolean,
        val isEnabled: Boolean,
        val iconUri: String?,
        val hasCustomIcon: Boolean
    )

    /**
     * 保存快捷指令基本信息
     *
     * @param command 快捷指令
     * @return 操作是否成功
     */
    private fun saveQuickCommandBasicInfo(command: QuickCommand): Boolean {
        return try {
            val operations = listOf(
                StorageOperation.createStringOperation(
                    StorageDomain.QUICK_COMMANDS,
                    generateQuickCommandKey(command.id, FIELD_NAME),
                    command.name
                ),
                StorageOperation.createBooleanOperation(
                    StorageDomain.QUICK_COMMANDS,
                    generateQuickCommandKey(command.id, FIELD_IS_ALL_DAY_EFFECTIVE),
                    command.isAllDayEffective
                ),
                StorageOperation.createStringOperation(
                    StorageDomain.QUICK_COMMANDS,
                    generateQuickCommandKey(command.id, FIELD_EFFECTIVE_START_TIME),
                    command.effectiveStartTime
                ),
                StorageOperation.createStringOperation(
                    StorageDomain.QUICK_COMMANDS,
                    generateQuickCommandKey(command.id, FIELD_EFFECTIVE_END_TIME),
                    command.effectiveEndTime
                ),
                StorageOperation.createBooleanOperation(
                    StorageDomain.QUICK_COMMANDS,
                    generateQuickCommandKey(command.id, FIELD_IS_ENABLED),
                    command.isEnabled
                ),
                StorageOperation.createBooleanOperation(
                    StorageDomain.QUICK_COMMANDS,
                    generateQuickCommandKey(command.id, FIELD_REQUIRE_ALL_CONDITIONS),
                    command.requireAllConditions
                ),
                StorageOperation.createBooleanOperation(
                    StorageDomain.QUICK_COMMANDS,
                    generateQuickCommandKey(command.id, FIELD_REQUIRE_ALL_ABORT_CONDITIONS),
                    command.requireAllAbortConditions
                ),
                StorageOperation.createStringOperation(
                    StorageDomain.QUICK_COMMANDS,
                    generateQuickCommandKey(command.id, FIELD_ICON_URI),
                    command.iconUri ?: ""
                ),
                StorageOperation.createBooleanOperation(
                    StorageDomain.QUICK_COMMANDS,
                    generateQuickCommandKey(command.id, FIELD_HAS_CUSTOM_ICON),
                    command.hasCustomIcon
                )
            )

            storageManager.executeBatch(operations)
        } catch (e: Exception) {
            Log.e(TAG, "保存快捷指令基本信息时发生异常: ${command.id}", e)
            false
        }
    }

    /**
     * 加载快捷指令基本信息
     *
     * @param commandId 快捷指令ID
     * @return 基本信息，失败时返回null
     */
    private fun loadQuickCommandBasicInfo(commandId: String): QuickCommandBasicInfo? {
        return try {
            val name = storageManager.loadString(
                StorageDomain.QUICK_COMMANDS,
                generateQuickCommandKey(commandId, FIELD_NAME)
            )

            if (name.isEmpty()) {
                Log.w(TAG, "快捷指令名称为空: $commandId")
                return null
            }

            QuickCommandBasicInfo(
                name = name,
                isAllDayEffective = storageManager.loadBoolean(
                    StorageDomain.QUICK_COMMANDS,
                    generateQuickCommandKey(commandId, FIELD_IS_ALL_DAY_EFFECTIVE),
                    true
                ),
                effectiveStartTime = storageManager.loadString(
                    StorageDomain.QUICK_COMMANDS,
                    generateQuickCommandKey(commandId, FIELD_EFFECTIVE_START_TIME),
                    "00:00"
                ),
                effectiveEndTime = storageManager.loadString(
                    StorageDomain.QUICK_COMMANDS,
                    generateQuickCommandKey(commandId, FIELD_EFFECTIVE_END_TIME),
                    "23:59"
                ),
                isEnabled = storageManager.loadBoolean(
                    StorageDomain.QUICK_COMMANDS,
                    generateQuickCommandKey(commandId, FIELD_IS_ENABLED),
                    true
                ),
                requireAllConditions = storageManager.loadBoolean(
                    StorageDomain.QUICK_COMMANDS,
                    generateQuickCommandKey(commandId, FIELD_REQUIRE_ALL_CONDITIONS),
                    true
                ),
                requireAllAbortConditions = storageManager.loadBoolean(
                    StorageDomain.QUICK_COMMANDS,
                    generateQuickCommandKey(commandId, FIELD_REQUIRE_ALL_ABORT_CONDITIONS),
                    false
                ),
                iconUri = storageManager.loadString(
                    StorageDomain.QUICK_COMMANDS,
                    generateQuickCommandKey(commandId, FIELD_ICON_URI),
                    ""
                ).takeIf { it.isNotEmpty() },
                hasCustomIcon = storageManager.loadBoolean(
                    StorageDomain.QUICK_COMMANDS,
                    generateQuickCommandKey(commandId, FIELD_HAS_CUSTOM_ICON),
                    false
                )
            )
        } catch (e: Exception) {
            Log.e(TAG, "加载快捷指令基本信息时发生异常: $commandId", e)
            null
        }
    }

    /**
     * 保存条件列表
     *
     * @param conditions 条件列表
     * @return 操作是否成功
     */
    private fun saveConditions(conditions: List<SharedTriggerCondition>): Boolean {
        return try {
            var successCount = 0
            conditions.forEach { condition ->
                if (conditionAdapterManager.saveCondition(condition)) {
                    successCount++
                }
            }

            val allSuccess = successCount == conditions.size
            if (allSuccess) {
                Log.d(TAG, "所有条件保存成功: $successCount/${conditions.size}")
            } else {
                Log.w(TAG, "部分条件保存失败: $successCount/${conditions.size}")
            }

            allSuccess
        } catch (e: Exception) {
            Log.e(TAG, "保存条件列表时发生异常", e)
            false
        }
    }

    /**
     * 保存任务列表
     *
     * @param tasks 任务列表
     * @return 操作是否成功
     */
    private fun saveTasks(tasks: List<SharedTask>): Boolean {
        return try {
            var successCount = 0
            tasks.forEach { task ->
                if (taskAdapterManager.saveTask(task)) {
                    successCount++
                }
            }

            val allSuccess = successCount == tasks.size
            if (allSuccess) {
                Log.d(TAG, "所有任务保存成功: $successCount/${tasks.size}")
            } else {
                Log.w(TAG, "部分任务保存失败: $successCount/${tasks.size}")
            }

            allSuccess
        } catch (e: Exception) {
            Log.e(TAG, "保存任务列表时发生异常", e)
            false
        }
    }

    /**
     * 保存快捷指令关联关系
     *
     * @param command 快捷指令
     * @return 操作是否成功
     */
    private fun saveQuickCommandRelationships(command: QuickCommand): Boolean {
        return try {
            val operations = mutableListOf<StorageOperation>()

            // 保存触发条件ID列表
            operations.addAll(saveIdList(command.id, FIELD_TRIGGER_CONDITION_IDS, command.triggerConditions.map { it.id }))

            // 保存中止条件ID列表
            operations.addAll(saveIdList(command.id, FIELD_ABORT_CONDITION_IDS, command.abortConditions.map { it.id }))

            // 保存任务ID列表
            operations.addAll(saveIdList(command.id, FIELD_TASK_IDS, command.tasks.map { it.id }))

            storageManager.executeBatch(operations)
        } catch (e: Exception) {
            Log.e(TAG, "保存快捷指令关联关系时发生异常: ${command.id}", e)
            false
        }
    }

    /**
     * 保存ID列表
     *
     * @param commandId 快捷指令ID
     * @param fieldName 字段名
     * @param ids ID列表
     * @return 存储操作列表
     */
    private fun saveIdList(commandId: String, fieldName: String, ids: List<String>): List<StorageOperation> {
        val operations = mutableListOf<StorageOperation>()

        // 保存数量
        operations.add(
            StorageOperation.createIntOperation(
                StorageDomain.QUICK_COMMANDS,
                generateQuickCommandKey(commandId, "${fieldName}_count"),
                ids.size
            )
        )

        // 保存每个ID
        ids.forEachIndexed { index, id ->
            operations.add(
                StorageOperation.createStringOperation(
                    StorageDomain.QUICK_COMMANDS,
                    generateQuickCommandKey(commandId, "${fieldName}_$index"),
                    id
                )
            )
        }

        return operations
    }

    /**
     * 加载条件ID列表
     *
     * @param commandId 快捷指令ID
     * @param fieldName 字段名
     * @return 条件ID列表
     */
    private fun loadConditionIds(commandId: String, fieldName: String): List<String> {
        return loadIdList(commandId, fieldName)
    }

    /**
     * 加载任务ID列表
     *
     * @param commandId 快捷指令ID
     * @return 任务ID列表
     */
    private fun loadTaskIds(commandId: String): List<String> {
        return loadIdList(commandId, FIELD_TASK_IDS)
    }

    /**
     * 加载ID列表
     *
     * @param commandId 快捷指令ID
     * @param fieldName 字段名
     * @return ID列表
     */
    private fun loadIdList(commandId: String, fieldName: String): List<String> {
        return try {
            val count = storageManager.loadInt(
                StorageDomain.QUICK_COMMANDS,
                generateQuickCommandKey(commandId, "${fieldName}_count"),
                0
            )

            if (count == 0) {
                return emptyList()
            }

            val ids = mutableListOf<String>()
            for (index in 0 until count) {
                val id = storageManager.loadString(
                    StorageDomain.QUICK_COMMANDS,
                    generateQuickCommandKey(commandId, "${fieldName}_$index")
                )
                if (id.isNotEmpty()) {
                    ids.add(id)
                }
            }

            ids
        } catch (e: Exception) {
            Log.e(TAG, "加载ID列表时发生异常: $commandId, $fieldName", e)
            emptyList()
        }
    }

    /**
     * 根据ID列表加载条件
     *
     * @param conditionIds 条件ID列表
     * @return 条件列表
     */
    private fun loadConditionsByIds(conditionIds: List<String>): List<SharedTriggerCondition> {
        val conditions = mutableListOf<SharedTriggerCondition>()

        conditionIds.forEach { conditionId ->
            try {
                val condition = conditionAdapterManager.loadConditionByStoredType(conditionId)
                if (condition != null) {
                    conditions.add(condition)
                } else {
                    Log.w(TAG, "无法加载条件: $conditionId")
                }
            } catch (e: Exception) {
                Log.e(TAG, "加载条件时发生异常: $conditionId", e)
            }
        }

        return conditions
    }

    /**
     * 根据ID列表加载任务
     *
     * @param taskIds 任务ID列表
     * @return 任务列表
     */
    private fun loadTasksByIds(taskIds: List<String>): List<SharedTask> {
        val tasks = mutableListOf<SharedTask>()

        taskIds.forEach { taskId ->
            try {
                // 首先读取任务类型
                val taskTypeKey = "task_${taskId}_type"
                val taskType = storageManager.loadString(StorageDomain.TASKS, taskTypeKey)

                if (taskType.isNotEmpty()) {
                    val task = taskAdapterManager.loadTask<SharedTask>(taskId, taskType)
                    if (task != null) {
                        tasks.add(task)
                    } else {
                        Log.w(TAG, "无法加载任务: $taskId (类型: $taskType)")
                    }
                } else {
                    Log.w(TAG, "任务类型信息不存在: $taskId")
                }
            } catch (e: Exception) {
                Log.e(TAG, "加载任务时发生异常: $taskId", e)
            }
        }

        return tasks
    }

    /**
     * 根据ID列表删除条件
     *
     * @param conditionIds 条件ID列表
     * @return 成功删除的条件数量
     */
    private fun deleteConditionsByIds(conditionIds: List<String>): Int {
        var successCount = 0

        conditionIds.forEach { conditionId ->
            try {
                // 首先读取条件类型
                val conditionTypeKey = "condition_${conditionId}_type"
                val conditionType = storageManager.loadString(StorageDomain.CONDITIONS, conditionTypeKey)

                if (conditionType.isNotEmpty()) {
                    if (conditionAdapterManager.deleteCondition(conditionId, conditionType)) {
                        successCount++
                    }
                } else {
                    Log.w(TAG, "条件类型信息不存在，直接删除前缀: $conditionId")
                    // 如果类型信息不存在，尝试直接删除前缀
                    val prefix = "condition_${conditionId}_"
                    if (storageManager.deleteByPrefix(StorageDomain.CONDITIONS, prefix)) {
                        successCount++
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "删除条件时发生异常: $conditionId", e)
            }
        }

        Log.d(TAG, "删除条件完成: 成功 $successCount/${conditionIds.size}")
        return successCount
    }

    /**
     * 根据ID列表删除任务
     *
     * @param taskIds 任务ID列表
     * @return 成功删除的任务数量
     */
    private fun deleteTasksByIds(taskIds: List<String>): Int {
        var successCount = 0

        taskIds.forEach { taskId ->
            try {
                // 首先读取任务类型
                val taskTypeKey = "task_${taskId}_type"
                val taskType = storageManager.loadString(StorageDomain.TASKS, taskTypeKey)

                if (taskType.isNotEmpty()) {
                    if (taskAdapterManager.deleteTask(taskId, taskType)) {
                        successCount++
                    }
                } else {
                    Log.w(TAG, "任务类型信息不存在，直接删除前缀: $taskId")
                    // 如果类型信息不存在，尝试直接删除前缀
                    val prefix = "task_${taskId}_"
                    if (storageManager.deleteByPrefix(StorageDomain.TASKS, prefix)) {
                        successCount++
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "删除任务时发生异常: $taskId", e)
            }
        }

        Log.d(TAG, "删除任务完成: 成功 $successCount/${taskIds.size}")
        return successCount
    }

    /**
     * 加载所有快捷指令
     *
     * @return 快捷指令列表
     */
    fun loadAllQuickCommands(): List<QuickCommand> {
        Log.d(TAG, "开始加载所有快捷指令")

        return try {
            val commands = mutableListOf<QuickCommand>()

            // 获取所有快捷指令的ID
            val commandIds = getAllQuickCommandIds()

            commandIds.forEach { commandId ->
                try {
                    val command = loadQuickCommand(commandId)
                    if (command != null) {
                        commands.add(command)
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "加载快捷指令时发生异常: $commandId", e)
                }
            }

            Log.d(TAG, "加载所有快捷指令完成: ${commands.size}/${commandIds.size}")
            commands

        } catch (e: Exception) {
            Log.e(TAG, "加载所有快捷指令时发生异常", e)
            emptyList()
        }
    }

    /**
     * 获取所有快捷指令ID
     *
     * @return 快捷指令ID列表
     */
    private fun getAllQuickCommandIds(): List<String> {
        return try {
            val prefs = storageManager.getPreferences(StorageDomain.QUICK_COMMANDS)
            val allKeys = prefs.all.keys

            // 提取所有快捷指令ID（从name字段的键中提取）
            val commandIds = mutableSetOf<String>()
            allKeys.forEach { key ->
                if (key.startsWith("quick_command_") && key.endsWith("_name")) {
                    val commandId = key.removePrefix("quick_command_").removeSuffix("_name")
                    commandIds.add(commandId)
                }
            }

            commandIds.toList()
        } catch (e: Exception) {
            Log.e(TAG, "获取所有快捷指令ID时发生异常", e)
            emptyList()
        }
    }

    /**
     * 批量保存快捷指令
     *
     * @param commands 快捷指令列表
     * @return 成功保存的快捷指令数量
     */
    fun saveQuickCommands(commands: List<QuickCommand>): Int {
        var successCount = 0
        commands.forEach { command ->
            if (saveQuickCommand(command)) {
                successCount++
            }
        }
        Log.d(TAG, "批量保存快捷指令完成: 成功 $successCount/${commands.size}")
        return successCount
    }

    /**
     * 批量删除快捷指令
     *
     * @param commandIds 快捷指令ID列表
     * @return 成功删除的快捷指令数量
     */
    fun deleteQuickCommands(commandIds: List<String>): Int {
        var successCount = 0
        commandIds.forEach { commandId ->
            if (deleteQuickCommand(commandId)) {
                successCount++
            }
        }
        Log.d(TAG, "批量删除快捷指令完成: 成功 $successCount/${commandIds.size}")
        return successCount
    }

    /**
     * 获取快捷指令数量
     *
     * @return 快捷指令数量
     */
    fun getQuickCommandCount(): Int {
        return getAllQuickCommandIds().size
    }

    /**
     * 清空所有快捷指令数据
     * 注意：这是一个危险操作，会删除所有数据
     *
     * @return 操作是否成功
     */
    fun clearAllData(): Boolean {
        Log.w(TAG, "开始清空所有快捷指令数据")

        return try {
            // 清空所有存储域
            val domains = listOf(
                StorageDomain.QUICK_COMMANDS,
                StorageDomain.CONDITIONS,
                StorageDomain.TASKS,
                StorageDomain.APP_LISTS
            )

            var allSuccess = true
            domains.forEach { domain ->
                val prefs = storageManager.getPreferences(domain)
                val editor = prefs.edit()
                editor.clear()
                if (!editor.commit()) {
                    allSuccess = false
                    Log.e(TAG, "清空存储域失败: ${domain.prefsName}")
                }
            }

            if (allSuccess) {
                Log.d(TAG, "所有数据清空成功")
            } else {
                Log.e(TAG, "部分数据清空失败")
            }

            allSuccess

        } catch (e: Exception) {
            Log.e(TAG, "清空所有数据时发生异常", e)
            false
        }
    }
}
