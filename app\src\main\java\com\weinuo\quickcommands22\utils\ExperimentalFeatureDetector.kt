package com.weinuo.quickcommands22.utils

import androidx.compose.runtime.*
import kotlinx.coroutines.delay
import java.util.*

/**
 * 实验性功能检测器
 *
 * 实现完全被动检测机制：
 * - 零主动检测：不在后台做任何时间检测
 * - 入口触发：只有当用户在凌晨4:00-4:01进入全局设置界面时才激活检测逻辑
 * - 完全静默：在非目标时间段，这个特性的代码完全不运行
 *
 * 激活条件：
 * 1. 时间限制：每天凌晨4:00-4:01
 * 2. 位置要求：用户在全局设置导航项
 * 3. 操作要求：连续轮流点击全局设置左下角白色背景和全局设置导航项20次
 * 4. 时间限制：超过1秒没有继续点击会重新计数
 */
class ExperimentalFeatureDetector {

    companion object {
        private const val TAG = "ExperimentalFeatureDetector"
        private const val REQUIRED_CLICKS = 20
        private const val CLICK_TIMEOUT_MS = 1000L
        private const val TARGET_HOUR = 4
        private const val TARGET_MINUTE_START = 0
        private const val TARGET_MINUTE_END = 1
    }

    /**
     * 点击目标类型
     */
    enum class ClickTarget {
        BACKGROUND_AREA,    // 全局设置左下角白色背景区域
        NAVIGATION_ITEM     // 全局设置导航项
    }

    /**
     * 检测状态
     */
    data class DetectionState(
        val isActive: Boolean = false,
        val clickCount: Int = 0,
        val lastClickTarget: ClickTarget? = null,
        val lastClickTime: Long = 0L,
        val isCompleted: Boolean = false
    )

    private var _detectionState = mutableStateOf(DetectionState())
    val detectionState: State<DetectionState> = _detectionState

    /**
     * 检查当前时间是否在目标时间窗口内（凌晨4:00-4:01）
     * 只有在用户进入全局设置界面时才会调用此方法
     */
    fun isInTargetTimeWindow(): Boolean {
        val calendar = Calendar.getInstance()
        val hour = calendar.get(Calendar.HOUR_OF_DAY)
        val minute = calendar.get(Calendar.MINUTE)

        return hour == TARGET_HOUR && minute >= TARGET_MINUTE_START && minute <= TARGET_MINUTE_END
    }

    /**
     * 激活检测（仅在目标时间窗口内且用户在全局设置界面时调用）
     */
    fun activateDetection() {
        val currentState = _detectionState.value

        // 如果已经激活，不要重复激活（避免重置状态）
        if (currentState.isActive) {
            return
        }

        if (!isInTargetTimeWindow()) {
            return
        }

        _detectionState.value = currentState.copy(isActive = true)
    }

    /**
     * 停用检测（用户离开全局设置界面时调用）
     */
    fun deactivateDetection() {
        _detectionState.value = DetectionState()
    }

    /**
     * 处理点击事件
     *
     * @param target 点击目标类型
     * @return 是否完成了激活序列
     */
    fun handleClick(target: ClickTarget): Boolean {
        val currentState = _detectionState.value

        // 如果检测未激活，忽略点击
        if (!currentState.isActive) {
            return false
        }

        val currentTime = System.currentTimeMillis()

        // 检查是否超时（超过1秒没有点击）
        if (currentState.clickCount > 0 &&
            currentTime - currentState.lastClickTime > CLICK_TIMEOUT_MS) {
            _detectionState.value = currentState.copy(
                clickCount = 0,
                lastClickTarget = null,
                lastClickTime = 0L
            )
            return false
        }

        // 检查是否是正确的轮流点击模式
        val isValidClick = when {
            currentState.clickCount == 0 -> true // 第一次点击，任何目标都可以
            currentState.lastClickTarget == null -> true // 安全检查
            currentState.lastClickTarget != target -> true // 必须轮流点击不同目标
            else -> false // 连续点击同一目标，无效
        }

        if (!isValidClick) {
            _detectionState.value = currentState.copy(
                clickCount = 0,
                lastClickTarget = null,
                lastClickTime = 0L
            )
            return false
        }

        val newClickCount = currentState.clickCount + 1

        // 检查是否完成激活序列
        if (newClickCount >= REQUIRED_CLICKS) {
            _detectionState.value = currentState.copy(
                clickCount = newClickCount,
                lastClickTarget = target,
                lastClickTime = currentTime,
                isCompleted = true
            )
            return true
        }

        // 更新状态
        _detectionState.value = currentState.copy(
            clickCount = newClickCount,
            lastClickTarget = target,
            lastClickTime = currentTime
        )

        return false
    }

    /**
     * 重置检测状态
     */
    fun reset() {
        _detectionState.value = DetectionState()
    }

    /**
     * 获取当前进度百分比
     */
    fun getProgress(): Float {
        val currentState = _detectionState.value
        return if (currentState.isActive) {
            currentState.clickCount.toFloat() / REQUIRED_CLICKS.toFloat()
        } else {
            0f
        }
    }
}

/**
 * Composable函数：实验性功能检测器状态管理
 *
 * @param isInGlobalSettings 是否在全局设置界面
 * @param onActivationCompleted 激活完成回调
 */
@Composable
fun rememberExperimentalFeatureDetector(
    isInGlobalSettings: Boolean,
    onActivationCompleted: () -> Unit = {}
): ExperimentalFeatureDetector {
    val detector = remember { ExperimentalFeatureDetector() }
    val detectionState by detector.detectionState

    // 监听全局设置界面状态变化
    LaunchedEffect(isInGlobalSettings) {
        if (isInGlobalSettings) {
            detector.activateDetection()
        } else {
            detector.deactivateDetection()
        }
    }

    // 监听激活完成状态
    LaunchedEffect(detectionState.isCompleted) {
        if (detectionState.isCompleted) {
            onActivationCompleted()
            // 延迟重置状态，避免重复触发
            delay(1000)
            detector.reset()
        }
    }

    return detector
}
