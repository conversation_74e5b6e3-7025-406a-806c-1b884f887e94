package com.weinuo.quickcommands22.ui.activities

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.runtime.Composable
import androidx.compose.ui.platform.LocalContext
import com.weinuo.quickcommands22.ui.configuration.ConfigurationMode
import com.weinuo.quickcommands22.ui.configuration.ConfigurationItem
import com.weinuo.quickcommands22.ui.configuration.ConfigurationDataProvider
import com.weinuo.quickcommands22.ui.screens.DetailedConfigurationContent
import com.weinuo.quickcommands22.ui.theme.QuickCommandsTheme
import com.weinuo.quickcommands22.storage.NavigationDataStorageManager
import com.weinuo.quickcommands22.model.SharedTriggerCondition
import com.weinuo.quickcommands22.model.SharedTask

/**
 * 详细配置Activity
 * 
 * 独立的Activity，用于详细配置界面，不包含底部导航栏。
 * 提供更好的用户体验和更大的配置空间。
 * 支持编辑模式，可以预填充现有配置数据进行修改。
 */
class DetailedConfigurationActivity : ComponentActivity() {
    
    companion object {
        private const val EXTRA_CONFIGURATION_MODE = "configuration_mode"
        private const val EXTRA_ITEM_TYPE = "item_type"
        private const val EXTRA_EDIT_DATA = "edit_data"
        private const val EXTRA_EDIT_INDEX = "edit_index"
        
        /**
         * 启动详细配置Activity
         */
        fun startForConfiguration(
            context: Context,
            configurationItem: ConfigurationItem,
            configurationMode: ConfigurationMode,
            editData: String? = null,
            editIndex: Int? = null
        ) {
            val intent = Intent(context, DetailedConfigurationActivity::class.java).apply {
                putExtra(EXTRA_CONFIGURATION_MODE, configurationMode.name)
                putExtra(EXTRA_ITEM_TYPE, configurationItem.id)
                editData?.let { putExtra(EXTRA_EDIT_DATA, it) }
                editIndex?.let { putExtra(EXTRA_EDIT_INDEX, it) }
            }
            context.startActivity(intent)
        }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 获取传入的参数
        val configurationModeString = intent.getStringExtra(EXTRA_CONFIGURATION_MODE)
        val itemType = intent.getStringExtra(EXTRA_ITEM_TYPE)
        val editData = intent.getStringExtra(EXTRA_EDIT_DATA)
        val editIndex = intent.getIntExtra(EXTRA_EDIT_INDEX, -1).takeIf { it != -1 }
        
        // 解析配置模式
        val configurationMode = try {
            ConfigurationMode.valueOf(configurationModeString ?: ConfigurationMode.TRIGGER_CONDITION.name)
        } catch (e: IllegalArgumentException) {
            ConfigurationMode.TRIGGER_CONDITION
        }
        
        setContent {
            QuickCommandsTheme {
                DetailedConfigurationActivityContent(
                    configurationMode = configurationMode,
                    itemType = itemType,
                    editData = editData,
                    editIndex = editIndex,
                    onFinish = { finish() },
                    onConfigurationComplete = { navigationKey, editIdx ->
                        android.util.Log.d("DetailedConfigurationActivity", "onConfigurationComplete called: navigationKey=$navigationKey, editIdx=$editIdx")

                        // 通过Intent返回结果
                        val resultIntent = Intent().apply {
                            putExtra("navigation_key", navigationKey)
                            putExtra("edit_index", editIdx)
                            putExtra("config_mode", configurationMode.name)
                        }

                        android.util.Log.d("DetailedConfigurationActivity", "Setting result: RESULT_OK with intent extras")
                        setResult(Activity.RESULT_OK, resultIntent)

                        android.util.Log.d("DetailedConfigurationActivity", "Finishing DetailedConfigurationActivity")
                        finish()
                    }
                )
            }
        }
    }
}

/**
 * 详细配置Activity的内容组件
 */
@Composable
private fun DetailedConfigurationActivityContent(
    configurationMode: ConfigurationMode,
    itemType: String?,
    editData: String?,
    editIndex: Int?,
    onFinish: () -> Unit,
    onConfigurationComplete: (String, Int?) -> Unit
) {
    val context = LocalContext.current

    // 获取所有配置项
    // 实验性功能过滤已在子配置项级别实现，主配置项无需过滤
    val allConfigItems = ConfigurationDataProvider.getItemsForMode(context, configurationMode)

    // 根据itemType查找对应的配置项
    val configurationItem = itemType?.let { type ->
        allConfigItems.find { it.id == type }
    }
    
    if (configurationItem != null) {
        // 加载初始配置对象（如果是编辑模式）
        val initialConfigObject = editData?.let { data ->
            val navigationDataManager = NavigationDataStorageManager(context)
            when (configurationMode) {
                ConfigurationMode.TRIGGER_CONDITION,
                ConfigurationMode.ABORT_CONDITION -> {
                    navigationDataManager.loadConditionEditData(data)?.condition
                }
                ConfigurationMode.TASK -> {
                    navigationDataManager.loadTaskEditData(data)?.task
                }
            }
        }
        
        DetailedConfigurationContent(
            configurationItem = configurationItem,
            configurationMode = configurationMode,
            initialConfigObject = initialConfigObject,
            editIndex = editIndex,
            onNavigateBack = onFinish,
            onConfigured = { configuredItem, editIdx ->
                android.util.Log.d("DetailedConfigurationActivity", "onConfigured called: configuredItem=$configuredItem, editIdx=$editIdx")

                // 配置完成，保存配置数据到NavigationDataStorageManager
                val navigationDataManager = NavigationDataStorageManager(context)
                val navigationKey = when (configurationMode) {
                    ConfigurationMode.TRIGGER_CONDITION -> {
                        val key = "trigger_condition_edit_${System.currentTimeMillis()}"
                        val saveResult = navigationDataManager.saveConditionEditData(
                            key,
                            configuredItem as SharedTriggerCondition,
                            editIdx
                        )
                        android.util.Log.d("DetailedConfigurationActivity", "Saved trigger condition data: key=$key, success=$saveResult")
                        key
                    }
                    ConfigurationMode.ABORT_CONDITION -> {
                        val key = "abort_condition_edit_${System.currentTimeMillis()}"
                        val saveResult = navigationDataManager.saveConditionEditData(
                            key,
                            configuredItem as SharedTriggerCondition,
                            editIdx
                        )
                        android.util.Log.d("DetailedConfigurationActivity", "Saved abort condition data: key=$key, success=$saveResult")
                        key
                    }
                    ConfigurationMode.TASK -> {
                        val key = "task_edit_${System.currentTimeMillis()}"
                        val saveResult = navigationDataManager.saveTaskEditData(
                            key,
                            configuredItem as SharedTask,
                            editIdx
                        )
                        android.util.Log.d("DetailedConfigurationActivity", "Saved task data: key=$key, success=$saveResult")
                        key
                    }
                }

                android.util.Log.d("DetailedConfigurationActivity", "About to call onConfigurationComplete with navigationKey=$navigationKey, editIdx=$editIdx")

                // 通过回调返回结果并立即关闭Activity
                onConfigurationComplete(navigationKey, editIdx)
            }
        )
    } else {
        // 配置项未找到，直接返回
        onFinish()
    }
}
